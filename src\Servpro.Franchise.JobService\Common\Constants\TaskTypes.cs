﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class TaskTypes
    {
        public static readonly Guid CustomerRefusedService = new Guid("40081B43-8BC8-4FB1-AF72-A690D205DA0F");
        public static readonly Guid PreExistingCondition = new Guid("e848c768-f283-48c4-8c37-1ab8114683f7");
        public static readonly Guid ZoneNotConfirmed = new Guid("826E4FA3-49BA-4A2D-8237-8E488C63288A");
        public static readonly Guid EquipmentValidationException = new Guid("052BBC51-3FA9-421D-A0A3-D81A6708C5DE");
        public static readonly Guid WaterClassOverriden = new Guid("DB8EDACF-F1F0-456A-A3DB-1BEA88F4EAAE");
        public static readonly Guid CustomerFinalWalkThrough = new Guid("4C17E978-ED99-441D-8FF8-9152BA0BF589");
        public static readonly Guid CosJobCompletionNotSigned = new Guid("977C0787-E5B1-4E59-A6EA-04176D95B505");
    }
}
