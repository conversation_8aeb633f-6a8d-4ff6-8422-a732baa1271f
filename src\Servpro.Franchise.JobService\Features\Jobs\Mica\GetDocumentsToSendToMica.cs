﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Infrastructure.MicaService.GetSentEntities;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetDocumentsToSendToMica
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public List<Guid> DocumentIds { get; set; }
        }

        public class Dto
        {
            public List<Document> Documents { get; set; }
            public class Document
            {
                public Guid Id { get; set; }
                public string FileName { get; set; }
                public string Description { get; set; }
                public string BucketName { get; set; }
                public string MediaPath { get; set; }
                public string CreatedBy { get; set; }
            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetDocumentsToSendToMica> _logger;
            private readonly IMicaServiceClient _micaServiceClient;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(JobReadOnlyDataContext context,
                ILogger<GetDocumentsToSendToMica> logger,
                IAmazonS3 clientS3,
                IMicaServiceClient micaServiceClient)
            {
                _context = context;
                _logger = logger;
                _micaServiceClient = micaServiceClient;
                _clientS3 = clientS3;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Documents to send to Mica");

                var returnDto = new Dto()
                {
                    Documents = new List<Dto.Document>()
                };
                List<MediaMetadata> documents = new List<MediaMetadata>();

                if (request.DocumentIds.Any())
                {
                    documents = await _context.MediaMetadata
                        .Where(x => request.DocumentIds.Contains(x.Id))
                        .ToListAsync(cancellationToken);

                    foreach (var doc in documents)
                    {
                        returnDto.Documents.Add(await MapDocumentAsync(doc, cancellationToken));
                    }
                }
                return returnDto;
            }

            private async Task<Dto.Document> MapDocumentAsync(MediaMetadata media, CancellationToken cancellationToken)
            => new Dto.Document
                {
                    Id = media.Id,
                    FileName = media.Name,
                    Description = media.Description,
                    CreatedBy = media.CreatedBy,
                    BucketName = media.BucketName,
                    MediaPath = media.MediaPath
                };
        }
    }
}
