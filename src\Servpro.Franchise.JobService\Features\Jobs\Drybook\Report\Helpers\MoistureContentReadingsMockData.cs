﻿using System;
using System.Collections.Generic;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class MoistureContentReadingsMockData
    {
        public static MoistureContentReadingsDto GetMockForPreview()
        {
            var moistureContentReadingsModel = new MoistureContentReadingsDto
            {
                MoistureContentReadings = new List<MoistureContentReading>
                {
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 31, 13, 16, 0),
                        VisitIndex = 6,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 4, 1, 13, 16, 0),
                        VisitIndex = 7,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 4, 2, 13, 16, 0),
                        VisitIndex = 8,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 4, 3, 13, 16, 0),
                        VisitIndex = 9,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 4, 4, 13, 16, 0),
                        VisitIndex = 10,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 4, 5, 13, 16, 0),
                        VisitIndex = 11,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 4, 6, 13, 16, 0),
                        VisitIndex = 12,
                        Zone = "Zone 1"
                    },
                    // original
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = 25,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 500,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 500,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 500,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 500,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 500,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 400,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 400,
                        ReadingValue = 75,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = true,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = 155,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = true,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 200,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 200,
                        ReadingValue = 67,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 205,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 205,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 205,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 205,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 205,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 30,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 300,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 60,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 45,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Hallway",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 1"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 300,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 300,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Concrete - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Concrete - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Concrete - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Concrete - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Concrete - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Basement",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 4"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 300,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = true,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 20,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = true,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 65,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Living Room",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 400,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = 300,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = 110,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 400,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 100,
                        ReadingValue = 55,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 100,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 100,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bathroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 300,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 250,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = true,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 75,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 125,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Kitchen",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 200,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 200,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 300,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = true,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 100,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Tile - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 300,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 75,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 18,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Floor",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 250,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 18,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Floor",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 250,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 18,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Floor",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 250,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 18,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Floor",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 250,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 18,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Floor",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 250,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Den",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 2"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = 75,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = 40,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Carpet - Floor",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = null,
                        ReadingValue = 100,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 100,
                        ReadingValue = 80,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 100,
                        ReadingValue = 30,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 100,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 20,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Drywall - Wall",
                        NotDrying = false,
                        PointsOrPercent = "Pts",
                        PreviousReadingValue = 100,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = null,
                        ReadingValue = 150,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 26, 9, 30, 50),
                        VisitIndex = 1,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 75,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 27, 9, 33, 0),
                        VisitIndex = 2,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = true,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = 25,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 28, 10, 16, 0),
                        VisitIndex = 3,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = true,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "",
                        VisitDate = new DateTime(2015, 3, 29, 0, 0, 0),
                        VisitIndex = 4,
                        Zone = "Zone 3"
                    },
                    new MoistureContentReading
                    {
                        Goal = 30,
                        GoalMet = false,
                        IsMissingVisit = false,
                        Material = "Wood - Trim, Baseboard",
                        NotDrying = false,
                        PointsOrPercent = "%",
                        PreviousReadingValue = 150,
                        ReadingValue = null,
                        RemovedOnVisit = false,
                        Room = "Bedroom 2",
                        Technician = "MT",
                        VisitDate = new DateTime(2015, 3, 30, 13, 16, 0),
                        VisitIndex = 5,
                        Zone = "Zone 3"
                    }
                }
            };
            return moistureContentReadingsModel;
        }

    }
}
