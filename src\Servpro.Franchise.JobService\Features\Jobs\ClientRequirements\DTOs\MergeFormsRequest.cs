﻿using System.Collections.Generic;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class MergeFormsRequest
    {

        public MergeFormsRequest(Job job, FranchiseDto franchise, GetFranchiseSetDto franchiseSet)
        {
            Job = job;
            Franchise = franchise;
            FranchiseSet = franchiseSet;
        }

        public Job Job { get; }
        public FranchiseDto Franchise { get; }
        public GetFranchiseSetDto FranchiseSet { get; }
        public List<FormTemplate> FormTemplates { get; } = new List<FormTemplate>();
    }
}
