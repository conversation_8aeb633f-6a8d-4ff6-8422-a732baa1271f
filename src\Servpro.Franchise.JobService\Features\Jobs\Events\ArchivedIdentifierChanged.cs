﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Contacts;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ArchivedIdentifierChanged
    {
        public class Event : ArchivedIdentifierChangedEvent, IRequest
        {
            public Event(JobDto jobDto, Guid correlationId) : base(jobDto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<ArchivedIdentifierChanged> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<ArchivedIdentifierChanged> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("Event: {eventName}, JobId: {jobId}",
                    nameof(ArchivedIdentifierChangedEvent ),
                    request.Job.JobId);
                _logger.LogInformation("Starting with request {@request}", request);

                var jobDto = request.Job;
                var job = await _context.Jobs.FirstOrDefaultAsync(x => x.Id == request.Job.JobId, cancellationToken);
                var wipRecord = await _context.WipRecords.FirstOrDefaultAsync(w => w.Id == request.Job.JobId, cancellationToken);

                if (job == null || wipRecord == null)
                {
                    _logger.LogWarning("{event} The Job or WipRecord with Id: {id} Does Not Exist", nameof(ArchivedIdentifierChangedEvent), jobDto.JobId);
                    return Unit.Value;
                }

                job.IsArchived = jobDto.IsArchived;
                job.ModifiedBy = nameof(ArchivedIdentifierChangedEvent);
                wipRecord.IsArchived = jobDto.IsArchived;
                wipRecord.ModifiedBy = nameof(ArchivedIdentifierChangedEvent);

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}
