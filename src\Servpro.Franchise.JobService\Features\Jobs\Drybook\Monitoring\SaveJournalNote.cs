﻿using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class SaveJournalNote
    {
        #region Command
        public class Command : IRequest<Guid>
        {
            public Guid JobId { get; set; }
            public Guid? Id { get; set; }
            public Guid TypeId { get; set; }
            public Guid CategoryId { get; set; }
            public DateTime ActionDate { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid VisibilityId { get; set; }
            public Guid? ZoneReadingId { get; set; }
        }
        #endregion Command

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.TypeId).NotEmpty();
                RuleFor(x => x.CategoryId).NotEmpty();
                RuleFor(x => x.Subject)
                    .NotNull()
                    .NotEmpty();
                RuleFor(x => x.VisibilityId).NotEmpty();
            }
        }
        #endregion Validation

        #region Handler
        public class Handler : IRequestHandler<Command, Guid>
        {
            private readonly ILogger<Handler> _logger;
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(ILogger<Handler> logger,
                JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor)
            {
                _logger = logger;
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Guid> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Starting {method} , JobId: {jobid}", nameof(SaveJournalNote), request?.JobId);
                //Check if Job exists
                var job = await _context.Jobs
                    .Include(x => x.JournalNotes)
                    .Include(x => x.Zones)
                        .ThenInclude(x => x.ZoneReadings)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                //Check if journal exists (if the Id is not empty)
                _logger.LogInformation("Check if journal exists {method} , JobId: {jobid}", nameof(SaveJournalNote), request?.JobId);
                var journalNote = job.JournalNotes
                    .FirstOrDefault(x => x.Id == request.Id);

                if (journalNote is null && request.Id.HasValue)
                    throw new ResourceNotFoundException($"JournalNote not found (Id: {request.Id}");

                //Insert or update the JournalNote
                _logger.LogInformation("Insert or update the JournalNote {method} , JobId: {jobid}", nameof(SaveJournalNote), request?.JobId);
                var userInfo = _userInfo.GetUserInfo();
                if (journalNote is null)
                {
                    journalNote = new JournalNote
                    {
                        Id = Guid.NewGuid(),
                        CreatedBy = userInfo.Username,
                        Author = userInfo.Name,
                        CreatedDate = DateTime.UtcNow
                    };
                    job.JournalNotes.Add(journalNote);
                }
                else
                {
                    journalNote.ModifiedBy = userInfo.Username;
                    journalNote.ModifiedDate = DateTime.UtcNow;
                }
                //Set the values
                journalNote.TypeId = request.TypeId;
                journalNote.CategoryId = request.CategoryId;
                journalNote.ActionDate = request.ActionDate;
                journalNote.Subject = request.Subject;
                journalNote.Note = request.Note;
                journalNote.VisibilityId = request.VisibilityId;

                if(request.ZoneReadingId.HasValue)
                {
                    var zoneReading = job.Zones
                        .SelectMany(x => x.ZoneReadings)
                        .FirstOrDefault(x => x.Id == request.ZoneReadingId);
                    if (zoneReading != null)
                        zoneReading.JournalNote = journalNote;
                }

                //Publish Events
                _logger.LogInformation("Publish events {method} , JobId: {jobid}", nameof(SaveJournalNote), request?.JobId);
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var events = GenerateJournalNoteEvent(correlationId, userInfo, request);
                await _context.OutboxMessages.AddRangeAsync(events, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);
                return journalNote.Id;
            }

            private IEnumerable<OutboxMessage> GenerateJournalNoteEvent(Guid correlationId, UserInfo user, Command request)
            {
                if (!request.Id.HasValue)
                {
                    return GenerateJournalNoteCreatedEvents(correlationId, user);
                }
                else
                {
                    return GenerateJournalNoteUpdatedEvents(correlationId, user);
                }
            }

            #region Methods for generating JournalNoteCreatedEvents
            private IEnumerable<OutboxMessage> GenerateJournalNoteCreatedEvents(Guid correlationId, UserInfo userInfo)
            {
                _logger.LogInformation("Generate {event}", nameof(GenerateJournalNoteCreatedEvents));
                var journalNoteCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JournalNote && x.State == EntityState.Added)
                    .Select(x => x.Entity as JournalNote)
                    .Select(x => MapJournalNoteCreatedDto(x, userInfo))
                    .Select(x => new JournalNoteCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username)).ToList();

                return journalNoteCreatedEvents;
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteCreatedDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id
                };

          
            #endregion Methods for generating JournalNoteCreatedEvents

            #region Methods for generating JournalNoteCreatedEvents
            private IEnumerable<OutboxMessage> GenerateJournalNoteUpdatedEvents(Guid correlationId, UserInfo userInfo)
            {
                _logger.LogInformation("Generate {event}", nameof(GenerateJournalNoteUpdatedEvents));
                var journalNoteCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JournalNote && x.State == EntityState.Modified)
                    .Select(x => x.Entity as JournalNote)
                    .Select(x => MapJournalNoteUpdatedDto(x, userInfo))
                    .Select(x => new JournalNoteCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username));

                return journalNoteCreatedEvents;
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteUpdatedDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id
                };
            #endregion Methods for generating JournalNoteCreatedEvents

        }
        #endregion
    }
}
