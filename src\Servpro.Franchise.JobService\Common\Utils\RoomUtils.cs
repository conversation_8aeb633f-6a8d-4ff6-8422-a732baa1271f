﻿using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public static class RoomUtils
    {
        public static RoomCalculatedData CalculateRoomData(Room room, int equipmentPlacementCount)
        {
            var flooringTypesAffected = room.RoomFlooringTypesAffected;
            var hasMissingFlooring = true;
            var hasMissingDimensions = false;
            var hasEquipmentPlacements = false;
            var totalSqFt = 0.0m;
            var affectedSqFeet = 0.0m;
            var affectedPercentage = 0.0m;
            if (!(flooringTypesAffected is null))
            {
                foreach (var ft in flooringTypesAffected)
                {
                    if (!(ft.TotalSquareFeet is null) && !(ft.AffectedSquareFeet is null))
                    {
                        totalSqFt += ft.TotalSquareFeet.Value;
                        affectedSqFeet += ft.AffectedSquareFeet.Value;
                    }
                }
                if (totalSqFt != 0)
                {
                    affectedPercentage = affectedSqFeet / totalSqFt * 100;
                }
                hasMissingFlooring = false;
            }

            if (equipmentPlacementCount > 0)
            {
                hasEquipmentPlacements = true;
            }

            if (room.RoomShapeId == RoomShapes.Box &&
                (room.Width1TotalInches == 0
                || room.Length1TotalInches == 0
                || room.Height1TotalInches == 0))
            {
                hasMissingDimensions = true;
            }

            else if (room.RoomShapeId == RoomShapes.VaultedCeiling &&
             (room.Width1TotalInches == 0
             || room.Length1TotalInches == 0
             || room.Height1TotalInches == 0
             || room.Height2TotalInches == 0))
            {
                hasMissingDimensions = true;
            }

            else if (room.RoomShapeId == RoomShapes.LShaped &&
              (room.Width1TotalInches == 0
              || room.Width2TotalInches == 0
              || room.Length1TotalInches == 0
              || room.Length2TotalInches == 0
              || room.Height1TotalInches == 0))
            {
                hasMissingDimensions = true;
            }

            return new RoomCalculatedData
            {
                HasMissingFlooring = hasMissingFlooring,
                HasMissingDimensions = hasMissingDimensions,
                HasEquipmentPlacements = hasEquipmentPlacements,
                TotalSqFt = totalSqFt,
                AffectedSqFeet = affectedSqFeet,
                AffectedPercentage = affectedPercentage
            };
        }
    }

    public class RoomCalculatedData {
        public bool HasMissingFlooring { get; set; }
        public bool HasMissingDimensions { get; set; }
        public bool HasEquipmentPlacements { get; set; }
        public decimal TotalSqFt { get; set; }
        public decimal AffectedSqFeet { get; set; }
        public decimal AffectedPercentage { get; set; }
    }
}
