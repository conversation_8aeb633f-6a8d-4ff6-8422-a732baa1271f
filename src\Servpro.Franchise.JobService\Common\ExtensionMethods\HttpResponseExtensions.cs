﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService
{
    public static class HttpResponseExtensions
    {
        public static async Task SetErrorResponseAsync(this HttpResponse response, string errorMessage,
            ILogger logger = null, HttpStatusCode statusCode = HttpStatusCode.InternalServerError)
        {
            if (response != null)
            {
                response.StatusCode = (int)statusCode;

                byte[] errorBytes = Encoding.UTF8.GetBytes(errorMessage);
                await response.Body.WriteAsync(errorBytes, 0, errorBytes.Length).ConfigureAwait(false);
            }

            if (statusCode == HttpStatusCode.Forbidden || statusCode == HttpStatusCode.Unauthorized)
            {
                logger?.LogWarning(LoggingEvents.UnauthorizedUser, errorMessage);
            }
            else
            {
                logger?.LogError(errorMessage);
            }
        }
    }
}
