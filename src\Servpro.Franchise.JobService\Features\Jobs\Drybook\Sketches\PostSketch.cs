﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Sketches
{
    public class PostSketch
    {
        public class Command : IRequest<ResponseDto>
        {
            public string Name { get; set; }
            public string CanvasJson { get; set; }
            public MediaMetadataDto MediaMetadata { get; set; } = new MediaMetadataDto();
        }

        public class MediaMetadataDto
        {
            public Guid JobId { get; set; }
            public string Name { get; set; }
            public Guid JobArtifactId { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.MediaMetadata.JobId).NotEmpty();
            }
        }

        public class PresignedUrlDto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
        }

        public class ResponseDto
        {
            public ResponseDto(Guid id, string signedUrl, string name, Guid jobId, Guid mediaMetadataId, Guid jobArtifactId)
            {
                Id = id;
                SignedUrl = signedUrl;
                Name = name;
                JobId = jobId;
                JobArtifactId = jobArtifactId;
                MediaId = mediaMetadataId;
            }

            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public Guid JobId { get; set; }
            public Guid MediaId { get; set; }
            public Guid JobArtifactId { get; set; }

        }

        public class Handler : IRequestHandler<Command, ResponseDto>
        {
            private readonly JobDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";
            private readonly IMediaEventGenerator _mediaEventGenerator;

            public Handler(
                JobDataContext db,
                IConfiguration config,
                IAmazonS3 clientS3,
                IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor, 
                IMediaEventGenerator mediaEventGenerator)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _mediaEventGenerator = mediaEventGenerator;
            }

            public async Task<ResponseDto> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _db.Jobs
                    .Include(j => j.JobLocks)
                    .FirstOrDefaultAsync(q => q.Id == request.MediaMetadata.JobId, cancellationToken: cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.MediaMetadata.JobId}");
                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var mediaMetadata = MapMediaMetadata(request);
                var sketch = MapSketch(request, mediaMetadata);
                mediaMetadata.JobSketchId = sketch.Id;
                mediaMetadata.MediaPath = mediaMetadata.GetKey();
                mediaMetadata.BucketName = _config[S3MediaBucketNameKey];
                _db.JobSketch.Add(sketch);
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var medias = new List<MediaMetadata>() {mediaMetadata};
                var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(medias, correlationId, userInfo);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);

               
                await _db.SaveChangesAsync(cancellationToken);
                
                var signedUrl = GetPreSignedUrl(mediaMetadata);
                return new ResponseDto(sketch.Id, signedUrl.SignedUrl, mediaMetadata.Name, mediaMetadata.JobId, mediaMetadata.Id, mediaMetadata.ArtifactTypeId);
            }

            private PresignedUrlDto GetPreSignedUrl(MediaMetadata metadata)
            {
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = _config[S3MediaBucketNameKey],
                    Key = metadata.GetKey(),
                    Verb = HttpVerb.PUT,
                    ContentType = "multipart/form-data",
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };

                var preSignedUrlDto = new PresignedUrlDto
                {
                    Id = metadata.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = metadata.Name
                };

                return preSignedUrlDto;
            }

            private JobSketch MapSketch(Command command, MediaMetadata mediaMetadata)
                => new JobSketch
                {
                    Id = Guid.NewGuid(),
                    JobId = mediaMetadata.JobId,
                    Name = command.Name,
                    CanvasJson = command.CanvasJson,
                    MediaMetadata = mediaMetadata
                };

            private MediaMetadata MapMediaMetadata(Command command)
                => new MediaMetadata
                {
                    Id = Guid.NewGuid(),
                    JobId = command.MediaMetadata.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    FranchiseSetId = command.MediaMetadata.FranchiseSetId,
                    Name = command.Name,
                    MediaTypeId = MediaTypes.Sketch,
                    ArtifactTypeId = command.MediaMetadata.JobArtifactId
                };
        }
    }
}