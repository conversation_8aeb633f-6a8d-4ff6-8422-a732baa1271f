﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JournalNoteCreated
    {
        public class Event : JournalNoteCreatedEvent, IRequest
        {
            public Event(JournalNoteDto journalNote, Guid correlationId)
                : base(journalNote, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JournalNoteCreated> _logger;
            public Handler(JobDataContext db, ILogger<JournalNoteCreated> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with request: {@request}", request);
                
                var journalNoteDto = request.JournalNote;
                
                var journalNoteExists = await _db.JournalNote.AnyAsync(x => x.Id == journalNoteDto.Id, cancellationToken);
                if (!journalNoteExists)
                {
                    await _db.JournalNote.AddAsync(Map(request.JournalNote), cancellationToken);

                    var job = await _db.Jobs
                        .Include(x => x.Customer)
                        .ThenInclude(c => c.Business)
                        .Where(x => x.Id == request.JournalNote.JobId)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (job != null)
                    {
                       // Add first notice activity
                       _logger.LogInformation("Add first notice activity AddPriorityResponseJournal");
                       var fnActivity = Map(request.JournalNote.JobId, request.JournalNote.CreatedBy);
                       await _db.FirstNoticeActivity.AddAsync(fnActivity, cancellationToken);

                        var projectKeyFieldUpdatedEvent = GenerateProjectKeyFieldsUpdatedEvent(job, 
                            request.JournalNote.CreatedById, request.CorrelationId, request.JournalNote.CreatedBy);
                        await _db.OutboxMessages.AddAsync(projectKeyFieldUpdatedEvent, cancellationToken);
                    }

                    await _db.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    _logger.LogWarning("Event JournalNote Already Exists - {Event}: The JournalNote with id:{ID} already exists and could not be added", nameof(JournalNoteCreatedEvent), journalNoteDto.Id);
                }

                return Unit.Value;
            }

            private JournalNote Map(JournalNoteCreatedEvent.JournalNoteDto journalNote) =>
                new JournalNote
                {
                    Id = journalNote.Id,
                    ActionDate = journalNote.ActionDate,
                    Subject = journalNote.Subject,
                    CreatedDate = journalNote.CreatedDate,
                    Note = journalNote.Message,
                    VisibilityId = journalNote.VisibilityId,
                    TypeId = journalNote.TypeId,
                    Author = journalNote.Author,
                    CategoryId = journalNote.CategoryId ?? Guid.Empty,
                    IsDeleted = false,
                    JobId = journalNote.JobId,
                    ModifiedDate = DateTime.UtcNow
                };

            private FirstNoticeActivity Map(Guid jobId, string createdBy)
            {
                return new FirstNoticeActivity
                {
                    ActivityTypeId = ActivityType.AddPriorityResponseJournal,
                    RevisedValue = "new note added",
                    ModifiedBy = createdBy,
                    CreatedBy = createdBy,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    RecordSourceId = RecordSourceTypes.WorkCenter,
                    JobId = jobId,
                    Id = Guid.NewGuid()
                };
            }

            private ProjectKeyFieldsUpdatedEvent.ProjectKeyFieldsUpdatedDto Map(Job job, Guid createdById, string createdBy)
            {
                return new ProjectKeyFieldsUpdatedEvent.ProjectKeyFieldsUpdatedDto
                {
                    FranchiseId = job.FranchiseId,
                    FranchiseSetId = job.FranchiseSetId,
                    JobId = job.Id,
                    JobProgress =
                        (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)job.JobProgress,
                    MentorId = job.MentorId,
                    ProjectNumber = job.ProjectNumber,
                    PropertyTypeId = job.PropertyTypeId,
                    UserId = createdById,
                    Username = createdBy,
                    BusinessName = job.BusinessName,
                    LossTypeId = job.LossTypeId,
                    Customer = new ProjectKeyFieldsUpdatedEvent.ContactDto
                    {
                        FirstName = job.Customer?.FirstName,
                        LastName = job.Customer?.LastName
                    }
                };
            }

            private OutboxMessage GenerateProjectKeyFieldsUpdatedEvent(Job job, Guid createdById, Guid correlationId, string createdBy)
            {
                var eventDto = Map(job, createdById, createdBy);

                _logger.LogInformation("ProjectKeyFieldsUpdatedDto: {@projectKeyFieldsUpdated}: ", eventDto);

                var @event = new ProjectKeyFieldsUpdatedEvent(eventDto, correlationId);
                var outboxMessage = new OutboxMessage(@event.ToJson(), nameof(ProjectKeyFieldsUpdatedEvent),
                    correlationId, createdBy ?? "JobService");

                _logger.LogInformation("Generated event {eventName}: {@payload}", nameof(ProjectKeyFieldsUpdatedEvent), @event);

                return outboxMessage;
            }
        }
    }
}
