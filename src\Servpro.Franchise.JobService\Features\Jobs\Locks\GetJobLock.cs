﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Locks
{
    public class GetJobLock
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public bool IsLocked { get; set; }
            public Guid LockedByUserId { get; set; }
            public string LockedByUserFullName { get; set; }
            public DateTime LockedTimestamp { get; set; }
            public string LockedByDevice { get; set; }
            public Guid LockedByApplicationId { get; set; }
            public string LockedByApplicationName { get; set; }
            public Guid? UnlockedByUserId { get; set; }
            public DateTime? UnlockedTimestamp { get; set; }
            public string UnlockedByDevice { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetJobLock> _logger;

            public Handler(JobReadOnlyDataContext context, 
                ILogger<GetJobLock> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}{eventName}", request.JobId, nameof(GetJobLock));
                _logger.LogDebug("Begin command with request: {@request}", request);

                var job = await _context.Jobs
                                        .Include(x => x.JobLocks)
                                        .AsNoTracking()
                                        .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var jobLocks = job.JobLocks.Where(a => a.IsLocked);

                if (!jobLocks.Any())
                    return null;

                _logger.LogDebug("Job locks: {@jobLocks}", job.JobLocks);

                var jobLock = jobLocks.OrderByDescending(x => x.LockedTime).FirstOrDefault();

                return Map(jobLock);
            }

            public static Dto Map(JobLock jobLock)
            {
                return new Dto
                {
                    Id = jobLock.Id,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserFullName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.UnlockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId
                };
            }
        }
    }
}