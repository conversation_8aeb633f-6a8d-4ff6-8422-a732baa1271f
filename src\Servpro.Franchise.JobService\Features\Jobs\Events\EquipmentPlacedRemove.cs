﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class EquipmentPlacedRemove
    {
        public class Event : EquipmentRemovedFromRoomEvent, IRequest
        {
            public Event(EquipmentRemovedFromRoomDto equipmentRemoved, Guid correlationId) : base(equipmentRemoved, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<Handler> _logger;

            public Handler(ILogger<Handler> logger, JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} Handler Activated", nameof(EquipmentRemovedFromRoomEvent));
                var dto = request.EquipmentRemovedFromRoom;

                var job = await _context.Jobs
                    .Include(j => j.JobAreas)
                    .FirstOrDefaultAsync(q => q.Id == dto.JobId, cancellationToken: cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {dto.JobId}");

                var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == dto.RoomId);

                if (jobArea is null)
                {
                    jobArea = job.JobAreas.FirstOrDefault(x => x.JobAreaTypeId == JobAreaTypes.Job);
                    if (jobArea is null)
                        throw new ResourceNotFoundException($"Job Room not found: {dto.RoomId}");
                }

                var equipmentsIds = dto.Removals.Select(x => x.EquipmentId).ToList();

                var equipmentPlacementToRemove = await _context.EquipmentPlacements
                    .Where(x => equipmentsIds.Contains(x.EquipmentId))
                    .ToListAsync(cancellationToken);
                _context.EquipmentPlacements.RemoveRange(equipmentPlacementToRemove);

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("{event} Handler Completed Successfully", nameof(EquipmentRemovedFromRoomEvent));
                return Unit.Value;
            }
        }
    }
}
