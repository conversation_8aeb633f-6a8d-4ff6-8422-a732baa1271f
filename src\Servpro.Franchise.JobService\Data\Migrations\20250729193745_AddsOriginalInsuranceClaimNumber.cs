﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddsOriginalInsuranceClaimNumber : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("9ef5e102-0bfc-4fc9-b06f-f8336269d3cf"));

            migrationBuilder.AddColumn<string>(
                name: "OriginalInsuranceClaimNumber",
                table: "WipRecord",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                collation: "utf8mb4_general_ci");

            migrationBuilder.AddColumn<string>(
                name: "OriginalInsuranceClaimNumber",
                table: "Job",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                collation: "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("22ab52a7-763f-443f-bbdd-1227f6210ca6"), null, new DateTime(2025, 7, 29, 19, 37, 44, 698, DateTimeKind.Utc).AddTicks(8976), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("22ab52a7-763f-443f-bbdd-1227f6210ca6"));

            migrationBuilder.DropColumn(
                name: "OriginalInsuranceClaimNumber",
                table: "WipRecord");

            migrationBuilder.DropColumn(
                name: "OriginalInsuranceClaimNumber",
                table: "Job");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("9ef5e102-0bfc-4fc9-b06f-f8336269d3cf"), null, new DateTime(2025, 7, 28, 18, 33, 17, 677, DateTimeKind.Utc).AddTicks(3186), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
