﻿using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class EquipmentUsageHelper
    {
        public const int MaxColumnsPerTableLine = 10;

        public static long GetJavascriptTimestamp(DateTime input)
        {
            TimeSpan span = new TimeSpan(DateTime.Parse("1/1/1970").Ticks);
            DateTime time = input.Subtract(span);
            return (long)(time.Ticks / 10000);
        }

        public class EquipmentPlacementCountSummaryDto
        {
            public Dictionary<string, int> TotalsByEquipmentType { get; set; } = new Dictionary<string, int>();
            public List<Dictionary<string, int>> TotalsByDay { get; set; } = new List<Dictionary<string, int>>();
            public Dictionary<(string, string), int> EquipmentCountByDay { get; set; } = new Dictionary<(string, string), int>();
        }

        public class EquipmentPlacementSummaryDto
        {
            public List<string> EquipmentTypes { get; set; } = new List<string>();
            public Dictionary<string, List<string>> Rooms { get; set; } = new Dictionary<string, List<string>>();
            public Dictionary<(string, string), List<EquipmentPlacementDetail>> EquipmentPlacementDetails { get; set; } = new Dictionary<(string, string), List<EquipmentPlacementDetail>>();
            public Dictionary<(string, string, string), decimal> Totals { get; set; } = new Dictionary<(string, string, string), decimal>();
        }

        public class DailyEquipmentReportDto
        {
            public EquipmentPlacementCountSummaryDto EquipmentPlacementCountSummary { get; set; } = new EquipmentPlacementCountSummaryDto();
            public EquipmentPlacementSummaryDto EquipmentPlacementSummary { get; set; } = new EquipmentPlacementSummaryDto();
        }

        public static DailyEquipmentReportDto ProcessModel(DailyEquipmentCountDto model)
        {
            var equipmentSummary = model.EquipmentSummary
                .Where(equipment => equipment.EquipmentType != null)
                .ToList();
            return new DailyEquipmentReportDto
            {
                EquipmentPlacementCountSummary = GetEquipmentPlacementCountSummary(
                    model.EquipmentPlacementCountByDay,
                    model.FirstJobVisitDate,
                    model.LastJobVisitDate),
                EquipmentPlacementSummary = GetEquipmentPlacementSummaryByEquipmentTypeAndRoom(equipmentSummary)
            };
        }

        public static EquipmentPlacementCountSummaryDto GetEquipmentPlacementCountSummary(
            List<EquipmentCountDetail> equipmentDetails,
            DateTime? startDate,
            DateTime? endDate)
        {
            EquipmentPlacementCountSummaryDto result = new EquipmentPlacementCountSummaryDto();
            List<string> days = new List<string>();

            if (!equipmentDetails.Any()) return result;

            startDate ??= equipmentDetails.Select(detail => detail.Day).Min();
            endDate ??= equipmentDetails.Select(detail => detail.Day).Max();

            if (endDate?.CompareTo(startDate) < 0)
            {
                return result;
            }

            if (endDate.HasValue)
            {

                var dayCount = (int)Math.Round((endDate.Value.Date - startDate.Value.Date).TotalDays, 0) + 1;
                days = Enumerable.Range(0, dayCount)
                    .Select(x => startDate.Value.AddDays(x))
                    .Select(x => x.ToString("MM/dd"))
                    //In the rare case that there are jobvisits outside the range of a year - there will be wrap around
                    // The Distinct() here ensures that this wont be an issue when converting generated days (to be displayed)
                    // to a dictionary.  This makes it more forgiving but the data could look odd in the report.  The user would
                    // have to know to fix the datetimes to be more accurate (hopefully jobs dont last over a year and it was just fat fingered dates).
                    .Distinct()  
                    .ToList();
            }
            var equipmentTypes = equipmentDetails.Select(equipmentDetail => equipmentDetail.EquipmentType).AsEnumerable().Distinct();

            result.TotalsByEquipmentType = (from eqpType in equipmentTypes
                                            where eqpType != null
                                            select eqpType).ToDictionary(eqpType => eqpType, eqpType => 0);
            result.EquipmentCountByDay = new Dictionary<(string, string), int>();

            var totalsByDay = days?.ToDictionary(day => day, day => 0);

            foreach (var detail in equipmentDetails)
            {
                result.EquipmentCountByDay[(detail.Day.Date.ToString("MM/dd"), detail.EquipmentType)] = detail.Count ?? 0;
                if (result.TotalsByEquipmentType != null && !string.IsNullOrEmpty(detail.EquipmentType))
                {
                    result.TotalsByEquipmentType[detail.EquipmentType] += detail.Count ?? 0;
                }
                if (totalsByDay != null)
                {
                    if (totalsByDay.ContainsKey(detail.Day.Date.ToString("MM/dd")))
                    {
                        totalsByDay[detail.Day.Date.ToString("MM/dd")] += detail.Count ?? 0;
                    }

                }
            }
            result.TotalsByDay = GetTotalsByDayRangeList(totalsByDay);
            return result;
        }

        private static List<Dictionary<string, int>> GetTotalsByDayRangeList(Dictionary<string, int> totalsByDate)
        {
            var result = new List<Dictionary<string, int>>();

            for (var i = 0; i < totalsByDate.Count; i += MaxColumnsPerTableLine)
            {
                result.Add(totalsByDate.ToList().GetRange(i, Math.Min(MaxColumnsPerTableLine, totalsByDate.Count - i)).ToDictionary(total => total.Key, total => total.Value));
            }

            return result;
        }

        public static EquipmentPlacementSummaryDto GetEquipmentPlacementSummaryByEquipmentTypeAndRoom(List<EquipmentPlacementDetail> equipmentSummary)
        {
            var result = new EquipmentPlacementSummaryDto
            {
                EquipmentPlacementDetails = new Dictionary<(string, string), List<EquipmentPlacementDetail>>(),
                Totals = new Dictionary<(string, string, string), decimal>()
            };
            result.EquipmentTypes = equipmentSummary
                .GroupBy(equipmentDetail => equipmentDetail.EquipmentType)
                .Select(equipmentByType =>
                {
                    var rooms = equipmentByType
                        .OrderBy(r => r.Room)
                        .GroupBy(equipmentDetailByType => equipmentDetailByType.Room)
                        .Select(equipmentByRoom =>
                        {
                            var equipmentDetailsByRoom = equipmentByRoom.ToList();
                            equipmentDetailsByRoom.Sort((detailA, detailB) => detailB.Placed.Value.CompareTo(detailA.Placed.Value));

                            result.Totals.Add((equipmentByType.Key, equipmentByRoom.Key, "days"), equipmentDetailsByRoom.Sum(detail => detail.Days));
                            result.Totals.Add((equipmentByType.Key, equipmentByRoom.Key, "hours"), equipmentDetailsByRoom.Sum(detail => detail.Hours));
                            result.Totals.Add((equipmentByType.Key, equipmentByRoom.Key, "assets"), equipmentDetailsByRoom.Count);
                            result.Totals.Add((equipmentByType.Key, equipmentByRoom.Key, "rowspan"), equipmentDetailsByRoom.Count + 1);

                            result.EquipmentPlacementDetails.Add((equipmentByType.Key, equipmentByRoom.Key), equipmentDetailsByRoom);

                            return equipmentByRoom.Key;
                        }).ToList();

                    result.Rooms.Add(equipmentByType.Key, rooms);
                    result.Totals.Add((equipmentByType.Key, "total", "days"), equipmentByType.Sum(detail => detail.Days));
                    result.Totals.Add((equipmentByType.Key, "total", "hours"), equipmentByType.Sum(detail => detail.Hours));
                    result.Totals.Add((equipmentByType.Key, "rowspan", "rowspan"), equipmentByType.Count() + rooms.Count + 1);

                    return equipmentByType.Key;
                }).ToList();
            result.Totals.Add(("total", "total", "hours"), equipmentSummary.Sum(detail => detail.Hours));
            result.Totals.Add(("total", "total", "days"), equipmentSummary.Sum(detail => detail.Days));

            return result;
        }
    }
}