@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.ZoneCompositionDto
@using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers

@if (Model?.ZoneCompositions != null && Model.ZoneCompositions.Count > 0)
{
    <div class="title">{zoneComposition}Zone Composition and Validation{/zoneComposition}</div>
    
    <div class="floatLeft">

        @foreach (var zoneComposition in Model.ZoneCompositions)
        {
            <div class="divisorSubtitleDiv">
                <span class="divisorSubtitleText" style="font-size: 14px;">
                  <!--TODO: We removed this bookmark becuase it is causing issues with the styling - the header will wrap if its too long 
                  This will need to be addressed.-->
                   @*{zoneCompName}@zoneComposition.ZoneName{/zoneCompName}*@

                   @zoneComposition.ZoneName
                </span>
            </div>

            <table class="zoneCompositionTable" summary="Zone Composition">
                <tr>
                    <th scope="col" class="tableCell darkTableCell" style="width: 17%">
                        Water Category:
                    </th>
                    <th scope="col" class="tableCell" style="width: 30%">
                        @zoneComposition.WaterCategory
                    </th>
                    <th scope="col" class="tableCell darkTableCell" style="width: 20%">
                        Water Class:
                    </th>
                    <th scope="col" class="tableCell">
                        @zoneComposition.WaterClass
                    </th>
                </tr>
            </table>

            <table class="zoneCompositionTable" summary="Zone Composition table B">
                <tr>
                    <th scope="col" class="tableCell darkTableCell" style="width: 33%">
                        Dehu Min Capacity Required:
                    </th>
                    <td class="tableCell" style="width: 14%;">
                        @ZoneCompositionHelper.GetDehuMinCapacityRequired(
                     zoneComposition.DehuValidationType,
                     zoneComposition.MinDehuPpdsRequired,
                     zoneComposition.MinDehuCfmsRequired)
                    </td>
                    <th scope="col" class="tableCell darkTableCell" style="width: 36%">
                        Air Movers Requirement Method:
                    </th>
                    <td class="tableCell">
                        @zoneComposition.AirMoverValidationMethod.ToString().ToProperCaseWithSpaces()
                    </td>
                </tr>

                <tr>
                    <th scope="col" class="tableCell darkTableCell">
                        Dehu Actual Capacity Placed:
                    </th>
                    <td class="tableCell" style="background-color: @zoneComposition.DehusRequirementsProximity.GetColor(zoneComposition.IsConfirmed);">
                        @ZoneCompositionHelper.GetDehuActualCapacityPlaced(
                    zoneComposition.DehuValidationType,
                    zoneComposition.DehuPpdsPlaced,
                    zoneComposition.DehuCfmsPlaced)
                    </td>
                    <th scope="col" class="tableCell darkTableCell">
                        Air Movers Required:
                    </th>
                    <td class="tableCell">
                        @if (zoneComposition.MinAirMoversRequired.Equals(zoneComposition.MaxAirMoversRequired))
                        {
                            @Html.Raw(zoneComposition.MinAirMoversRequired);
                        }
                        else
                        {
                            @($"{zoneComposition.MinAirMoversRequired} - {zoneComposition.MaxAirMoversRequired}")
                            ;
                        }
                    </td>
                </tr>
                <tr>
                    <th scope="col" class="tableCell darkTableCell">
                        Dehumidifiers Placed:
                    </th>
                    <td class="tableCell">
                        @zoneComposition.CountDehusPlaced
                    </td>
                    <th scope="col" class="tableCell darkTableCell">
                        Air Movers Placed:
                    </th>
                    <td class="tableCell" style="background-color: @zoneComposition.AirMoversRequirementsProximity.GetAirMoverValidationColor(zoneComposition.IsConfirmed);">
                        @zoneComposition.CountAirMoversPlaced
                    </td>
                <tr>
                    <th class="tableCell" colspan="2" scope="colgroup" style="background-color: @zoneComposition.DehusRequirementsProximity.GetColor(zoneComposition.IsConfirmed);">
                        @zoneComposition.DehusRequirementsProximity.GetDehuValidationText(zoneComposition.DehuValidationType)
                    </th>
                    <th class="tableCell" colspan="2" scope="colgroup" style="background-color: @zoneComposition.AirMoversRequirementsProximity.GetAirMoverValidationColor(zoneComposition.IsConfirmed);">
                        @zoneComposition.AirMoversRequirementsProximity.GetAirMoverValidationTextV2()
                    </th>
                </tr>
            </table>

            <div class="divisorSubtitleDiv">
                <span>
                    @zoneComposition.ZoneName Cubic Footage, Floor Linear Footage and Total Affected Square Footage
                </span>
            </div>


            <table class="zoneCompositionTable evenOddTable tableWithTotal" style="width: 100%" summary="Zone Composition">
                <colgroup span="1"></colgroup>
                <colgroup span="2"></colgroup>
                <colgroup span="3"></colgroup>
                <colgroup span="3"></colgroup>
                <tr>
                    <th class="grayBorder" colspan="1" scope="colgroup"></th>
                    <th class="grayBorder" colspan="2" scope="colgroup">Cubic Footage</th>
                    <th class="grayBorder" colspan="2" scope="colgroup">Floor Linear Footage</th>
                    <th class="grayBorder" colspan="2" scope="colgroup">Total Affected Square Footage</th>
                </tr>
                <tr>
                    <th class="grayBorder" scope="col">Room Name</th>
                    <th class="grayBorder" scope="col">Total Room</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms</th>
                    <th class="grayBorder" scope="col">Affected</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms</th>
                    <th class="grayBorder" scope="col">Affected</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms</th>
                </tr>
                @foreach (var room in zoneComposition.RoomScopes)
                {
                    <tr>
                        <td class="grayBorder">@room.RoomName</td>
                        <td class="grayBorder cellRightText">@room.RoomVolumeTotal.FormatDecimalPlaces()</td>
                        <td class="grayBorder cellRightText">@room.RoomVolumeOffsetsInsets.FormatDecimalPlaces()</td>
                        <td class="grayBorder cellRightText">@room.FloorLinearAffected.FormatDecimalPlaces()</td>
                        <td class="grayBorder cellRightText">@room.FloorLinearOffsetsInsets.FormatDecimalPlaces()</td>
                        <td class="grayBorder cellRightText">@room.TotalAffectedSquareFootage.FormatDecimalPlaces()</td>
                        <td class="grayBorder cellRightText">@room.TotalOffsetsInsetsSquareFootage.FormatDecimalPlaces()</td>
                    </tr>
                }

                <tr>
                    <td class="grayBorder doubleTopBorder">Total</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.RoomVolumeTotal).FormatDecimalPlaces()</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.RoomVolumeOffsetsInsets).FormatDecimalPlaces()</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.FloorLinearAffected).FormatDecimalPlaces()</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.FloorLinearOffsetsInsets).FormatDecimalPlaces()</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.TotalAffectedSquareFootage).FormatDecimalPlaces()</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.TotalOffsetsInsetsSquareFootage).FormatDecimalPlaces()</td>
                </tr>
            </table>

            <div class="divisorSubtitleDiv">
                <span>
                    @zoneComposition.ZoneName Affected Surfaces % and Equipment Placements for Validation
                </span>
            </div>

            <table class="zoneCompositionTable tableWithTotal" style="width: 100%" summary="Zone Composition">
                <tr>
                    <th class="grayBorder" scope="col">Room Name</th>
                    <th class="grayBorder" scope="col">Total</th>
                    <th class="grayBorder" scope="col">Floor</th>
                    <th class="grayBorder" scope="col">Walls</th>
                    <th class="grayBorder" scope="col">Ceiling</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms<br />> 18 inches</th>
                    <th class="grayBorder" scope="col">Air<br />Movers</th>
                    <th class="grayBorder" scope="col">Dehus</th>
                    <th class="grayBorder" scope="col">Air Movers<br />Recommended</th>
                </tr>

                @foreach (var room in zoneComposition.RoomScopes)
                {
                    <tr style="background-color: @room.AirMoversRequirementsProximity.GetAirMoverByRoomValidationColor(room.IsConfirmed);">
                        <td class="grayBorder">@room.RoomName</td>
                        <td class="grayBorder cellRightText">@($"{decimal.Round((room.TotalAffectedPercentage * 100)??0m,0)} %")</td>
                        <td class="grayBorder cellRightText">@($"{decimal.Round((room.TotalFloorPercentage * 100)??0m,0)} %")</td>
                        <td class="grayBorder cellRightText">@($"{decimal.Round((room.TotalWallPercentage * 100)??0m,0)} %")</td>
                        <td class="grayBorder cellRightText">@($"{decimal.Round((room.TotalCeilingPercentage * 100)??0m,0)} %")</td>
                        <td class="grayBorder cellRightText">@room.OffsetsInsetsGreaterThan18Inches</td>
                        <td class="grayBorder cellRightText">@room.TotalNumberOfAirMovers</td>
                        <td class="grayBorder cellRightText">@room.TotalNumberOfDehus</td>
                        <td class="grayBorder cellRightText">
                            @if (room.MinAirMoversRequired.Equals(room.MaxAirMoversRequired))
                            {
                                @Html.Raw(room.MinAirMoversRequired);
                            }
                            else
                            {
                                @($"{room.MinAirMoversRequired} - {room.MaxAirMoversRequired}")
                                ;
                            }
                        </td>
                    </tr>
                }
                <tr>
                    <td class="grayBorder doubleTopBorder">Total</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@($"{ (int) zoneComposition.RoomScopes?.Select(x => (x.TotalAffectedPercentage ?? 0.0m) * 100).DefaultIfEmpty(0.0m).Average() } %")</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@($"{ (int) zoneComposition.RoomScopes?.Select(x => (x.TotalFloorPercentage ?? 0.0m) * 100).DefaultIfEmpty(0.0m).Average() } %")</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@($"{ (int) zoneComposition.RoomScopes?.Select(x => (x.TotalWallPercentage ?? 0.0m) * 100).DefaultIfEmpty(0.0m).Average()} %")</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@($"{ (int) zoneComposition.RoomScopes?.Select(x => (x.TotalCeilingPercentage ?? 0.0m) * 100).DefaultIfEmpty(0.0m).Average()} %")</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes?.Sum(x => x.OffsetsInsetsGreaterThan18Inches)</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes?.Sum(x => x.TotalNumberOfAirMovers)</td>
                    <td class="grayBorder doubleTopBorder cellRightText">@zoneComposition.RoomScopes?.Sum(x => x.TotalNumberOfDehus)</td>
                    <td class="grayBorder cellRightText">
                        @if (zoneComposition.RoomScopes.Sum(x => x.MinAirMoversRequired).Equals(zoneComposition.RoomScopes?.Sum(x => x.MaxAirMoversRequired)))
                        {
                            @Html.Raw(zoneComposition.RoomScopes?.Sum(x => x.MinAirMoversRequired));
                        }
                        else
                        {
                            @($"{zoneComposition.RoomScopes?.Sum(x => x.MinAirMoversRequired)} - {zoneComposition.RoomScopes?.Sum(x => x.MaxAirMoversRequired)}");
                        }
                    </td>
                </tr>
            </table>

            <div class="divisorSubtitleDiv">
                <span>
                    @zoneComposition.ZoneName Floor, Walls and Ceiling Square Footage
                </span>
            </div>


            <table class="zoneCompositionTable evenOddTable tableWithTotal" style="width: 100%" summary="Zone Composition">
                <colgroup span="1"></colgroup>
                <colgroup span="3"></colgroup>
                <colgroup span="3"></colgroup>
                <colgroup span="3"></colgroup>
                <tr>
                    <th class="grayBorder" colspan="1" scope="colgroup"></th>
                    <th class="grayBorder" colspan="2" scope="colgroup">Floor Square Footage</th>
                    <th class="grayBorder" colspan="2" scope="colgroup">Wall Square Footage</th>
                    <th class="grayBorder" colspan="2" scope="colgroup">Ceiling Square Footage</th>
                </tr>
                <tr>
                    <th class="grayBorder" scope="col">Room Name</th>
                    <th class="grayBorder" scope="col">Affected</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms</th>
                    <th class="grayBorder" scope="col">Affected</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms</th>
                    <th class="grayBorder" scope="col">Affected</th>
                    <th class="grayBorder" scope="col">Offsets / Insets /<br />Subrooms</th>
                </tr>
                @foreach (var room in zoneComposition.RoomScopes)
                {
                    <tr>
                        <td class="grayBorder">@room.RoomName</td>
                        <td class="grayBorder cellRightText">
                            @if (room.FloorAreaAffected is null)
                            {@Html.Raw(0.0);
                        }
                        else
                        {@Html.Raw(room.FloorAreaAffected.FormatDecimalPlaces());
                    }
                        </td>
                        <td class="grayBorder cellRightText">
                            @if (room.FloorAreaOffsetsInsets is null)
                            {@Html.Raw(0.0);
                        }
                        else
                        {@Html.Raw(room.FloorAreaOffsetsInsets.FormatDecimalPlaces());
                    }
                        </td>
                        <td class="grayBorder cellRightText">
                            @if (room.WallAreaAffected is null)
                            {@Html.Raw(0.0);
                        }
                        else
                        {@Html.Raw(room.WallAreaAffected.FormatDecimalPlaces());
                    }
                        </td>
                        <td class="grayBorder cellRightText">
                            @if (room.WallAreaOffsetsInsets is null)
                            {@Html.Raw(0.0);
                        }
                        else
                        {@Html.Raw(room.WallAreaOffsetsInsets.FormatDecimalPlaces());
                    }
                        </td>
                        <td class="grayBorder cellRightText">
                            @if (room.CeilingAreaAffected is null)
                            {@Html.Raw(0.0);
                        }
                        else
                        {@Html.Raw(room.CeilingAreaAffected.FormatDecimalPlaces());
                    }
                        </td>
                        <td class="grayBorder cellRightText">
                            @if (room.CeilingAreaOffsetsInsets is null)
                            {@Html.Raw(0.0);
                        }
                        else
                        {@Html.Raw(room.CeilingAreaOffsetsInsets.FormatDecimalPlaces());
                    }
                        </td>
                    </tr>
                }

                <tr>
                    <td class="grayBorder doubleTopBorder">Total</td>

                    <td class="grayBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.FloorAreaAffected).FormatDecimalPlaces()</td>
                    <td class="grayBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.FloorAreaOffsetsInsets).FormatDecimalPlaces()</td>

                    <td class="grayBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.WallAreaAffected).FormatDecimalPlaces()</td>
                    <td class="grayBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.WallAreaOffsetsInsets).FormatDecimalPlaces()</td>

                    <td class="grayBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.CeilingAreaAffected).FormatDecimalPlaces()</td>
                    <td class="grayBorder cellRightText">@zoneComposition.RoomScopes.Sum(x => x.CeilingAreaOffsetsInsets).FormatDecimalPlaces()</td>
                </tr>
            </table>
        }

    </div>

    <div style='page-break-before: always;'></div>
}