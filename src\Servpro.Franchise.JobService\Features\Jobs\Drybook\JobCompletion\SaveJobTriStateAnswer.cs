﻿using FluentValidation;
using FluentValidation.Results;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion
{
    public class SaveJobTriStateAnswer
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid QuestionId { get; set; }
            public bool? Answer { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.QuestionId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {

                var userInfo = _userInfo.GetUserInfo();

                var job = await _context.Jobs
                    .Include(x => x.JobTriStateAnswers)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var questionIds = lookups.JobTriStateQuestions.Select(x => x.Id).ToHashSet();
                if (!questionIds.Contains(request.QuestionId))
                    throw new ValidationException(new List<ValidationFailure>
                    {
                        new ValidationFailure(nameof(request.QuestionId), "Invalid question")
                    });

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var answer = job.JobTriStateAnswers.FirstOrDefault(x => x.JobTriStateQuestionId == request.QuestionId);
                if(answer == null)
                {
                    job.JobTriStateAnswers.Add(new Models.Drybook.JobTriStateAnswer
                    {
                        Answer = request.Answer,
                        JobTriStateQuestionId = request.QuestionId,
                        CreatedBy = userInfo.Username
                    });
                }
                else
                {
                    answer.Answer = request.Answer;
                    answer.ModifiedBy = userInfo.Username;
                    answer.ModifiedDate = DateTime.UtcNow;
                }
                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }
        }
    }
}