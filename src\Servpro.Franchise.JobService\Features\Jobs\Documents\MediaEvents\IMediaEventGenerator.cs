﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents
{
    public interface IMediaEventGenerator
    {
        OutboxMessage GenerateMediaAddedEvent(IEnumerable<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo);
        OutboxMessage GenerateMediaAddedEvent(IEnumerable<MediaMetadata> metadata, Guid correlationId, string userName,
            bool isMobileSync = false);

        Task<OutboxMessage> GeneratePhotoSavedEvent(List<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo);
        Task<OutboxMessage> GeneratePhotoCreateEvent(List<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo);
        Task<OutboxMessage> GeneratePhotoDeletedEvent(List<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo);

    }
}
