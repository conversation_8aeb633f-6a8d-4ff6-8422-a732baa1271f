﻿using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationEstimaticRequirement : CrsValidationBase
    {
        public string ValidationMessage { get; set; }
        public string ValidationEffect { get; set; }
        public bool BypassAllowed { get; set; }
        public List<KeyValuePair<string, string>> RuleParameters { get; set; }

        public List<CrsValidationEstimatic> Estimatics { get; set; }

        public CrsValidationEstimaticRequirement()
        {
            this.RuleParameters = new List<KeyValuePair<string, string>>();
            this.Estimatics = new List<CrsValidationEstimatic>();
        }
    }
}