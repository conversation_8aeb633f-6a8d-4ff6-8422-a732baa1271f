﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using Aspose.Pdf;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public interface IFormManager
    {
        MemoryStream CreateRequestedForms(CreateFormPackageRequest request, ILogger logger, List<InsuranceClient> insuranceClients, string diagnosticModePath = null);
    }

    public class FormManager : IFormManager
    {
        private readonly License _license;

        public FormManager()
        {
            _license = new License();
            _license.SetLicense("Aspose.Pdf.lic");
        }

        public MemoryStream CreateRequestedForms(CreateFormPackageRequest request, ILogger logger, List<InsuranceClient> insuranceClients, string diagnosticModePath = null)
        {
            return request?.Job == null
                ? new MemoryStream()
                : ProcessForms(request, logger, insuranceClients, diagnosticModePath);
        }

        private static MemoryStream ProcessForms(CreateFormPackageRequest request, ILogger logger, List<InsuranceClient> insuranceClients, string diagnosticModePath = null)
        {
            var fieldMapper = new FieldMapper(request.Job, request.Franchise, request.Lookups, logger, insuranceClients);
            var forms = new List<IForm>();
            var mergeDocument = new Document();

            foreach (var template in request.FormTemplates)
            {
                IForm form;

                if (template.FileType.ToUpper().Equals("PDF"))
                {
                    form = new PdfForm();
                }
                else
                {
                    form = new DocForm();
                }

                logger.LogInformation($"Generating form of type: {form.GetType().Name}");

                forms.Add(form);

                const int copies = 1;

                if (template.Form == null)
                    continue;

                form.Merge(mergeDocument, copies, template, request.FormFields, fieldMapper, request.TimeZone, logger);
            }

            var stream = GetReturnFile(mergeDocument, diagnosticModePath);

            foreach (var form in forms)
            {
                form.Dispose();
            }

            mergeDocument.Dispose();
            return stream;
        }

        private static MemoryStream GetReturnFile(Document mergeDocument, string diagnosticModePath = null)
        {
            // saves the file for debugging purposes in both formats prior to returning the stream.
            if (!string.IsNullOrEmpty(diagnosticModePath))
            {
                var pdfName =
                    $@"{diagnosticModePath}\{DateTime.Now.ToOADate().ToString(CultureInfo.InvariantCulture)}.pdf";
                mergeDocument.Save(pdfName);

                var docName =
                    $@"{diagnosticModePath}\{DateTime.Now.ToOADate().ToString(CultureInfo.InvariantCulture)}.doc";
                mergeDocument.Save(docName, SaveFormat.DocX);
            }

            var outStream = new MemoryStream();
            mergeDocument.Save(outStream);

            return outStream;
        }
    }
}