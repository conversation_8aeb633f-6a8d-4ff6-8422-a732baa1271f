﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using MediaAddedDto = Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents.DocuSketchMediaAdded;
using Servpro.Franchise.JobService.Common.Constants;

namespace Servpro.Franchise.JobService.Features.Jobs.DocuSketch
{
    public class DocuSketchMediaAdded
    {
        public class Event : DocuSketchMediaAddedEvent, IRequest
        {
            public Event(MediaAddedDto dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly string _s3BucketName;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";

            public Handler(JobDataContext dataContext,
                ILogger<Handler> logger,
                ISessionIdAccessor sessionIdAccessor,
                IMediaEventGenerator mediaEventGenerator,
                IConfiguration config)
            {
                _db = dataContext;
                _logger = logger;
                _mediaEventGenerator = mediaEventGenerator;
                _sessionIdAccessor = sessionIdAccessor;
                _s3BucketName = config.GetValue(S3MediaBucketNameKey, string.Empty);
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var mediaDto = request.Media;

                ValidateRequest(mediaDto);
                using var jobScope = _logger.BeginScope("{jobId}", mediaDto.JobId);

                var mediaMetaDataToAdd = await ProcessMediaAsync(mediaDto, cancellationToken);
                await InsertMediaMetaDataAsync(mediaMetaDataToAdd, cancellationToken);
                await RaiseMediaAddedEventAsync(mediaMetaDataToAdd, correlationId, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task RaiseMediaAddedEventAsync(List<MediaMetadata> mediaMetaDataToAdd, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising media added event.");
                var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(mediaMetaDataToAdd, correlationId, nameof(DocuSketchMediaAddedEvent));
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task InsertMediaMetaDataAsync(List<MediaMetadata> mediaMetaDataToAdd, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing MediaMetadata records to add.");
                await _db.MediaMetadata.AddRangeAsync(mediaMetaDataToAdd, cancellationToken);
            }

            private async Task<List<MediaMetadata>> ProcessMediaAsync(MediaAddedDto mediaDto, CancellationToken cancellationToken)
            {
                var mediaMetaDataToAdd = new List<MediaMetadata>();
                var jobId = mediaDto.JobId;
                foreach (var file in mediaDto.Files.DistinctBy(x => x.MediaId))
                {
                    using var mediaScope = _logger.BeginScope("{mediaId}", file.MediaId);
                    _logger.LogInformation("Mapping file from DocuSketch to MediaMetaData.");
                    var media = await _db.MediaMetadata.FirstOrDefaultAsync(x => x.Id == file.MediaId, cancellationToken);
                    if (media != null)
                    {
                        _logger.LogWarning("Media with Id already exists.");
                        continue;
                    }

                    if (file.S3BucketLocation.IsNullOrWhiteSpace())
                    {
                        _logger.LogWarning("MediaPath should not be Null or Empty");
                        continue;
                    }

                    var mediaMetadata = await MapAsync(file, jobId, cancellationToken);
                    mediaMetaDataToAdd.Add(mediaMetadata);
                }

                return mediaMetaDataToAdd;
            }

            private void ValidateRequest(MediaAddedDto mediaDto)
            {
                if (mediaDto is null)
                    throw new Exception("Media provided in the request should not be null.");

                if (_s3BucketName.IsNullOrWhiteSpace())
                    throw new Exception("S3 bucket name should not be default value.");
            }

            private async Task<MediaMetadata> MapAsync(File dto, Guid jobId, CancellationToken cancellationToken)
            {
                var validatedName = ValidateFileName(dto);
                var adjustedName = AdjustDocuSketch360FileName(dto.ArtifactTypeId, validatedName);

                return new MediaMetadata
                {
                    Id = dto.MediaId,
                    JobId = jobId,
                    FranchiseSetId = await GetFranchiseSetId(jobId, cancellationToken),
                    MediaTypeId = dto.MediaTypeId,
                    ArtifactTypeId = dto.ArtifactTypeId,
                    Name = adjustedName,
                    BucketName = _s3BucketName,
                    ArtifactDate = DateTime.UtcNow,
                    MediaPath = dto.S3BucketLocation
                };
            }

            private string ValidateFileName(File dto)
            {
                if (string.IsNullOrWhiteSpace(dto.Name))
                {
                    _logger.LogWarning("The file name is null or empty for MediaId: {MediaId}", dto.MediaId);
                    return "Unnamed File";
                }

                return dto.Name;
            }

            private string AdjustDocuSketch360FileName(Guid artifactTypeId, string name)
            {
                if (artifactTypeId == ArtifactTypes.DocuSketch360StillImage)
                {
                    var parts = name.Split(' ');
                    if (parts.Length > 3)
                    {
                        // Assumes the format is "Name - Number - MMM dd"
                        var month = parts[^2].ToUpper(); // Converts the month to uppercase
                        parts[^2] = month;
                        return string.Join(' ', parts);
                    }
                    else
                    {
                        _logger.LogWarning("Unexpected format for DocuSketch 360 file name: {Name}", name);
                    }
                }

                return name;
            }

            private async Task<Guid> GetFranchiseSetId(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.Select(job => new { job.Id, job.FranchiseSetId }).FirstOrDefaultAsync(job => job.Id == jobId, cancellationToken);
                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {jobId}");

                return job.FranchiseSetId;
            }

        }
    }
}