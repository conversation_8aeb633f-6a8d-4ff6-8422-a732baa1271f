﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobInvoice
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult MediaResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> MediaIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobInvoice>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobInvoice> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyJobInvoice> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobInvoice));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.MediaIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var invoiceTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedInvoiceIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(invoiceTargetIds, 
                    GetJobInvoiceIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobInvoice, ResaleJobInvoice>(
                    request.ResaleId,
                    invoice =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.MediaResult.FailedEntities.Contains(invoice.MediaMetadataId))
                            failedDependencies.Add((nameof(MediaMetadata), invoice.MediaMetadataId));

                        return failedDependencies;
                    },
                    alreadyCopiedInvoiceIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobInvoices.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobInvoice>> GetSourceEntitiesAsync(List<Guid> mediaIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobInvoices = await _context.JobInvoices
                    .Where(ji => mediaIds.Contains(ji.MediaMetadataId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobInvoices.Count);
                return jobInvoices;
            }

            private async Task<List<Guid>> GetJobInvoiceIdsAsync(List<Guid?> invoiceTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobInvoices
                    .Where(x => invoiceTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
