apt-get update
apt-get install unzip
apt-get install sudo
apt-get install curl -y
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
aws --version
env=$(aws ssm get-parameter --name /workcenter/misc/env --output text --query "Parameter.Value" --region us-west-2)
aws secretsmanager get-secret-value --secret-id $env/$1/master --query SecretString > artifacts/secret