﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.CorporateService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Mica;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using System.Linq;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class SubmitToMica
    {
        public class Command : IRequest<Unit>
        {
            public Guid JobId { get; set; }
            public List<Guid> FormIds { get; set; }
            public List<Guid> DocumentIds { get; set; }
            public List<Guid> PhotoIds { get; set; }
            public List<Guid> CorrespondenceIds { get; set; }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<SubmitToMica> _logger;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMicaServiceClient _micaServiceClient;
            private readonly IXactServiceClient _xactServiceClient;
            private readonly ICorporateServiceClient _corporateServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(JobDataContext context,
                ILogger<SubmitToMica> logger,
                IMicaServiceClient micaServiceClient,
                IXactServiceClient xactServiceClient,
                IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor,
                ICorporateServiceClient corporateServiceClient,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _context = context;
                _logger = logger;
                _micaServiceClient = micaServiceClient;
                _xactServiceClient = xactServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _corporateServiceClient = corporateServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Submiting Job: [{jobId}] to Mica", request.JobId);
                _logger.LogInformation("Submiting Documents to Mica: {@docs}", request.DocumentIds);
                _logger.LogInformation("Submiting Forms to Mica: {@forms}", request.FormIds);
                _logger.LogInformation("Submiting Photos to Mica: {@photos}", request.PhotoIds);
                _logger.LogInformation("Submiting Correspondence to Mica: {@correspondence}", request.CorrespondenceIds);

                var job = await GetJobAsync(request.JobId, cancellationToken);
                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                await _micaServiceClient.SaveEntitiesAsync(Map(request), cancellationToken);

                var xactMfn = await _xactServiceClient.GetJobMfnAsync(request.JobId, cancellationToken);

                var xactNetAddress = await _corporateServiceClient.GetMicaXactnetAddress(job.FranchiseId, cancellationToken);
                var franchiseEntityCodes = await _franchiseServiceClient.GetFranchiseEntityCodesAsync(job.FranchiseId, cancellationToken);
                _logger.LogInformation("Franchise Entity Codes: {@franchiseEntityCodes}", franchiseEntityCodes);

                await RaiseSubmitToMicaStartedEvent(request.JobId, job.ProjectNumber,
                                xactMfn.MasterFileNumber, xactNetAddress, franchiseEntityCodes, job.JobCustomAttributes, cancellationToken);

                return Unit.Value;
            }

            private async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken = default)
                => await _context.Jobs
                        .Include(x => x.JobCustomAttributes)
                    .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);

            private Guid GetCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out var correlationId))
                    correlationId = Guid.NewGuid();
                return correlationId;
            }

            private async Task RaiseSubmitToMicaStartedEvent(
                                   Guid jobId,
                                   string projnumber,
                                   string xactMfn,
                                   string xactNetAddress,
                                   IEnumerable<FranchiseEntityCodeDto> franchiseEntityCodes,
                                   ICollection<JobCustomAttribute> jobCustomAttributes,
                                   CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = GetCorrelationId();
                var entityCodes = franchiseEntityCodes.Select(x => new SubmitToMicaStartedEvent.FranchiseEntityCodeDto
                {
                    FranchiseNumber = x.FranchiseNumber,
                    EntityCode = x.EntityCode,
                    EntityType = x.EntityType,
                    SoftwareId = x.SoftwareId
                });

                var outgoingEvent = new SubmitToMicaStartedEvent(jobId, 
                    projnumber, 
                    xactMfn,
                    xactNetAddress,
                    userInfo.Username, 
                    userInfo.Name, 
                    userInfo.Id, 
                    false, 
                    false, 
                    entityCodes,
                    jobCustomAttributes.ToDictionary(x => x.Key, x => x.Value),
                    correlationId);

                var eventJson = outgoingEvent.ToJson();

                var outboxMessage = new OutboxMessage(eventJson, nameof(SubmitToMicaStartedEvent),
                    correlationId, userInfo.Username);

                await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);
            }

            private SubmitToMicaCommand Map(Command request)
            {
                return new SubmitToMicaCommand
                {
                    JobId = request.JobId,
                    Entities = new EntitiesDto
                    {
                        FormIds = request.FormIds,
                        DocumentIds = request.DocumentIds,
                        PhotoIds = request.PhotoIds,
                        CorrespondenceIds = request.CorrespondenceIds
                    }
                };
            }
        }
    }
}
