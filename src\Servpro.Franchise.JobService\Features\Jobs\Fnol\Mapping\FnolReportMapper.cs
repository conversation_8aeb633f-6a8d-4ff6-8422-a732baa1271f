﻿using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.JobService.Models.FnolReport;
using Servpro.Franchise.LookupService.Features.LookUps;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.Fnol.Mapping
{
    public static class FnolReportMapper
    {
        public static FnolReportDto Map(Job job, GetLookups.Dto lookups, List<InsuranceClient> insurances)
        {
            var insuranceCarrierLookups = insurances.ToDictionary(x => x.Id, x => x.Name);
            var lossTypeLookups = lookups.LossTypes.ToDictionary(x => x.Id, x => x.Name);
            var propertyTypeLookups = lookups.PropertyTypes.ToDictionary(x => x.Id, x => x.Name);
            var lossSeverityTypeLookups = lookups.LossSeverityTypes.ToDictionary(x => x.Id, x => x.Name);
            //For some reason this has duplicate Ids, so DistinctBy fixes that
            //The Id field is unique, but doesn't match job.CauseOfLossId
            var causeOfLossTypeLookups = lookups.CausesOfLoss
                .DistinctBy(x => x.CauseOfLossId)
                .ToDictionary(x => x.CauseOfLossId, x => x.Name);
            var facilityTypeLookups = lookups.FacilityTypes.ToDictionary(x => x.Id, x => x.Name);
            var structureTypeLookups = lookups.StructureTypes.ToDictionary(x => x.Id, x => x.Name);
            var flooringTypeLookups = lookups.FlooringTypes.ToDictionary(x => x.Id, x => x.Name);

            var insuranceCarrierId = job.InsuranceCarrierId ?? Guid.Empty;
            var lossSeverityId = job.LossSeverityId ?? -1;
            var facilityTypeId = job.FacilityTypeId ?? -1;

            return new FnolReportDto()
            {
                JobId = job.Id,
                JobNumber = job.ProjectNumber,
                JobProgress = job.JobProgress,
                PropertyType = propertyTypeLookups.ContainsKey(job.PropertyTypeId) ? propertyTypeLookups[job.PropertyTypeId] : null,
                Caller = job.Caller is Contact ? MapContact(job.Caller, JobContactTypes.Caller) : null,
                CorporateNumber = job.CorporateJobNumber,

                Customer = job.Customer is Contact ? MapContact(job.Customer, JobContactTypes.Customer) : null,
                LossAddress = job.LossAddress,
                CustomerCalled = job.GetDate(JobDateTypes.CustomerCalled),

                LossOccurred = job.DateOfLoss,
                LossType = lossTypeLookups.ContainsKey(job.LossTypeId) ? lossTypeLookups[job.LossTypeId] : null,
                CauseOfLoss = causeOfLossTypeLookups.ContainsKey(job.CauseOfLossId) ? causeOfLossTypeLookups[job.CauseOfLossId] : null,
                LossSeverity = lossSeverityTypeLookups.ContainsKey(lossSeverityId) ? lossSeverityTypeLookups[lossSeverityId] : null,
                FacilityType = job.PropertyTypeId != PropertyTypes.Residential ? (facilityTypeLookups.ContainsKey(facilityTypeId) ? facilityTypeLookups[facilityTypeId] : null) : null,
                CommercialStructureType = job.StructureTypeId.HasValue ? 
                    (structureTypeLookups.ContainsKey(job.StructureTypeId.Value) ? structureTypeLookups[job.StructureTypeId.Value] : null) 
                    : null,

                IsWaterAvailable = job.IsWaterAvailable,
                IsElectricAvailable = job.IsElectricAvailable,
                IsCeilingAffected = job.IsCeilingAffected,
                IsWallAffected = job.IsWallAffected,
                IsContentAffected = job.IsContentAffected,
                LevelsAffected = job.LevelsAffected,
                RoomsAffected = job.RoomsAffected,
                LossOccurredOnLevel = job.LossOccurredOnLevel,
                SquareFeetAffected = job.SquareFeetAffected,
                SquareFeet = job.SquareFeet,
                IsMultiUnitStructure = job.IsMultiUnitStructure,
                LossNote = job.LossNote,

                InsuranceCompanyName = insuranceCarrierLookups.ContainsKey(insuranceCarrierId) ? insuranceCarrierLookups[insuranceCarrierId] : null,
                InsuranceClaimNumber = job.InsuranceClaimNumber,
                InsurancePolicyNumber = job.InsurancePolicyNumber,
                NotToExceedAmount = job.NteAmount,
                CollectDeductible = job.CollectDeductible,
                DeductibleAmount = job.DeductibleAmount,
                IsBidRequested = job.IsBidRequested,
                BusinessName = job.BusinessName,
                FnolReceived = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.ReceivedDate)?.Date,
                ProjectManager = job.ProjectManagerId?.ToString(),
                SiteInspected = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.InitialOnSiteArrival)?.Date,
                EmergencyServicesScheduled = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.EmergencyServicesScheduled)?.Date,
                PriorityResponder = job.PriorityResponderId.ToString(),
                LossBusinessCustomerName = job.Customer?.FullName,
                LossPhoneNumber = job.Customer?.MainPhone,
                PicturesSourceInformation = job.MediaMetadata.Where(m => m.MediaTypeId == MediaTypes.Photo && !m.IsDeleted).Any(),
                LeadEstimate = job.LeadEstimate,
                PurchaseOrderNumber = job.PurchaseOrderNumber,
                WorkOrderNumber = job.WorkOrderNumber,
                SiteReferenceNumber = job.SiteReferenceNumber,

                QuestionAnswers = job.JobTriStateAnswers.Select(MapQuestionAnswer).ToList(),
                Contacts = job.JobContacts.Where(jc => jc.Contact is Contact).Select(jc => MapContact(jc.Contact, jc.JobContactTypeId)).ToList(),
                JournalNotes = job.JournalNotes.Select(MapJournalNote).ToList(),
                Photos = job.MediaMetadata.Where(m => m.MediaTypeId == MediaTypes.Photo && !m.IsDeleted).Select(MapPhoto).ToList(),
                FlooringTypesAffected = job.FlooringTypesAffected.Where(ft => flooringTypeLookups.ContainsKey(ft)).Select(ft => flooringTypeLookups[ft]).ToList()
            };
        }

        private static QuestionAnswerDto MapQuestionAnswer(JobTriStateAnswer question)
        {
            return new QuestionAnswerDto()
            {
                QuestionId = question.JobTriStateQuestionId,
                Answer = question.Answer
            };
        }

        private static JournalNoteDto MapJournalNote(JournalNote journalNote)
        {
            return new JournalNoteDto()
            {
                Id = journalNote.Id,
                Author = journalNote.Author,
                Subject = journalNote.Subject,
                Note = journalNote.Note,
                CategoryId = journalNote.CategoryId,
                TypeId = journalNote.TypeId,
                VisibilityId = journalNote.VisibilityId,
                ActionDate = journalNote.ActionDate,
                IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage
            };
        }

        private static PhotoDto MapPhoto(MediaMetadata photo)
        {
            return new PhotoDto()
            {
                Id = photo.Id,
                PhotoType = photo.MediaTypeId,
                ArtifactTypeId = photo.ArtifactTypeId,
                Name = photo.Name,
                Description = photo.Description,
                ThumbnailUrl = photo.MediaPath,
                IsDeleted = photo.IsDeleted,
                IncludeForUpload = photo.IsForUpload
            };
        }

        private static ContactDto MapContact(Contact contact, Guid jobContactTypeId)
        {
            return new ContactDto()
            {
                Id = contact.Id,
                FirstName = contact.FirstName,
                LastName = contact.LastName,
                EmailAddress = contact.EmailAddress,
                PhoneNumbers = contact.PhoneNumbers,
                Address = contact.Address,
                BusinessName = contact.Business?.Name,
                JobContactTypeId = jobContactTypeId
            };
        }
    }
}
