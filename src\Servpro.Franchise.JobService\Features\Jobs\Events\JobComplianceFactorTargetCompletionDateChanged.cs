﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.WipBoards.ScheduleService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobComplianceFactorTargetCompletionDateChanged
    {
        public class Event : JobComplianceFactorTargetCompletionDateChangedEvent, IRequest
        {
            public Event(JobDto jcfData, Guid correlationId) : base(jcfData, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobComplianceFactorTargetCompletionDateChanged> _logger;

            public Handler(JobDataContext db, 
                ILogger<JobComplianceFactorTargetCompletionDateChanged> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with: {@request}", request);

                var jobDto = request.Job;

                var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobDto.JobId, cancellationToken);

                if (job == null)
                {
                    _logger.LogWarning("{event} The Job or WipRecord with Id: {id} Does Not Exist", nameof(JobComplianceFactorTargetCompletionDateChangedEvent), jobDto.JobId);
                    return Unit.Value;
                }

                job.TargetCompletionDate = jobDto.TargetCompletionDateTime;
                job.ModifiedDate = DateTime.UtcNow;
                job.ModifiedBy = nameof(JobComplianceFactorTargetCompletionDateChanged);

                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}