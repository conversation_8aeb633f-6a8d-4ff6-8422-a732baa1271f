{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "RescindByRSC",
      "invokeTarget": {
        "target": "code",
        "lambdaHandler": "Servpro.Franchise.JobService.Lambda::Servpro.Franchise.JobService.Lambda.Features.DocuSign.RescindByRsc::Handler",
        "projectRoot": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda"
      },
      "lambda": {
        "runtime": "dotnet6",
        "payload": {
          "path": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/DebugAssets/rescindByComboSqsEvent.json"
        }
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:SendAndSubscribe (dotnet6)",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "SendDocument"
      },
      "lambda": {
        "payload": {
          "path": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/DebugAssets/sendDocumentTest.json"
        },
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Rescind Document (via SQS) (dotnet6)",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "RescindDocument"
      },
      "lambda": {
        "payload": {
          "path": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/DebugAssets/rescindDocumentSqsEvent.json"
        },
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Rescind Document (via CRON) (dotnet6)",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "RescindDocument"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:ReadParametersPolicy",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "ReadParametersPolicy"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:ReadSecretsPolicy",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "ReadSecretsPolicy"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:SendToS3Policy",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "SendToS3Policy"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:PublishToSNSPolicy",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "PublishToSNSPolicy"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:ReadSSMPolicy",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "ReadSSMPolicy"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:SignDocumentsTable",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "SignDocumentsTable"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:SendSubscribeQueue",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "SendSubscribeQueue"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    },
    {
      "type": "aws-sam",
      "request": "direct-invoke",
      "name": "Servpro.Franchise.JobService.Lambda:SendSubscribeDLQ",
      "invokeTarget": {
        "target": "template",
        "templatePath": "${workspaceFolder}/src/Servpro.Franchise.JobService.Lambda/template.yaml",
        "logicalId": "SendSubscribeDLQ"
      },
      "lambda": {
        "payload": {},
        "environmentVariables": {}
      }
    }
  ]
}
