﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyRoomFlooringTypeAffected
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult RoomResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> RoomIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyRoomFlooringTypeAffected>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyRoomFlooringTypeAffected> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyRoomFlooringTypeAffected> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(RoomFlooringTypeAffected));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.RoomIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var roomFlooringTypeAffectedTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedRoomFlooringTypeAffectedIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(roomFlooringTypeAffectedTargetIds, 
                    GetRoomFlooringTypeAffectedIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<RoomFlooringTypeAffected, ResaleRoomFlooringTypeAffected>(
                    request.ResaleId,
                    roomFlooringTypeAffected =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.RoomResult.FailedEntities.Contains(roomFlooringTypeAffected.RoomId))
                            failedDependencies.Add((nameof(Room), roomFlooringTypeAffected.RoomId));

                        return failedDependencies;
                    },
                    alreadyCopiedRoomFlooringTypeAffectedIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.RoomFlooringTypesAffected.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<RoomFlooringTypeAffected>> GetSourceEntitiesAsync(List<Guid> roomIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var roomFlooringTypeAffected = await _context.RoomFlooringTypesAffected
                    .Where(rfta => roomIds.Contains(rfta.RoomId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", roomFlooringTypeAffected.Count);
                return roomFlooringTypeAffected;
            }

            private async Task<List<Guid>> GetRoomFlooringTypeAffectedIdsAsync(List<Guid?> roomFlooringTypeAffectedTargetIds, 
                CancellationToken cancellationToken)
            {
                return await _context.RoomFlooringTypesAffected
                    .Where(x => roomFlooringTypeAffectedTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
