﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using JobAreaTypes = Servpro.Franchise.JobService.Common.JobAreaTypes;
using JobDateTypes = Servpro.Franchise.LookupService.Constants.JobDateTypes;
using Task = System.Threading.Tasks.Task;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones
{
    public class PlaceEquipment
    {
        public class Command : IRequest<IEnumerable<GetEquipmentPlacementByRoom.Dto>>
        {
            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
            public bool IsUsedInValidation { get; set; }
            public DateTime BeginDate { get; set; }
            public IEnumerable<PlacementDto> EquipmentPlacements { get; set; }

            public class PlacementDto
            {
                public Guid EquipmentId { get; set; }
                public string AssetNumber { get; set; }
                public string EquipmentType { get; set; }
                public string Manufacturer { get; set; }
                public string SerialNumber { get; set; }
                public string Model { get; set; }
                public int? VolumeRate { get; set; }
                public decimal? Amps { get; set; }
                public Guid EquipmentTypeId { get; set; }
                public Guid BaseEquipmentTypeId { get; set; }
            }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleForEach(x => x.EquipmentPlacements)
                    .Must(x => x.EquipmentId != Guid.Empty
                        && x.BaseEquipmentTypeId != Guid.Empty);
            }
        }

        public class Handler : IRequestHandler<Command, IEnumerable<GetEquipmentPlacementByRoom.Dto>>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            public readonly ISessionIdAccessor _sessionIdAccessor;
            public readonly ILogger<Handler> _logger;

            public Handler(JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo,
                ILogger<Handler> logger)
            {
                _userInfo = userInfo;
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
                _logger = logger;
            }

            public async Task<IEnumerable<GetEquipmentPlacementByRoom.Dto>> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();

                var checkJob = await _context.Jobs
                                .Select(x => new { x.Id, x.CurrentJobLock, x.FranchiseSetId, x.JobLocks})
                                .FirstOrDefaultAsync(x => x.FranchiseSetId == userInfo.FranchiseSetId.Value
                                                          && x.Id == request.JobId, 
                                                    cancellationToken);

                if (checkJob is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var joblocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(checkJob.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(checkJob.CurrentJobLock is null ? null : GetJobLock.Handler.Map(checkJob.CurrentJobLock));

                var equipmentToAddIds = request.EquipmentPlacements.Select(x => x.EquipmentId).ToList();

                var existingEquipment = await _context.Equipments
                    .Include(x => x.EquipmentModel)
                    .ThenInclude(x => x.EquipmentType)
                    .Where(x => x.FranchiseSetId == userInfo.FranchiseSetId.Value
                                && equipmentToAddIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                var jsonExistingEquipment = existingEquipment.ToJson();
                _logger.LogInformation($"Entering into SettingValidatedDryingStart with existingequip : {jsonExistingEquipment} and JobID: {request.JobId} ");

                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                    .FirstOrDefaultAsync(x => x.FranchiseSetId == userInfo.FranchiseSetId.Value
                                && x.Id == request.JobId, cancellationToken);

                await SetValidDryingStartedDate(job, request, cancellationToken);

                var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == request.RoomId);

                if (jobArea is null)
                {
                    jobArea = new Models.Drybook.JobArea
                    {
                        Id = Guid.NewGuid(),
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        JobId = request.JobId,
                        Name = "Job Room",
                        JobAreaTypeId = JobAreaTypes.Job
                    };

                    job.JobAreas.Add(jobArea);
                }

                var existingEquipmentIds = existingEquipment.Select(x => x.Id).ToHashSet();
                var existingEquipmentTypes = await _context.EquipmentTypes
                    .ToDictionaryAsync(x => x.Id, cancellationToken: cancellationToken);
                var newEquipmentTypes = request.EquipmentPlacements
                    .Where(x => !existingEquipmentTypes.ContainsKey(x.EquipmentTypeId))
                    .GroupBy(x => x.EquipmentTypeId)
                    .Select(x => new KeyValuePair<Guid, Models.Drybook.EquipmentType>(x.First().EquipmentTypeId, new Models.Drybook.EquipmentType
                    {
                        Id = x.First().EquipmentTypeId,
                        Name = x.First().EquipmentType,
                        BaseEquipmentTypeId = x.First().BaseEquipmentTypeId
                    }));
                var equipmentTypes = existingEquipmentTypes
                    .Concat(newEquipmentTypes)
                    .ToDictionary(x => x.Key, x => x.Value);
                var equipmentToAdd = request.EquipmentPlacements
                    .Where(x => !existingEquipmentIds.Contains(x.EquipmentId))
                    .Select(x => Map(x, equipmentTypes, userInfo));

                _context.Equipments.AddRange(equipmentToAdd);

                var equipmentPlacements = request.EquipmentPlacements
                    .Select(x => Map(x, request.BeginDate, request.IsUsedInValidation, userInfo));

                var equipPlacements = equipmentPlacements.ToList();

                foreach (var equipPlacement in equipPlacements)
                    jobArea.EquipmentPlacements.Add(equipPlacement);

                var outboxMessage = MapToEvent(equipPlacements, jobArea, userInfo);
                _context.OutboxMessages.Add(outboxMessage);

                await _context.SaveChangesAsync(cancellationToken);

                var equipmentLookup = equipmentToAdd.Concat(existingEquipment).ToDictionary(x => x.Id);
                return equipPlacements.Select(x => new GetEquipmentPlacementByRoom.Dto
                {
                    EquipmentId = x.EquipmentId,
                    BeginDate = x.BeginDate,
                    EndDate = x.EndDate,
                    EquipmentPlacementId = x.Id,
                    RoomName = jobArea.Name,
                    VolumeRate = equipmentLookup[x.EquipmentId].VolumeRate,
                    Amps = equipmentLookup[x.EquipmentId].EquipmentModel.Amps,
                    AssetNumber = equipmentLookup[x.EquipmentId].AssetNumber,
                    EquipmentTypeId = equipmentLookup[x.EquipmentId].EquipmentModel.EquipmentType.Id,
                    EquipmentTypeName = equipmentLookup[x.EquipmentId].EquipmentModel.EquipmentType.Name,
                    Manufacturer = equipmentLookup[x.EquipmentId].EquipmentModel.ManufacturerName,
                    ModelName = equipmentLookup[x.EquipmentId].EquipmentModel.Name,
                    SerialNumber = equipmentLookup[x.EquipmentId].SerialNumber
                });
            }

            private async Task SetValidDryingStartedDate
                (Job job, Command request, CancellationToken cancellationToken)
            {
                try
                {

                    //Validating if job type applies to drying start
                    var validLostTypes = new List<Guid> { LossTypes.Mold, LossTypes.Water, LossTypes.Sewage, LossTypes.FireWater };
                    var isValidLostType = validLostTypes.Any(item => job.LossTypeId == item);

                    var dryingEquipmentsBase = new List<Guid>
                {
                    BaseEquipmentTypes.AirMover,
                    BaseEquipmentTypes.Dehumidifier
                };

                    if (isValidLostType)
                    {
                        var dryingEquipmentExist = false;
                        //validate if is adding the first equipment
                        var jobAreas = job.JobAreas.Where(q => q.JobId == request.JobId).ToList();
                        if (jobAreas.Any())
                        {
                            var equipments = new List<EquipmentPlacement>();

                            foreach (var area in jobAreas)
                            {
                                var equipmentPlacements = await _context.EquipmentPlacements
                                       .Include(q => q.Equipment)
                                       .ThenInclude(e => e.EquipmentModel)
                                       .ThenInclude(et => et.EquipmentType).Where(q => q.JobAreaId == area.Id).ToListAsync(cancellationToken);

                                var jsonEquipPlace = JsonConvert.SerializeObject(equipmentPlacements);
                                _logger.LogInformation($"Equipment Placements with JobArea: {jsonEquipPlace} and JobID: {request.JobId} ");

                                if (equipmentPlacements != null && equipmentPlacements.Any())
                                    equipments.AddRange(equipmentPlacements);
                            }
                            if (equipments.Any())
                            {
                                var jsonEquipments = JsonConvert.SerializeObject(equipments);
                                _logger.LogInformation($"Before Drying Equipment Existing : {jsonEquipments} and JobID: {request.JobId} ");
                                dryingEquipmentExist = dryingEquipmentsBase
                                    .Any(item => equipments
                                        .Any(q => q.Equipment.EquipmentModel.EquipmentType.BaseEquipmentTypeId == item));
                            }

                        }


                        if (!jobAreas.Any() || !dryingEquipmentExist)
                        {
                            _logger.LogInformation($"Entering into GenerateDryStartDate with JobID: {request.JobId} ");
                            var user = _userInfo.GetUserInfo();
                            job.SetOrUpdateDate(JobDateTypes.DryingStarted, request.BeginDate);

                            var workStartDate = job.GetDate(JobDateTypes.WorkStartDate);

                            if(workStartDate == null)
                            {
                                job.SetOrUpdateDate(JobDateTypes.WorkStartDate, request.BeginDate);
                                await GenerateJobDateCreatedEvent(request, user, _sessionIdAccessor.GetCorrelationGuid(), cancellationToken);
                            }

                            await GenerateDryStartedEvent(request, user, _sessionIdAccessor.GetCorrelationGuid(), cancellationToken);
                        }
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError(ex, $"Exception caught and ignored in PlaceEquipment during SetValidDryingStartedDate");
                }
            }

            private async Task GenerateDryStartedEvent(Command request, UserInfo user, Guid correlationId, CancellationToken cancellationToken)
            {
                var eventDto = new DryingStartedEvent.DryingStartedDto()
                {
                    JobId = request.JobId,
                    JobDateTypeId = JobDateTypes.DryingStarted,
                    Date = request.BeginDate,
                    ModifiedBy = user.Username
                };

                var outboxEvent = new DryingStartedEvent(eventDto, correlationId);
                var outboxMessage = new OutboxMessage(outboxEvent.ToJson(), nameof(DryingStartedEvent),
                    correlationId, user.Username);
                await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task GenerateJobDateCreatedEvent(Command request, UserInfo user, Guid correlationId, CancellationToken cancellationToken)
            {
                var dto = new JobDateCreatedEvent.JobDateCreatedDto(
                            request.JobId, JobDateTypes.WorkStartDate, request.BeginDate, user.Username);
                var @event = new JobDateCreatedEvent(dto, correlationId);
                var outboxMessage = new OutboxMessage(@event.ToJson(), nameof(JobDateCreatedEvent), correlationId, user.Username);
                await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            OutboxMessage MapToEvent(
                IEnumerable<Models.Drybook.EquipmentPlacement> equipmentPlacements,
                Models.Drybook.JobArea jobArea,
                UserInfo userInfo)
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();
                var equipmentPlacedDto = new EquipmentPlacedDto
                {
                    JobId = jobArea.JobId,
                    FranchiseSetId = userInfo.FranchiseSetId.Value,
                    JobAreaId = jobArea.Id,
                    RoomId = jobArea.RoomId == null ? (Guid?)null : jobArea.RoomId.Value,
                    UserId = userInfo.Id,
                    Username = userInfo.Username,
                    Placements = equipmentPlacements.Select(x => new EquipmentPlacedDto.PlacementDto
                    {
                        Id = x.Id,
                        EquipmentId = x.EquipmentId,
                        BeginDate = x.BeginDate
                    })
                };
                var equipmentPlacedEvent = new EquipmentPlacedEvent(equipmentPlacedDto, correlationId);
                return new OutboxMessage(equipmentPlacedEvent.ToJson(),
                                   nameof(EquipmentPlacedEvent), correlationId, userInfo.Username);
            }

            Models.Drybook.EquipmentPlacement Map(
                Command.PlacementDto placementDto,
                DateTime beginDate,
                bool isUsedInValidation,
                UserInfo userInfo)
                => new Models.Drybook.EquipmentPlacement
                {
                    Id = Guid.NewGuid(),
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.UtcNow,
                    BeginDate = beginDate,
                    EquipmentId = placementDto.EquipmentId,
                    IsUsedInValidation = isUsedInValidation
                };

            Models.Drybook.Equipment Map(
                Command.PlacementDto placement,
                Dictionary<Guid, Models.Drybook.EquipmentType> equipmentTypes,
                UserInfo userInfo)
            {
                return new Models.Drybook.Equipment
                {
                    Id = placement.EquipmentId,
                    AssetNumber = placement.AssetNumber,
                    FranchiseSetId = userInfo.FranchiseSetId.Value,
                    CreatedBy = userInfo.Username,
                    SerialNumber = placement.SerialNumber,
                    VolumeRate = placement.VolumeRate,
                    EquipmentModel = new Models.Drybook.EquipmentModel
                    {
                        Id = Guid.NewGuid(),
                        Name = placement.Model,
                        ManufacturerName = placement.Manufacturer,
                        FranchiseSetId = userInfo.FranchiseSetId.Value,
                        Amps = placement.Amps ?? 0,
                        EquipmentType = equipmentTypes[placement.EquipmentTypeId]
                    }
                };

            }
        }
    }
}
