﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetJournalNote
    {
        public class Query : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid JournalNoteId { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.JournalNoteId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? JobId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid CategoryId { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public DateTime? ActionDate { get; set; }
            public DateTime CreatedDate { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;
            public Handler(JobReadOnlyDataContext db) => _db = db;

            public async Task<Dto> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var journalNote = await _db.JournalNote
                    .Where(j => j.JobId == request.JobId && j.Id == request.JournalNoteId)
                    .FirstOrDefaultAsync(cancellationToken);

                if (journalNote is null)
                    throw new ResourceNotFoundException($"JournalNote not found (Id: {request.JournalNoteId}");

                journalNote.ActionDate = (journalNote.ActionDate != null) ? journalNote.ActionDate : DateTime.UtcNow;
                return Map(journalNote);
            }

            private Dto Map(JournalNote journalNote)
            => new Dto
            {
                Id = journalNote.Id,
                JobId = journalNote.JobId,
                CreatedDate = journalNote.CreatedDate,
                Author = journalNote.Author,
                Note = journalNote.Note,
                CategoryId = journalNote.CategoryId,
                TypeId = journalNote.TypeId,
                Subject = journalNote.Subject,
                ActionDate = journalNote.ActionDate,
                VisibilityId = journalNote.VisibilityId
            };
        }
    }
}
