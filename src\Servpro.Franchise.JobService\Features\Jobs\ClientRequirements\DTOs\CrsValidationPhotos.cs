﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationPhotos
    {
        public Guid? JobAreaId { get; set; }
        public Guid? JobDiaryEntryId { get; set; }
        public Guid? JobVisitId { get; set; }
        public Guid? JobArtifactId { get; set; }
        public Guid? JobAreaMaterialId { get; set; }
        public bool Valid { get; set; }
    }

    public class ValidationPhotoRequirements : CrsValidationBase
    {
        public Guid? ArtifactTypeId { get; set; }
        public string ValidationEffect { get; set; }
        public bool RequiredPerVisit { get; set; }
        public bool RequiredPerRoom { get; set; }
        public bool RequiredLastVisit { get; set; }
        public Guid? JobTriStateQuestionId { get; set; }
        public bool? TriggerAnswer { get; set; }
        public bool BypassAllowed { get; set; }

        public List<CrsValidationPhotos> Photos { get; set; }
        public List<KeyValuePair<string, string>> RuleParameters { get; set; }

        public ValidationPhotoRequirements()
        {
            this.Photos = new List<CrsValidationPhotos>();
            this.RuleParameters = new List<KeyValuePair<string, string>>();
        }
    }
}