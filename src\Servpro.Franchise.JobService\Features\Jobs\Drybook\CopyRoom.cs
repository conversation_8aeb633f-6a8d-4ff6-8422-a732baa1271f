﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class CopyRoom
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
            public int CopyCount { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.RoomId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo)
            {
                _context = context;
                _userInfo = userInfo;
            }
            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var jobArea = await _context.JobAreas
                            .Include(ja => ja.Job)
                            .Include(ja => ja.Room)
                                .ThenInclude(r => r.RoomFlooringTypesAffected)
                            .FirstOrDefaultAsync(ja => ja.RoomId == request.RoomId && ja.JobId == request.JobId && !ja.IsDeleted, cancellationToken);

                var userInfo = _userInfo.GetUserInfo();

                var job = jobArea.Job;
                if (job != null)
                {
                    job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                    job.JournalNotes = await _context.JournalNote.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                    job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                    if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                        throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));
                }

                //Check number of copies 
                var copiesJobArea = 0;
                if (jobArea != null)
                {
                    var copyName = $"{jobArea.Name} - Copy";
                    var copiesJobAreaList = await _context.JobAreas
                            .Where(ja =>  ja.JobId == request.JobId && 
                                !ja.IsDeleted &&
                                ja.Name.Contains(copyName)).ToListAsync(cancellationToken);
                    copiesJobArea = copiesJobAreaList.Count;
                }
                var room = jobArea?.Room;

                if (room is null)
                    throw new ResourceNotFoundException($"Room not found (Id: {request.RoomId}");

                for (int i = 0; i < request.CopyCount; i++)
                {
                    var roomId = Guid.NewGuid();
                    var copyRoom = new Room
                    {
                        Id = roomId,
                        CreatedDate = DateTime.UtcNow,
                        CreatedBy = userInfo.Username,
                        RoomTypeId = room.RoomTypeId,
                        RoomShapeId = room.RoomShapeId,
                        WaterCategoryId = WaterCategories.Sanitary,
                        WaterClassId = WaterClasses.NoWaterDamage,
                        FloorTypeId = FloorTypes.AboveGround,
                        AffectedCeilingAreaSquareInches = room.AffectedCeilingAreaSquareInches,
                        AffectedWallAreaSquareInches = room.AffectedWallAreaSquareInches,
                        AffectedWallAreaAbove2FeetSquareInches = room.AffectedWallAreaAbove2FeetSquareInches,
                        AffectedWallAreaBelow2FeetSquareInches = room.AffectedWallAreaBelow2FeetSquareInches,
                        CeilingAreaSquareInches = room.CeilingAreaSquareInches,
                        CeilingPerimeterInches = room.CeilingPerimeterInches,
                        FloorAreaSquareInches = room.FloorAreaSquareInches,
                        FloorPerimeterInches = room.FloorPerimeterInches,
                        Height1TotalInches = room.Height1TotalInches,
                        Height2TotalInches = room.Height2TotalInches,
                        Height3TotalInches = room.Height3TotalInches,
                        Length1TotalInches = room.Length1TotalInches,
                        Length2TotalInches = room.Length2TotalInches,
                        MissingCeilingAreaSquareInches = room.MissingCeilingAreaSquareInches,
                        MissingCeilingPerimeterInches = room.MissingCeilingPerimeterInches,
                        MissingFloorAreaSquareInches = room.MissingFloorAreaSquareInches,
                        MissingFloorPerimeterInches = room.MissingFloorPerimeterInches,
                        MissingRoomVolumeCubicInches = room.MissingRoomVolumeCubicInches,
                        MissingWallAreaSquareInches = room.MissingWallAreaSquareInches,
                        OffsetCeilingAreaSquareInches = room.OffsetCeilingAreaSquareInches,
                        OffsetCeilingPerimeterInches = room.OffsetCeilingPerimeterInches,
                        OffsetFloorAreaSquareInches = room.OffsetFloorAreaSquareInches,
                        OffsetFloorPerimeterInches = room.OffsetFloorPerimeterInches,
                        OffsetRoomVolumeCubicInches = room.OffsetRoomVolumeCubicInches,
                        OffsetWallAreaSquareInches = room.OffsetWallAreaSquareInches,
                        WallAreaBelow2FeetSquareInches = room.WallAreaBelow2FeetSquareInches,
                        WallAreaSquareInches = room.WallAreaSquareInches,
                        Width1TotalInches = room.Width1TotalInches,
                        Width2TotalInches = room.Width2TotalInches,
                        OffsetSpaces = room.OffsetSpaces,
                        MissingSpaces = room.MissingSpaces
                    };

                    if (room.PreExistingConditionsDiaryNoteId != null &&
                        room.PreExistingConditionsDiaryNoteId != Guid.Empty)
                    {
                        var journalNote = jobArea.Job.JournalNotes
                               .FirstOrDefault(x => x.Id == room.PreExistingConditionsDiaryNoteId);
                        var journalNoteGuid = Guid.NewGuid();

                        _context.JournalNote.Add(new JournalNote
                        {
                            Id = journalNoteGuid,
                            JobId = request.JobId,
                            CategoryId = JournalNoteCategories.Room,
                            TypeId = TaskTypes.PreExistingCondition,
                            VisibilityId = JournalNoteVisibilities.FranchiseClientAndCustomer,
                            Subject = journalNote.Subject,
                            Note = journalNote.Note,
                            Author = userInfo.Name,
                            ActionDate = DateTime.UtcNow,
                            CreatedBy = userInfo.Username,
                            CreatedDate = DateTime.UtcNow,
                        });
                        copyRoom.PreExistingConditions = journalNote.Subject;
                        copyRoom.PreExistingConditionsDiaryNoteId = journalNoteGuid;
                    }

                    if (room.RoomFlooringTypesAffected != null)
                    {
                        foreach (var floorigTypeAffected in room.RoomFlooringTypesAffected)
                        {
                            _context.RoomFlooringTypesAffected.Add(new RoomFlooringTypeAffected
                            {
                                Id = Guid.NewGuid(),
                                CreatedDate = DateTime.UtcNow,
                                CreatedBy = userInfo.Username,
                                RoomId = roomId,
                                AffectedPercentage = floorigTypeAffected.AffectedPercentage,
                                AffectedSquareFeet = floorigTypeAffected.AffectedSquareFeet,
                                FlooringTypeId = floorigTypeAffected.FlooringTypeId,
                                IsPadRestorable = floorigTypeAffected.IsPadRestorable,
                                IsSalvageable = floorigTypeAffected.IsSalvageable,
                                OtherText = floorigTypeAffected.OtherText ?? "",
                                SavedPercentage = floorigTypeAffected.SavedPercentage,
                                SavedSquareFeet = floorigTypeAffected.SavedSquareFeet,
                                TotalSquareFeet = floorigTypeAffected.TotalSquareFeet
                            });
                        }
                    }

                    _context.Rooms.Add(copyRoom);
                    var copyRoomName = jobArea.Name + " - Copy";
                    if (copiesJobArea >= 1)
                    {
                        copiesJobArea++;
                        copyRoomName = $"{jobArea.Name} - Copy ({copiesJobArea})";
                    }
                    _context.JobAreas.Add(new JobArea
                    {
                        Id = Guid.NewGuid(),
                        CreatedBy =userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        JobId = request.JobId,
                        JobAreaTypeId = JobAreaTypes.Room,
                        RoomId = roomId,
                        Name = copyRoomName
                    }); 
                }

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }
        }
    }
}
