﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class SaveAtmosphericReadings
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
            public IEnumerable<ZoneReadingDto> ZoneReadings { get; set; }

            public class ZoneReadingDto
            {
                public Guid ZoneId { get; set; }
                public string Description { get; set; }
                public decimal? Temperature { get; set; }
                public decimal? RelativeHumidity { get; set; }
                public bool? IsHvacOn { get; set; }
            }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.JobVisitId).NotEmpty();
                RuleForEach(x => x.ZoneReadings)
                    .Must(y => y.ZoneId != Guid.Empty);
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILogger<SaveAtmosphericReadings> _logger;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILogger<SaveAtmosphericReadings> logger)
            {
                _context = context;
                _userInfo = userInfo;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Begin handler with: {@request}", request);

                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.Zones)
                        .ThenInclude(y => y.ZoneReadings)
                    .Include(x => x.Zones)
                        .ThenInclude(y => y.JobAreas)
                            .ThenInclude(z => z.BeginJobVisit)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var visit = job.JobVisits
                    .FirstOrDefault(x => x.Id == request.JobVisitId);

                if (visit is null && request.JobVisitId == Guid.Empty)
                    throw new ResourceNotFoundException($"Visit not found (Id: {request.JobVisitId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                foreach (var zoneReading in request.ZoneReadings)
                {
                    var zone = job.Zones.FirstOrDefault(x => x.Id == zoneReading.ZoneId);
                    if (zone != null && !ZoneIsActiveForVisit(zone, visit))
                        continue;
                    zone.ModifiedBy = userInfo.Username;
                    zone.ModifiedDate = DateTime.UtcNow;
                    zone.Description = zoneReading.Description;

                    var existingReading = zone.ZoneReadings.FirstOrDefault(x => x.JobVisitId == visit.Id && x.ZoneId == zone.Id);
                    if (existingReading is null)
                    {
                        // do not add zone reading for a zone of type HVAC
                        // when the hvac zone is not on
                        if (zone.ZoneTypeId == ZoneTypes.HVAC
                            && zoneReading.IsHvacOn != true)
                            continue;
                        zone.ZoneReadings.Add(new ZoneReading
                        {
                            Id = Guid.NewGuid(),
                            CreatedBy = userInfo.Username,
                            CreatedDate = DateTime.UtcNow,
                            JobVisitId = request.JobVisitId,
                            ZoneId = zoneReading.ZoneId,
                            RelativeHumidity = zoneReading.RelativeHumidity,
                            Temperature = zoneReading.Temperature
                        });
                    }
                    else
                    {
                        if (zoneReading.IsHvacOn == false && zone.ZoneTypeId == ZoneTypes.HVAC)
                        {
                            _logger.LogDebug("Deleting Zone reading: {existingReading}", existingReading);
                            zone.ZoneReadings.Remove(existingReading);
                        }
                        else
                        {
                            existingReading.ModifiedBy = userInfo.Username;
                            existingReading.ModifiedDate = DateTime.UtcNow;
                            existingReading.RelativeHumidity = zoneReading.RelativeHumidity;
                            existingReading.Temperature = zoneReading.Temperature;
                        }
                    }
                }

                _logger.LogDebug("Handler Completed Successfully");

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private bool ZoneIsActiveForVisit(Zone zone, JobVisit visit)
            {
                // these zone types are always active for any visit
                var alwaysActiveZoneTypes = new HashSet<Guid>
                {
                    ZoneTypes.HVAC, ZoneTypes.Outside, ZoneTypes.Unaffected
                };
                if (alwaysActiveZoneTypes.Contains(zone.ZoneTypeId))
                    return true;
                var date = zone.JobAreas?
                            .Where(x => x.ZoneId == zone.Id
                                && !x.EndJobVisitId.HasValue
                                && x.BeginJobVisitId.HasValue)
                            .Select(x => x.BeginJobVisit?.Date)
                            .Min(x => x);
                return date.HasValue && date <= visit.Date;
            }
        }
        #endregion
    }
}
