﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Models.MicaAutomation;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion
{
    public class CompleteJob
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public DateTime CompletionDate { get; set; }
        }
        
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMicaAutomationUtility _micaAutomationUtility;
            private readonly ILogger<CompleteJob> _logger;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor,
                IMicaAutomationUtility micaAutomationUtility,
                ILogger<CompleteJob> logger)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
                _micaAutomationUtility = micaAutomationUtility;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                var jobProgress = JobProgress.FinalReview;
                var userInfo = _userInfo.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var job = await _context.Jobs
                        .Include(x => x.JobCustomAttributes)
                        .Include(x => x.JournalNotes)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken: cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                job.SetOrUpdateDate(JobDateTypes.Complete, request.CompletionDate);
                job.ModifiedBy = userInfo.Username;
                job.ModifiedDate = DateTime.UtcNow;
                job.JobProgress = jobProgress;
                job.JobProgressModifiedDate = DateTime.UtcNow;

                var drybookCompleteJournalNote = new JournalNote
                {
                    JobId = request.JobId,
                    Author = userInfo.Name,
                    CreatedBy = userInfo.Username,
                    ActionDate = DateTime.UtcNow,
                    TypeId = JournalNoteTypes.StatusNote,
                    VisibilityId = JournalNoteVisibilities.Franchise,
                    Subject = "Work process has completed",
                    CategoryId = JournalNoteCategories.Notes
                };
                job.JournalNotes.Add(drybookCompleteJournalNote);

                List<OutboxMessage> events = new List<OutboxMessage>();
                events.Add(GenerateJobProgressEvent(request, jobProgress, userInfo.Username, correlationId));
                events.Add(GenerateJobCompleteEvent(request, request.CompletionDate, userInfo.Username, correlationId));
                events.Add(GenerateJournalNoteCreatedEvent(drybookCompleteJournalNote, userInfo, correlationId));
                _context.OutboxMessages.AddRange(events);

                await _context.SaveChangesAsync(cancellationToken);

                await _micaAutomationUtility.HandleMicaAutomationConditionallyAsync(job, 
                    MicaAutomationTrigger.DryingComplete, 
                    correlationId, 
                    false, 
                    cancellationToken);
                return Unit.Value;
            }

            private OutboxMessage GenerateJobCompleteEvent(Command request, in DateTime date, string userName, Guid correlationId)
            {
                var completedDto = new JobCompletedEvent.JobCompletedDto(request.JobId, date, userName);
                var completedEvent = new JobCompletedEvent(completedDto, correlationId);
                return new OutboxMessage(completedEvent.ToJson(), nameof(JobCompletedEvent),
                    correlationId, userName);
            }

            private OutboxMessage GenerateJobProgressEvent(Command request, JobProgress progress, string userName, Guid correlationId)
            {
                var jobUpdatedDto = new JobProgressUpdatedEvent.JobProgressUpdatedDto
                {
                    JobId = request.JobId,
                    JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)progress,
                    ModifiedBy = userName
                };
                var jobProgressEvent = new JobProgressUpdatedEvent(jobUpdatedDto, correlationId);
                return new OutboxMessage(jobProgressEvent.ToJson(), nameof(JobProgressUpdatedEvent), correlationId, nameof(JobProgressUpdatedEvent));
            }

            private OutboxMessage GenerateJournalNoteCreatedEvent(JournalNote journalNoteCreated, UserInfo userInfo, Guid correlationId)
            {
                var journalNoteCreatedDto = MapJournalNoteCreatedDto(journalNoteCreated, userInfo);
                var journalNoteCreatedEvent = new JournalNoteCreatedEvent(journalNoteCreatedDto, correlationId);
                return new OutboxMessage(journalNoteCreatedEvent.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username);
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteCreatedDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id
                };
        }
    }
}
