@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.MonitoringModelDto

@if (Model != null && (Model.AtmosphericReadingsModel != null || Model.MoistureContentReadingsModel != null))
{
    <div class="floatLeft">
        <p class="title">{monitoring}Monitoring{/monitoring}</p>

        <span class="keys-title">Key</span>
        <div>
            <table class="keys-table" role="presentation">
                <thead>
                </thead>
                <tr>
                    <td class="blue-cell leftColumn">Blue</td>
                    <td class="rightColumn"> = GPP Equal To OR Above 65 on Day 2</td>
                </tr>
                <tr>
                    <td class="blue-cell leftColumn">Blue</td>
                    <td class="rightColumn"> = RH Equal To OR Above 40 on Day 2</td>
                </tr>
                <tr>
                    <td class="gray-cell leftColumn">Gray</td>
                    <td class="rightColumn"> = GPP Not Making Progress(Not Decreasing Daily)</td>
                </tr>
                <tr>
                    <td class="purple-cell leftColumn">Purple</td>
                    <td class="rightColumn"> = Zone Temp Outside Optimal Range for Equip (68 to 90)</td>
                </tr>
                <tr>
                    <td class="red-cell leftColumn">Red</td>
                    <td class="rightColumn"> = Negative Grains Depression</td>
                </tr>
                <tr>
                    <td class="yellow-cell leftColumn">Yellow</td>
                    <td class="rightColumn"> = Low or Zero Grains Depression</td>
                </tr>
            </table>
        </div>
    </div>
    @await Html.PartialAsync("~/Features/Jobs/Drybook/Report/ReportViews/_atmosphericReadingsSubSubReport.cshtml", Model.AtmosphericReadingsModel)
    @await Html.PartialAsync("~/Features/Jobs/Drybook/Report/ReportViews/_moistureContentReadingsSubReport.cshtml", Model.MoistureContentReadingsModel)
    <div style='page-break-after: avoid;'></div>
}