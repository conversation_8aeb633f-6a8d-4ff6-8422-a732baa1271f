﻿using System;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Servpro.Franchise.JobService.Features.Forms
{
    [Route("api/forms")]
    public class FormsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<FormsController> _logger;

        public FormsController(
            IMediator mediator,
            ILogger<FormsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("queries/get-form-templates")]
        public async Task<ActionResult<GetForms.Dto>> GetFormsAsync([FromQuery] GetForms.Query query)
        {
            _logger.LogDebug("Getting form templates since: {modifiedSince}", query.ModifiedSince);
            var forms = await _mediator.Send(query);
            return Ok(forms);
        }

        [HttpGet("{formId}/queries/get-storage-info")]
        public async Task<ActionResult<GetForm.Dto>> GetFormAsync(Guid formId)
        {
            if (formId == Guid.Empty)
                return BadRequest();

            _logger.LogDebug("Getting form information");
            var form = await _mediator.Send(new GetForm.Query { FormId = formId });
            return Ok(form);
        }
    }
}
