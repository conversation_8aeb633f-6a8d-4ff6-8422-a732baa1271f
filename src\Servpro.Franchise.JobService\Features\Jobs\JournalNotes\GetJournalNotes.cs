﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class GetJournalNotes
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.FranchiseSetId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? JobId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid CategoryId { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public DateTime? ActionDate { get; set; }
            public DateTime CreatedDate { get; set; }
            public bool? IncludeInSummaryCoverPage { get; set; }
            public List<int> RuleIds { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext db) => _db = db;

            public async Task<IEnumerable<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.AsNoTracking()
                    .Include(j => j.JournalNotes)
                    .Where(j => j.Id == request.JobId && j.FranchiseSetId == request.FranchiseSetId)
                    .FirstOrDefaultAsync(cancellationToken: cancellationToken);
                return job?.JournalNotes?.Where(n => !n.IsDeleted).Select(Map).OrderByDescending(a => a.ActionDate);
            }

            private static Dto Map(JournalNote journalNote)
                => new Dto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Note = journalNote.Note,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    Subject = journalNote.Subject,
                    ActionDate = journalNote.ActionDate,
                    VisibilityId = journalNote.VisibilityId,
                    IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage,
                    RuleIds = journalNote.RuleIds?.ToList()
                };
        }
    }
}