﻿using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.RawSqlModels;
using Servpro.Franchise.LookupService.Constants;
using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Common.MergeCandidates
{
    public static class Common
    {
        private static readonly HashSet<JobProgress> InvalidCandidateMergeStatuses = new HashSet<JobProgress>
        { JobProgress.OnHold, JobProgress.NotSoldCancelled, JobProgress.TurnedDown, JobProgress.Closed };

        private static readonly HashSet<JobProgress> InvalidSourceMergeStatuses = new HashSet<JobProgress>
        { JobProgress.OnHold, JobProgress.NotSoldCancelled, JobProgress.TurnedDown, JobProgress.Closed };

        /// <summary>
        /// Returns true if the if the candidate job can be merged into the target job.
        /// <para>1 - Candidate and Target must be different jobs</para>
        /// <para>2 - Candidate must not be in the following statuses 
        /// (<see cref="JobProgress.TurnedDown"/>, <see cref="JobProgress.NotSoldCancelled"/>, <see cref="JobProgress.TurnedDown"/>)</para>
        /// <para>3 - Target/Candidate job must not be in <see cref="JobProgress.Closed"/></para>
        /// <para>4 - If target is a local job then the candidate must be local as well.
        ///           Both job's losstype need to be of type water/mold</para>
        /// <para>5 - if the target is a corporate job then the candidate must be a local job</para>
        /// </summary>
        /// <param name="target"></param>
        /// <param name="candidate"></param>
        /// <returns>True if it is a merge candidate, otherwise false</returns>
        public static bool IsMergeCandidate(WipRecord target, MergeCandidate candidate)
        {
            if (target.Id == candidate.Id)
                return false;
            if (InvalidCandidateMergeStatuses.Contains(candidate.JobProgress))
                return false;
            if (InvalidSourceMergeStatuses.Contains(target.JobProgress))
                return false;
            if (WasPreviouslyMerged(target))
                return false;
            if (!IsLocalJob(candidate))
                return false;
            if (IsSelfPay(target) || IsSelfPay(candidate) || target.InsuranceCompanyName == candidate.InsuranceCarrierId)
                return true;
            return false;
        }

        private static bool IsSelfPay(WipRecord target)
            => target.InsuranceCompanyName == InsuranceCarrierTypes.SelfPay;

        private static bool IsSelfPay(MergeCandidate candidate)
            => candidate.InsuranceCarrierId == InsuranceCarrierTypes.SelfPay;

        /// <summary>
        /// Returns true if job is from call center, otherwise returns false.
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        private static bool IsCorporateJob(WipRecord record)
            => record.LeadSource == RecordSourceTypes.CallCenter;

        /// <summary>
        /// Returns true if job's loss type is either water or mold, otherwise returns false.
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        private static bool IsWaterMoldJob(WipRecord record)
            => IsWaterOrMold(record.JobTypeName);

        /// <summary>
        /// Returns true if job's loss type is either water or mold, otherwise returns false.
        /// </summary>
        /// <param name="candidate"></param>
        /// <returns></returns>
        private static bool IsWaterMoldJob(MergeCandidate candidate)
            => IsWaterOrMold(candidate.LossTypeId);

        private static bool IsWaterOrMold(Guid lossTypeId)
            => WaterLossTypes.List.Contains(lossTypeId);

        /// <summary>
        /// Returns true if job is from mobile application or web application, otherwise returns false.
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        private static bool IsLocalJob(WipRecord record)
            => record.LeadSource == RecordSourceTypes.WorkCenter
            || record.LeadSource == RecordSourceTypes.DrybookMobile
            || record.LeadSource == RecordSourceTypes.FirstNotice;

        /// <summary>
        /// Returns true if job is from mobile application or web application, otherwise returns false.
        /// </summary>
        /// <param name="candidate"></param>
        /// <returns></returns>
        private static bool IsLocalJob(MergeCandidate candidate)
            => candidate.RecordSourceId == RecordSourceTypes.WorkCenter
            || candidate.RecordSourceId == RecordSourceTypes.DrybookMobile
            || candidate.RecordSourceId == RecordSources.FirstNotice;

        /// <summary>
        /// Returns true if job has been previously in a merge either as source or target job; otherwise returns false.
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        private static bool WasPreviouslyMerged(WipRecord record)
            => !string.IsNullOrEmpty(record.MergeSource) || !string.IsNullOrEmpty(record.MergeTarget);

    }
}
