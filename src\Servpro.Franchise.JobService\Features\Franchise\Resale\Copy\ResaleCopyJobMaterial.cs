﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobMaterial
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobMaterial>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobMaterial> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobMaterial> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobMaterial));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var materialTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedMaterialIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(materialTargetIds, 
                    GetJobMaterialIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobMaterial, ResaleJobMaterial>(
                    request.ResaleId,
                    material =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(material.JobId))
                            failedDependencies.Add((nameof(Job), material.JobId));

                        return failedDependencies;
                    },
                    alreadyCopiedMaterialIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobMaterials.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobMaterial>> GetSourceEntitiesAsync(List<Guid> jobIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobMaterials = await _context.JobMaterials
                    .Where(jm => jobIds.Contains(jm.JobId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobMaterials.Count);
                return jobMaterials;
            }

            private async Task<List<Guid>> GetJobMaterialIdsAsync(List<Guid?> materialTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobMaterials
                    .Where(x => materialTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
