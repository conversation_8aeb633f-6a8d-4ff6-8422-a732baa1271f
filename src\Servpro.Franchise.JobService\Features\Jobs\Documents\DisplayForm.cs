﻿using AutoMapper;

using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class DisplayForm
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid FormTemplateId { get; set; }
        }

        public class Dto
        {
            public MemoryStream Stream { get; set; }
            public string FileType { get; set; }
            public string FileName { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.FormTemplateId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IFormsService _formService;
            private readonly ILogger<Handler> _logger;

            public Handler(JobReadOnlyDataContext db, IFranchiseServiceClient franchiseServiceClient, IFormsService formService, ILogger<Handler> logger)
            {
                _db = db;
                _franchiseServiceClient = franchiseServiceClient;
                _formService = formService;
                _logger = logger;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogDebug("{DisplayForm}: Begin handler with: { @request}", nameof(DisplayForm), request);

                try
                {
                    var job = await GetJobAsync(request.JobId, request.FranchiseSetId, cancellationToken);
                    var formTemplate = await GetFormTemplateAsync(request.FormTemplateId, cancellationToken);
                    if (job == null || formTemplate == null)
                    {
                        return null;
                    }
                    var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, cancellationToken);
                    var franchise = franchiseSet.Franchises.FirstOrDefault(x => x.Id == job.FranchiseId);

                    var stream = await GetFormDataAsync(job, franchise, franchiseSet, formTemplate, cancellationToken);
                    var dto = new Dto
                    {
                        Stream = stream,
                        FileType = MimeTypesDictionary.ContainsKey(formTemplate.FileType) ? MimeTypesDictionary[formTemplate.FileType] : "application/pdf",
                        FileName = $"{formTemplate.Name}.{formTemplate.FileType}"
                    };

                    return dto;
                }
                catch (Exception e)
                {
                    _logger.LogError("Error retrieving forms. {errorMessage}. {@error}", e.Message, e);
                    throw;
                }
            }

            private async Task<MemoryStream> GetFormDataAsync(Job job, FranchiseDto franchise, GetFranchiseSetDto franchiseSet, FormTemplate formTemplate, CancellationToken cancellationToken)
            {
                var mergedFormRequest = new MergeFormsRequest(job, franchise, franchiseSet);
                mergedFormRequest.FormTemplates.Add(formTemplate);
                var formData = await _formService.GetMergedFormsAsync(mergedFormRequest, cancellationToken);

                return formData;
            }


            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.FirstOrDefaultAsync(j => j.Id == jobId && j.FranchiseSetId == franchiseSetId, cancellationToken);
                job.MediaMetadata = await _db.MediaMetadata.Include(mm => mm.JobInvoice).Where(mm => mm.JobId == jobId).ToListAsync(cancellationToken);
                job.JobContacts = await _db.JobContactMap.Include(jc => jc.Contact).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);
                job.JobAreas = await _db.JobAreas.Include(x => x.JobAreaMaterials).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);
                return job;
            }

            private async Task<FranchiseDto> GetFranchiseAsync(Guid franchiseId)
            {
                return await _franchiseServiceClient.GetFranchiseAsync(franchiseId);
            }

            private async Task<FormTemplate> GetFormTemplateAsync(Guid formTemplateId, CancellationToken cancellationToken)
            {
                var formTemplates = await _db.FormTemplates.FirstOrDefaultAsync(x => x.Id == formTemplateId, cancellationToken);
                return formTemplates;
            }

            private static readonly Dictionary<string, string> MimeTypesDictionary =
                new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    {"jpeg", "image/jpeg"},
                    {"jpe", "image/jpeg"},
                    {"jpg", "image/jpg"},
                    {"png", "image/png"},
                    {"gif", "image/gif"},
                    {"pdf", "application/pdf"},
                    {"ppt", "application/vnd.ms-powerpoint"},
                    {"pptm", "application/vnd.ms-powerpoint.presentation.macroenabled.12"},
                    {"pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
                    {"xls", "application/vnd.ms-excel"},
                    {"xlsb", "application/vnd.ms-excel.sheet.binary.macroenabled.12"},
                    {"xlsm", "application/vnd.ms-excel.sheet.macroenabled.12"},
                    {"xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
                    {"doc", "application/msword"},
                    {"docm", "application/vnd.ms-word.document.macroenabled.12"},
                    //docx should not be attempted to be shown as docx files, instead that need to show as pdf
                    {"docx", "application/pdf"},
                };
        }
    }
}