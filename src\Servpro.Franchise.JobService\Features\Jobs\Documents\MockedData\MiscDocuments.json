﻿[
  {
    "Id": 72250138,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-13T01:38:43.104",
    "Comments": "Scope Document",
    "Name": "Scope Sheet 2020-03-12 No Notes.pdf",
    "ArtifactTypeId": "d30dbcd3-aa1f-48bb-ac8a-897b03f5dbd4",
    "MediaId": "50960d6e-87e0-45c2-ab9f-27b46be8cae1",
    "InsertionDate": "2020-03-13T01:41:22.447Z",
    "LastModified": "2020-03-13T01:41:36.367",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": null,
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Scoping Document",
    "IsForUpload": false,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 72250143,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-13T01:38:43.085",
    "Comments": "Scope Document",
    "Name": "Scope Sheet 2020-03-12.pdf",
    "ArtifactTypeId": "d30dbcd3-aa1f-48bb-ac8a-897b03f5dbd4",
    "MediaId": "cf8a9e57-8335-4b9a-a5f4-ee791a73d164",
    "InsertionDate": "2020-03-13T01:41:22.43Z",
    "LastModified": "2020-03-13T01:41:36.147",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": null,
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Scoping Document",
    "IsForUpload": false,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 72250145,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-13T01:40:29.171",
    "Comments": "Material Readings Document",
    "Name": "Visit 1 Material Readings 20200312.pdf",
    "ArtifactTypeId": "d30dbcd3-aa1f-48bb-ac8a-897b03f5dbd4",
    "MediaId": "1bbc3289-b210-453b-bb44-49169a245567",
    "InsertionDate": "2020-03-13T01:41:22.46Z",
    "LastModified": "2020-03-13T01:41:37.367",
    "JobAreaId": null,
    "JobVisitId": "4679d474-d54a-4e65-afc9-b5eab9f408ad",
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": "2020-03-13T01:33:00",
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Scoping Document",
    "IsForUpload": false,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 72250146,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-13T01:40:29.162",
    "Comments": "Atmospheric Readings Document",
    "Name": "Visit 1 Atmospheric Readings 20200312.pdf",
    "ArtifactTypeId": "d30dbcd3-aa1f-48bb-ac8a-897b03f5dbd4",
    "MediaId": "d463c034-2cc5-4cdf-9f52-1cee29f06870",
    "InsertionDate": "2020-03-13T01:41:22.46Z",
    "LastModified": "2020-03-13T01:41:37.147",
    "JobAreaId": null,
    "JobVisitId": "4679d474-d54a-4e65-afc9-b5eab9f408ad",
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": "2020-03-13T01:33:00",
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Scoping Document",
    "IsForUpload": false,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 72250170,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-13T01:43:04.0737617",
    "Comments": "",
    "Name": "DryingReport_Mar-12-2020.pdf",
    "ArtifactTypeId": "2596e17e-55cf-43ca-8df8-7862a3ff7bd9",
    "MediaId": "652c5239-1718-49e8-9aff-7894449ae07a",
    "InsertionDate": "2020-03-13T01:43:04.073Z",
    "LastModified": "2020-03-23T19:53:19.887",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": "",
    "ZoneName": "",
    "VisitDate": "2020-03-13T01:43:04.073",
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Drying Report",
    "IsForUpload": false,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 72813489,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-23T19:53:19.9411042",
    "Comments": "",
    "Name": "DryingReport_Mar-23-2020.pdf",
    "ArtifactTypeId": "2596e17e-55cf-43ca-8df8-7862a3ff7bd9",
    "MediaId": "68a1e110-9f6a-4122-bebd-0312c6046faa",
    "InsertionDate": "2020-03-23T19:53:19.94Z",
    "LastModified": "2020-03-23T19:53:19.94",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": "",
    "ZoneName": "",
    "VisitDate": "2020-03-23T19:53:19.94",
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Drying Report",
    "IsForUpload": true,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 73005702,
    "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
    "ArtifactDate": "2020-03-26T12:53:50.4620331",
    "Comments": "",
    "Name": "MVC Sonarqube.png",
    "ArtifactTypeId": "82de5a26-9555-49a1-a7a4-b8a3b92d3144",
    "MediaId": "0450a6d5-a8cd-4e80-8e66-e83210df6adf",
    "InsertionDate": "2020-03-26T12:53:50.463Z",
    "LastModified": "2020-03-26T12:53:50.463",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": "",
    "ZoneName": "",
    "VisitDate": "2020-03-26T12:53:50.463",
    "DocumentCategory": "Required: Miscellaneous Documents",
    "DocumentType": "Invoice",
    "IsForUpload": true,
    "IsRequired": false,
    "JobInvoice": {
      "JobId": "A3BFF37F-A97A-4160-AA83-70ACB7CD26D1",
      "JobInvoiceId": 1188130,
      "JobInvoiceNumber": 123,
      "QuickBooksClassId": null,
      "QuickBooksTermId": 1,
      "Id": null,
      "ServiceTypeId": 1,
      "QuickBooksId": null,
      "InvoiceDate": "2020-03-26T00:00:00",
      "JobBillingInformationId": 1,
      "InvoiceMemo": "MVC Sonarqube.png",
      "InvoiceComment": null,
      "InvoiceVoidReasonId": null,
      "SubmittedDate": null,
      "IsSubmitted": false,
      "IsProcessed": false,
      "ItemDesc": "MVC Sonarqube.png",
      "CreatedDate": "2020-03-26T12:53:50.49",
      "Amount": "99.00",
      "BalanceAmount": "",
      "PaymentApplied": 0.0,
      "Comments": null,
      "JobInvoiceLineItems": [
        {
          "JobInvoiceLineItemId": 1390563,
          "QuickBooksItemId": null,
          "Amount": "99.00",
          "Tax": "9.00",
          "Description": "From miscellaneous document upload",
          "IsTaxApplicable": true,
          "ItemTypes": null
        }
      ],
      "SaleTaxId": null,
      "SalesTaxPercent": 0.0,
      "InvoiceAmount": null,
      "JobInvoicePaymentId": 0
    },
    "IsXactUploaded": false
  },
  {
    "Id": 0,
    "JobId": "00000000-0000-0000-0000-000000000000",
    "ArtifactDate": null,
    "Comments": null,
    "Name": "",
    "ArtifactTypeId": "16ddb670-aa6b-4247-8622-459c7f2d781f",
    "MediaId": "00000000-0000-0000-0000-000000000000",
    "InsertionDate": "0001-01-01T00:00:00",
    "LastModified": "0001-01-01T00:00:00",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": null,
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "External Document",
    "IsForUpload": false,
    "IsRequired": false,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 0,
    "JobId": "00000000-0000-0000-0000-000000000000",
    "ArtifactDate": null,
    "Comments": null,
    "Name": "",
    "ArtifactTypeId": "bf2082f3-310f-4a81-8fa9-89b5679b3d32",
    "MediaId": "00000000-0000-0000-0000-000000000000",
    "InsertionDate": "0001-01-01T00:00:00",
    "LastModified": "0001-01-01T00:00:00",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": null,
    "DocumentCategory": "Required: Miscellaneous Documents",
    "DocumentType": "Xactimate Estimate",
    "IsForUpload": true,
    "IsRequired": true,
    "JobInvoice": null,
    "IsXactUploaded": false
  },
  {
    "Id": 0,
    "JobId": "00000000-0000-0000-0000-000000000000",
    "ArtifactDate": null,
    "Comments": null,
    "Name": "",
    "ArtifactTypeId": "39cfbdba-04d2-4988-bb64-6313a37f2b19",
    "MediaId": "00000000-0000-0000-0000-000000000000",
    "InsertionDate": "0001-01-01T00:00:00",
    "LastModified": "0001-01-01T00:00:00",
    "JobAreaId": null,
    "JobVisitId": null,
    "RoomName": null,
    "ZoneName": null,
    "VisitDate": null,
    "DocumentCategory": "Other: Miscellaneous Documents",
    "DocumentType": "Subcontract Invoice",
    "IsForUpload": true,
    "IsRequired": true,
    "JobInvoice": null,
    "IsXactUploaded": false
  }
]