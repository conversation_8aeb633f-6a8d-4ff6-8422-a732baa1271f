# Franchise Job Service

## Display Form Functionality Research
[Jira Ticket](https://jira.servpronet.com/jira/browse/WC3-1949)

## Purpose

Displays the requested form based on the specified `FormTemplateId` and `Job`.

### How to execute the functionality

User clicks the `Display` button on the `Documentation` > `Forms` tab in `WorkCenter`

## Current Implementation

### Summary

`WorkCenter` makes a request to `EGv19` service's `CreateRequestedForms` WCF endpoint. `EGv19` retrieves the `FormTemplates` based on the request. It iterates through the list of templates, setting the form template's fields based on the specified `Job`, and then rendering the form using `Aspose.Pdf.Document`.

##### WorkCenter / EGv19 Interaction Diagram

[Edit](https://www.lucidchart.com/invitations/accept/e7999c71-aab5-400e-b093-1614909de328)

![WC/EGv19 Diagram](https://www.lucidchart.com/publicSegments/view/870ffb3e-f374-46ec-aca7-75b7c5b0f71e/image.png)


## Proposed Implementation

### Summary

`WorkCenter MVC` will make a request to the `Job Service` via the `API Gateway`. `Job Service` will retrieve the specified form templates from its own `FormTemplate` table. The code will iterate through the list of form templates; a request for the media will be downloaded from the `AWS S3` bucket. The `Aspose.Pdf.Document` component will be used to merge and render the forms. The results will be returned to the `WorkCenter MVC` to be displayed in a new browser tab.

#### Note: The `FormTemplates` table will be filled with data using an import process based on received messages published from the `Sharepoint Connector Service`.

##### WorkCenter / Job Service Interaction Diagram

[Edit](https://www.lucidchart.com/invitations/accept/e7999c71-aab5-400e-b093-1614909de328)

![WC/Job Service Diagram](https://www.lucidchart.com/publicSegments/view/71081655-ed8e-4150-ace5-5326ec113f66/image.png)

