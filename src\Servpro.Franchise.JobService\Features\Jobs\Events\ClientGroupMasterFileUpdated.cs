﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.CorporateService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Client;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ClientGroupMasterFileUpdated
    {
        #region Event
        public class Event : ClientGroupMasterFileUpdatedEvent, IRequest
        {
            public Event(ClientGroupMasterFileUpdatedDto client, Guid correlationId) : base(client, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private const string ClientGroupTypeIdKey = "ClientGroupTypeId";
            private const int DefaultClientGroupTypeId = 14;
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly ICorporateServiceClient _corporateServiceClient;
            private readonly IConfiguration _config;

            public Handler(JobDataContext db,
                ILogger<Handler> logger,
                ICorporateServiceClient corporateServiceClient,
                IConfiguration config)
            {
                _db = db;
                _logger = logger;
                _corporateServiceClient = corporateServiceClient;
                _config = config;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Feature {feature} invoked", nameof(ClientGroupMasterFileUpdatedEvent));

                var clientGroupTypeId = _config.GetValue(ClientGroupTypeIdKey, DefaultClientGroupTypeId);
                var clientGroupMasterFileRecords = (await _corporateServiceClient.GetClientGroupMasterFileRecords(cancellationToken))
                    .Where(cg => cg.IsActive && cg.GroupTypeId == clientGroupTypeId)
                    .ToList();

                foreach (var clientGroupMasterFileRecord in clientGroupMasterFileRecords)
                {
                    var insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(ic => ic.InsuranceNumber == clientGroupMasterFileRecord.MemberId, cancellationToken);

                    if (insuranceClient == null)
                    {
                        insuranceClient = GetInsuranceFromMemoryDB(null, clientGroupMasterFileRecord.MemberId);
                    }

                    if (insuranceClient == null)
                    {
                        insuranceClient = new InsuranceClient
                        {
                            Id = Guid.NewGuid(),
                            Name = "Placeholder Corporate Insurance",
                            InsuranceNumber = clientGroupMasterFileRecord.MemberId,
                            ParentInsuranceNumber = clientGroupMasterFileRecord.ParentId,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow
                        };

                        await _db.InsuranceClients.AddAsync(insuranceClient, cancellationToken);
                        _logger.LogWarning("{event}: Inserting Placeholder Insurance Client for InsuranceNumber {insuranceNumber}, {@insuranceClient}", nameof(ClientGroupMasterFileUpdatedEvent), clientGroupMasterFileRecord.MemberId, insuranceClient);
                    }
                    else
                    {
                        insuranceClient.ParentInsuranceNumber = clientGroupMasterFileRecord.ParentId;
                    }
                }

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{feature} completed - processed {count} records", nameof(ClientGroupMasterFileUpdatedEvent), clientGroupMasterFileRecords?.Count());

                return Unit.Value;
            }

            private InsuranceClient GetInsuranceFromMemoryDB(Guid? insuranceId, int? insuranceNumber) //Check in memory changes in case a client exists more than one time before saving changes to DB.
            {
                if (!insuranceId.HasValue && !insuranceNumber.HasValue)
                {
                    return null;
                }

                return _db.ChangeTracker.Entries()
                                   .Where(x => x.State == EntityState.Added && x.Entity is InsuranceClient)
                                   .Select(x => x.Entity as InsuranceClient)
                                   .FirstOrDefault(ic => ic.Id == (insuranceId ?? Guid.Empty) || ic.InsuranceNumber == (insuranceNumber ?? -1));
            }
        }
        #endregion Handler
    }
}
