﻿using System.Collections.Generic;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public class CreateFormPackageRequest
    {
        public CreateFormPackageRequest(Job job, FranchiseDto franchise, List<FormTemplate> formTemplates, GetLookups.Dto lookups, List<FormFieldDto> formFields, string timeZone)
        {
            Job = job;
            Franchise = franchise;
            FormTemplates = formTemplates;
            Lookups = lookups;
            FormFields = formFields;
            TimeZone = timeZone;
        }

        public List<FormTemplate> FormTemplates { get; set; }
        public Job Job { get; set; }
        public FranchiseDto Franchise { get; set; }
        public GetLookups.Dto Lookups { get; set; }
        public List<FormFieldDto> FormFields { get; set; }
        public string TimeZone { get; }
    }
}