﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Setup.Caching;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Client
{
    public class GetInsuranceClients
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {

        }

        public class Dto
        {
            public Guid Id { get; set;  }
            public string Name { get; set; }
            public int InsuranceNumber { get; set; }
            public bool IsActive { get; set; }
            public bool IsLocalAuditRequired { get; set; }
            public bool IsAuditRequired { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IDistributedCache _cache;
            private readonly IConfiguration _config;
            private const string insuranceKey = "GetInsuranceClients";

            public Handler(JobReadOnlyDataContext context,
                IDistributedCache cache,
                IConfiguration config)
            {
                _context = context;
                _cache = cache;
                _config = config;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var cacheExpiration = _config.GetValue("caching:GetInsuranceExpirationInHours", 6);
                var cachedInsuranceClients = await _cache.GetOrCreateAsync(insuranceKey, async options =>
                {
                    options.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(cacheExpiration);
                    return await GetInsuranceClients(cancellationToken);
                }, cancellationToken: cancellationToken);
                return cachedInsuranceClients;
                
            }

            private async Task<IEnumerable<Dto>> GetInsuranceClients(CancellationToken cancellationToken)
            {
                var insuranceClients = await _context.InsuranceClients
                    .AsNoTracking()
                    .ToListAsync(cancellationToken: cancellationToken);
                return insuranceClients.Select(Map).ToList();
            }

            private Dto Map(InsuranceClient insuranceClient)
                => new Dto
                {
                    Id = insuranceClient.Id,
                    Name = insuranceClient.Name,
                    InsuranceNumber = insuranceClient.InsuranceNumber,
                    IsActive = insuranceClient.IsActive,
                    IsLocalAuditRequired = insuranceClient.IsLocalAuditRequired,
                    IsAuditRequired = insuranceClient.IsAuditRequired
                };

        }
    }
}