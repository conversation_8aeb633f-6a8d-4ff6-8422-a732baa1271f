﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobAreas
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult RoomResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobAreaIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobAreas>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobAreas> _logger;
            private readonly IMapper _mapper;
            private readonly JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyJobAreas> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _context = context;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(JobArea));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobAreaIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var areaTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedAreaIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(areaTargetIds, 
                    GetJobAreaIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobArea, ResaleJobArea>(
                    request.ResaleId,
                    area =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(area.JobId))
                            failedDependencies.Add((nameof(Job), area.JobId));
                        if (area.RoomId.HasValue && request.RoomResult.FailedEntities.Contains(area.RoomId.Value))
                            failedDependencies.Add((nameof(Room), area.RoomId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedAreaIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobAreas.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobArea>> GetSourceEntitiesAsync(List<Guid> jobAreaIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobAreas = await _context.JobAreas
                    .Where(j => jobAreaIds.Contains(j.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobAreas.Count);
                return jobAreas;
            }

            private async Task<List<Guid>> GetJobAreaIdsAsync(List<Guid?> areaTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobAreas
                    .Where(x => areaTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
