﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NuGet.Protocol;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class SaveExportedMarketingCalls
    {
        public class Command : IRequest<Unit>
        {
           public List<CallsToSave> Calls { get; set; }          
        }

        public class CallsToSave
        {
            public Guid CallId { get; set; }
            public DateTime LastExported { get; set; }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<SaveExportedMarketingCalls> _logger;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;


            public Handler(ILogger<SaveExportedMarketingCalls> logger,                
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfoAccessor,
                JobDataContext db)
            {
                _logger = logger;
                _lookupServiceClient = lookupServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _db = db;               
            }

            public async Task<Unit> Handle( Command request, CancellationToken cancellationToken)
            {
                if (request.Calls.Count== 0)
                    return Unit.Value;

                _logger.LogInformation("Saving Exported Calls");
                var user = _userInfoAccessor.GetUserInfo();

                foreach (var call in request.Calls)
                {
                    await UpdateCallAsync(call, cancellationToken);
                }
                                  
                await _db.SaveChangesAsync(cancellationToken);
                
                return Unit.Value;
            }

            private async Task UpdateCallAsync(CallsToSave call, CancellationToken cancellationToken)
            {
                if (call!= null)
                {
                    var dbCall = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => x.Id == call.CallId, cancellationToken);
                    if (dbCall != null)                    
                        dbCall.LastExported = call.LastExported;  
                }              
            }
        }
    }
}
