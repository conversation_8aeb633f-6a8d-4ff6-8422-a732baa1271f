﻿using Servpro.Franchise.JobService.Models;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class ActiveJobProgresses
    {
        public static HashSet<JobProgress> List { get; } = new HashSet<JobProgress>()
            {
                JobProgress.InitialSiteInspection,
                JobProgress.CustomerConsultation,
                JobProgress.InitialServices,
                JobProgress.ScopePlan,
                JobProgress.ComplianceEstimate,
                JobProgress.Approvals,
                JobProgress.ProjectSetup,
                JobProgress.ProductionWorkCycle,
                JobProgress.FinalWalkThrough
            };

    }
}
