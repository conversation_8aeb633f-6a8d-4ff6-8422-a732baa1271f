﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobInvoiceCreated
    {
        public class Event : InvoiceCreatedEvent, IRequest
        {
            public Event(InvoiceCreatedDto invoiceCreated, Guid correlationId) : base(invoiceCreated, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<JobInvoiceCreated> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<JobInvoiceCreated> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler began with {@incomingEvent}", incomingEvent);

                var incomingInvoice = incomingEvent.InvoiceCreated;
                var incomingType = incomingInvoice.EventType;

                //Get job
                var job = await _context.Jobs
                    .Include(m => m.MediaMetadata)
                    .ThenInclude(j => j.JobInvoice)
                    .FirstOrDefaultAsync(q => q.Id == incomingInvoice.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {incomingInvoice.JobId}");

                var invoiceId = await InvoiceUpsert(incomingInvoice, incomingType, job);

                try
                {
                    await _context.SaveChangesAsync(cancellationToken);
                }
                catch (DbUpdateException ex)
                {
                    _logger.LogWarning(ex.Message);
                }

                _logger.LogInformation("Job {jobId} updated with the creation of invoice {invoiceId} ", job.Id, invoiceId);

                return Unit.Value;
            }

            private async Task<Guid> InvoiceUpsert(InvoiceCreatedEvent.InvoiceCreatedDto incomingInvoice, InvoiceEventType incomingType, Job job)
            {
                var invoiceMedia = job.MediaMetadata?
                        .FirstOrDefault(q => q.JobId == incomingInvoice.JobId
                                            && q.JobInvoice?.Id == incomingInvoice.InvoiceId);

                if (invoiceMedia != null)
                {
                    var invoiceInfo = invoiceMedia.JobInvoice;
                    invoiceInfo.Description = incomingInvoice.Description ?? string.Empty;
                    invoiceInfo.InvoiceNumber = incomingInvoice.InvoiceNumber ?? string.Empty;
                    invoiceInfo.Source = incomingInvoice.Source ?? string.Empty;
                    invoiceInfo.Date = incomingInvoice.Date;
                    if (incomingType == InvoiceEventType.Invoice)
                    {
                        invoiceInfo.Amount = incomingInvoice.Amount;
                    }
                    if (incomingType == InvoiceEventType.Collected)
                    {
                        invoiceInfo.AmountCollected += incomingInvoice.Amount;
                    }
                    return invoiceMedia.Id;
                }

                var media = GenerateMediaMetadata(incomingInvoice);
                media.FranchiseSetId = job.FranchiseSetId;
                job.MediaMetadata?.Add(media);
                return new Guid(media.JobInvoiceId.ToString());
            }

            private MediaMetadata GenerateMediaMetadata(InvoiceCreatedEvent.InvoiceCreatedDto eventInvoice)
            {
                var invoice = GenerateInvoice(eventInvoice);
                var mediaMetadata = new MediaMetadata
                {
                    Id = Guid.NewGuid(),
                    JobId = eventInvoice.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    IsForUpload = false,
                    Name = invoice.Description,
                    MediaTypeId = MediaTypes.Document,
                    ArtifactTypeId = ArtifactTypes.Invoice,
                    MediaPath = string.Empty,
                    JobInvoice = invoice,
                    JobInvoiceId = invoice?.Id
                };
                return mediaMetadata;
            }

            private JobInvoice GenerateInvoice(InvoiceCreatedEvent.InvoiceCreatedDto invoice)
            {
                return new JobInvoice()
                {
                    Id = invoice.InvoiceId,
                    InvoiceNumber = invoice.InvoiceNumber ?? string.Empty,
                    Description = invoice.Description ?? string.Empty,
                    Source = invoice.Source ?? string.Empty,
                    Date = invoice.Date,
                    Amount = invoice.Amount
                };
            }
        }
    }
}
