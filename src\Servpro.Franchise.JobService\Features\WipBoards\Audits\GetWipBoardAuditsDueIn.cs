﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.WipBoards.Customization;
using Servpro.Franchise.JobService.Features.WipBoards.ScheduleService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Customization.Dtos;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.WipBoards.Audits
{
    public class GetWipBoardAuditsDueIn
    {
        public class Query : IRequest<List<WipRecord>>
        {
            public Guid FranchiseSetId { get; set; }
            public int DueInDays { get; set; }
            public DateTime? Date { get; set; }
            public ICollection<GridColumnDto> CustomColumns { get; internal set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.FranchiseSetId).NotEmpty();
            }
        }


        public class Handler : IRequestHandler<Query, List<WipRecord>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IScheduleService _scheduleService;

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                IScheduleService scheduleService)
            {
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _scheduleService = scheduleService;
            }

            public async Task<List<WipRecord>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                TimeZoneInfo timeZoneInfo = await _franchiseServiceClient.GetFranchiseSetPrimaryTimeZoneAsync(request.FranchiseSetId, cancellationToken: cancellationToken);
                DateTime requestDate = request.Date == null ? DateTime.UtcNow : request.Date.Value;

                //begin query is always the due in days minus 1 because we only want to take into
                // acount that day - not all days before it.
                DateTime franchiseBeginOfQuery = request.DueInDays <= 0 ?
                    await _scheduleService.LastCloseOfNormalDay(timeZoneInfo) :
                    await _scheduleService.NextCloseNormalDayInDays(timeZoneInfo, request.DueInDays - 1, requestDate);

                DateTime franchiseEndOfQuery = await _scheduleService.NextCloseNormalDayInDays(timeZoneInfo, request.DueInDays, requestDate);

                //This handles the case when the due date is on a weekend and we are wanting to check for monday
                // both begin and end times are pushed to monday but we should back track a day for begin time.
                franchiseBeginOfQuery = franchiseBeginOfQuery.AreEqual(franchiseEndOfQuery) ? franchiseBeginOfQuery.AddDays(-1) : franchiseBeginOfQuery;

                var recordsDueIn = await GetRecordsDueIn(franchiseBeginOfQuery, franchiseEndOfQuery, _context, 
                    request.FranchiseSetId, timeZoneInfo, cancellationToken);

                return recordsDueIn;
            }

            public static async Task<List<WipRecord>> GetRecordsDueIn(DateTime franchiseBeginOfQuery, DateTime franchiseEndOfQuery, JobDataContext context, 
                Guid franchiseSetId, TimeZoneInfo timeZoneInfo, CancellationToken cancellationToken)
            {
                // The counts are only going off of Final Upload
                // This means that Audit Due Records due in the past day are equal to Final Uploads Due counts
                var records = await context.WipRecords
                    .Where(r =>
                            r.FinalUploadDueDate.HasValue &&
                            r.FinalUploadDueDate >= franchiseBeginOfQuery &&
                            r.FinalUploadDueDate < franchiseEndOfQuery)
                    // Do not count jobs that are closed and an upload was completed
                    .Where(r => r.JobProgress != JobProgress.Closed)
                    // Do not include jobs that are cancelled
                    .Where(j => j.JobProgress != JobProgress.NotSoldCancelled)
                    .Where(j => j.FranchiseSetId == franchiseSetId)
                    .ToListAsync(cancellationToken);

                records = GetWipBoard.PerformWipRecordCalculations(records, timeZoneInfo);

                return records;
            }


        }
    }
}
