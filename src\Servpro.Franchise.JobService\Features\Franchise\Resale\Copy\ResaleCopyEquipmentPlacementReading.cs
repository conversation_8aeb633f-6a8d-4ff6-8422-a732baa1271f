﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyEquipmentPlacementReading
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult EquipmentPlacementResult { get; set; }
            public ProcessEntityResult JobVisitResult { get; set; }
            public ProcessEntityResult JobZoneResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> EquipmentPlacementIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyEquipmentPlacementReading>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyEquipmentPlacementReading> _logger;
            private readonly IMapper _mapper;
            private readonly JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyEquipmentPlacementReading> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _context = context;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(EquipmentPlacementReading));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.EquipmentPlacementIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var equipmentPlacementReadingTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedEquipmentPlacementReadingIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(equipmentPlacementReadingTargetIds, 
                    GetEquipmentPlacementReadingIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<EquipmentPlacementReading, ResaleEquipmentPlacementReading>(
                    request.ResaleId,
                    equipmentPlacementReading =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.EquipmentPlacementResult.FailedEntities.Contains(equipmentPlacementReading.EquipmentPlacementId))
                            failedDependencies.Add((nameof(EquipmentPlacement), equipmentPlacementReading.EquipmentPlacementId));
                        if (request.JobVisitResult.FailedEntities.Contains(equipmentPlacementReading.JobVisitId))
                            failedDependencies.Add((nameof(JobVisit), equipmentPlacementReading.JobVisitId));
                        if (request.JobZoneResult.FailedEntities.Contains(equipmentPlacementReading.ZoneId))
                            failedDependencies.Add((nameof(Zone), equipmentPlacementReading.ZoneId));

                        return failedDependencies;
                    },
                    alreadyCopiedEquipmentPlacementReadingIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.EquipmentPlacementReadings.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<EquipmentPlacementReading>> GetSourceEntitiesAsync(List<Guid> equipmentPlacementIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var equipmentPlacementReadings = await _context.EquipmentPlacementReadings
                    .Where(epr => equipmentPlacementIds.Contains(epr.EquipmentPlacementId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", equipmentPlacementReadings.Count);
                return equipmentPlacementReadings;
            }

            private async Task<List<Guid>> GetEquipmentPlacementReadingIdsAsync(List<Guid?> equipmentPlacementReadingTargetIds, 
                CancellationToken cancellationToken)
            {
                return await _context.EquipmentPlacementReadings
                    .Where(x => equipmentPlacementReadingTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
