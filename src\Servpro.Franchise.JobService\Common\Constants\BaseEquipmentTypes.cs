﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class BaseEquipmentTypes
    {
        public static readonly Guid AirMover = new Guid("F8509906-5063-42AE-92A3-45DD78682032");
        public static readonly Guid Dehumidifier = new Guid("3E8085A3-D764-4252-9A6F-7DB2521F360B");
        /// <summary>
        /// NOTE: Set via "AirScrubberCorporateBaseEquipmentTypeId" configuration.
        /// </summary>
        public static Guid AirScrubber => ConfigurableAirScrubber;
        /// <summary>
        /// NOTE: Set via "AirScrubberCorporateBaseEquipmentTypeId" configuration.
        /// This is used because the Id is different in each environment.
        /// </summary>
        /// <remarks>
        /// <list type="bullet">
        /// <item><term>Dev</term><description> 42DC6FBC-778C-438A-8B3D-B05D4A36E8B0</description></item>
        /// <item><term>QA</term><description> 5825FA82-A253-412E-A31B-F3C794D60846</description></item>
        /// <item><term>Prod</term><description> 6F4538B2-4F6D-4F53-9F9A-DE5C73BE19F0</description></item>
        /// </list>
        /// </remarks>
        public static Guid ConfigurableAirScrubber { get; set; }
    }
}
