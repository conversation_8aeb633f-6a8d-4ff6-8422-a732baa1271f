﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetEquipmentPlacementDetails
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Dto
        {
            public string RoomName { get; set; }
            public string ZoneName { get; set; }
            public decimal? CeilingAreaSqFt { get; set; }
            public decimal? CeilingPerimeterLnFt { get; set; }
            public decimal? FloorAreaSqFt { get; set; }
            public decimal? FloorPerimeterLnFt { get; set; }
            public decimal? VolumeCuFt { get; set; }
            public decimal? WallAreaSqFt { get; set; }
            public string AssetModelName { get; set; } // called "Asset Id" for some reason by the BAs
            public string EquipmentType { get; set; }
            public DateTime BeginDate { get; set; }
            public DateTime? EndDate { get; set; }
            public decimal TotalDays { get; set; }
            public decimal TotalHours { get; set; }

        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {

            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var areas = await _context.JobAreas
                    .Include(ja => ja.Room)
                    .Include(ja => ja.Zone)
                    .Include(ja => ja.EquipmentPlacements)
                        .ThenInclude(ep => ep.Equipment)
                        .ThenInclude(e => e.EquipmentModel)
                        .ThenInclude(em => em.EquipmentType)
                    .Where(ja => ja.JobId == request.JobId)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                if (!areas.Any())
                    return new List<Dto>();
                return Map(areas);
            }

            private IEnumerable<Dto> Map(IEnumerable<JobArea> areas)
            {
                const string notApply = "N/A";
                var list = new List<Dto>();

                foreach (var item in areas)
                {
                    var zone = item.Zone;
                    var room = item.Room ?? new Room();

                    foreach (var ep in item.EquipmentPlacements)
                    {
                        var equipmentType = ep.Equipment.EquipmentModel.EquipmentType;

                        var placementDetail = new Dto
                        {
                            RoomName = item.Name,
                            ZoneName = zone != null ? zone.Name : notApply,
                            AssetModelName = ep.Equipment.AssetNumber,
                            EquipmentType = equipmentType != null ? equipmentType.Name : notApply,
                            BeginDate = ep.BeginDate,
                            EndDate = ep.EndDate,
                            TotalDays = GetTotalDays(ep.BeginDate, ep.EndDate),
                            TotalHours = GetTotalHours(ep.BeginDate, ep.EndDate)
                        };
                        PlacementDetailCalculations(placementDetail, room);
                        list.Add(placementDetail);
                    }
                }
                return list;
            }

            private void PlacementDetailCalculations(Dto placementDetail, Room room)
            {
                placementDetail.CeilingAreaSqFt = ConvertSquareInchesToSquareFeets(room.CeilingAreaSquareInches);
                placementDetail.CeilingPerimeterLnFt = ConvertSquareInchesToSquareFeets(room.CeilingPerimeterInches);
                placementDetail.FloorAreaSqFt = ConvertSquareInchesToSquareFeets(room.FloorAreaSquareInches);
                placementDetail.FloorPerimeterLnFt = ConvertSquareInchesToSquareFeets(room.FloorPerimeterInches);
                placementDetail.VolumeCuFt = ConvertCubicInchesToCubicFeet(room.RoomVolumeCubicInches);
                placementDetail.WallAreaSqFt = ConvertSquareInchesToSquareFeets(room.WallAreaSquareInches);
            }

            private decimal ConvertSquareInchesToSquareFeets(decimal valor)
            {
                return Math.Round(valor / 144m, 2);
            }

            public static decimal ConvertCubicInchesToCubicFeet(long inches)
            {
                return Math.Round(inches / 1728m, 2);
            }

            private decimal GetTotalDays(DateTime beginDate, DateTime? endDate)
            {
                return endDate.HasValue ?
                            Math.Round((decimal)((DateTime)endDate).Subtract(beginDate).TotalDays, 2) :
                            Math.Round((decimal)DateTime.UtcNow.Subtract(beginDate).TotalDays, 2);
            }

            private decimal GetTotalHours(DateTime beginDate, DateTime? endDate)
            {
                var totalDays = endDate.HasValue ?
                            Math.Round((decimal)((DateTime)endDate).Subtract(beginDate).TotalDays, 2) :
                            Math.Round((decimal)DateTime.UtcNow.Subtract(beginDate).TotalDays, 2);

                return Math.Round(totalDays * 24m, 1);

            }
        }
    }
}
