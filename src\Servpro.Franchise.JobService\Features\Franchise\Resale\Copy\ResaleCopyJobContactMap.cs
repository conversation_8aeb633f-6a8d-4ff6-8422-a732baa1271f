﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobContactMap
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult ContactResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobContactMap>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobContactMap> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobContactMap> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobContactMap));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var contactMapTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedContactMapIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(contactMapTargetIds, 
                    GetJobContactMapIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobContactMap, ResaleJobContactMap>(
                    request.ResaleId,
                    contactMap =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(contactMap.JobId))
                            failedDependencies.Add((nameof(Job), contactMap.JobId));
                        if (request.ContactResult.FailedEntities.Contains(contactMap.ContactId))
                            failedDependencies.Add((nameof(Contact), contactMap.ContactId));

                        return failedDependencies;
                    },
                    alreadyCopiedContactMapIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobContactMap.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobContactMap>> GetSourceEntitiesAsync(List<Guid> jobIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobContactMaps = await _context.JobContactMap
                    .Where(jcm => jobIds.Contains(jcm.JobId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobContactMaps.Count);
                return jobContactMaps;
            }

            private async Task<List<Guid>> GetJobContactMapIdsAsync(List<Guid?> contactMapTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobContactMap
                    .Where(x => contactMapTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
