﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddedJobAreaMaterialReadingFieldAndJobConfigExt : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobConfigExtension_Job_JobId",
                table: "JobConfigExtension");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("1b2f7188-ab26-4e54-9f49-67cd06fc26f2"));

            migrationBuilder.DropColumn(
                name: "IsDryBookMobileAddEquipmentOptedIn",
                table: "JobConfigExtension");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<Guid>(
                name: "JobId",
                table: "JobConfigExtension",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci",
                oldClrType: typeof(Guid),
                oldType: "char(36)")
                .OldAnnotation("Relational:Collation", "latin1_swedish_ci");

            migrationBuilder.AddColumn<Guid>(
                name: "MediaMetadataId",
                table: "JobAreaMaterialReading",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("0bfe1ac2-83b7-4d37-94dd-21262ddac63c"), null, new DateTime(2023, 1, 11, 18, 21, 52, 657, DateTimeKind.Utc).AddTicks(3172), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterialReading_MediaMetadataId",
                table: "JobAreaMaterialReading",
                column: "MediaMetadataId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobAreaMaterialReading_MediaMetadata_MediaMetadataId",
                table: "JobAreaMaterialReading",
                column: "MediaMetadataId",
                principalTable: "MediaMetadata",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_JobConfigExtension_Job_JobId",
                table: "JobConfigExtension",
                column: "JobId",
                principalTable: "Job",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobAreaMaterialReading_MediaMetadata_MediaMetadataId",
                table: "JobAreaMaterialReading");

            migrationBuilder.DropForeignKey(
                name: "FK_JobConfigExtension_Job_JobId",
                table: "JobConfigExtension");

            migrationBuilder.DropIndex(
                name: "IX_JobAreaMaterialReading_MediaMetadataId",
                table: "JobAreaMaterialReading");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("0bfe1ac2-83b7-4d37-94dd-21262ddac63c"));

            migrationBuilder.DropColumn(
                name: "MediaMetadataId",
                table: "JobAreaMaterialReading");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<Guid>(
                name: "JobId",
                table: "JobConfigExtension",
                type: "char(36)",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                collation: "latin1_swedish_ci",
                oldClrType: typeof(Guid),
                oldType: "char(36)",
                oldNullable: true)
                .OldAnnotation("Relational:Collation", "latin1_swedish_ci");

            migrationBuilder.AddColumn<bool>(
                name: "IsDryBookMobileAddEquipmentOptedIn",
                table: "JobConfigExtension",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("1b2f7188-ab26-4e54-9f49-67cd06fc26f2"), null, new DateTime(2023, 1, 11, 15, 46, 18, 557, DateTimeKind.Utc).AddTicks(4323), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.AddForeignKey(
                name: "FK_JobConfigExtension_Job_JobId",
                table: "JobConfigExtension",
                column: "JobId",
                principalTable: "Job",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
