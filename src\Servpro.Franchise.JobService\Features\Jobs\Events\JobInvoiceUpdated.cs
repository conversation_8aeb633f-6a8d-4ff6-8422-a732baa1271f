﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobInvoiceUpdated
    {

        public class Event : InvoiceUpdatedEvent, IRequest
        {
            public Event(InvoiceUpdatedDto invoiceUpdated, Guid correlationId) : base(invoiceUpdated, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<JobInvoiceCreated> _logger;
            private readonly JobDataContext _context;

            public Handler(ILogger<JobInvoiceCreated> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {

                using var scope = _logger.BeginScope("{jobId}", incomingEvent.InvoiceUpdated.JobId);
                _logger.LogInformation("Handler began with {@incomingEvent}", incomingEvent);

                var incomingInvoice = incomingEvent.InvoiceUpdated;

                var incomingType = incomingInvoice.EventType;
                var job = await _context.Jobs
                    .Include(x => x.MediaMetadata)
                    .ThenInclude(i => i.JobInvoice)
                    .FirstOrDefaultAsync(q => q.Id == incomingInvoice.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {incomingInvoice.JobId}");

                var invoiceMedia = job.MediaMetadata
                        .FirstOrDefault(q => q.JobId == incomingInvoice.JobId 
                                             && q.JobInvoice?.Id == incomingInvoice.InvoiceId);

                if (invoiceMedia?.JobInvoice != null)
                {
                    var invoiceInfo = invoiceMedia.JobInvoice;
                    invoiceInfo.Description = incomingInvoice.Description ?? string.Empty;
                    invoiceInfo.InvoiceNumber = incomingInvoice.InvoiceNumber ?? string.Empty;
                    invoiceInfo.Source = incomingInvoice.Source ?? string.Empty;
                    invoiceInfo.Date = incomingInvoice.Date;
                    if (incomingType == InvoiceEventType.Invoice)
                    {
                        invoiceInfo.Amount = incomingInvoice.Amount;
                    }
                    if (incomingType == InvoiceEventType.Collected)
                    {
                        invoiceInfo.AmountCollected += incomingInvoice.Amount;
                    }

                    await _context.SaveChangesAsync(cancellationToken);
                }

                return Unit.Value;
            }
            
        }
    }
}
