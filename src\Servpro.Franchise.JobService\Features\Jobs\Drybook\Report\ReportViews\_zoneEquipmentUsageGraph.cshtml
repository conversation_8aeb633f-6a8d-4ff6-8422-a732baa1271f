@model List<Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.EquipmentCountDetail>
@using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
@using Servpro.Franchise.JobService.Common

@{
    var chartId = $"chart{Guid.NewGuid().GetHashCode().ToString().Replace("-", "")}";
    var line1 = Model
        .Where(w => w.EquipmentType == nameof(BaseEquipmentTypes.Dehumidifier))
        .OrderBy(w => w.Day)
        .Select(s => new { s.Day, s.Count })
        .ToList()
        .Select(s => new
        {
            Day = EquipmentUsageHelper.GetJavascriptTimestamp(s.Day),
            Count = s.Count.GetValueOrDefault()
        });

    var line2 = Model
        .Where(x => x.EquipmentType == "Air Mover")
        .OrderBy(w => w.Day)
        .Select(s => new { s.Day, s.Count })
        .ToList()
        .Select(s => new
        {
            Day = EquipmentUsageHelper.GetJavascriptTimestamp(s.Day),
            Count = s.Count.GetValueOrDefault()
        });
}
<style>

    .ct-chart {
        position: relative;
        padding-top: 10px;
        padding-bottom: 20px;
    }

    .ct-legend {
        position: relative;
        z-index: 10;
        list-style: none;
        text-align: center;
    }

        .ct-legend li {
            position: relative;
            padding-left: 23px;
            margin-right: 10px;
            margin-bottom: 3px;
            cursor: pointer;
            display: inline-block;
        }

            .ct-legend li:before {
                width: 12px;
                height: 12px;
                position: absolute;
                left: 0;
                content: '';
                border: 3px solid transparent;
                border-radius: 2px;
            }

            .ct-legend li.inactive:before {
                background: transparent;
            }

        .ct-legend.ct-legend-inside {
            position: absolute;
            top: 0;
            right: 0;
        }

            .ct-legend.ct-legend-inside li {
                display: block;
                margin: 0;
            }

        .ct-legend .ct-series-0:before {
            background-color: #d70206;
            border-color: #d70206;
        }

        .ct-legend .ct-series-1:before {
            background-color: #3F33FF;
            border-color: #3F33FF;
        }

        .ct-legend .ct-series-2:before {
            background-color: #f4c63d;
            border-color: #f4c63d;
        }

    .ct-chart .ct-series-b .ct-line,
    .ct-chart .ct-series-b .ct-point {
        stroke: #3F33FF;
    }
</style>

<div class="ct-chart" id="@chartId"></div>

<script>

    var data1 = [];
    var data2 = [];
    var line1 = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(line1));
    var line2 = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(line2));
    var serie;

    for (serie of line1) {
        data1.push({ x: new Date(serie.Day), y: serie.Count });
    }

    for (serie of line2) {
        data2.push({ x: new Date(serie.Day), y: serie.Count });
    }

    divisorLength = Math.max(data1.length - 1, data2.length - 1);

    var options = {
          width: 700,
          height: 350,
          // Don't draw the line chart points
          showPoint: true,
          // Disable line smoothing
          lineSmooth: false,
          // X-Axis specific configuration
          axisX: {
              type: Chartist.FixedScaleAxis,
              divisor: divisorLength,
                labelInterpolationFnc: function (value) {
                    //using moment.utc() because the time is already converted to franchise local time
                    // and we dont want to convert the datetime yet again.
                    return moment.utc(value).format('MM/D');
                },
          },
          plugins: [
                Chartist.plugins.legend({
                    legendNames: ['Dehumidifier', 'Air Mover'],
                })
          ]
    };
    
    var data = {
        series: [
            {
                name: 'series-1',
                data: data1
            },
            {
                name: 'series-2',
                data: data2
            }
        ]
    };

    new Chartist.Line('#@chartId', data, options);
</script>