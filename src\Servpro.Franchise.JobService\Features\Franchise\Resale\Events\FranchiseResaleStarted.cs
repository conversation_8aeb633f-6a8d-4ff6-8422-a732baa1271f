﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Copy;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Resale;
using Servpro.FranchiseSystems.Framework.Messaging.Offloading;
using Servpro.FranchiseSystems.Framework.Setup.Caching;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto.EquipmentMapping;
using ResaleFranchise = Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto.Franchise;
using Task = System.Threading.Tasks.Task;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Events
{
    public class FranchiseResaleStarted
    {
        public class Event : FranchiseResaleStartedEvent, IRequest
        {
            public Event(Guid id,
                int resaleAttempt,
                ResaleFranchise sellingFranchise,
                ResaleFranchise buyingFranchise,
                IEnumerable<MappingLocation> mappingLocations,
                bool copyVendors,
                DateTime resaleAttemptsExpireOn,
                Guid correlationId)
                : base(id, resaleAttempt, sellingFranchise, buyingFranchise, mappingLocations, copyVendors, resaleAttemptsExpireOn, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<FranchiseResaleStarted> _logger;
            private readonly JobDataContext _context;
            private readonly IS3Client _s3Client;
            private readonly IConfiguration _config;
            private readonly IMediator _mediator;
            private readonly IDistributedCache _cache;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private static readonly int _cacheDuration = 12;
            private readonly int _retryLimit;
            private static readonly string RetryLimitKey = "Resales:RetryLimit";
            private const string TokenTimeoutMs = "Messaging:CancelTokenTimeoutMs";

            public Handler(
                ILogger<FranchiseResaleStarted> logger,
                JobDataContext context,
                IS3Client s3Client,
                IConfiguration config,
                IMediator mediator,
                IDistributedCache cache,
                IResaleSplitComboUtility resaleSplitComboUtility)
            {
                _logger = logger;
                _context = context;
                _s3Client = s3Client;
                _config = config;
                _mediator = mediator;
                _cache = cache;
                _retryLimit = _config.GetValue(RetryLimitKey, 4);
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var requestStartedAt = DateTime.UtcNow;
                using var scope = _logger.BeginScope("{@resale}",
                    new
                    {
                        id = request.Id,
                        attempt = request.ResaleAttempt,
                        sellingFranchiseId = request.SellingFranchise.Id,
                        buyingFranchiseId = request.BuyingFranchise.Id,
                        sellingFranchiseNumber = request.SellingFranchise.FranchiseNumber,
                        buyingFranchiseNumber = request.BuyingFranchise.FranchiseNumber
                    });
                _logger.LogInformation("Precessing resell: {@payload}", request);

                if (await HasReachedLimitOfTriesForTodayAsync(request, cancellationToken))
                    return Unit.Value;

                var correlationId = request.CorrelationId;
                var resaleId = request.Id;
                var sellingFranchiseId = request.SellingFranchise.Id;
                var purchasingFranchiseId = request.BuyingFranchise.Id;
                var sellingFranchiseSetId = request.SellingFranchise.FranchiseSetId;
                var purchasingFranchiseSetId = request.BuyingFranchise.FranchiseSetId;

                // contact mapping
                var contactMapping = await GetMappingAsync<ContactMapping>(
                    MappingType.Contact,
                    request, cancellationToken);
                if (contactMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Contact mappings: {@contactMapping}", contactMapping);

                // employeeMapping
                var employeeMapping = await GetMappingAsync<EmployeeMapping>(
                    MappingType.Employee,
                    request,
                    cancellationToken);
                if (employeeMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Employee mappings: {@employeeMapping}", employeeMapping);

                // equipmentMapping
                var equipmentMapping = await GetMappingAsync<EquipmentMapping>(
                    MappingType.Equipment,
                    request,
                    cancellationToken);
                if (equipmentMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Equipment mappings: {@equipmentMapping}", equipmentMapping);

                // xact job mapping
                var xactJobMapping = await GetMappingAsync<XactJobMapping>(
                    MappingType.XactJob,
                    request,
                    cancellationToken);
                if (xactJobMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Xact job mappings: {@xactJobMapping}", xactJobMapping);

                // all distinct business that need to copied over to the target franchiseSet
                var marketingBusinessIds = contactMapping.SoldMarketingBusinesses
                    .Select(x => x.Id)
                    .ToHashSet();
                var businessToBeCopied = contactMapping.BusinessesToBeCopied
                    .Select(x => x.Id)
                    .Concat(marketingBusinessIds)
                    .ToHashSet();

                // list of all the equipment that will be copied
                var equipmentToBeCopiedIds = equipmentMapping.EquipmentSold
                    .Select(x => x.Id)
                    .Concat(equipmentMapping.EquipmentToBeCopied.Select(x => x.Id))
                    .ToHashSet();

                var resaleSourceIds = await GetResaleLookupsAsync(
                    businessToBeCopied,
                    contactMapping.ContactsToBeCopied.Select(x => x.Id).ToHashSet(),
                    marketingBusinessIds,
                    sellingFranchiseId,
                    equipmentToBeCopiedIds,
                    cancellationToken);

                var errors = await CopyDataAsync(
                    resaleSourceIds,
                    employeeMapping.EmployeeMap,
                    employeeMapping.Owner,
                    marketingBusinessIds,
                    request.Id,
                    request.BuyingFranchise,
                    equipmentMapping.ServproOwnedBaseEquipmentTypes,
                    equipmentMapping.EquipmentSold,
                    xactJobMapping.XactJobs,
                    equipmentToBeCopiedIds,
                    requestStartedAt,
                    cancellationToken);

                await _resaleSplitComboUtility.TriggerRescindByRscAsync(request.SellingFranchise.FranchiseSetId, 
                    request.SellingFranchise.Id, 
                    request.CorrelationId, 
                    cancellationToken);

                var resaleCompletedEvent = GenerateResaleCompletedEvent(request.Id,
                    request.ResaleAttempt,
                    errors.Any() ? Status.Failed : Status.Succeeded,
                    errors.Any() ? errors.Count() : (int?)null,
                    request.CorrelationId);

                if (errors.Any())
                {
                    var errorsPerEvent = _config.GetValue("Resales:MaxErrorPerEvent", 10);
                    var outboxMessages = new List<OutboxMessage>() { GenerateOutboxMessage(resaleCompletedEvent, request.CorrelationId) };
                    foreach (var errorBatch in errors.Batch(errorsPerEvent))
                    {
                        outboxMessages.Add(GenerateOutboxMessage(
                            GenerateResaleErrorOccuredEvent(request.Id, request.ResaleAttempt, errorBatch, request.CorrelationId),
                            request.CorrelationId));
                    }
                    await _context.OutboxMessages.AddRangeAsync(outboxMessages, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    var outboxMessage = GenerateOutboxMessage(resaleCompletedEvent, request.CorrelationId);
                    await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                }

                return Unit.Value;
            }

            private async Task<bool> HasReachedLimitOfTriesForTodayAsync(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Checking how many times resale has been retries today");
                var numberOfTimesRanTodayKey = $"{request.Id}:{request.CorrelationId}";
                var updateCacheEntry = true;
                var numberOfTimesRanToday = await _cache.GetOrCreateAsync(numberOfTimesRanTodayKey, async options => 
                {
                    _logger.LogInformation("Cache key was not found for resale run, creating new cache entry");
                    options.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(_cacheDuration);
                    updateCacheEntry = false;
                    return 1;
                }, cancellationToken: cancellationToken);

                if (updateCacheEntry)
                {
                    numberOfTimesRanToday += 1;
                    //This uses setStringAsync so should work as an update
                    await _cache.CreateAsync(numberOfTimesRanTodayKey, numberOfTimesRanToday, TimeSpan.FromHours(_cacheDuration), cancellationToken: cancellationToken);
                }

                _logger.LogInformation("Resale has attempted to run in the same service {times}/{limit} today.", numberOfTimesRanToday, _retryLimit);
                return numberOfTimesRanToday > _retryLimit;
            }

            private async Task<IEnumerable<ErrorInfo>> CopyDataAsync(
                ResaleLookup resaleSourceIds,
                ImmutableDictionary<Guid, Guid> employeeMapping,
                EmployeeMapping.Employee owner,
                HashSet<Guid> marketingBusinessIds,
                Guid resaleId,
                ResaleFranchise purchasingFranchise,
                ImmutableHashSet<Guid> servproOwnedBaseEquipmentIds,
                ImmutableList<EquipmentMapping.EquipmentDto> equipmentSold,
                ImmutableList<Guid> xactJobs,
                HashSet<Guid> equipmentIds,
                DateTime requestStartedAt,
                CancellationToken cancellationToken)
            {
                var mappingOptions = GetMappingOptions(resaleId,
                    purchasingFranchise.FranchiseSetId,
                    purchasingFranchise.Id,
                    purchasingFranchise.Name,
                    purchasingFranchise.State,
                    employeeMapping,
                    owner,
                    servproOwnedBaseEquipmentIds,
                    xactJobs,
                    resaleSourceIds.EquipmentTypeIds,
                    resaleSourceIds.EquipmentModelIds);
                try
                {
                    var copyMobileDataTask = CopyMobileData(resaleSourceIds.MobileDataIds, resaleId, mappingOptions, cancellationToken);
                    var copyBusinessTask = CopyBusiness(resaleSourceIds.BusinessIds, resaleId, mappingOptions, cancellationToken);
                    var copyEquipmentTypesTask = CopyEquipmentTypes(resaleSourceIds.EquipmentTypeIds, resaleId, mappingOptions, cancellationToken);
                    await Task.WhenAll(copyMobileDataTask, copyBusinessTask, copyEquipmentTypesTask);

                    var mobileDataResult = copyMobileDataTask.Result;
                    var businessResult = copyBusinessTask.Result;
                    var equipmentTypeResult = copyEquipmentTypesTask.Result;

                    var contactResult = await CopyContacts(resaleSourceIds.ContactIds, marketingBusinessIds, resaleId, mappingOptions, businessResult, cancellationToken);

                    var copyJobTask = CopyJobs(resaleSourceIds.JobIds, resaleId, mappingOptions, mobileDataResult, contactResult, cancellationToken);
                    var copyWipRecordTask = CopyWipRecords(resaleSourceIds.JobIds, resaleId, mappingOptions, contactResult, cancellationToken);
                    await Task.WhenAll(copyJobTask, copyWipRecordTask);

                    var jobResult = copyJobTask.Result;
                    var wipRecordResult = copyWipRecordTask.Result;

                    var copyJobBusinessTask = CopyJobBusiness(resaleSourceIds.JobIds, resaleId, mappingOptions, businessResult, jobResult, cancellationToken);
                    var copyJobContactMapTask = CopyJobContactMap(resaleSourceIds.JobIds, resaleId, mappingOptions, contactResult, jobResult, cancellationToken);
                    var copyJobVisitTask = CopyJobVisit(resaleSourceIds.JobVisitIds, resaleId, mappingOptions, jobResult, cancellationToken);
                    var copyJobMaterialTask = CopyJobMaterial(resaleSourceIds.JobIds, resaleId, mappingOptions, jobResult, cancellationToken);
                    var copyJobTriStateAnswerTask = CopyJobTriStateAnswer(resaleSourceIds.JobIds, resaleId, mappingOptions, jobResult, cancellationToken);
                    var copyZoneTask = CopyZones(resaleSourceIds.ZoneIds, resaleId, mappingOptions, jobResult, cancellationToken);
                    await Task.WhenAll(copyJobBusinessTask, copyJobContactMapTask, copyJobVisitTask, copyJobMaterialTask, copyJobTriStateAnswerTask, copyZoneTask);

                    var jobBusinessResult = copyJobBusinessTask.Result;
                    var jobContactMapResult = copyJobContactMapTask.Result;
                    var jobVisitResult = copyJobVisitTask.Result;
                    var jobMateriaResult = copyJobMaterialTask.Result;
                    var jobTriStateAnswerResult = copyJobTriStateAnswerTask.Result;
                    var zoneResult = copyZoneTask.Result;

                    var copyTask = CopyTask(resaleSourceIds.TaskIds, resaleId, mappingOptions, jobResult, jobVisitResult, zoneResult, cancellationToken);
                    var copyJobVisitTriStateAnswerTask = CopyJobVisitTriStateAnswer(resaleSourceIds.JobVisitIds, resaleId, mappingOptions, jobVisitResult, cancellationToken);
                    await Task.WhenAll(copyTask, copyJobVisitTriStateAnswerTask);

                    var taskResult = copyTask.Result;
                    var jobVisitTriStateAnswerResult = copyJobVisitTriStateAnswerTask.Result;

                    var journalNotesWithoutRFTResult = await CopyJournalNotesWithoutRFT(resaleSourceIds.JournalNoteIds, resaleId, mappingOptions, jobResult, jobVisitResult, zoneResult, taskResult, cancellationToken);
                    var roomResult = await CopyRooms(resaleSourceIds.RoomIds, resaleId, mappingOptions, jobVisitResult, journalNotesWithoutRFTResult, cancellationToken);
                    var areaResult = await CopyJobAreas(resaleSourceIds.JobAreaIds, resaleId, mappingOptions, roomResult, jobResult, cancellationToken);

                    var copyRoomFlooringTypeAffectedTask = CopyRoomFlooringTypeAffected(resaleSourceIds.RoomIds, resaleId, mappingOptions, roomResult, cancellationToken);
                    var copyJobAreaMaterialTask = CopyJobAreaMaterial(resaleSourceIds.JobAreaMaterialIds, resaleId, mappingOptions, areaResult, jobVisitResult, jobMateriaResult, cancellationToken);
                    var copyEquipmentModelTask = CopyEquipmentModel(resaleSourceIds.EquipmentModelIds, resaleId, mappingOptions, equipmentTypeResult, cancellationToken);
                    var copyMediaMetadataTask = CopyMediaMetadata(resaleSourceIds.MediaIds, resaleId, mappingOptions, jobResult, jobVisitResult, areaResult, cancellationToken);
                    await Task.WhenAll(copyRoomFlooringTypeAffectedTask, copyJobAreaMaterialTask, copyEquipmentModelTask, copyMediaMetadataTask);

                    var roomFlooringTypeAffectedResult = copyRoomFlooringTypeAffectedTask.Result;
                    var jobAreaMaterialResult = copyJobAreaMaterialTask.Result;
                    var equipmentModelResult = copyEquipmentModelTask.Result;
                    var mediaMetadataResult = copyMediaMetadataTask.Result;

                    var journalNotesWithRFTResult = await CopyJournalNotesWithRFT(resaleSourceIds.JournalNoteIds, resaleId, mappingOptions, jobResult, jobVisitResult, zoneResult, taskResult, roomFlooringTypeAffectedResult, cancellationToken);

                    var copyEquipmentTask = CopyEquipment(equipmentIds, resaleId, mappingOptions, equipmentModelResult, equipmentSold, cancellationToken);
                    var copyZoneReadingTask = CopyZoneReading(resaleSourceIds.ZoneIds, resaleId, mappingOptions, jobResult, jobVisitResult, zoneResult, journalNotesWithoutRFTResult, journalNotesWithRFTResult, cancellationToken);
                    var copyJobAreaMaterialReadingTask = CopyJobAreaMaterialReading(resaleSourceIds.JobAreaMaterialIds, resaleId, mappingOptions, jobVisitResult, jobAreaMaterialResult, cancellationToken);
                    var copyJobSketchTask = CopyJobSketch(resaleSourceIds.JobIds, resaleId, mappingOptions, jobResult, mediaMetadataResult, cancellationToken);
                    var copyJobInvoiceTask = CopyJobInvoice(resaleSourceIds.MediaIds, resaleId, mappingOptions, mediaMetadataResult, cancellationToken);
                    await Task.WhenAll(copyEquipmentTask, copyZoneReadingTask, copyJobAreaMaterialReadingTask, copyJobSketchTask, copyJobInvoiceTask);

                    var equipmentResult = copyEquipmentTask.Result;
                    var zoneReadingResult = copyZoneReadingTask.Result;
                    var jobAreaMaterialReadingResult = copyJobAreaMaterialReadingTask.Result;
                    var jobSketchResult = copyJobSketchTask.Result;
                    var jobInvoiceResult = copyJobInvoiceTask.Result;

                    var equipmentPlacementResult = await CopyEquipmentPlacement(resaleSourceIds.EquipmentPlacementIds, resaleId, mappingOptions, equipmentResult, areaResult, cancellationToken);
                    var equipmentPlacementReadingResult = await CopyEquipmentPlacementReading(resaleSourceIds.EquipmentPlacementIds, resaleId, mappingOptions, equipmentPlacementResult, jobVisitResult, zoneResult, cancellationToken);

                    return mobileDataResult.Errors
                        .Concat(businessResult.Errors)
                        .Concat(equipmentTypeResult.Errors)
                        .Concat(contactResult.Errors)
                        .Concat(jobResult.Errors)
                        .Concat(wipRecordResult.Errors)
                        .Concat(jobBusinessResult.Errors)
                        .Concat(jobContactMapResult.Errors)
                        .Concat(jobVisitResult.Errors)
                        .Concat(jobMateriaResult.Errors)
                        .Concat(jobTriStateAnswerResult.Errors)
                        .Concat(zoneResult.Errors)
                        .Concat(taskResult.Errors)
                        .Concat(jobVisitTriStateAnswerResult.Errors)
                        .Concat(journalNotesWithoutRFTResult.Errors)
                        .Concat(roomResult.Errors)
                        .Concat(areaResult.Errors)
                        .Concat(roomFlooringTypeAffectedResult.Errors)
                        .Concat(jobAreaMaterialResult.Errors)
                        .Concat(equipmentModelResult.Errors)
                        .Concat(journalNotesWithRFTResult.Errors)
                        .Concat(equipmentResult.Errors)
                        .Concat(zoneReadingResult.Errors)
                        .Concat(jobAreaMaterialReadingResult.Errors)
                        .Concat(equipmentPlacementResult.Errors)
                        .Concat(equipmentPlacementReadingResult.Errors)
                        .Concat(jobSketchResult.Errors)
                        .Concat(jobInvoiceResult.Errors)
                        .Concat(mediaMetadataResult.Errors);
                }
                catch (OperationCanceledException)
                {
                    if (IsOperationCanceledFromTimeout(requestStartedAt))
                        throw;

                    //otherwise, we assume the cancellation is from the ApplicationStopping event
                    throw new PodKilledException("The pod handling the resale request is shutting down, re-triggering resale.");
                }
            }

            private bool IsOperationCanceledFromTimeout(DateTime requestStartedAt)
            {
                //the CancellationToken is a linked token that can be triggered from two locations
                //we need to determine if its been running for the same amount of time as the token allows
                //or if the cancellation comes from the pod being killed
                var operationCancelledAt = DateTime.UtcNow;
                string eventTimeoutOverrideKey = $"{TokenTimeoutMs}:{nameof(FranchiseResaleStartedEvent)}";
                var eventTimeoutOverrideMs = _config.GetValue(eventTimeoutOverrideKey, 0);
                var expectedEndTime = requestStartedAt.AddMilliseconds(eventTimeoutOverrideMs);
                var upperBoundRunTime = expectedEndTime.AddMinutes(1);
                var lowerBoundRunTime = expectedEndTime.AddMinutes(-1);
                //if operation is cancelled within a minute of the expected end time, then it has ran out of time
                return operationCancelledAt >= lowerBoundRunTime && operationCancelledAt <= upperBoundRunTime;
            }

            private async Task<ProcessEntityResult> CopyJobInvoice(
                HashSet<Guid> mediaIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult mediaResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobInvoice.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    MediaIds = mediaIds,
                    MediaResult = mediaResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobSketch(
                HashSet<Guid> jobIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                ProcessEntityResult mediaResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobSketch.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    JobResult = jobResult,
                    MediaResult = mediaResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyMediaMetadata(
                HashSet<Guid> mediaIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult areaResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyMediaMetadata.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    MediaIds = mediaIds,
                    JobResult = jobResult,
                    AreaResult = areaResult,
                    JobVisitResult = jobVisitResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobMaterial(
                HashSet<Guid> jobIds,
                Guid resaleId, 
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobMaterial.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    JobResult = jobResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobAreaMaterial(
                HashSet<Guid> jobAreaMaterialIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult areaResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult materialResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobAreaMaterial.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobAreaMaterialIds = jobAreaMaterialIds,
                    AreaResult = areaResult,
                    JobVisitResult = jobVisitResult,
                    MaterialResult = materialResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobAreaMaterialReading(
                HashSet<Guid> jobAreaMaterialIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult jobAreaMaterialResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobAreaMaterialReading.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobAreaMaterialIds = jobAreaMaterialIds,
                    JobVisitResult = jobVisitResult,
                    JobAreaMaterialResult = jobAreaMaterialResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobTriStateAnswer(
                HashSet<Guid> jobIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobTriStateAnswer.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    JobResult = jobResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyZones(
                HashSet<Guid> zoneIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyZones.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    ZoneIds = zoneIds,
                    JobResult = jobResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobVisitTriStateAnswer(
               HashSet<Guid> jobVisitIds,
               Guid resaleId,
               Dictionary<string, object> mappingOptions,
               ProcessEntityResult jobVisitResult,
               CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobVisitTriStateAnswer.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobVisitIds = jobVisitIds,
                    JobVisitResult = jobVisitResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyRoomFlooringTypeAffected(
               HashSet<Guid> roomIds,
               Guid resaleId,
               Dictionary<string, object> mappingOptions,
               ProcessEntityResult roomResult,
               CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyRoomFlooringTypeAffected.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    RoomIds = roomIds,
                    RoomResult = roomResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyTask(
                HashSet<Guid> taskIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult zoneResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyTask.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    TaskIds = taskIds,
                    JobResult = jobResult,
                    JobVisitResult = jobVisitResult,
                    ZoneResult = zoneResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyRooms(
                HashSet<Guid> roomIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult journalNotesWithoutRFTResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyRooms.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    RoomIds = roomIds,
                    JobVisitResult = jobVisitResult,
                    JournalNotesWithoutRFTResult = journalNotesWithoutRFTResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobAreas(
                HashSet<Guid> jobAreaIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult roomResult,
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobAreas.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobAreaIds = jobAreaIds,
                    RoomResult = roomResult,
                    JobResult = jobResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJournalNotesWithoutRFT(
                HashSet<Guid> journalNoteIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult zoneResult,
                ProcessEntityResult taskResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJournalNotesWithoutRFT.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JournalNoteIds = journalNoteIds,
                    JobResult = jobResult,
                    JobVisitResult = jobVisitResult,
                    ZoneResult = zoneResult,
                    TaskResult = taskResult
                }, cancellationToken);
            }
            private async Task<ProcessEntityResult> CopyZoneReading(
                HashSet<Guid> zoneIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult zoneResult,
                ProcessEntityResult journalNoteWithoutRFTResult,
                ProcessEntityResult journalNoteWithRFTResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyZoneReading.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    ZoneIds = zoneIds,
                    JobResult = jobResult,
                    JobVisitResult = jobVisitResult,
                    ZoneResult = zoneResult,
                    JournalNoteWithoutRFTResult = journalNoteWithoutRFTResult,
                    JournalNoteWithRFTResult = journalNoteWithRFTResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJournalNotesWithRFT(
                HashSet<Guid> journalNoteIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult zoneResult,
                ProcessEntityResult taskResult,
                ProcessEntityResult roomFlooringTypeAffected,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJournalNoteWithRFT.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JournalNoteIds = journalNoteIds,
                    JobResult = jobResult,
                    JobVisitResult = jobVisitResult,
                    ZoneResult = zoneResult,
                    TaskResult = taskResult,
                    RoomFlooringTypeAffectedResult = roomFlooringTypeAffected
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobVisit(
                HashSet<Guid> jobVisitIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobVisit.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobVisitIds = jobVisitIds,
                    JobResult = jobResult
                }, cancellationToken);
            }
            private async Task<ProcessEntityResult> CopyEquipmentModel(
                HashSet<Guid> equipmentModelIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult equipmentTypeResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyEquipmentModel.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    EquipmentModelIds = equipmentModelIds,
                    EquipmentTypeResult = equipmentTypeResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyEquipment(
                HashSet<Guid> equipmentIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult equipmentModelResult,
                ImmutableList<EquipmentDto> equipmentSold,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyEquipment.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    EquipmentIds = equipmentIds,
                    EquipmentModelResult = equipmentModelResult,
                    EquipmentSold = equipmentSold
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyEquipmentPlacement(
                HashSet<Guid> equipmentPlacementIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult equipmentResult,
                ProcessEntityResult areaResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyEquipmentPlacement.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    EquipmentPlacementIds = equipmentPlacementIds,
                    EquipmentResult = equipmentResult,
                    AreaResult = areaResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyEquipmentPlacementReading(
                HashSet<Guid> equipmentPlacementIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult equipmentPlacementResult,
                ProcessEntityResult jobVisitResult,
                ProcessEntityResult jobZoneResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyEquipmentPlacementReading.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    EquipmentPlacementIds = equipmentPlacementIds,
                    EquipmentPlacementResult = equipmentPlacementResult,
                    JobVisitResult = jobVisitResult,
                    JobZoneResult = jobZoneResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobContactMap(
                HashSet<Guid> jobIds,
                Guid resaleId, 
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult contactResult, 
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobContactMap.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    JobResult = jobResult,
                    ContactResult = contactResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobBusiness(
                HashSet<Guid> jobIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult businessResult,
                ProcessEntityResult jobResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJobBusinessMap.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    JobResult = jobResult,
                    BusinessResult = businessResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyWipRecords(
                HashSet<Guid> jobIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult contactResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyWipRecord.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    ContactResult = contactResult
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyJobs(
                HashSet<Guid> jobIds,
                Guid resaleId, 
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult mobileDataResult,
                ProcessEntityResult contactResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyJob.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    JobIds = jobIds,
                    MobileDataResult = mobileDataResult,
                    ContactResult = contactResult,
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyContacts(
                HashSet<Guid> contactIds,
                HashSet<Guid> marketingBusinessIds,
                Guid resaleId, 
                Dictionary<string, object> mappingOptions,
                ProcessEntityResult businessResult,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyContact.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    ContactIds = contactIds,
                    BusinessResult = businessResult,
                    MarketingBusinessIds = marketingBusinessIds
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyBusiness(
                HashSet<Guid> businessIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyBusiness.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    BusinessIds = businessIds
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyEquipmentTypes(
                HashSet<Guid> equipmentTypeIds,
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyEquipmentType.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    EquipmentTypeIds = equipmentTypeIds
                }, cancellationToken);
            }

            private async Task<ProcessEntityResult> CopyMobileData(
                HashSet<Guid> mobileDataIds, 
                Guid resaleId,
                Dictionary<string, object> mappingOptions,
                CancellationToken cancellationToken)
            {
                return await _mediator.Send(new ResaleCopyMobileData.Command
                {
                    MappingOptions = mappingOptions,
                    ResaleId = resaleId,
                    MobileDataIds = mobileDataIds
                }, cancellationToken);
            }

            private Dictionary<string, object> GetMappingOptions(
                Guid resaleId,
                Guid purchasingFranchiseSetId,
                Guid purchasingFranchiseId,
                string purchasingFranchiseName,
                string purchasingFranchiseState,
                ImmutableDictionary<Guid, Guid> employeeMapping,
                EmployeeMapping.Employee owner,
                ImmutableHashSet<Guid> servproOwnedBaseEquipmentIds,
                ImmutableList<Guid> xactJobs,
                HashSet<Guid> equipmentTypeIds,
                HashSet<Guid> equipmentModelIds)
                => new Dictionary<string, object>()
                {
                    { nameof(Job.Id), resaleId },
                    { nameof(Job.FranchiseSetId), purchasingFranchiseSetId },
                    { nameof(Job.FranchiseId), purchasingFranchiseId },
                    { nameof(Job.FranchiseName), purchasingFranchiseName },
                    { nameof(Job.FranchiseState), purchasingFranchiseState },
                    { ResaleMappingProfile.EmployeeMappingKey, employeeMapping },
                    { ResaleMappingProfile.OwnerKey, owner },
                    { ResaleMappingProfile.ServproOwnedBaseEquipmentKey, servproOwnedBaseEquipmentIds },
                    { ResaleMappingProfile.XactJobsKey, xactJobs.Distinct().ToHashSet() },
                    { ResaleMappingProfile.EquipmentTypeToCopyIdsKey, equipmentTypeIds},
                    { ResaleMappingProfile.EquipmentModelToCopyIdsKey, equipmentModelIds}
                };

            private async Task<ResaleLookup> GetResaleLookupsAsync(
                HashSet<Guid> businessIds,
                HashSet<Guid> contactIds,
                HashSet<Guid> marketingBusinessIds,
                Guid sellingFranchiseId,
                HashSet<Guid> equipmentIds,
                CancellationToken cancellationToken)
            {
                //jobs
                var (jobIds, mobileDataIds) = await GetJobAndMobileDataIdsAsync(sellingFranchiseId, cancellationToken);
                
                //contacts/businesses, we need to get all the contacts associated to the marketing businesses as well
                var (allContactIds, allBusinessIds) = await GetContactAndBusinessIdsAsync(businessIds, contactIds, marketingBusinessIds, cancellationToken);

                var mediaIds = await GetMediaMetaDataIdsAsync(jobIds, cancellationToken);
                var jobVisitIds = await GetJobVisitIdsAsync(jobIds, cancellationToken);
                var journalNoteIds = await GetJournalNoteIdsAsync(jobIds, jobVisitIds, cancellationToken);
                var (jobAreaIds, jobAreaMaterialIds, roomIds) = await GetDryingDataIdsAsync(jobIds, jobVisitIds, journalNoteIds, cancellationToken);

                var zoneIds = await GetZoneIdsAsync(jobIds, cancellationToken);
                var taskIds = await GetTaskIdsAsync(jobIds, jobVisitIds, zoneIds, cancellationToken);
                var (equipmentTypeIds, equipmentModelIds, equipmentPlacementIds) = await GetEquipmentAndPlacementIds(equipmentIds, jobAreaIds, cancellationToken);

                return new ResaleLookup(jobIds, mobileDataIds, allContactIds, allBusinessIds, mediaIds, 
                    jobVisitIds, journalNoteIds, jobAreaIds, jobAreaMaterialIds, roomIds, zoneIds, taskIds,
                    equipmentTypeIds, equipmentModelIds, equipmentPlacementIds);
            }

            private async Task<HashSet<Guid>> GetTaskIdsAsync(HashSet<Guid> jobIds, HashSet<Guid> jobVisitIds, HashSet<Guid> zoneIds, CancellationToken cancellationToken)
            {
                var taskIdsAssociatedToJob = await _context.Tasks
                    .Where(t => (t.JobId.HasValue && jobIds.Contains(t.JobId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                var taskIdsAssociatedToJobVisit = await _context.Tasks
                    .Where(t => (!t.JobId.HasValue && t.JobVisitId.HasValue && jobVisitIds.Contains(t.JobVisitId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                var taskIdsAssociatedToZone = await _context.Tasks
                    .Where(t => (!t.JobId.HasValue && t.ZoneId.HasValue && zoneIds.Contains(t.ZoneId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var taskIds = taskIdsAssociatedToJob
                    .Concat(taskIdsAssociatedToJobVisit)
                    .Concat(taskIdsAssociatedToZone);

                return taskIds.ToHashSet();
            }

            private async Task<(HashSet<Guid> equipmentTypeIds, HashSet<Guid> equipmentModelIds, HashSet<Guid> equipmentPlacementIds)> GetEquipmentAndPlacementIds(
                HashSet<Guid> equipmentIds, 
                HashSet<Guid> jobAreaIds, 
                CancellationToken cancellationToken)
            {
                var equipmentModelIds = await _context.Equipments
                    .Where(x => equipmentIds.Contains(x.Id))
                    .Select(x => x.EquipmentModelId)
                    .ToListAsync(cancellationToken);

                var uniqueEquipmentModelIds = equipmentModelIds.ToHashSet();

                // Get models that are associated to the equipment that is marked to sell or identified to be copied
                // Filter out models that reference no equipment type and servpro owned models
                var franchiseSetEquipmentModelAndTypeIds = await _context.EquipmentModels
                    .Where(x => x.EquipmentTypeId != Guid.Empty
                        && x.FranchiseSetId.HasValue
                        && uniqueEquipmentModelIds.Contains(x.Id))
                    .Select(x => new { EquipmentModelId = x.Id, EquipmentTypeId = x.EquipmentTypeId })
                    .ToListAsync(cancellationToken);

                var equipmentTypeIds = franchiseSetEquipmentModelAndTypeIds.Select(x => x.EquipmentTypeId).ToHashSet();

                var franchiseSetEquipmentTypeIds = await _context.EquipmentTypes
                    .Where(x => x.FranchiseSetId.HasValue 
                        && equipmentTypeIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var equipmentModelToCopyIds = franchiseSetEquipmentModelAndTypeIds.Select(x => x.EquipmentModelId).ToHashSet();
                var equipmentTypeToCopyIds = franchiseSetEquipmentTypeIds.ToHashSet();

                //equipment placement
                var equipmentPlacements = await _context.EquipmentPlacements
                    .Where(ep => jobAreaIds.Contains(ep.JobAreaId))
                    .AsNoTracking()
                    .Select(ep => ep.Id)
                    .ToListAsync(cancellationToken);

                return (equipmentTypeToCopyIds, equipmentModelToCopyIds, equipmentPlacements.ToHashSet());
            }

            private async Task<HashSet<Guid>> GetZoneIdsAsync(HashSet<Guid> jobIds, CancellationToken cancellationToken)
            {
                var zoneIds = await _context.Zones
                    .Where(z => jobIds.Contains(z.JobId))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                return zoneIds.ToHashSet();
            }

            private async Task<(HashSet<Guid> jobAreaIds, HashSet<Guid> jobAreaMaterialIds, HashSet<Guid> roomIds)> GetDryingDataIdsAsync(
                HashSet<Guid> jobIds,
                HashSet<Guid> jobVisitIds,
                HashSet<Guid> journalNoteIds,
                CancellationToken cancellationToken)
            {
                var jobAreaAndRoomIds = await _context.JobAreas
                    .Where(ja => jobIds.Contains(ja.JobId))
                    .AsNoTracking()
                    .Select(x => new { JobAreaId = x.Id, RoomId = x.RoomId })
                    .ToListAsync(cancellationToken);
                var jobAreaIds = jobAreaAndRoomIds.Select(ja => ja.JobAreaId).ToHashSet();
                var jobAreaMaterialIds = await _context.JobAreaMaterials
                    .Where(jam => jobAreaIds.Contains(jam.JobAreaId))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                
                var jobAreaRoomIds = jobAreaAndRoomIds
                    .Where(x => x.RoomId.HasValue)
                    .Select(x => x.RoomId.Value)
                    .ToHashSet();

                var roomIds = await GetRoomIdsAsync(jobVisitIds, journalNoteIds, jobAreaRoomIds, cancellationToken);

                return (jobAreaIds, jobAreaMaterialIds.ToHashSet(), roomIds);
            }

            private async Task<HashSet<Guid>> GetRoomIdsAsync(HashSet<Guid> jobVisitIds,
                HashSet<Guid> journalNoteIds,
                HashSet<Guid> jobAreaRoomIds,
                CancellationToken cancellationToken)
            {
                var roomIdsAssociatedToJobVisit = await _context.Rooms
                    .Where(r => (r.DryOnJobVisitId.HasValue && jobVisitIds.Contains(r.DryOnJobVisitId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var roomIdsAssociatedToJournalNote = await _context.Rooms
                    .Where(r => (r.PreExistingConditionsDiaryNoteId.HasValue && journalNoteIds.Contains(r.PreExistingConditionsDiaryNoteId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var roomIdsAssociatedToJobAreas = await _context.Rooms
                    .Where(r => jobAreaRoomIds.Contains(r.Id))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var roomIds = roomIdsAssociatedToJobVisit
                    .Concat(roomIdsAssociatedToJournalNote)
                    .Concat(roomIdsAssociatedToJobAreas)
                    .ToHashSet();

                return roomIds;
            }

            private async Task<HashSet<Guid>> GetJournalNoteIdsAsync(HashSet<Guid> jobIds, 
                HashSet<Guid> jobVisitIds, 
                CancellationToken cancellationToken)
            {
                var journalNoteAssociatedToJob = await _context.JournalNote
                    .Where(jn => (jn.JobId.HasValue && jobIds.Contains(jn.JobId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var journalNoteAssociatedToJobVisit = await _context.JournalNote
                    .Where(jn => (!jn.JobId.HasValue && jn.JobVisitId.HasValue && jobVisitIds.Contains(jn.JobVisitId.Value)))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var journalNoteIds = journalNoteAssociatedToJob.Concat(journalNoteAssociatedToJobVisit);
                return journalNoteIds.ToHashSet();
            }

            private async Task<HashSet<Guid>> GetJobVisitIdsAsync(HashSet<Guid> jobIds, CancellationToken cancellationToken)
            {
                var jobVisitIds = await _context.JobVisit
                    .Where(jv => jobIds.Contains(jv.JobId))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                return jobVisitIds.ToHashSet();
            }

            private async Task<HashSet<Guid>> GetMediaMetaDataIdsAsync(HashSet<Guid> jobIds, CancellationToken cancellationToken)
            {
                var mediaMetaDataIds = await _context.MediaMetadata
                    .Where(mmd => jobIds.Contains(mmd.JobId))
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                return mediaMetaDataIds.ToHashSet();
            }

            private async Task<(HashSet<Guid> allContactIds, HashSet<Guid> allBusinessIds)> GetContactAndBusinessIdsAsync(
                HashSet<Guid> businessIds,
                HashSet<Guid> contactIds,
                HashSet<Guid> marketingBusinessIds,
                CancellationToken cancellationToken)
            {
                var contactsToCopyWithBusinessIds = await _context.Contacts
                    .Where(x => contactIds.Contains(x.Id))
                    .Select(x => new { ContactId = x.Id, BusinessId = x.BusinessId })
                    .ToListAsync(cancellationToken);

                var contactsAssociatedToMarketingWithBusinessIds = await _context.Contacts
                    .Where(x => x.BusinessId.HasValue && marketingBusinessIds.Contains(x.BusinessId.Value))
                    .Select(x => new { ContactId = x.Id, BusinessId = x.BusinessId })
                    .ToListAsync(cancellationToken);

                var contactAndBusinessIds = contactsToCopyWithBusinessIds
                    .Concat(contactsAssociatedToMarketingWithBusinessIds);

                // first include all the contacts businesses in case they weren't selected, but were on a job
                var allBusinessIds = contactAndBusinessIds
                    .Where(x => x.BusinessId.HasValue)
                    .Select(x => x.BusinessId.Value)
                    .ToHashSet();

                // then include all the additional businessIds that were specified by the franchise
                // utilizing UnionWith as it is less expensive and preserves the original list
                allBusinessIds.UnionWith(businessIds);

                var allContactIds = contactAndBusinessIds.Select(x => x.ContactId).ToHashSet();
                return (allContactIds, allBusinessIds);
            }

            private async Task<(HashSet<Guid> jobIds, HashSet<Guid> mobileDataIds)> GetJobAndMobileDataIdsAsync(
                Guid sellingFranchiseId, 
                CancellationToken cancellationToken)
            {
                var jobAndMobileDataIds = await _context.Jobs
                    .Where(j => j.FranchiseId == sellingFranchiseId)
                    .Select(x => new { JobId = x.Id, MobileDataId = x.MobileDataId })
                    .ToListAsync(cancellationToken);

                var jobIds = jobAndMobileDataIds.Select(x => x.JobId).ToHashSet();
                var mobileDataIds = jobAndMobileDataIds
                    .Where(x => x.MobileDataId.HasValue)
                    .Select(x => x.MobileDataId.Value)
                    .ToHashSet();

                return (jobIds, mobileDataIds);
            }

            /// <summary>
            /// Attempts to get the Mapping for the specified mappingType from S3
            /// </summary>
            /// <typeparam name="T"></typeparam>
            /// <param name="mappingType"></param>
            /// <param name="request"></param>
            /// <param name="cancellationToken"></param>
            /// <returns></returns>
            private async Task<T> GetMappingAsync<T>(
                MappingType mappingType,
                Event request,
                CancellationToken cancellationToken)
                where T : BaseMapping
            {
                var mappingLocation = request.MappingLocations
                    .FirstOrDefault(x => x.MappingType == mappingType);
                if (mappingLocation is null)
                {
                    _logger.LogError("Mapping configuration for {mappingType} was not provided. Resale failed. {@providedMappings}", mappingType, request.MappingLocations);
                    var completedEvent = GenerateResaleCompletedEvent(
                        request.Id,
                        request.ResaleAttempt,
                        Status.Failed,
                        1,
                        request.CorrelationId);
                    var errorEvent = GenerateResaleErrorOccuredEvent(
                        request.Id,
                        request.ResaleAttempt,
                        new List<ErrorInfo> { new ErrorInfo(null, null, "ValidationError", $"Mapping configuration for {mappingType} was not provided. Resale failed.") },
                        request.CorrelationId);
                    await _context.OutboxMessages.AddRangeAsync(
                        GenerateOutboxMessage(completedEvent, request.CorrelationId),
                        GenerateOutboxMessage(errorEvent, request.CorrelationId));
                    await _context.SaveChangesAsync(cancellationToken);
                    return default;
                }

                try
                {
                    var mapping = await _s3Client.GetResaleMappingAsync<T>(
                        mappingLocation.BucketName, mappingLocation.Path, cancellationToken);
                    return mapping;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error reading mapping from S3. {@mappingLocation}", mappingLocation);
                    var completedEvent = GenerateResaleCompletedEvent(
                        request.Id,
                        request.ResaleAttempt,
                        Status.Failed,
                        1,
                        request.CorrelationId);
                    var errorEvent = GenerateResaleErrorOccuredEvent(
                        request.Id,
                        request.ResaleAttempt,
                        new List<ErrorInfo> { new ErrorInfo(null, ex.GetType().Name, ex.Message, ex.StackTrace) },
                        request.CorrelationId);
                    await _context.OutboxMessages.AddRangeAsync(
                        GenerateOutboxMessage(completedEvent, request.CorrelationId),
                        GenerateOutboxMessage(errorEvent, request.CorrelationId));
                    await _context.SaveChangesAsync(cancellationToken);
                }
                return default;
            }

            private FranchiseResaleCompletedEvent GenerateResaleCompletedEvent(Guid resaleId, int resaleAttempt, Status status, int? errorCount, Guid correlationId)
                => new FranchiseResaleCompletedEvent(resaleId, resaleAttempt, Service.JobService, status, errorCount, correlationId);

            private FranchiseResaleErrorOccurredEvent GenerateResaleErrorOccuredEvent(
                Guid resaleId,
                int resaleAttempt,
                IEnumerable<ErrorInfo> errors,
                Guid correlationId)
                => new FranchiseResaleErrorOccurredEvent(resaleId, resaleAttempt, Service.JobService, errors.ToList(), correlationId);

            private OutboxMessage GenerateOutboxMessage<T>(T message, Guid correlationId, string createdBy = null)
            {
                if (createdBy is null)
                    createdBy = typeof(T).Name;
                return new OutboxMessage(message.ToJson(), typeof(T).Name, correlationId, createdBy);
            }

        }
    }
}
