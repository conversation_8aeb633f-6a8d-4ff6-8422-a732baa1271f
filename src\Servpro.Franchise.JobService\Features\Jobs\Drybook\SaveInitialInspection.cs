﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.TriStateAnswer;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading;
using System.Threading.Tasks;

using Task = System.Threading.Tasks.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Models.MicaAutomation;
using Servpro.Franchise.JobService.Infrastructure.Utilities;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class SaveInitialInspection
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public int LevelsAffected { get; set; }
            public DateTime OnSiteArrival { get; set; }
            
            public IEnumerable<QuestionAnswerDto> QuestionAnswers { get; set; } = new List<QuestionAnswerDto>();

            public class QuestionAnswerDto
            {
                public Guid? Id { get; set; }
                public Guid JobTriStateQuestionId { get; set; }
                public bool? Answer { get; set; }
            }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.LevelsAffected)
                    .LessThan(100)
                    .GreaterThanOrEqualTo(0);
                RuleFor(x => x.OnSiteArrival)
                    .NotEqual(DateTime.MinValue)
                    .LessThan(DateTime.UtcNow)
                    .WithMessage("On site arrival must not be in the future and it is required.");
                // only handle questions related to initial inspection tab
                // return bad request if the user specifies other type of questions
                RuleForEach(x => x.QuestionAnswers)
                    .Must(q => InitialInspectionQuestionIds.AllQuestionIds.Contains(q.JobTriStateQuestionId));
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly ILogger<SaveInitialInspection> _logger;
            private readonly JobDataContext _context;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IMicaAutomationUtility _micaAutomationUtility;

            public Handler(
                ILogger<SaveInitialInspection> logger,
                JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient,
                IMicaAutomationUtility micaAutomationUtility)
            {
                _logger = logger;
                _userInfo = userInfo;
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
                _lookupServiceClient = lookupServiceClient;
                _micaAutomationUtility = micaAutomationUtility;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogDebug("Begin handler with request: {@request}", request);

                var userInfo = _userInfo.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                if (userInfo == null)
                {
                    _logger.LogWarning("User information could not be obtained.");
                    throw new SecurityException("User information could not be obtained.");
                }

                var job = await _context.Jobs
                        .Include(x => x.JobCustomAttributes)
                        .Include(j => j.JobTriStateAnswers)
                        .Include(j => j.Zones)
                    .FirstOrDefaultAsync(j => j.Id == request.JobId && j.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);
                
                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.JobVisits = await _context.JobVisit.Include(jv => jv.JobVisitTriStateAnswers)
                                                        .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                job.LevelsAffected = request.LevelsAffected;
                job.ModifiedBy = userInfo.Username;
                job.ModifiedDate = DateTime.UtcNow;

                // Generate Job Visit Information
                var jobVisit = CreateOrUpdateJobVisit(request, job, userInfo);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                _logger.LogInformation("Entering into ProcessQuestionAnswers: " );
                await ProcessQuestionAnswers(job, request.QuestionAnswers, userInfo, jobVisit, lookups, cancellationToken, correlationId);

                // when saving initial inspection for the first time
                // three zones get generated
                if (!job.Zones.Any())
                {
                    job.Zones = GetDefaultZones(job.Id, userInfo, jobVisit).ToList();
                }

                // Update Dates
                job.SetOrUpdateDate(JobDateTypes.InitialOnSiteArrival, request.OnSiteArrival);

                // Generate events
                var jobDateEventMessage = GenerateInitialOnSiteArrivalJobDateEvent(request, userInfo.Username, correlationId);
                await _context.OutboxMessages.AddAsync(jobDateEventMessage, cancellationToken);

                // Updating JobProgress
                await UpdateJobProgress(request, cancellationToken, job, correlationId, userInfo.Username);

                await _context.SaveChangesAsync(cancellationToken);

                await _micaAutomationUtility.HandleMicaAutomationConditionallyAsync(job, MicaAutomationTrigger.SaveInitialInspection, correlationId, true, cancellationToken);
                return Unit.Value;
            }

            private static JobVisit CreateOrUpdateJobVisit(Command request, Job job, UserInfo userInfo)
            {
                var jobVisit = job.JobVisits
                    .OrderBy(x => x.Date)
                    .FirstOrDefault();

                if (jobVisit is null)
                {
                    jobVisit = new JobVisit
                    {
                        Id = Guid.NewGuid(),
                        Date = request.OnSiteArrival,
                        JobId = request.JobId,
                        CreatedDate = DateTime.UtcNow,
                        CreatedBy = userInfo.Username,
                        JobVisitTriStateAnswers = new List<JobVisitTriStateAnswer>()
                    };
                    job.JobVisits.Add(jobVisit);
                }
                else
                {
                    jobVisit.Date = request.OnSiteArrival;
                    jobVisit.ModifiedBy = userInfo.Username;
                    jobVisit.ModifiedDate = DateTime.UtcNow;
                }

                return jobVisit;
            }

            private async Task UpdateJobProgress(
                Command request, CancellationToken cancellationToken, Job job,
                Guid correlationId, string userName)
            {
                if (job.JobProgress <= JobProgress.InitialSiteInspection)
                {
                    job.JobProgress = JobProgress.CustomerConsultation;
                    job.JobProgressModifiedDate = DateTime.UtcNow;
                    var jobProgressEventMessage = GenerateJobProgressUpdatedEvent(request, userName, correlationId, job.JobProgress);
                    await _context.OutboxMessages.AddAsync(jobProgressEventMessage, cancellationToken);
                }
            }

            private static OutboxMessage GenerateJobProgressUpdatedEvent(
                Command request, string userName, Guid correlationId, JobProgress jobProgress)
            {
                var jobProgressUpdatedDto = new JobProgressUpdatedEvent.JobProgressUpdatedDto()
                {
                    JobId = request.JobId,
                    ModifiedBy = userName,
                    JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)jobProgress
                };
                var jobProgressUpdatedEvent = new JobProgressUpdatedEvent(jobProgressUpdatedDto, correlationId);
                return new OutboxMessage(jobProgressUpdatedEvent.ToJson(), nameof(JobProgressUpdatedEvent),
                    correlationId, userName);
            }

            private static OutboxMessage GenerateInitialOnSiteArrivalJobDateEvent(
                Command request, string userName, Guid correlationId)
            {
                var dateEventDto = new JobDateUpdatedEvent
                    .JobDateUpdatedDto(request.JobId, JobDateTypes.InitialOnSiteArrival, request.OnSiteArrival,
                        userName);
            

                var dateEvent = new JobDateUpdatedEvent(dateEventDto, correlationId);
                return new OutboxMessage(dateEvent.ToJson(), nameof(JobDateUpdatedEvent),
                    correlationId, userName);
            }

            private static IEnumerable<Zone> GetDefaultZones(
                Guid jobId,
                UserInfo userInfo,
                JobVisit jobVisit)
            {
                yield return CreateZone(jobId, "HVAC", ZoneTypes.HVAC, jobVisit, userInfo.Username, true);
                yield return CreateZone(jobId, "Unaffected", ZoneTypes.Unaffected, jobVisit, userInfo.Username);
                yield return CreateZone(jobId, "Outside", ZoneTypes.Outside, jobVisit, userInfo.Username);

                static Zone CreateZone(
                    Guid jobId,
                    string name,
                    Guid zoneTypeId,
                    JobVisit visit,
                    string username,
                    bool addReading = false)
                    => new Zone
                    {
                        Id = Guid.NewGuid(),
                        Name = name,
                        ZoneTypeId = zoneTypeId,
                        CreatedBy = username,
                        CreatedDate = DateTime.UtcNow,
                        AirMoverCalculationTypeId = AirMoverCalculationTypes.LinearFeet,
                        WaterClassId = WaterClasses.NoWaterDamage,
                        WaterCategoryId = WaterCategories.Sanitary,
                        JobAreas = new List<JobArea>
                        {
                            new JobArea
                            {
                                Id = Guid.NewGuid(),
                                BeginJobVisit = visit,
                                Name = name,
                                JobAreaTypeId = JobAreaTypes.UnaffectedRoom,
                                JobId = jobId,
                                IsUsedInValidation = false,
                                CreatedBy = username,
                                CreatedDate = DateTime.UtcNow
                            }
                        },
                        ZoneReadings = addReading
                            ? new List<ZoneReading> { new ZoneReading { CreatedBy = username, JobVisit = visit } }
                            : new List<ZoneReading>()
                    };
            }

            private async Task ProcessQuestionAnswers(
                Job job,
                IEnumerable<Command.QuestionAnswerDto> questionAnswers,
                UserInfo userInfo,
                JobVisit initialJobVisit,
                GetLookups.Dto lookups,
                CancellationToken cancellationToken,
                Guid correlationId)
            {

                foreach(var answerDto in questionAnswers)
                {

                    var isAskedPerVisit = lookups.JobTriStateQuestions
                        .First(x => x.Id == answerDto.JobTriStateQuestionId).IsAskedPerVisit;

                    _logger.LogInformation("Checking isAksedPerVisit validation: {isAskedPerVisit}", isAskedPerVisit);
                    if (isAskedPerVisit)
                    {
                        UpsertJobVisitTriStateAnswer(initialJobVisit, answerDto, userInfo);
                    }
                    else
                    {
                        await UpsertJobTriStateAnswer(job, answerDto, userInfo, cancellationToken, correlationId);
                    }
                }
            }

            private static void UpsertJobVisitTriStateAnswer(
                JobVisit jobVisit,
                Command.QuestionAnswerDto answerDto,
                UserInfo userInfo)
            {
                var existingAnswer = jobVisit.JobVisitTriStateAnswers
                    .FirstOrDefault(x => x.JobTriStateQuestionId == answerDto.JobTriStateQuestionId);

                if (existingAnswer == null)
                {
                    existingAnswer = new JobVisitTriStateAnswer()
                    {
                        Answer = answerDto.Answer,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        JobVisitId = jobVisit.Id,
                        JobTriStateQuestionId = answerDto.JobTriStateQuestionId
                    };
                    jobVisit.JobVisitTriStateAnswers.Add(existingAnswer);
                }
                else
                {
                    existingAnswer.Answer = answerDto.Answer;
                    existingAnswer.ModifiedBy = userInfo.Username;
                    existingAnswer.ModifiedDate = DateTime.UtcNow;
                }
            }

            private async Task UpsertJobTriStateAnswer(
                Job job,
                Command.QuestionAnswerDto answerDto,
                UserInfo userInfo,
                CancellationToken cancellationToken,
                Guid correlationId)
            {
                var existingAnswer = job.JobTriStateAnswers
                    .FirstOrDefault(x => x.JobTriStateQuestionId == answerDto.JobTriStateQuestionId);

                // if adding a new question
                if (existingAnswer is null)
                {
                    _logger.LogInformation("Entered into GenerateJobTriStateAnswerAddedEvent: ");
                    var newAnswer = new JobTriStateAnswer
                    {
                        JobId = job.Id,
                        Answer = answerDto.Answer,
                        JobTriStateQuestionId = answerDto.JobTriStateQuestionId,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow
                    };
                    job.JobTriStateAnswers.Add(newAnswer);
                    _logger.LogInformation("Calling  GenerateJobTriStateAnswerAddedEvent: ");
                    await _context.OutboxMessages.AddAsync(GenerateJobTriStateAnswerAddedEvent(newAnswer, correlationId, userInfo), cancellationToken);
                }
                else
                {
                    // update the existing record
                    _logger.LogInformation("Entered into GenerateJobTriStateAnswerUpdatedEvent: ");
                    existingAnswer.Answer = answerDto.Answer;
                    existingAnswer.ModifiedBy = userInfo.Username;
                    existingAnswer.ModifiedDate = DateTime.UtcNow;
                    _logger.LogInformation("Calling GenerateJobTriStateAnswerUpdatedEvent: ");
                    await _context.OutboxMessages.AddAsync(GenerateJobTriStateAnswerUpdatedEvent(existingAnswer, correlationId, userInfo), cancellationToken);
                }
            }

            private static OutboxMessage GenerateJobTriStateAnswerAddedEvent(JobTriStateAnswer jobTriStateAnswer, Guid correlationId, UserInfo userInfo)
            {
                var dto = new JobTriStateAnswerAddedEvent.AnswerAddedDto()
                {
                    Value = jobTriStateAnswer.Answer,
                    CreatedBy = jobTriStateAnswer.CreatedBy,
                    CreatedDate = jobTriStateAnswer.CreatedDate,
                    JobId = jobTriStateAnswer.JobId,
                    JobTriStateAnswerId = jobTriStateAnswer.Id,
                    JobTriStateQuestionId = jobTriStateAnswer.JobTriStateQuestionId
                };
                var addedEvent = new JobTriStateAnswerAddedEvent(dto, correlationId);
                return new OutboxMessage(addedEvent.ToJson(), nameof(JobTriStateAnswerAddedEvent), correlationId, userInfo.Username);
            }

            private static OutboxMessage GenerateJobTriStateAnswerUpdatedEvent(JobTriStateAnswer jobTriStateAnswer, Guid correlationId, UserInfo userInfo)
            {
                var dto = new JobTriStateAnswerUpdatedEvent.AnswerUpdatedDto()
                {
                    Value = jobTriStateAnswer.Answer,
                    UpdatedBy = jobTriStateAnswer.ModifiedBy,
                    UpdatedDate = jobTriStateAnswer.ModifiedDate ?? DateTime.UtcNow,
                    JobId = jobTriStateAnswer.JobId,
                    JobTriStateAnswerId = jobTriStateAnswer.Id,
                    JobTriStateQuestionId = jobTriStateAnswer.JobTriStateQuestionId
                };
                var addedEvent = new JobTriStateAnswerUpdatedEvent(dto, correlationId);
                return new OutboxMessage(addedEvent.ToJson(), nameof(JobTriStateAnswerUpdatedEvent), correlationId, userInfo.Username);
            }

        }
    }
}