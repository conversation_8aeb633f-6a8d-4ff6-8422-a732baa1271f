﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale
{
    public class GetAssociatedContactsAndBusinesses
    {
        public class Command : IRequest<Dto>
        {
            public Guid FranchiseSetId { get; set; }
            public Guid FranchiseId { get; set; }
        }

        public class Dto
        {
            public List<ContactDto> Contacts { get; set; }
            public List<BusinessDto> Businesses { get; set; }
        }

        public class ContactDto
        {
            public Guid Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
        }

        public class BusinessDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobReadOnlyDataContext _jobDataContext;
            private readonly ILogger<GetAssociatedContactsAndBusinesses> _logger;

            public Handler(JobReadOnlyDataContext jobDataContext, ILogger<GetAssociatedContactsAndBusinesses> logger)
            {
                _jobDataContext = jobDataContext;
                _logger = logger;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{franchiseSetId}{franchiseId}", request.FranchiseSetId, request.FranchiseId);

                _logger.LogInformation("Begin query with: {@request}", request);

                var contact = await (
                    from j in _jobDataContext.Jobs
                    join jcm in _jobDataContext.JobContactMap on j.Id equals jcm.JobId
                    join c in _jobDataContext.Contacts on jcm.ContactId equals c.Id
                    where j.FranchiseSetId == request.FranchiseSetId 
                          && j.FranchiseId == request.FranchiseId
                    select new ContactDto { Id = c.Id, FirstName = c.FirstName, LastName = c.LastName })
                    .ToListAsync(cancellationToken: cancellationToken);

                var possibleMissingContacts = await HandleContactsMissingFromJobContactMap(request.FranchiseSetId, request.FranchiseId, cancellationToken);

                if (!contact.Any())
                    _logger.LogWarning("No Contacts found for FranchiseSetId: {franchiseSetId} and FranchiseId: {franchiseId}", request.FranchiseSetId, request.FranchiseId);

                var business = await (
                    from j in _jobDataContext.Jobs
                    join jbm in _jobDataContext.JobBusinessMaps on j.Id equals jbm.JobId
                    join b in _jobDataContext.Businesses on jbm.BusinessId equals b.Id
                    where j.FranchiseSetId == request.FranchiseSetId
                          && j.FranchiseId == request.FranchiseId
                    select new BusinessDto { Id = b.Id, Name = b.Name })
                    .ToListAsync(cancellationToken: cancellationToken);

                if (!business.Any())
                    _logger.LogWarning("No Business found for FranchiseSetId: {franchiseSetId} and FranchiseId: {franchiseId}", request.FranchiseSetId, request.FranchiseId);
                
                return new Dto()
                {
                    Businesses = business.DistinctBy(c => c.Id).ToList(),
                    Contacts = contact.Concat(possibleMissingContacts).DistinctBy(c => c.Id).ToList()
                };
            }

            private async Task<List<ContactDto>> HandleContactsMissingFromJobContactMap(Guid franchiseSetId, Guid franchiseId, CancellationToken cancellationToken)
            {
                var possibleMissingContacts = new List<ContactDto>();
                var possibleMissingContactsFromJobContactMaps = await _jobDataContext.Jobs
                        .Include(x => x.Customer)
                        .Include(x => x.Caller)
                    .Where(x => x.FranchiseSetId == franchiseSetId && x.FranchiseId == franchiseId)
                    .ToListAsync(cancellationToken);

                foreach (var job in possibleMissingContactsFromJobContactMaps)
                {
                    if (job.CallerId.HasValue)
                        possibleMissingContacts.Add(Map(job.Caller));

                    if (job.CustomerId.HasValue)
                        possibleMissingContacts.Add(Map(job.Customer));
                }

                return possibleMissingContacts;
            }
        }

        private static ContactDto Map(Contact contact)
            => new ContactDto()
            {
                Id = contact.Id,
                FirstName = contact.FirstName,
                LastName = contact.LastName
            };

        private static BusinessDto Map(Business business)
        {
            return new BusinessDto()
            {
                Id = business.Id,
                Name = business.Name
            };
        }
    }
}
