﻿using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones.ZoneValidation
{
    public interface IZoneValidationMapper
    {
        public Task<ZoneForValidationDto> ExecuteAsync(Zone zone, Dictionary<Guid, WaterClassDto> waterClassLookups, DateTime? fnolDate);

    }
}