﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.ClaimNumber
{
    public class SetJobClaimNumber
    {
        public class Command : IRequest<Unit>
        {
            public Guid JobId { get; set; }
            public string ClaimNumber { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.ClaimNumber).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILogger<SetJobClaimNumber> _logger;

            public Handler(JobDataContext context, IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor, ILogger<SetJobClaimNumber> logger)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var logScope = _logger.BeginScope("{jobId}", request.JobId);

                _logger.LogInformation("Begin handler with {@request}", request);

                var job = await _context.Jobs
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException();

                request.ClaimNumber = request.ClaimNumber?.Trim() ?? null;

                job.InsuranceClaimNumber = request.ClaimNumber;

                await RaiseEvents(request);

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task RaiseEvents(Command request)
            {
                var user = _userInfoAccessor.GetUserInfo();

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var eventDto = new ClaimNumberUpdatedEvent.ClaimNumberUpdatedDto(
                    request.JobId, request.ClaimNumber, user.Username, DateTime.UtcNow);

                var updatedEvent = new ClaimNumberUpdatedEvent(eventDto, correlationId);

                await GenerateOutboxMessage(updatedEvent.ToJson(), nameof(ClaimNumberUpdatedEvent),
                    updatedEvent.CorrelationId, eventDto.UpdatedBy);
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _context.OutboxMessages.AddAsync(newEvent);
            }
        }
    }
}