﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyMobileData
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> MobileDataIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyMobileData>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyMobileData> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyMobileData> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(MobileData));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.MobileDataIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                //var sourceEntities = await GetSourceEntitiesAsync(request.MobileDataIds, cancellationToken);
                var mobileDataTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedMobileDataIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(mobileDataTargetIds, 
                    GetMobileDataIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<MobileData, ResaleMobileData>(
                    request.ResaleId,
                    null,
                    alreadyCopiedMobileDataIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.MobileData.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<MobileData>> GetSourceEntitiesAsync(List<Guid> mobileDataIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var mobileData = await _context.MobileData
                    .Where(md => mobileDataIds.Contains(md.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", mobileData.Count);
                return mobileData;
            }

            private async Task<List<Guid>> GetMobileDataIdsAsync(List<Guid?> mobileDataTargetIds, CancellationToken cancellationToken)
            {
                return await _context.MobileData
                    .Where(x => mobileDataTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
