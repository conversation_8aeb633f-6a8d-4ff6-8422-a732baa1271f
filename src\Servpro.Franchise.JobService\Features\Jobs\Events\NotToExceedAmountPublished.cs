﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;

using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class NotToExceedAmountPublished
    {
        public class Event : NotToExceedAmountPublishedEvent, IRequest
        {
            public Event(NotToExceedAmountPublishedEvent.NotToExceedAmountPublishedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<NotToExceedAmountPublished> _logger;

            public Handler(JobDataContext db, ILogger<NotToExceedAmountPublished> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler began with {@request}", request);

                var job = await _db.Jobs.FirstOrDefaultAsync(j => j.Id == request.NotToExceedAmountPublished.JobId, cancellationToken: cancellationToken);

                if (job == null)
                {
                    throw new ResourceNotFoundException("Job not found");
                }

                job.NteAmount = request.NotToExceedAmountPublished.NteAmount;
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}