using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Xact;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class FormUploadedToXact
    {
        #region Event
        public class Event : FormUploadedToXactEvent, IRequest
        {
            public Event(Guid jobId, IEnumerable<Guid> formIds, Guid correlationId) : base(jobId, formIds, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db,
                ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{@event} received.", request);

                var uploadedForms = await _db.MediaMetadata
                    .Where(mmdt => mmdt.JobId == request.JobId && request.FormIds.Contains(mmdt.Id))
                    .ToListAsync(cancellationToken);

                uploadedForms.ForEach(f => { f.IsUploadedToXact = true; });

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{event} forms for job {jobId} updated successfully.", nameof(FormUploadedToXactEvent), request.JobId);

                return Unit.Value;
            }
        }
        #endregion Handler
    }
}
