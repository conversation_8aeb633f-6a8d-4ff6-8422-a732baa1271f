﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobAreaDeletedEvent;
using Servpro.Franchise.JobService.Models;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.RoomDeletedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class DeleteRoom
    {
        public class Command : IRequest
        {
            public Command(Guid jobId, Guid roomId, UserInfo userInfo)
            {
                JobId = jobId;
                RoomId = roomId;
            }

            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
            public UserInfo UserInfo { get; set; }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async System.Threading.Tasks.Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var jobArea = await _context.JobAreas
                            .Include(x => x.Job)
                            .Include(x => x.Room)
                                .ThenInclude(x => x.RoomFlooringTypesAffected)
                            .Include(j => j.EquipmentPlacements)
                            .FirstOrDefaultAsync(x => x.RoomId == request.RoomId && x.JobId == request.JobId, cancellationToken);

                var room = jobArea?.Room;

                var userInfo = _userInfo.GetUserInfo();

                if (jobArea == null || room is null)
                    throw new ResourceNotFoundException($"Room not found (Id: {request.RoomId}");

                if (jobArea.Job != null)
                {
                    jobArea.Job.JobLocks = await _context.JobLock.Where(x => x.JobId == jobArea.Job.Id).ToListAsync(cancellationToken);
                    jobArea.Job.Tasks = await _context.Tasks.Where(x => x.JobId == jobArea.Job.Id).ToListAsync(cancellationToken);
                }

                if (JobLockUtils.HasLockConflict(jobArea.Job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(jobArea.Job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(jobArea.Job.CurrentJobLock));


                if (room.RoomFlooringTypesAffected != null)
                {
                    DeleteFlooringTypes(room.RoomFlooringTypesAffected);
                }

                if (jobArea.EquipmentPlacements != null)
                {
                    DeleteEquipmentPlacements(jobArea.EquipmentPlacements);
                }

                //Unconfirms the zone if the room belongs to one
                if (jobArea.ZoneId != null)
                { 
                    var task = jobArea.Job.Tasks
                        .FirstOrDefault(t => t.TaskTypeId == TaskTypes.ZoneNotConfirmed
                         && t.ZoneId == jobArea.ZoneId);

                    task.ModifiedDate = DateTime.UtcNow;
                    task.ModifiedBy = userInfo.Username;
                    task.PercentComplete = 0;
                    task.TaskStatusId = TaskStatuses.Active;
                }

                _context.Remove(jobArea);
                _context.Remove(room);

                //Publish Events
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var events = GenerateEvents(correlationId, userInfo, request.JobId);
                await _context.OutboxMessages.AddRangeAsync(events, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;

            }

            private IEnumerable<OutboxMessage> GenerateEvents(Guid correlationId, UserInfo user, Guid jobId)
            {
                var jobAreaDeletedEvent = GenerateJobAreaDeletedEvent(correlationId, user);
                var taskUpdatedEvent = GenerateTaskUpdatedEvent(correlationId, user);
                var roomDeletedEvent = GenerateRoomDeletedEvent(correlationId, user, jobId);

                return jobAreaDeletedEvent
                        .Concat(taskUpdatedEvent)
                        .Concat(roomDeletedEvent)
                        .ToList();
            }

            #region Methods for generating JobAreaDeletedEvent
            private IEnumerable<OutboxMessage> GenerateJobAreaDeletedEvent(
                            Guid correlationId,
                            UserInfo userInfo)
            {
                var jobAreaDeletedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobArea
                        && x.State == EntityState.Deleted)
                    .Select(x => x.Entity as JobArea)
                    .Select(x => new JobAreaDeletedEvent(MapJobAreaDeletedDto(x, userInfo), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JobAreaDeletedEvent), correlationId, userInfo.Username));

                return jobAreaDeletedEvents;
            }

            private JobAreaDeletedDto MapJobAreaDeletedDto(JobArea jobArea, UserInfo userInfo)
                => new JobAreaDeletedDto
                {
                    Id = jobArea.Id,
                    JobId = jobArea.JobId,
                    DeletedBy = userInfo.Name,
                    DeletedDate = DateTime.UtcNow,
                    JobAreaId = jobArea.Id
                };
            #endregion Methods for generating JobAreaDeletedEvent

            #region Methods for generating JournalNoteUpdatedEvent
            private IEnumerable<OutboxMessage> GenerateTaskUpdatedEvent(
                            Guid correlationId,
                            UserInfo userInfo)
            {
                var taskUpdatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Task
                        && x.State == EntityState.Modified)
                    .Select(x => x.Entity as Task)
                    .Select(x => new TaskUpdatedEvent(MapTaskUpdatedEvent(x, userInfo), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(TaskUpdatedEvent), correlationId, userInfo.Username));

                return taskUpdatedEvents;
            }

            private TaskUpdatedEvent.TaskUpdatedEventDto MapTaskUpdatedEvent(Task task, UserInfo userInfo)
                => new TaskUpdatedEvent.TaskUpdatedEventDto
                {
                    JobId = task.JobId,
                    Id = task.Id,
                    Body = task.Body,
                    CancellationDate = task.CancellationDate,
                    CompletionDate = task.CompletionDate,
                    DueDate = task.DueDate,
                    EstimateId = task.EstimateId,
                    FranchiseSetId = task.FranchiseSetId,
                    IsSystem = task.IsSystem,
                    IsTemplate = task.IsTemplate,
                    JobTriStateQuestionId = task.JobTriStateQuestionId,
                    JobVisitId = task.JobVisitId,
                    PercentComplete = task.PercentComplete,
                    ReminderDate = task.ReminderDate,
                    StartDate = task.StartDate,
                    Subject = task.Subject,
                    TaskPriorityId = task.TaskPriorityId,
                    TaskStatusId = task.TaskStatusId,
                    TaskTypeId = task.TaskTypeId,
                    WorkOrderId = task.WorkOrderId,
                    ZoneId = task.ZoneId,
                    ZoneReadingId = task.ZoneReadingId,
                    ModifiedBy = userInfo.Id,
                    ModifiedDate = DateTime.UtcNow
                };
        #endregion Methods for generating JournalNoteUpdatedEvent

        #region Methods for generating RoomDeletedEvent
        private IEnumerable<OutboxMessage> GenerateRoomDeletedEvent(
                            Guid correlationId,
                            UserInfo userInfo,
                            Guid jobId)
            {
                var roomDeletedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Room
                        && x.State == EntityState.Deleted)
                    .Select(x => x.Entity as Room)
                    .Select(x => new RoomDeletedEvent(MapRoomDeletedDto(x, userInfo, jobId), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(RoomDeletedEvent), correlationId, userInfo.Username));

                return roomDeletedEvents;
            }

            private RoomDeletedDto MapRoomDeletedDto(Room room, UserInfo userInfo, Guid jobId)
                => new RoomDeletedDto
                {
                    RoomId = room.Id,
                    JobId = jobId, //room.JobAreas.FirstOrDefault().JobId,
                    DeletedBy = userInfo.Name,
                    DeletedDate = DateTime.UtcNow
                };
            #endregion Methods for generating JobAreaDeletedEvent

            private void DeleteFlooringTypes(ICollection<RoomFlooringTypeAffected> flooringTypeAffected)
            {
                foreach (var flooringType in flooringTypeAffected)
                {
                    _context.Remove(flooringType);
                }
            }

            private void DeleteEquipmentPlacements(ICollection<EquipmentPlacement> equipmentPlacements)
            {
                foreach (var equipmentPlacement in equipmentPlacements)
                {
                    _context.Remove(equipmentPlacement);
                }
            }

        }
    }
}
