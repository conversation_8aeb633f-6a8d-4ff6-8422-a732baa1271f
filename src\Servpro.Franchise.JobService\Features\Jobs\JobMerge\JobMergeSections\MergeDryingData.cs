﻿using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Constants;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeDryingData : IJobMergeSection
    {
        private readonly JobDataContext _db;
        private readonly List<Guid> mobileSketchArtifactIds = new List<Guid>()
        {
            ArtifactTypes.MobileSketchESX,
            ArtifactTypes.MobileSketchJson,
            ArtifactTypes.MobileSketchXML
        };

        public MergeDryingData(JobDataContext db)
        {
            _db = db;
        }
        public async void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            var targetAnsweredQuestions = targetJob.JobTriStateAnswers
                .Select(x => x.JobTriStateQuestionId)
                .ToHashSet();

            //merge visit answers from initial inspection
            var targetVisitAnsweredQuestions = targetJob.JobVisits
                .OrderBy(x => x.Date)
                .FirstOrDefault()?.JobVisitTriStateAnswers
                .Select(x => x.JobTriStateQuestionId)
                .ToHashSet();

            var sourceVisitAnsweredQuestions = sourceJob.JobVisits
                .OrderBy(x => x.Date)
                .FirstOrDefault()?.JobVisitTriStateAnswers
                .Select(x => x.JobTriStateQuestionId)
                .ToHashSet();

            //Mobile Sketch Files contains Rooms data
            sourceJob.MediaMetadata
                .Where(x => !x.IsDeleted && x.MediaTypeId == MediaTypes.Document &&
                            mobileSketchArtifactIds.Contains(x.ArtifactTypeId))
                .Select(x => MapMobileSketchData(x, targetJob.Id))
                .ToList().ForEach(targetJob.MediaMetadata.Add);

            //if source job has answers not in target, add to target
            sourceJob.JobTriStateAnswers
                // the question can only be answered once per job as constraint by DB.
                .Where(x => targetAnsweredQuestions == null || targetAnsweredQuestions.Count() == 0 || !targetAnsweredQuestions.Contains(x.JobTriStateQuestionId))
                .Select(x => MapJobTriStateAnswers(x, targetJob.Id))
                .ToList().ForEach(targetJob.JobTriStateAnswers.Add);

            //update target answers if they exist in source
            foreach (var targetAnswer in targetJob.JobTriStateAnswers)
            {
                var sourceAnswer = sourceJob.JobTriStateAnswers.FirstOrDefault(x => x.JobTriStateQuestionId == targetAnswer.JobTriStateQuestionId);
                targetAnswer.Answer = sourceAnswer?.Answer != null ? sourceAnswer.Answer : targetAnswer.Answer;
            }


            //merge answers for initial visit, add to source job first since target visit is marked to be deleted
            var sourceInitialVisit = sourceJob.JobVisits.OrderBy(x => x.Date).FirstOrDefault();
            var targetInitialVisit = targetJob.JobVisits.OrderBy(x => x.Date).FirstOrDefault();

            if (targetInitialVisit != null && sourceInitialVisit != null)
            {
                sourceInitialVisit.JobVisitTriStateAnswers
                .ToList().ForEach(x =>
                {
                    if (targetVisitAnsweredQuestions.Contains(x.JobTriStateQuestionId))
                    {
                        var targetAnswer = targetInitialVisit.JobVisitTriStateAnswers.FirstOrDefault(t => t.JobTriStateQuestionId == x.JobTriStateQuestionId);
                        targetInitialVisit.JobVisitTriStateAnswers.Remove(targetAnswer);
                        targetInitialVisit.JobVisitTriStateAnswers.Add(MapJobVisitTriStateAnswers(x, x.JobVisitId));
                    }
                    else
                    { 
                        targetInitialVisit.JobVisitTriStateAnswers.Add(MapJobVisitTriStateAnswers(x, x.JobVisitId));

                        var task = targetJob.Tasks.FirstOrDefault(t => t.JobTriStateQuestionId == x.JobTriStateQuestionId);
                        if (task != null)
                        {
                            var journalNotes = targetJob.Tasks.FirstOrDefault(x => x.Id == task.Id).JournalNotes;
                            targetJob.Tasks.Remove(task);
                            targetJob.Tasks.Add(MapTasks(sourceInitialVisit.Tasks.FirstOrDefault(t => t.JobTriStateQuestionId == x.JobTriStateQuestionId), targetJob.Id, sourceInitialVisit.Id));

                            if (journalNotes != null)
                            {
                                var newTaskId = targetJob.Tasks.FirstOrDefault(task => task.JobTriStateQuestionId == x.JobTriStateQuestionId).Id;
                                journalNotes.ToList().ForEach(x => targetJob.JournalNotes.Add(MapJournalNotes(x, targetJob.Id, sourceInitialVisit.Id, newTaskId)));
                            }
                        }
                    }
                });

                targetInitialVisit.JobVisitTriStateAnswers
                .ToList().ForEach(x =>
                {
                    if (x.JobVisitId != sourceInitialVisit.Id)
                    {
                        x.JobVisitId = sourceInitialVisit.Id;
                        _db.Entry(x).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                        targetJob.Tasks.Where(t => t.JobTriStateQuestionId == x.JobTriStateQuestionId).ToList().ForEach(t =>
                        {
                            t.JobVisitId = sourceInitialVisit.Id;
                            t.JobVisit = sourceInitialVisit;
                            _db.Entry(t).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                            t.JournalNotes.ToList().ForEach(tj =>
                            {
                                tj.JobVisitId = sourceInitialVisit.Id;
                                tj.IsDeleted = false;
                                _db.Entry(tj).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                            });
                            targetJob.JournalNotes.Where(j => j.TaskId == t.Id).ToList().ForEach(j =>
                            {
                                j.JobVisitId = sourceInitialVisit.Id;
                                j.IsDeleted = false;
                                _db.Entry(j).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                            });
                        });
                        
                    }
                });
            }

            //no initial visit on source but it exists on target, must set target visit to not be deleted
            if (sourceInitialVisit == null && targetInitialVisit != null)
            {
                _db.Entry(targetInitialVisit).State = Microsoft.EntityFrameworkCore.EntityState.Unchanged;
                targetInitialVisit.JobVisitTriStateAnswers.ToList().ForEach(x => _db.Entry(x).State = Microsoft.EntityFrameworkCore.EntityState.Unchanged);
                targetJob.Tasks.Where(x => x.JobVisitId == targetInitialVisit.Id).ToList().ForEach(x =>
                {
                    _db.Entry(x).State = Microsoft.EntityFrameworkCore.EntityState.Unchanged;
                    x.JournalNotes.ToList().ForEach(jn => _db.Entry(jn).State = Microsoft.EntityFrameworkCore.EntityState.Unchanged);
                });
                targetJob.JournalNotes.Where(x => x.JobVisitId == targetInitialVisit.Id).ToList().ForEach(x => _db.Entry(x).State = Microsoft.EntityFrameworkCore.EntityState.Unchanged);
            }

            if (sourceJob.MobileData != null)
            {
                MapMobileData(sourceJob.MobileData, targetJob.MobileData);
            }
        }

        private JobTriStateAnswer MapJobTriStateAnswers(JobTriStateAnswer sourceJobTriStateAnswer, Guid targetJobId)
        => new JobTriStateAnswer
        {
            JobId = targetJobId,
            JobTriStateQuestionId = sourceJobTriStateAnswer.JobTriStateQuestionId,
            Answer = sourceJobTriStateAnswer.Answer
        };

        private JobVisitTriStateAnswer MapJobVisitTriStateAnswers(JobVisitTriStateAnswer jobVisitTriStateAnswer, Guid jobVisitId)
        => new JobVisitTriStateAnswer
        {
            JobVisitId = jobVisitId,
            JobTriStateQuestionId = jobVisitTriStateAnswer.JobTriStateQuestionId,
            Answer = jobVisitTriStateAnswer.Answer
        };

        private Task MapTasks(Task task, Guid jobId, Guid jobVisitId)
        => new Task
        {
            JobId = jobId,
            JobVisitId = jobVisitId,
            Body = task.Body,
            Subject = task.Subject,
            CancellationDate = task.CancellationDate,
            CompletionDate = task.CompletionDate,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = task.CreatedBy,
            DueDate = task.DueDate,
            EstimateId = task.EstimateId,
            FranchiseSetId = task.FranchiseSetId,
            IsSystem = task.IsSystem,
            IsTemplate = task.IsTemplate,
            JobTriStateQuestionId = task.JobTriStateQuestionId,
            WorkOrderId = task.WorkOrderId,
            PercentComplete = task.PercentComplete,
            TaskTypeId = task.TaskTypeId,
            StartDate = task.StartDate,
            ReminderDate = task.ReminderDate,
            TaskPriorityId = task.TaskPriorityId,
            TaskStatusId = task.TaskStatusId
        };

        private JournalNote MapJournalNotes(JournalNote journalNote, Guid jobId, Guid jobVisitId, Guid taskId)
        => new JournalNote
        {
            JobId = jobId,
            JobVisitId = jobVisitId,
            TaskId = taskId,
            Note = journalNote.Note,
            Subject = journalNote.Subject,
            ActionDate = journalNote.ActionDate,
            Author = journalNote.Author,
            CategoryId = journalNote.CategoryId,
            CreatedBy = journalNote.CreatedBy,
            CreatedDate = DateTime.UtcNow,
            IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage,
            IsDeleted = journalNote.IsDeleted,
            RoomFlooringTypeAffectedId = journalNote.RoomFlooringTypeAffectedId,
            JobAreaMaterialId = journalNote.JobAreaMaterialId,
            OtherName = journalNote.OtherName,
            TypeId = journalNote.TypeId,
            VisibilityId = journalNote.VisibilityId,
            RuleIds = journalNote.RuleIds
        };

        private MediaMetadata MapMobileSketchData(MediaMetadata sourceMedia, Guid jobId) => new MediaMetadata
        {
            CreatedDate = DateTime.UtcNow,
            CreatedBy = sourceMedia.CreatedBy,
            BucketName = sourceMedia.BucketName,
            FranchiseSetId = sourceMedia.FranchiseSetId,
            JobId = jobId,
            MediaTypeId = sourceMedia.MediaTypeId,
            ArtifactTypeId = sourceMedia.ArtifactTypeId,
            FormTemplateId = sourceMedia.FormTemplateId,
            Name = sourceMedia.Name,
            Description = sourceMedia.Description,
            MediaPath = sourceMedia.MediaPath,
            IsDeleted = sourceMedia.IsDeleted,
            IsForUpload = sourceMedia.IsForUpload,
            UploadedSuccessfully = sourceMedia.UploadedSuccessfully,
            JobSketchId = sourceMedia.JobSketchId,
            JobSketch = sourceMedia.JobSketch,
            JobInvoiceId = sourceMedia.JobInvoiceId,
            JobInvoice = sourceMedia.JobInvoice,
            SignedDate = sourceMedia.SignedDate,
            FormVersion = sourceMedia.FormVersion,
            SyncDate = sourceMedia.SyncDate,
            ArtifactDate = sourceMedia.ArtifactDate,
            Comment = sourceMedia.Comment            
        };

        public void MapMobileData(MobileData sourceMobileData, MobileData targetMobileData)
        {
            if (sourceMobileData != null)
            {
                targetMobileData.OccupantsId = sourceMobileData.OccupantsId ?? targetMobileData.OccupantsId;
                targetMobileData.BuildOutDensityId = sourceMobileData.BuildOutDensityId ?? targetMobileData.BuildOutDensityId;
                targetMobileData.WetDocumentsId = sourceMobileData.WetDocumentsId ?? targetMobileData.WetDocumentsId;
                targetMobileData.ProjectRangeId = sourceMobileData.ProjectRangeId ?? targetMobileData.ProjectRangeId;
            }
        }
    }
}
