﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobDateUpdated
    {
        public class Event : JobDateUpdatedEvent, IRequest
        {
            public Event(JobDateUpdatedDto jobDateUpdatedDto, Guid correlationId) : base(jobDateUpdatedDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobDateUpdated> _logger;

            public Handler(JobDataContext db, ILogger<JobDateUpdated> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with: {@request}", request);

                var jobDateUpdatedDto = request.JobDateUpdated;

                var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobDateUpdatedDto.JobId, cancellationToken);
                _logger.LogDebug("Job fetched: {@job}", job);

                if (job == null)
                {
                    _logger.LogWarning("The Job With Id({ID}) Does Not Exist", jobDateUpdatedDto.JobId);
                    return Unit.Value;
                }

                job.SetOrUpdateDate(jobDateUpdatedDto.JobDateTypeId, jobDateUpdatedDto.Date);

                if (jobDateUpdatedDto.JobDateTypeId == JobDateTypes.LossOccurred)
                {
                    _logger.LogDebug("Updating DateOfLoss for JobId={JobId} to {Date}", job.Id, jobDateUpdatedDto.Date);
                    //This date is in both JobDates and DateOfLoss column, hence updating both
                    //Also this should update DateOfLoss on WipRecord
                    job.DateOfLoss = jobDateUpdatedDto.Date.Value;
                }

                if (jobDateUpdatedDto.Date.HasValue &&
                    jobDateUpdatedDto.JobDateTypeId == Servpro.Franchise.LookupService.Constants.JobDateTypes.JobNotSold)
                {
                    var wipRecord = await _db.WipRecords.FirstOrDefaultAsync(x => x.Id == jobDateUpdatedDto.JobId, cancellationToken);
                    if (wipRecord != null)
                    {
                        _logger.LogDebug("Updating NotSoldOrCancelledDate for WipRecord JobId={JobId} to {Date}", jobDateUpdatedDto.JobId, jobDateUpdatedDto.Date);
                        wipRecord.NotSoldOrCancelledDate = jobDateUpdatedDto.Date.Value;
                    }
                }

                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}