﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class JobContactTypes
    {
        public static readonly Guid Customer                         = new Guid("00000001-000A-5365-7276-70726F496E63");
        public static readonly Guid ReferralSource                   = new Guid("00000002-000A-5365-7276-70726F496E63");
        public static readonly Guid Agent                            = new Guid("00000006-000A-5365-7276-70726F496E63");
        public static readonly Guid Adjuster                         = new Guid("00000007-000A-5365-7276-70726F496E63");
        public static readonly Guid Caller                           = new Guid("0000000C-000A-5365-7276-70726F496E63");
        public static readonly Guid OnSite                           = new Guid("00000010-000A-5365-7276-70726F496E63");
        public static readonly Guid Occupant                         = new Guid("00000011-000A-5365-7276-70726F496E63");
        public static readonly Guid DefaultBilling                   = new Guid("00000012-000A-5365-7276-70726F496E63");
        public static readonly Guid AfterHours                       = new Guid("00000013-000A-5365-7276-70726F496E63");
        public static readonly Guid MortgageCompanyRepresentative    = new Guid("00000014-000A-5365-7276-70726F496E63");
        public static readonly Guid AdditionalContact                = new Guid("00000016-000A-5365-7276-70726F496E63");
        public static readonly Guid Other                            = new Guid("00000018-000A-5365-7276-70726F496E63");
        public static readonly Guid PublicAdjuster                   = new Guid("00000019-000A-5365-7276-70726F496E63");
        public static readonly Guid CustomerServiceAgent             = new Guid("D9D84750-02A6-4B68-A39A-9B0EEA3B3504");
        public static readonly Guid ProductionManagerCrewChief       = new Guid("D9F2BC67-**************-3BA475754B31");
        public static readonly Guid IndependentAdjuster              = new Guid("A1368BE8-D617-4815-9F39-47ED63A2D5AF");
        public static readonly Guid InsuranceAdjuster                = new Guid("2398C9CD-DC44-4F84-AD59-D2C8BADF2362");
        public static readonly Guid PointOfContact                   = new Guid("D7869DD5-D0CD-4589-B04E-CA4C844668A6");
    }
}
