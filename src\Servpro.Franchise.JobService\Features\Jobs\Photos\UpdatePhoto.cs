﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class UpdatePhoto
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid MediaMetadataId { get; set; }
            public string Comments { get; set; }
            public DateTime? ArtifactDate { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public string Name { get; set; }
            public bool IsForUpload { get; set; }
        }

        public class Validator : AbstractValidator<UpdatePhoto.Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public bool UpdatedSuccessfully { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _db;
            private readonly ILookupServiceClient _lookupServiceClient;
            public Handler(JobDataContext db, ILookupServiceClient lookupServiceClient)
            {
                _db = db;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Dto> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var media = await _db.MediaMetadata
                    .FirstOrDefaultAsync(q => q.Id == request.MediaMetadataId, cancellationToken: cancellationToken);
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == request.ArtifactTypeId);
               
                media.Comment = request.Comments;
                media.Name = request.Name;
                media.IsForUpload = (artifactType?.Required ?? false) || request.IsForUpload;
                media.ArtifactTypeId = request.ArtifactTypeId;
                media.ArtifactDate = request.ArtifactDate;
                
                await _db.SaveChangesAsync(cancellationToken);

                return new Dto
                {
                    UpdatedSuccessfully = true
                };
            }

        }
    }
}
