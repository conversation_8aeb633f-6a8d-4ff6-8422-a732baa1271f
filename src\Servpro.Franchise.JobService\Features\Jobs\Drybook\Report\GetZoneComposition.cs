﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities.Calculators;
using Servpro.Franchise.JobService.Infrastructure.Utilities.Helpers;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using Servpro.Standards.Drying;
using Servpro.Standards.Drying.Calculators.Abstracts;
using Servpro.Standards.Drying.Enums;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using JobDateTypes = Servpro.Franchise.LookupService.Constants.JobDateTypes;
using TaskStatuses = Servpro.Franchise.LookupService.Constants.TaskStatuses;
using TaskTypes = Servpro.Franchise.LookupService.Constants.TaskTypes;
using ZoneTypes = Servpro.Franchise.LookupService.Constants.ZoneTypes;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetZoneComposition
    {
        public class Query : IRequest<ZoneCompositionDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }


        public class Handler : IRequestHandler<Query, ZoneCompositionDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;
            private Dictionary<Guid, int> _waterCategories;
            private Dictionary<Guid, int> _waterClasses;
            private DryingStandard _standard;

            private AirMoverRequirementsCalculator AirMoverRequirementsCalculator;
            private ZoneCalculator ZoneCalculator;
            private RoomCalculator RoomCalculator;

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                ILookupServiceClient lookupServiceClient, ILogger<GenerateDryingReport> logger)
            {
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            private EquipmentPlacementRequirementsProximity GetAirMoverRequirementsProximityBasedOnRooms(List<RoomScope> rooms)
            {
                foreach (var room in rooms)
                {
                    if (room.AirMoversRequirementsProximity != EquipmentPlacementRequirementsProximity.Acceptable &&
                        room.AirMoversRequirementsProximity != EquipmentPlacementRequirementsProximity.HighAcceptable)
                        return room.AirMoversRequirementsProximity;
                }

                return EquipmentPlacementRequirementsProximity.Acceptable;
            }

            public async Task<ZoneCompositionDto> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("DryingReportLog - starting GetZoneComposition for jobId: {Id}", request.Job.Id);

                _logger.LogDebug("DryingReportLog - GetZoneComposition - getting lookups");
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                _waterCategories = lookups.WaterCategories.ToDictionary(wc => wc.Id, wc => wc.Ordinal ?? 0);
                _waterClasses = lookups.WaterClasses.ToDictionary(wc => wc.Id, wc => wc.Severity);

                var jobId = request.Job.Id;
                var job = await _context.Jobs.AsNoTracking()
                                .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.Job.Id}");

                var model = new ZoneCompositionDto();
                _logger.LogDebug("DryingReportLog - GetZoneComposition - getting AirMoverCalculator - for jobId: {Id}", job.Id);
                AirMoverRequirementsCalculator = GetAirmoverCalculator(job, request.FranchiseTimeZone);
                if (AirMoverRequirementsCalculator == null) return model;

                _logger.LogDebug("DryingReportLog - GetZoneComposition - getting zones for jobId: {Id}", job.Id);
                var data = await GetDryingReportZoneCompositionDto(job, lookups, cancellationToken);
                if (data == null) return model;

                ZoneCalculator = new ZoneCalculator();
                RoomCalculator = new RoomCalculator();

                _logger.LogDebug("DryingReportLog - GetZoneComposition - work through zones for jobId: {Id}", job.Id);
                foreach (var zone in data.ZoneDtos)
                {
                    var dehuValidationType = ZoneCalculator.GetDehuValidationType(zone);
                    var minDehuPpdsRequired = ZoneCalculator.GetMinDehuPpdsRequired(zone, _standard, _waterClasses);
                    var minDehuCfmsRequired = ZoneCalculator.GetMinDehuCfmsRequired(zone, _standard, _waterClasses);

                    var equipmentPlacementDtos = zone.RoomDtos.SelectMany(
                                rd => rd.EquipmentPlacementDtos.Where(epd => !zone.IsConfirmed || epd.IsUsedInValidation))
                            .ToList();

                    var dehuPpdsPlaced =
                        equipmentPlacementDtos.Where(
                            pd =>
                                pd.EquipmentTypeId == ZoneEquipmentTypes.ConvDehuEquipmentTypeId ||
                                pd.EquipmentTypeId == ZoneEquipmentTypes.LgrDehuEquipmentTypeId).Sum(pd => pd.Ppd);
                    var dehuCfmsPlaced =
                        equipmentPlacementDtos.Where(pd => pd.EquipmentTypeId == ZoneEquipmentTypes.DesiccantDehuEquipmentTypeId)
                            .Sum(pd => pd.Cfm);

                    var airMoverValidationMethod = zone.AirMoverCalculationTypeId == ZoneEquipmentTypes.LinearFeetAirmoverCalcTypeId
                        ? AirMoverValidationMethod.LinearFeet
                        : AirMoverValidationMethod.SquareFeet;

                    var waterClassValue = _waterClasses.ContainsKey(zone.WaterClassId) ? _waterClasses[zone.WaterClassId] : 0;
                    var roomScopes = GetRoomScope(zone, zone.IsConfirmed);

                    var countAirMoversPlaced =
                        equipmentPlacementDtos.Count(pd => pd.BaseEquipmentTypeId == ZoneEquipmentTypes.AirMoverBaseEquipmentTypeId);

                    var floorLinFtAffected = roomScopes.Sum(x => x.FloorLinearAffected ?? 0);

                    var validationMethod =
                            AirMoverRequirementsCalculator.CanGetRequirementsLinear
                                ? airMoverValidationMethod
                                : AirMoverValidationMethod.SquareFeet;

                    var airMoverRequirements =
                     validationMethod == AirMoverValidationMethod.LinearFeet && AirMoverRequirementsCalculator.CanGetRequirementsLinear ? AirMoverRequirementsCalculator.GetRequirementsLinear(floorLinFtAffected)
                       : AirMoverRequirementsCalculator.Type == AirMoverRequirementsCalculatorType.Zone ?
                           ZoneCalculator.GetAirMoverSquareFeetRequirments(roomScopes) : null;

                    _logger.LogDebug("DryingReportLog - GetZoneComposition - zone output for waterCategory: jobId: {Id}; zoneId: {ZoneId}, waterCategory: {WaterCategoryId} ", job.Id, zone.ZoneId, zone.WaterCategoryId);
                    if (_waterCategories.ContainsKey(zone.WaterCategoryId))
                    {
                        var zoneComposition = new ZoneComposition
                        {
                            ZoneName = zone.ZoneName,
                            WaterCategory = _waterCategories[zone.WaterCategoryId],
                            WaterClass = waterClassValue,
                            IsWaterClassOverridden = zone.IsWaterClassOverridden,
                            IsConfirmed = zone.IsConfirmed,
                            ZonePercentAffected = roomScopes == null || roomScopes.Select(x => x.FloorArea).Sum() <= 0m
                            ? 0m
                            : roomScopes.Select(s => s.FloorAreaAffected ?? 0m).Sum() /
                              roomScopes.Select(rs => rs.FloorArea ?? 0m).Sum(),
                            // Dehus
                            MinDehuPpdsRequired = minDehuPpdsRequired,
                            MinDehuCfmsRequired = minDehuCfmsRequired,
                            DehuPpdsPlaced = dehuPpdsPlaced,
                            DehuCfmsPlaced = dehuCfmsPlaced,
                            CountDehusPlaced =
                            equipmentPlacementDtos.Count(pd => pd.BaseEquipmentTypeId == ZoneEquipmentTypes.DehuBaseEquipmentTypeId),
                            DehuValidationType = dehuValidationType,
                            DehusRequirementsProximity =
                            ZoneCalculator.GetDehusRequirementsProximity(dehuValidationType, dehuPpdsPlaced, minDehuPpdsRequired,
                                dehuCfmsPlaced, minDehuCfmsRequired),
                            // AirMovers
                            CountAirMoversPlaced = countAirMoversPlaced,
                            MinAirMoversRequired = airMoverRequirements != null ? airMoverRequirements.Minimum : 0,
                            MaxAirMoversRequired = airMoverRequirements != null ? airMoverRequirements.Maximum : 0,
                            AirMoversRequirementsProximity = GetAirMoverRequirementsProximityBasedOnRooms(roomScopes),
                            AirMoverValidationMethod =
                            AirMoverRequirementsCalculator.CanGetRequirementsLinear
                                ? airMoverValidationMethod
                                : AirMoverValidationMethod.SquareFeet,
                            // Rooms
                            RoomScopes = roomScopes,
                        };

                        model.ZoneCompositions.Add(zoneComposition);
                    }
                }

                _logger.LogInformation("DryingReportLog - GetZoneComposition -completed for jobId: {Id}", job.Id);
                model.ZoneCompositions = model.ZoneCompositions.OrderBy(x => x.ZoneName).ToList();
                return model;
            }

            private async Task<DryingReportZoneCompositionDto> GetDryingReportZoneCompositionDto(Job job, GetLookups.Dto lookups, CancellationToken cancellationToken)
            {
                var waterClasses = lookups.WaterClasses;
                var zones = await _context.Zones
                    .AsNoTracking()
                    .Include(z => z.JobAreas)
                        .ThenInclude(y => y.Room)
                            .ThenInclude(z => z.RoomFlooringTypesAffected)
                    .Include(z => z.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                            .ThenInclude(y => y.Equipment)
                                .ThenInclude(z => z.EquipmentModel)
                                    .ThenInclude(z => z.EquipmentType)
                    .Include(x => x.ZoneReadings)
                    .Where(z => z.JobId == job.Id &&
                           z.ZoneTypeId == ZoneTypes.Drying)
                    .ToListAsync(cancellationToken);

                var zonesDto = new List<ZoneDto>();

                zones.ForEach(z =>
                {
                    var waterClass = waterClasses.FirstOrDefault(x => x.Id == z.WaterClassId);

                    var rooms = z.JobAreas.Where(ja => ja.EndJobVisit == null && ja.Room != null)
                        .Select(ja =>
                        {
                            return new RoomDto
                            {
                                RoomName = ja.Name,
                                RoomVolumeCubicInches = ja.Room.RoomVolumeCubicInches,
                                OffsetRoomVolumeCubicInches = ja.Room.OffsetRoomVolumeCubicInches,
                                MissingRoomVolumeCubicInches = ja.Room.MissingRoomVolumeCubicInches,
                                FloorAreaSquareInches = ja.Room.FloorAreaSquareInches,
                                OffsetFloorAreaSquareInches = ja.Room.OffsetFloorAreaSquareInches,
                                MissingFloorAreaSquareInches = ja.Room.MissingFloorAreaSquareInches,
                                FloorPerimeterInches = ja.Room.FloorPerimeterInches,
                                OffsetFloorPerimeterInches = ja.Room.OffsetFloorPerimeterInches,
                                MissingFloorPerimeterInches = ja.Room.MissingFloorPerimeterInches,
                                AreaAffectedPercentage = ja.Room.AreaAffectedPercentage,
                                WallAreaSquareInches = ja.Room.WallAreaSquareInches,
                                OffsetWallAreaSquareInches = ja.Room.OffsetWallAreaSquareInches,
                                MissingWallAreaSquareInches = ja.Room.MissingWallAreaSquareInches,
                                WallAffectedSquareInches = ja.Room.AffectedWallAreaSquareInches,
                                WallAffectedAreaAbove2FeetSquareInches = ja.Room.AffectedWallAreaAbove2FeetSquareInches,
                                WallAffectedAreaBelow2FeetSquareInches = ja.Room.AffectedWallAreaBelow2FeetSquareInches,
                                CeilingAreaSquareInches = ja.Room.CeilingAreaSquareInches,
                                OffsetCeilingAreaSquareInches = ja.Room.OffsetCeilingAreaSquareInches,
                                MissingCeilingAreaSquareInches = ja.Room.MissingCeilingAreaSquareInches,
                                CeilingAffectedSquareInches = ja.Room.AffectedCeilingAreaSquareInches,
                                SubRoomsCount = ja.Room.SubRoomsCount,
                                OffsetsInsetsCount = ja.Room.OffsetsInsetsCount,
                                FlooringDtos = ja.Room.RoomFlooringTypesAffected.Select(rfta =>
                                {
                                    return new FlooringDto
                                    {
                                        TotalSquareFeet = rfta.TotalSquareFeet,
                                        AffectedSquareFeet = rfta.AffectedSquareFeet,
                                        AffectedPercentage = rfta.AffectedPercentage,
                                        SavedSquareFeet = rfta.SavedSquareFeet,
                                        FlooringType = rfta.FlooringTypeId.ToString()
                                    };
                                }).ToList(),
                                EquipmentPlacementDtos = ja.EquipmentPlacements.Select(ep =>
                                {
                                    return new EquipmentPlacementDto
                                    {
                                        BaseEquipmentTypeId = ep.Equipment.EquipmentModel.EquipmentType?.BaseEquipmentTypeId,
                                        EquipmentTypeId = ep.Equipment.EquipmentModel.EquipmentTypeId,
                                        IsUsedInValidation = ep.IsUsedInValidation,
                                        ManufacturerName = ep.Equipment.EquipmentModel.ManufacturerName,
                                        ModelName = ep.Equipment.EquipmentModel.Name,
                                        Ppd = ep.Equipment.PintsPerDay ?? 0,
                                        Amps = ep.Equipment.EquipmentModel.Amps,
                                        Cfm = ep.Equipment.CubicFeetPerMinute ?? 0
                                    };
                                }).ToList(),
                                OffsetSpaceDtos = ja.Room.OffsetSpaces.Select(os =>
                                {
                                    return new OffsetSpaceDto
                                    {
                                        Name = os.Name,
                                        DepthTotalInches = os.DepthTotalInches,
                                        HeightTotalInches = os.HeightTotalInches,
                                        WidthTotalInches = os.WidthTotalInches,
                                        OffsetSpaceTypeId = os.OffsetSpaceTypeId
                                    };
                                }).ToList()
                            };
                        }).ToList();
                    zonesDto.Add(new ZoneDto
                    {
                        ZoneId = z.Id,
                        ZoneName = z.Name,
                        WaterCategoryId = z.WaterCategoryId,
                        WaterClassId = z.WaterClassId,
                        IsWaterClassOverridden = z.WaterClassOverridden,
                        IsConfirmed = z.Tasks.Any(t => t.TaskTypeId == TaskTypes.ZoneNotConfirmed && t.TaskStatusId == TaskStatuses.Completed),
                        AirMoverCalculationTypeId = z.AirMoverCalculationTypeId,
                        MinimumAirFiltersPerLinearFootFactor = waterClass.MinimumAirFiltersPerLinearFootFactor,
                        MaximumAirFiltersPerLinearFootFactor = waterClass.MaximumAirFiltersPerLinearFootFactor,
                        MinimumAirFiltersPerSquareFootFactor = waterClass.MinimumAirFiltersPerSquareFootFactor,
                        MaximumAirFiltersPerSquareFootFactor = waterClass.MaximumAirFiltersPerSquareFootFactor,
                        RoomDtos = rooms
                    });
                });

                return new DryingReportZoneCompositionDto { ZoneDtos = zonesDto };
            }

            private AirMoverRequirementsCalculator GetAirmoverCalculator(Job job, TimeZoneDto primaryTimeZone = null)
            {
                var fnolDate = job.JobDates
                    .First(x => x.JobDateTypeId == JobDateTypes.LossReceived)
                    .Date;

                _standard = new DryingStandardFactory().GetEffectiveStandard(fnolDate.Date.GetLocalFranchiseDateTime(primaryTimeZone));
                var amCalculator = _standard.AirMoverRequirementsCalculator;

                return amCalculator;

            }

            private List<RoomScope> GetRoomScope(ZoneDto zone, bool isConfirmed = false)
            {
                var roomScopes = zone.RoomDtos
                    .Select(MapFromRoom)
                    .OrderBy(r => r.RoomName)
                    .ToList();

                var airMoverValidationMethod =
                        zone.AirMoverCalculationTypeId == ZoneEquipmentTypes.LinearFeetAirmoverCalcTypeId
                            ? AirMoverValidationMethod.LinearFeet
                            : AirMoverValidationMethod.SquareFeet;

                var validationMethod =
                       AirMoverRequirementsCalculator.CanGetRequirementsLinear
                           ? airMoverValidationMethod
                           : AirMoverValidationMethod.SquareFeet;

                foreach (var room in roomScopes)
                {
                    var airMoverRequirement = new AirMoverRequirements() { Maximum = 0, Minimum = 0 };

                    var airMoverRequirementByRoom = validationMethod == AirMoverValidationMethod.LinearFeet && AirMoverRequirementsCalculator.CanGetRequirementsLinear ? AirMoverRequirementsCalculator.GetRequirementsLinear(room.FloorLinearAffected ?? 0)
                            : AirMoverRequirementsCalculator.Type == AirMoverRequirementsCalculatorType.Zone ?
                                ZoneCalculator.GetAirMoverForRoomSquareFeetRequirement(airMoverRequirement, room) : null;

                    if (airMoverRequirementByRoom != null)
                    {
                        room.MinAirMoversRequired = airMoverRequirementByRoom.Minimum ?? 0;
                        room.MaxAirMoversRequired = airMoverRequirementByRoom.Maximum ?? 0;
                        room.AirMoversRequirementsProximity = ZoneCalculator.GetAirMoverRequirementsProximity(room.TotalNumberOfAirMovers, airMoverRequirementByRoom.Minimum,
                            airMoverRequirementByRoom.Maximum);
                        room.IsConfirmed = isConfirmed;
                    }
                }

                return roomScopes;
            }

            private RoomScope MapFromRoom(RoomDto room)
            {
                var roomScope = new RoomScope
                {
                    RoomName = room.RoomName,
                    RoomVolumeTotal = RoomCalculator.CalculateTotalRoomVolumeCubicFeet(room),
                    RoomVolumeOffsetsInsets = RoomCalculator.CalculateRoomVolumeOffsetsInsetsCubicFeet(room),

                    FloorAreaPercentAffected = RoomCalculator.CalculateAffectedFloorPercentage(room),
                    FloorArea = RoomCalculator.CalculateFloorAreaSquareFeet(room),
                    FloorAreaAffected = RoomCalculator.CalculateAffectedFloorAreaSquareFeet(room),
                    FloorAreaOffsetsInsets = RoomCalculator.CalculateFloorAreaOffsetInsetSquareFeet(room),
                    FloorAreaMissingSpaces = RoomCalculator.CalculateFloorAreaMissingSpacesSquareFeet(room),
                    FloorLinearAffected = RoomCalculator.CalculateAffectedFloorLinearFeet(room),
                    FloorLinearOffsetsInsets = RoomCalculator.CalculateFloorOffsetInsetLinearFeet(room),
                    FloorLinearMissingSpaces = RoomCalculator.CalculateFloorMissingSpacesLinearFeet(room),

                    FloorAreaSaved = RoomCalculator.CalculateFloorSavedSquareFeet(room),
                    FlooringType = RoomCalculator.GetFlooringType(room),

                    TotalSquareFeet = RoomCalculator.CalculateTotalSquareFeet(room),
                    TotalAffectedSquareFootage = RoomCalculator.CalculateTotalAffectedSquareFeet(room),
                    TotalOffsetsInsetsSquareFootage = RoomCalculator.CalculateTotalOffsetInsetsSquareFeet(room),
                    TotalMissingSpacesSquareFootage = RoomCalculator.CalculateTotalMissingSpacesSquareFeet(room),
                    TotalAffectedPercentage = RoomCalculator.CalculateTotalAffectedPercentage(room),
                    TotalFloorPercentage = RoomCalculator.CalculateAffectedFloorPercentage(room),
                    TotalWallPercentage = RoomCalculator.CalculateAffectedWallPercentage(room),
                    TotalCeilingPercentage = RoomCalculator.CalculateAffectedCeilingPercentage(room),

                    WallAreaAffected = RoomCalculator.CalculateAffectedWallAreaSquareFeet(room),
                    WallAreaAffectedAbove2Feet = CalculatorHelper.SquareInchesToSquareFeet(room.WallAffectedAreaAbove2FeetSquareInches),
                    WallSquareFeet = RoomCalculator.CalculateWallAreaSquareFeet(room),
                    WallAreaMissingSpaces = RoomCalculator.CalculateWallAreaMissingSpacesSquareFeet(room),
                    WallAreaOffsetsInsets = RoomCalculator.CalculateWallAreaOffsetsInsetsSquareFeet(room),

                    CeilingAreaAffected = RoomCalculator.CalculateAffectedCeilingAreaSquareFeet(room),
                    CeilingSquareFeet = RoomCalculator.CalculateCeilingAreaSquareFeet(room),
                    CeilingAreaMissingSpaces = RoomCalculator.CalculateCeilingAreaMissingSpacesSquareFeet(room),
                    CeilingAreaOffsetsInsets = RoomCalculator.CalculateCeilingAreaOffsetsInsetsSquareFeet(room),

                    TotalNumberOfAirMovers = RoomCalculator.GetRoomAirMoverCount(room),
                    TotalNumberOfDehus = RoomCalculator.GetRoomDehuCount(room),
                    OffsetsInsetsCount = room.OffsetsInsetsCount,
                    SubRoomsCount = room.SubRoomsCount,

                    //Accounts for offsets/insets > 18 inches that are added as SubRooms or added in the new section on the DBMX rooms tab
                    //which allows users to account for additional offsets/insets < 18 inches to update Air Mover recommendations.
                    OffsetsInsetsGreaterThan18Inches = room.OffsetSpaceDtos.Count
                };
                return roomScope;
            }
        }
    }
}
