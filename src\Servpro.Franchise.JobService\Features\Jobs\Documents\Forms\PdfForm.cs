﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Aspose.Pdf;
using Aspose.Pdf.Facades;
using Aspose.Pdf.Forms;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public class PdfForm : FormBase<Document, string>
    {
        private Document _document;
        private Aspose.Pdf.Facades.Form _form;
        private MemoryStream _stream;

        protected override void Open(MemoryStream stream)
        {
            _stream = stream;
            _stream.Seek(0, SeekOrigin.Begin);
            _document = new Document(_stream);
            _form = new Aspose.Pdf.Facades.Form(_document);
        }

        protected override string[] GetFields()
        {
            var fields = _form.FieldNames;

            return fields
                .Where(field => _form.GetFieldType(field) == FieldType.Text || _form.GetFieldType(field) == FieldType.CheckBox || _form.GetFieldType(field) == FieldType.MultiLineText)
                .ToArray();
        }

        protected override IEnumerable<string> GetFields(IEnumerable<string> allFields, string token)
        {
            var fieldName = token.Replace("«", "").Replace("»", "").Replace("�", "").Replace("�", "");
            var fields = from ss in allFields where ss.Equals(fieldName) select ss;

            return fields;
        }

        protected override void SetFieldValue(string field, string value, Guid formFieldTypeId)
        {
            if (formFieldTypeId == FormFieldTypes.Grouped && _document.Form[field] is CheckboxField groupField)
            {
                groupField.Value = value;
                return;
            }
            _form.FillField(field, value);
        }

        protected override PageCollection Save(MemoryStream stream)
        {
            _document.Save(stream);
            return _document.Pages;
        }

        public override void Dispose()
        {
            base.Dispose();
            _form?.Dispose();
            _stream?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
