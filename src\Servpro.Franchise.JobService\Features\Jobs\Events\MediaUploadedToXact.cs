using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Xact;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class MediaUploadedToXact
    {
        #region Event
        public class Event : MediaUploadedToXactEvent, IRequest
        {
            public Event(Guid jobId, IEnumerable<Guid> mediaIds, Guid correlationId) : base(jobId, mediaIds, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db,
                ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{@event} received.", request);

                var uploadedForms = await _db.MediaMetadata
                    .Where(mmdt => mmdt.JobId == request.JobId && request.MediaIds.Contains(mmdt.Id))
                    .ToListAsync(cancellationToken);

                uploadedForms.ForEach(f => { f.IsUploadedToXact = true; });

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{event} media for job {jobId} updated successfully.", nameof(MediaUploadedToXactEvent), request.JobId);

                return Unit.Value;
            }
        }
        #endregion Handler
    }
}
