﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Equipments;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class EquipmentUpdated
    {
        public class Event : EquipmentUpdatedEvent, IRequest
        {
            public Event(EquipmentUpdatedDto equipmentUpdatedDto, Guid correlationId) : base(equipmentUpdatedDto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<EquipmentUpdated> _logger;
            private readonly IEquipmentServiceClient _equipmentServiceClient;
            private readonly JobDataContext _context;

            public Handler(JobDataContext context, 
                ILogger<EquipmentUpdated> logger, 
                IEquipmentServiceClient equipmentServiceClient)
            {
                _context = context;
                _logger = logger;
                _equipmentServiceClient = equipmentServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{equipmentId}", request.EquipmentUpdatedDto.Id);
                _logger.LogInformation("Handler began with {@incomingEvent}", request);

                var dto = request.EquipmentUpdatedDto;

                var equipment = await _context.Equipments
                    .FirstOrDefaultAsync(eq => eq.Id == dto.Id, cancellationToken);

                if (equipment is null)
                {
                    _logger.LogInformation($"Equipment not found: {dto.Id}");
                    return Unit.Value;
                }

                var equipmentModel = await _context.EquipmentModels.FirstOrDefaultAsync(
                        x => x.Id == dto.EquipmentModelId, cancellationToken);

                if (equipmentModel is null)
                {
                    var newEquipmentModel = await _equipmentServiceClient
                        .GetEquipmentModelAsync(dto.EquipmentModelId, cancellationToken);

                    if (newEquipmentModel is null)
                    {
                        _logger.LogWarning("Couldn't Update Equipment, EquipmentModel with Id: {id} not found.", dto.EquipmentModelId);
                        return Unit.Value;
                    }

                    var equipmentType = await _context.EquipmentTypes.FirstOrDefaultAsync(
                            x => x.Id == newEquipmentModel.EquipmentTypeId, cancellationToken);
                    if (equipmentType is null)
                    {
                        await _context.EquipmentTypes.AddAsync(Map(newEquipmentModel.EquipmentType), cancellationToken);
                    }

                    equipmentModel = Map(newEquipmentModel);
                    await _context.EquipmentModels.AddAsync(equipmentModel, cancellationToken);
                }

                equipment.EquipmentModelId = dto.EquipmentModelId;
                equipment.AssetNumber = dto.AssetNumber;
                equipment.SerialNumber = dto.SerialNumber;
                equipment.Notes = dto.Notes;
                equipment.IsDeleted = dto.IsDeleted;
                equipment.ModifiedBy = dto.ModifiedBy;
                equipment.ModifiedDate = dto.ModifiedDate;
                equipment.VolumeRate = GetVolumeRate(equipmentModel.EquipmentTypeId, equipmentModel.PintsPerDay, equipmentModel.CubicFeetPerMinute);

                if (dto.FranchiseSetId != Guid.Empty)
                {
                    equipment.FranchiseSetId = dto.FranchiseSetId;
                }
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Handler completed successfully");

                return Unit.Value;
            }

            public EquipmentModel Map(GetEquipmentModelDto equipmentModel)
            {
                return new EquipmentModel
                {
                    Id = equipmentModel.Id,
                    Name = equipmentModel.Name,
                    ManufacturerName = equipmentModel.ManufacturerName,
                    IsSymbol = equipmentModel.IsSymbol,
                    FranchiseSetId = equipmentModel.FranchiseSetId,
                    IsValidModel = equipmentModel.IsValidModel,
                    Description = equipmentModel.Description,
                    ManufacturerModelNumber = equipmentModel.ManufacturerModelNumber,
                    Notes = equipmentModel.Notes,
                    EquipmentTypeId = equipmentModel.EquipmentTypeId,
                    CubicFeetPerMinute = equipmentModel.CubicFeetPerMinute,
                    PintsPerDay = equipmentModel.PintsPerDay,
                    Amps = equipmentModel.Amps,
                    IsPenetratingMeter = equipmentModel.IsPenetratingMeter,
                    IsNotPenetratingMeter = equipmentModel.IsNotPenetratingMeter,
                    IsCurrent = equipmentModel.IsCurrent,
                    IsDeleted = equipmentModel.IsDeleted
                };
            }

            public EquipmentType Map(GetEquipmentTypeDto equipmentType)
            {
                return new EquipmentType
                {
                    Id = equipmentType.Id,
                    BaseEquipmentTypeId = equipmentType.BaseEquipmentTypeId,
                    Name = equipmentType.Name,
                    IsDeleted = equipmentType.IsDeleted,
                    FranchiseSetId = equipmentType.FranchiseSetId
                };
            }

            private int? GetVolumeRate(Guid equipmentTypeId, int pintsPerDay, int cubicFeetPerMinute)
            {
                if (equipmentTypeId == EquipmentTypes.LowGrainRefrigerantDehumidifier
                    || equipmentTypeId == EquipmentTypes.ConventionalDehumidifier)
                    return pintsPerDay;
                else if (equipmentTypeId == EquipmentTypes.DesiccantDehumidifier)
                    return cubicFeetPerMinute;
                return null;
            }
        }
    }
}
