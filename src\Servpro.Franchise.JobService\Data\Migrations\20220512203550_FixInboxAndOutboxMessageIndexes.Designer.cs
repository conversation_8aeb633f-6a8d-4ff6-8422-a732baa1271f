﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Servpro.Franchise.JobService.Data;

namespace Servpro.Franchise.JobService.Data.Migrations
{
    [DbContext(typeof(JobDataContext))]
    [Migration("20220512203550_FixInboxAndOutboxMessageIndexes")]
    partial class FixInboxAndOutboxMessageIndexes
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .UseGuidCollation("latin1_swedish_ci")
                .UseCollation("utf8mb4_general_ci")
                .HasAnnotation("Relational:MaxIdentifierLength", 64)
                .HasAnnotation("ProductVersion", "5.0.6");

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Business", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Address")
                        .HasColumnType("json");

                    b.Property<Guid>("BusinessTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("json");

                    b.Property<Guid?>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("HasErnetContract")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOther")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("json");

                    b.Property<string>("PreferredName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("RecordSourceId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.ToTable("Business");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Address")
                        .HasColumnType("json");

                    b.Property<Guid?>("BusinessId")
                        .HasColumnType("char(36)");

                    b.Property<bool?>("ContactViaTextMessage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsMarketingContact")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<Guid?>("MarketingRepId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("PhoneNumbers")
                        .HasColumnType("json");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("SmsNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("TitleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessId");

                    b.ToTable("Contact");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Equipment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)")
                        .HasColumnName("Id");

                    b.Property<string>("AssetNumber")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("EquipmentModelId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("VolumeRate")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentModelId");

                    b.ToTable("Equipment");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentModel", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)")
                        .HasColumnName("Id");

                    b.Property<decimal>("Amps")
                        .HasColumnType("decimal(19,4)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("CubicFeetPerMinute")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("varchar(250)");

                    b.Property<Guid>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsCurrent")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNotPenetratingMeter")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPenetratingMeter")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSymbol")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsValidModel")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ManufacturerModelNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ManufacturerName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(250)
                        .HasColumnType("varchar(250)");

                    b.Property<int>("PintsPerDay")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentTypeId");

                    b.ToTable("EquipmentModel");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentPlacement", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("EquipmentId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsUsedInValidation")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobAreaId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentId");

                    b.HasIndex("JobAreaId");

                    b.ToTable("EquipmentPlacements");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentPlacementReading", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("EquipmentPlacementId")
                        .HasColumnType("char(36)");

                    b.Property<int>("HourCount")
                        .HasColumnType("int");

                    b.Property<Guid>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("RelativeHumidity")
                        .HasColumnType("decimal(19,4)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<decimal?>("Temperature")
                        .HasColumnType("decimal(19,4)");

                    b.Property<Guid>("ZoneId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentPlacementId");

                    b.HasIndex("JobVisitId");

                    b.HasIndex("ZoneId");

                    b.ToTable("EquipmentPlacementReading");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentType", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("BaseEquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.ToTable("EquipmentType");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobArea", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("BeginJobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("EndJobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsUsedInValidation")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobAreaTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid?>("RoomId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<Guid?>("ZoneId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("BeginJobVisitId");

                    b.HasIndex("EndJobVisitId");

                    b.HasIndex("JobId");

                    b.HasIndex("RoomId");

                    b.HasIndex("ZoneId");

                    b.ToTable("JobAreas");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobAreaMaterial", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("BeginJobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("GoalMetOnJobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobAreaId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobMaterialId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("RemovedOnJobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("BeginJobVisitId");

                    b.HasIndex("GoalMetOnJobVisitId");

                    b.HasIndex("JobAreaId");

                    b.HasIndex("JobMaterialId");

                    b.HasIndex("RemovedOnJobVisitId");

                    b.ToTable("JobAreaMaterial");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobAreaMaterialReading", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobAreaMaterialId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("MaterialReadingTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<int?>("Value")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("JobAreaMaterialId");

                    b.HasIndex("JobVisitId");

                    b.ToTable("JobAreaMaterialReading");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobMaterial", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Goal")
                        .HasColumnType("int");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("MaterialReadingTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("MeterTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<Guid>("ObjectId")
                        .HasColumnType("char(36)");

                    b.Property<string>("OtherText")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<int?>("XactNarrativeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("JobMaterial");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobTriStateAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<bool?>("Answer")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobTriStateQuestionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.HasIndex("JobId", "JobTriStateQuestionId")
                        .IsUnique();

                    b.ToTable("JobTriStateAnswer");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobVisit", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("DefaultNonPenetratingMeterEquipmentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DefaultPenetratingMeterEquipmentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DefaultThermoHygrometerEquipmentId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DepartureDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EmployeeInitials")
                        .HasMaxLength(3)
                        .HasColumnType("varchar(3)");

                    b.Property<bool>("IsMissingVisit")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("JobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobVisitTriStateAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<bool?>("Answer")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobTriStateQuestionId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobVisitId");

                    b.HasIndex("JobVisitId", "JobTriStateQuestionId")
                        .IsUnique();

                    b.ToTable("JobVisitTriStateAnswer");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.RoomFlooringTypeAffected", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<decimal?>("AffectedPercentage")
                        .HasColumnType("decimal(9,2)");

                    b.Property<decimal?>("AffectedSquareFeet")
                        .HasColumnType("decimal(9,2)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("FlooringTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsPadRestorable")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsSalvageable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("OtherText")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid>("RoomId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<decimal?>("SavedPercentage")
                        .HasColumnType("decimal(9,2)");

                    b.Property<decimal?>("SavedSquareFeet")
                        .HasColumnType("decimal(9,2)");

                    b.Property<decimal?>("TotalSquareFeet")
                        .HasColumnType("decimal(9,2)");

                    b.HasKey("Id");

                    b.HasIndex("RoomId");

                    b.ToTable("RoomFlooringTypesAffected");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Task", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Body")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("CancellationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CompletionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("EstimateId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsSystem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsTemplate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<Guid?>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobTriStateQuestionId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("PercentComplete")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("ReminderDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Subject")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("TaskPriorityId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TaskStatusId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TaskTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("WorkOrderId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ZoneId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ZoneReadingId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.HasIndex("JobVisitId");

                    b.HasIndex("ZoneId");

                    b.ToTable("Task");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Zone", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int?>("AchievedDehuCapacity")
                        .HasColumnType("int");

                    b.Property<Guid>("AirMoverCalculationTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool?>("CanValidateDehuCapacity")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("varchar(250)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RequiredDehuCapacity")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<Guid?>("SketchMediaContentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("WaterCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("WaterClassId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("WaterClassOverridden")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ZoneHazordousMaterialTypes")
                        .HasColumnType("json");

                    b.Property<Guid>("ZoneTypeId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("Zones");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.ZoneReading", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool?>("IsInUse")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JournalNoteId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("RelativeHumidity")
                        .HasColumnType("decimal(19,4)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<decimal?>("Temperature")
                        .HasColumnType("decimal(19,4)");

                    b.Property<Guid>("ZoneId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("JobVisitId");

                    b.HasIndex("JournalNoteId");

                    b.HasIndex("ZoneId");

                    b.ToTable("ZoneReading");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.EmployeeWipColumnCustomization", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("CustomizationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Data")
                        .HasColumnType("json");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.ToTable("EmployeeWipColumnCustomization");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.FirstNoticeActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("ActivityTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("OriginalValue")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("RecordSourceId")
                        .HasColumnType("char(36)");

                    b.Property<string>("RevisedValue")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("FirstNoticeActivity");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.FirstNoticeUserActivityTracker", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)")
                        .UseCollation("utf8mb4_general_ci");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)")
                        .UseCollation("latin1_swedish_ci");

                    b.Property<DateTime>("LastChecked")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)")
                        .UseCollation("utf8mb4_general_ci");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("JobId", "UserId")
                        .IsUnique();

                    b.ToTable("FirstNoticeUserActivityTracker");

                    b
                        .UseCollation("utf8mb4_general_ci");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.FormTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<byte>("Approved")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<string>("CommercialClient")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Country")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("varchar(250)");

                    b.Property<string>("FileType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<byte>("FireForm")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte>("FireFormRequired")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<string>("FormVersion")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("FormalLanguage")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("InsuranceClient")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<byte>("IsActive")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte>("IsAuthorizationForm")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(3) unsigned")
                        .HasDefaultValue((byte)0);

                    b.Property<byte?>("IsAvailableInFirstNotice")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte?>("IsRequiredForAllLossTypes")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte?>("IsRequiredForCommercialJob")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte?>("IsRequiredForResidentialJob")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<string>("LinkedPage")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MediaPath")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<byte>("MoldForm")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte>("MoldFormRequired")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("RelatedForm")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RelatedForm2")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RelatedForm3")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("State")
                        .HasMaxLength(2)
                        .HasColumnType("varchar(2)");

                    b.Property<DateTime?>("SyncDate")
                        .HasColumnType("datetime(6)");

                    b.Property<byte>("WaterForm")
                        .HasColumnType("tinyint(3) unsigned");

                    b.Property<byte>("WaterFormRequired")
                        .HasColumnType("tinyint(3) unsigned");

                    b.HasKey("Id")
                        .HasName("PRIMARY");

                    b.ToTable("FormTemplates");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.FranchiseSetWipColumnCustomization", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("CustomizationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Data")
                        .HasColumnType("json");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.ToTable("FranchiseSetWipColumnCustomization");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.InsuranceClient", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("InsuranceNumber")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAuditRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsLocalAuditRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("ParentInsuranceNumber")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .IsUnique();

                    b.HasIndex("InsuranceNumber")
                        .IsUnique();

                    b.ToTable("InsuranceClients");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Job", b =>
                {
                    b.Property<long>("ReferenceNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<int?>("AssociatedErpId")
                        .HasColumnType("int");

                    b.Property<string>("AuditRejectRole")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("AuditRejectionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("BidStatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("00000000-0002-5365-7276-70726f496e63"));

                    b.Property<string>("BusinessName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("CallerId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("CauseOfLossId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClientConditions")
                        .HasMaxLength(65000)
                        .HasColumnType("longtext");

                    b.Property<int?>("ClientRequirementsCount")
                        .HasColumnType("int");

                    b.Property<bool?>("CollectDeductible")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("CollectDeductibleDecisionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("10319f7f-6520-473e-a776-7b6a77ccddf0"));

                    b.Property<double>("Confidence")
                        .HasColumnType("double");

                    b.Property<int?>("CorporateJobNumber")
                        .HasColumnType("int");

                    b.Property<Guid?>("CorporateJobSourceId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("CorrectionDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("CoverageTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("00000000-004e-5365-7276-70726f496e63"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CreatedByFullName")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CrewChiefId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("CustomerTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("6872a501-5486-4c79-a683-935037323d69"));

                    b.Property<int>("DailyExtensionCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("DailyExtensionReasonId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DailyUploadDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DailyUploadDueDateModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("DateOfLoss")
                        .HasColumnType("datetime(6)");

                    b.Property<double>("DeductibleAmount")
                        .HasColumnType("double");

                    b.Property<decimal>("DeductibleAmountDue")
                        .HasColumnType("decimal(19,4)");

                    b.Property<Guid?>("DefaultMentorId")
                        .HasColumnType("char(36)");

                    b.Property<string>("DropDownQuestionResponses")
                        .HasColumnType("json");

                    b.Property<int?>("FacilityTypeId")
                        .HasColumnType("int");

                    b.Property<int>("FinalExtensionCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("FinalExtensionReasonId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("FinalUploadDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("FinalUploadDueDateModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FlooringTypesAffected")
                        .HasColumnType("json");

                    b.Property<Guid>("FranchiseId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FranchiseName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FranchiseState")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("GeneralManagerId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("HasInvoice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("HasSourceBeenTurnedOff")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("InitialExtensionCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("InitialExtensionReasonId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("InitialUploadDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("InitialUploadDueDateModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("InsuranceCarrierId")
                        .HasColumnType("char(36)");

                    b.Property<string>("InsuranceClaimNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("InsurancePolicyNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAuditComplete")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsBidRequested")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsCeilingAffected")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsChase")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsContentAffected")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDispatched")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsElectricAvailable")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsMultiUnitStructure")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNonStandardJob")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRedFlagged")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsStreamlineAudit")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsThereStandingWater")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsWallAffected")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsWarmLead")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsWaterAvailable")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobCancelReasonId")
                        .HasColumnType("char(36)");

                    b.Property<string>("JobDates")
                        .HasColumnType("json");

                    b.Property<Guid?>("JobDispatchStatusId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobDispatchTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("JobEstimator")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("JobFileCoordinatorId")
                        .HasColumnType("char(36)");

                    b.Property<int>("JobProgress")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<Guid>("JobSourceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("00000001-004f-5365-7276-70726f496e63"));

                    b.Property<decimal?>("LeadEstimate")
                        .HasColumnType("decimal(19,4)");

                    b.Property<int>("LevelsAffected")
                        .HasColumnType("int");

                    b.Property<string>("LossAddress")
                        .HasColumnType("json");

                    b.Property<string>("LossNote")
                        .HasMaxLength(5000)
                        .HasColumnType("varchar(5000)");

                    b.Property<int>("LossOccurredOnLevel")
                        .HasColumnType("int");

                    b.Property<int?>("LossSeverityId")
                        .HasColumnType("int");

                    b.Property<Guid>("LossTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MarketingRepId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MentorId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MergeSource")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MergeTarget")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MobileDataId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("MostRecentDailyUploadCompleted")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("MostRecentFinalUploadCompleted")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("MostRecentInitialUploadCompleted")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("NteAmount")
                        .HasColumnType("decimal(19,4)");

                    b.Property<Guid?>("OfficeManagerId")
                        .HasColumnType("char(36)");

                    b.Property<int>("OnHoldReasonId")
                        .HasColumnType("int");

                    b.Property<double>("PreliminaryEstimate")
                        .HasColumnType("double");

                    b.Property<string>("PriorityResponderFullName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("PriorityResponderId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ProductionManagerId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ProgramTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("7968ce8e-fc1e-4b77-8384-bbdf3f2a91e8"));

                    b.Property<Guid?>("ProjectManagerId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ProjectNumber")
                        .IsRequired()
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int?>("ProjectRangeId")
                        .HasColumnType("int");

                    b.Property<Guid>("PropertyTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("PurchaseOrderNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("ReceivedContactMethodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)")
                        .HasDefaultValue(new Guid("00000001-009a-5365-7276-70726f496e63"));

                    b.Property<Guid?>("ReconSupportId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RecordSourceId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RecpDispatcherId")
                        .HasColumnType("char(36)");

                    b.Property<string>("RedFlagNotes")
                        .HasMaxLength(300)
                        .HasColumnType("varchar(300)");

                    b.Property<Guid?>("ReferredById")
                        .HasColumnType("char(36)");

                    b.Property<int?>("RejectedFinalAuditsCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("ReportedById")
                        .HasColumnType("char(36)");

                    b.Property<int>("RoomsAffected")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<DateTime?>("SelfAuditDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("SiteAppointmentById")
                        .HasColumnType("char(36)");

                    b.Property<string>("SiteReferenceNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("SourceOfOpportunity")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int?>("SquareFeet")
                        .HasColumnType("int");

                    b.Property<int?>("SquareFeetAffected")
                        .HasColumnType("int");

                    b.Property<string>("StatusNotes")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<Guid?>("StormId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("StructureTypeId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("TargetCompletionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("TotalAmountDue")
                        .HasColumnType("decimal(19,4)");

                    b.Property<decimal?>("TotalRevenue")
                        .HasColumnType("decimal(19,4)");

                    b.Property<Guid?>("TransferredToJobId")
                        .HasColumnType("char(36)");

                    b.Property<long?>("WorkCenterJobNumber")
                        .HasColumnType("bigint");

                    b.Property<string>("WorkOrderNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int?>("YearStructureBuilt")
                        .HasColumnType("int");

                    b.HasKey("ReferenceNumber");

                    b.HasIndex("CallerId");

                    b.HasIndex("CorporateJobNumber");

                    b.HasIndex("CustomerId");

                    b.HasIndex("FranchiseSetId");

                    b.HasIndex("Id")
                        .IsUnique();

                    b.HasIndex("MobileDataId");

                    b.HasIndex("ProjectNumber");

                    b.HasIndex("FranchiseSetId", "JobProgress", "LossTypeId");

                    b.ToTable("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobActionLocation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("ActionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<float?>("Altitude")
                        .HasColumnType("float");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DeviceKey")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<float?>("HorizontalAccuracy")
                        .HasColumnType("float");

                    b.Property<Guid>("JobActionTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(19,4)");

                    b.Property<DateTime?>("LocationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("LocationsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(19,4)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("RecordSourceId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("ReferenceDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ReferenceDescription")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<Guid?>("ReferenceId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<float?>("VerticalAccuracy")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("JobActionLocation");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobBusinessMap", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("BusinessId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobBusinessTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessId");

                    b.HasIndex("JobId");

                    b.ToTable("JobBusinessMaps");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobContactMap", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsBusinessContact")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobContactTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<Guid?>("TitleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ContactId");

                    b.HasIndex("JobId");

                    b.ToTable("JobContactMap");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobInvoice", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<double>("Amount")
                        .HasColumnType("double");

                    b.Property<double>("AmountCollected")
                        .HasColumnType("double");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<Guid>("MediaMetadataId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("Source")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.HasKey("Id");

                    b.HasIndex("MediaMetadataId")
                        .IsUnique();

                    b.ToTable("JobInvoices");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobLock", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("LockedByApplicationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LockedByApplicationName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("LockedByDeviceId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("LockedByUserFullName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid>("LockedByUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("LockedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("UnlockedByDeviceId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("UnlockedByUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("UnlockedTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.ToTable("JobLock");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobProgressHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ChangedBy")
                        .HasMaxLength(150)
                        .HasColumnType("varchar(150)");

                    b.Property<DateTime>("ChangedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<int>("JobProgress")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ChangedDate");

                    b.HasIndex("JobId");

                    b.ToTable("JobProgressHistory");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobSketch", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CanvasJson")
                        .HasMaxLength(30000000)
                        .HasColumnType("longtext");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("MediaMetadataId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.HasIndex("MediaMetadataId")
                        .IsUnique();

                    b.ToTable("JobSketch");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobSketchJobVisit", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobSketchId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("JobSketchId");

                    b.HasIndex("JobVisitId");

                    b.ToTable("JobSketchJobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobUploadLock", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobUploadTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.ToTable("JobUploadLocks");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JournalNote", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("ActionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Author")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IncludeInSummaryCoverPage")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid?>("JobAreaId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobAreaMaterialId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Note")
                        .HasMaxLength(65000)
                        .HasColumnType("longtext");

                    b.Property<string>("OtherName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid?>("RoomFlooringTypeAffectedId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("RuleIds")
                        .HasColumnType("json");

                    b.Property<string>("Rules")
                        .HasColumnType("json");

                    b.Property<string>("Subject")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("TaskId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("VisibilityId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ZoneId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.HasIndex("RoomFlooringTypeAffectedId");

                    b.HasIndex("TaskId");

                    b.ToTable("JournalNote");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.LeadRollbackError", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExceptionMessage")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ProjectNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("JobId");

                    b.HasIndex("ProjectNumber");

                    b.ToTable("LeadRollbackError");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.LineItem", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ActivityCode")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Category")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("Code")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("Info")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("LineItemId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("Percentage")
                        .HasColumnType("decimal(19,4)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(19,4)");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("RoomId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("UnitOfMeasure")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<Guid?>("XactUploadTransactionId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("JobId");

                    b.HasIndex("JobVisitId");

                    b.HasIndex("RoomId");

                    b.ToTable("LineItem");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.LineItemNote", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEditable")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid?>("LineItemId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Note")
                        .HasMaxLength(65000)
                        .HasColumnType("longtext");

                    b.Property<Guid?>("RoomId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.HasIndex("LineItemId");

                    b.HasIndex("RoomId");

                    b.ToTable("LineItemNote");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MaintenanceAlerts", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MaintenanceTypeId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Note")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.ToTable("MaintenanceAlerts");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MediaMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("ArtifactDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("ArtifactTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("BucketName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("Comment")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasMaxLength(400)
                        .HasColumnType("varchar(400)");

                    b.Property<Guid?>("FormTemplateId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FormVersion")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsForUpload")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsUploadedToXact")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid?>("JobAreaId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobAreaMaterialId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobInvoiceId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobSketchId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("MediaPath")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<Guid>("MediaTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<DateTime?>("SignedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("SyncDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("UploadedSuccessfully")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid?>("ZoneId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("JobAreaId");

                    b.HasIndex("JobId");

                    b.HasIndex("JobVisitId");

                    b.ToTable("MediaMetadata");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MenuItem", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FeatureSets")
                        .HasColumnType("json");

                    b.Property<string>("Items")
                        .HasColumnType("json");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Roles")
                        .HasColumnType("json");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("Url")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.HasKey("Id");

                    b.ToTable("MenuItem");

                    b.HasData(
                        new
                        {
                            Id = new Guid("ba004c89-db75-4c52-b15e-70b30fa79752"),
                            CreatedDate = new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc),
                            FeatureSets = "[]",
                            Items = "[{\"Order\":1,\"Name\":\"View Contacts\",\"Url\":\"ContactManagement/Contact\",\"Roles\":[\"F_AllFranchiseAccountingClerks\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseAccountingHR\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseMarketingSupportCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"a16cecaf-5f0f-4799-b7e5-ef3ef7173010\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":\"2019-12-18T00:00:00Z\",\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Contact Merge\",\"Url\":\"ContactManagement/ContactMerge\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d54c0715-ec0d-4217-8a3d-d4fedad99533\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Contact Diary Notes\",\"Url\":\"ContactManagement/DiaryNotes\",\"Roles\":[\"F_AllFranchiseAccountingClerks\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseAccountingHR\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"5ed1c3e8-267d-41a3-90cb-953641e5ba4c\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":\"2019-12-23T00:00:00Z\",\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]",
                            Name = "Contact",
                            Order = 1,
                            Roles = "[]"
                        },
                        new
                        {
                            Id = new Guid("996a0c51-8f09-4e27-9ac4-61a9a9699b12"),
                            CreatedDate = new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc),
                            FeatureSets = "[]",
                            Items = "[{\"Order\":1,\"Name\":\"View Scheduler\",\"Url\":\"ScheduleArea/Schedule?signin=oidc\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e6c61517-3f7d-4673-906f-9a92d15fb2b7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]",
                            Name = "Scheduler",
                            Order = 2,
                            Roles = "[]"
                        },
                        new
                        {
                            Id = new Guid("f1807962-9ab8-4286-b42c-807bf5e45bd3"),
                            CreatedDate = new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc),
                            FeatureSets = "[]",
                            Items = "[{\"Order\":1,\"Name\":\"View Tasks\",\"Url\":\"Tasks/Task.aspx?signin=oidc\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e7adff31-114d-4c44-98f0-c494259a4a9f\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]",
                            Name = "Tasks",
                            Order = 3,
                            Roles = "[]"
                        },
                        new
                        {
                            Id = new Guid("7479be22-361a-4da3-baef-11e91a990f89"),
                            CreatedDate = new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc),
                            FeatureSets = "[]",
                            Items = "[{\"Order\":1,\"Name\":\"Equipment/Consumables\",\"Url\":\"Equipment/Equipment.aspx?signin=oidc\",\"Roles\":[\"F_AllFranchiseAccountingClerks\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseAccountingHR\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"0eef3ac4-e5c7-403d-9087-c3fadab9c676\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Revenue Dashboard\",\"Url\":\"TableauDashboards/Revenue\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"c082203c-57d7-4cda-8333-c6b80f1085a1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Vendor List\",\"Url\":\"Vendors/Vendor.aspx?signin=oidc\",\"Roles\":[\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"562d6283-8c77-4d61-9122-a2757e748c2f\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"Client KPM Dashboard\",\"Url\":\"TableauDashboards/ClientKPM\",\"Roles\":[\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d0d9f30f-6d1e-4ac9-bd93-be4a96a8a95e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"Financial KPM Dashboard\",\"Url\":\"TableauDashboards/FinancialKPM\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"f0d8295f-5704-43ef-9ded-989f45deb790\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"Completed Jobs Dashboard\",\"Url\":\"TableauDashboards/CompletedJobsDashboard\",\"Roles\":[\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"6efcd318-fb3b-4cd7-afd0-581be16aac66\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]",
                            Name = "Dashboards",
                            Order = 4,
                            Roles = "[]"
                        },
                        new
                        {
                            Id = new Guid("8996855a-f826-4052-8f67-ac2a7e463f95"),
                            CreatedDate = new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc),
                            FeatureSets = "[]",
                            Items = "[{\"Order\":1,\"Name\":\"KPM Dashboard - Owner View\",\"Url\":\"views/KPMDashboard-Owner/KPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9e46d5e2-5349-4f80-ac0a-84388daa44aa\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"KPM Dashboard\",\"Url\":\"views/KPMDashboard/KPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9a152e44-6a00-4d17-bbf4-0a4dfd509f16\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Client KPM Scorecard\",\"Url\":\"views/ClientKPMDashboard/ClientKPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"10196706-1a1a-46f5-ad98-7be85c16daf1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"Financial KPM Scorecard\",\"Url\":\"FinancialKPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e400a392-2bdf-4072-8cfc-36aebb720ec7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":5,\"Name\":\"Office KPM Scorecard\",\"Url\":\"OfficeKPMsDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"202ee198-b0cc-4869-a206-2dff4faf31e1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"Production KPM Scorecard\",\"Url\":\"ProductionKPMsDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"a85b10c5-0986-4961-86a5-1b00e5b21618\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"Drive the 5 Scorecard\",\"Url\":\"DrivetheFiveScorecard\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"efb2a0d0-f627-4bcc-8349-80b5041e9335\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":8,\"Name\":\"Revenue Dashboard\",\"Url\":\"views/RevenueDashboard/VolumeDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ea8daf13-7612-4e49-ab71-83a364f3e570\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":9,\"Name\":\"Additional Reports\",\"Url\":\"projects\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"11a2ef5c-3ab7-4487-addb-4f40451dd6f5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]",
                            Name = "Business Intelligence/Reports",
                            Order = 5,
                            Roles = "[]"
                        },
                        new
                        {
                            Id = new Guid("9f431564-1812-4998-b373-9e24b9480394"),
                            CreatedDate = new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc),
                            FeatureSets = "[]",
                            Items = "[{\"Order\":1,\"Name\":\"Preferences\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Franchise Preferences\",\"Url\":\"FranchisePreferences\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"587231b2-c63b-4003-bd5e-37d0f23bf040\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"User Management/Alert Subscription\",\"Url\":\"admin/subscription\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"bbc2b295-c725-4913-954f-1484b0546ec4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":5,\"Name\":\"Alert Management\",\"Url\":\"admin/management\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ca31c9d2-5c69-4283-81b1-09ebeb4d23cb\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"Xact Options\",\"Url\":\"Xactimate\",\"Roles\":[\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseDirectors\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"84561a8c-0a38-496e-85e6-fe07734b4fd1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"Training Data Refresh\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9c292ab8-baa7-458a-bf13-dc6ee50bbc1e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}],\"Id\":\"36025296-81a9-4010-9c85-2045a3a28b25\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Navigation\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Dispatch Status Management\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"581ce55d-591a-484e-ae25-62258957adb4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Emergency READY Profile\",\"Url\":\"Account/Login\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9ff4a581-08c0-4633-aeb0-fe64aa0a484b\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Job File Audit\",\"Url\":\"jobfileaudit\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"0176aea4-326c-412e-bccb-ea1399b5d674\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"Manage Active Claims\",\"Url\":\"franchiseclaimsinfo/ActiveClaims.aspx\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d084b944-55fe-4144-9147-85c64e780fdd\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":5,\"Name\":\"Royalty Reporting\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseReceptionistDispatchers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"5d705364-6cb1-48ba-b492-98eeeee04478\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"ServproNET\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"4a4f5313-5a50-4cf7-a81e-a68b276a5f69\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"SSO User Management\",\"Url\":\"ARWebAdmin/\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9474842b-d051-441f-8873-67294a04fac2\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":8,\"Name\":\"Disaster Response Manager\",\"Url\":\"StormManager\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"043b3ac6-155c-452d-87c5-2ea7ae592bc7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":9,\"Name\":\"Salesforce Marketing\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"Users - Corporate\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"8dab9bf2-0e40-11eb-9edc-0242ac120004\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}],\"Id\":\"66401d3e-30a3-4668-a430-b9c97d3403d5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Help\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Helpdesk\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"533ad319-386a-4e65-a1a8-c9140d3ce4f4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}],\"Id\":\"24e3b472-f222-4280-9f7c-93516bd2d6b0\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]",
                            Name = "More",
                            Order = 6,
                            Roles = "[]"
                        });
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MobileData", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Access")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("AffectedArea")
                        .HasColumnType("int");

                    b.Property<int?>("AffectedFloors")
                        .HasColumnType("int");

                    b.Property<string>("AnyInjuriesNotes")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("BuildOutDensityId")
                        .HasColumnType("int");

                    b.Property<int?>("CommercialPropertyTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Conditions")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int?>("ContentsAffectedSeverityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DeductibleNotes")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("EstablishmentCompanyId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("FireExtinguishedMethodId")
                        .HasColumnType("char(36)");

                    b.Property<string>("HowCanIHelpYouNow")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("IacInvoiceAmount")
                        .HasColumnType("decimal(19,4)");

                    b.Property<string>("ImmediateNeeds")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("JobDispatchPendingReasonId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("JobDispatchReasonId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LeadIdentifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("LeadStatusReasonCodeId")
                        .HasColumnType("int");

                    b.Property<string>("LeadStatusReasonNote")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("LossAddressDirections")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("LossSeverityId")
                        .HasColumnType("int");

                    b.Property<int?>("MachineLearningId")
                        .HasColumnType("int");

                    b.Property<string>("ManagementOwner")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("MarketingCampaignId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("NotToExceedOverrideAmount")
                        .HasColumnType("int");

                    b.Property<Guid?>("NotToExceedOverrideCurrencyId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("NumberOfBuildings")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfElevators")
                        .HasColumnType("int");

                    b.Property<int?>("NumberOfFloors")
                        .HasColumnType("int");

                    b.Property<int?>("OccupantsId")
                        .HasColumnType("int");

                    b.Property<string>("OtherCompany")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherJobCause")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherNeeds")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherQuestions")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherStructureType")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherTypeOfLoss")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("PercentAffected")
                        .HasColumnType("int");

                    b.Property<int?>("ProjectRangeId")
                        .HasColumnType("int");

                    b.Property<string>("QuickBooksJobListId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ReferralName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("ReferralTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ResultOfFirstContact")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<Guid?>("ServiceTimeZoneId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Situation")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("SoaSynchronizationPassIdentifier")
                        .HasColumnType("int");

                    b.Property<Guid?>("TemperatureScaleId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ThirdPartyAdminCompanyId")
                        .HasColumnType("char(36)");

                    b.Property<int>("UnitsAffected")
                        .HasColumnType("int");

                    b.Property<int?>("WetDocumentsId")
                        .HasColumnType("int");

                    b.Property<string>("WhatIsTheSituation")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.ToTable("MobileData");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MobileLog", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("BucketName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<long>("Filesize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MediaPath")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Type")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("Username")
                        .HasMaxLength(60)
                        .HasColumnType("varchar(60)");

                    b.HasKey("Id");

                    b.ToTable("MobileLog");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MyCustomViews", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("CustomizationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Data")
                        .HasColumnType("json");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<string>("ViewName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.HasKey("Id");

                    b.ToTable("MyCustomViews");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.RawSqlModels.MergeCandidate", b =>
                {
                    b.Property<string>("Address1")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("JobProgress")
                        .HasColumnType("int");

                    b.Property<Guid>("LossTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RecordSourceId")
                        .HasColumnType("char(36)");

                    b.ToTable("MergeCandidates");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Room", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<decimal>("AffectedCeilingAreaSquareInches")
                        .HasColumnType("decimal(19,4)");

                    b.Property<decimal>("AffectedWallAreaAbove2FeetSquareInches")
                        .HasColumnType("decimal(19,4)");

                    b.Property<decimal>("AffectedWallAreaBelow2FeetSquareInches")
                        .HasColumnType("decimal(19,4)");

                    b.Property<decimal>("AffectedWallAreaSquareInches")
                        .HasColumnType("decimal(19,4)");

                    b.Property<decimal>("AreaAffectedPercentage")
                        .HasColumnType("decimal(18,4)");

                    b.Property<Guid?>("CarpetTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int>("CeilingAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int>("CeilingPerimeterInches")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("DryOnJobVisitId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Floor")
                        .HasColumnType("int");

                    b.Property<int>("FloorAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int>("FloorPerimeterInches")
                        .HasColumnType("int");

                    b.Property<Guid>("FloorTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("FlooringPercentageAffected")
                        .HasColumnType("int");

                    b.Property<int?>("FlooringPercentageSaved")
                        .HasColumnType("int");

                    b.Property<bool?>("HasDamagedContents")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("HasSpecialSituation")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Height1TotalInches")
                        .HasColumnType("int");

                    b.Property<int>("Height2TotalInches")
                        .HasColumnType("int");

                    b.Property<int>("Height3TotalInches")
                        .HasColumnType("int");

                    b.Property<bool>("IsContentScopeCompleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsPadRestorable")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSlopeOrientedAlongLength")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsStructureScopeCompleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Length1TotalInches")
                        .HasColumnType("int");

                    b.Property<int>("Length2TotalInches")
                        .HasColumnType("int");

                    b.Property<int>("MissingCeilingAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int>("MissingCeilingPerimeterInches")
                        .HasColumnType("int");

                    b.Property<int>("MissingFloorAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int>("MissingFloorPerimeterInches")
                        .HasColumnType("int");

                    b.Property<long>("MissingRoomVolumeCubicInches")
                        .HasColumnType("bigint");

                    b.Property<string>("MissingSpaces")
                        .HasColumnType("json");

                    b.Property<int>("MissingWallAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("OffsetCeilingAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int>("OffsetCeilingPerimeterInches")
                        .HasColumnType("int");

                    b.Property<int>("OffsetFloorAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int>("OffsetFloorPerimeterInches")
                        .HasColumnType("int");

                    b.Property<long>("OffsetRoomVolumeCubicInches")
                        .HasColumnType("bigint");

                    b.Property<string>("OffsetSpaces")
                        .HasColumnType("json");

                    b.Property<int>("OffsetWallAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<int?>("OldId")
                        .HasColumnType("int");

                    b.Property<string>("OtherCarpetType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OtherPadType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid?>("PadTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("PreExistingConditions")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("PreExistingConditionsDiaryNoteId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("RoomOrder")
                        .HasColumnType("int");

                    b.Property<Guid>("RoomShapeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RoomTypeId")
                        .HasColumnType("char(36)");

                    b.Property<long>("RoomVolumeCubicInches")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("SketchGroupId")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<decimal>("WallAreaBelow2FeetSquareInches")
                        .HasColumnType("decimal(19,4)");

                    b.Property<int>("WallAreaSquareInches")
                        .HasColumnType("int");

                    b.Property<Guid>("WaterCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("WaterClassId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Width1TotalInches")
                        .HasColumnType("int");

                    b.Property<int>("Width2TotalInches")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DryOnJobVisitId");

                    b.HasIndex("PreExistingConditionsDiaryNoteId");

                    b.ToTable("Rooms");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.ServproWipColumnCustomization", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("CustomizationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Data")
                        .HasColumnType("json");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id");

                    b.ToTable("ServproWipColumnCustomization");

                    b.HasData(
                        new
                        {
                            Id = new Guid("416020c8-1e92-4cdd-847e-ec7fcee7d333"),
                            CreatedDate = new DateTime(2019, 12, 6, 14, 49, 26, 149, DateTimeKind.Utc),
                            CustomizationTypeId = 0,
                            Data = "[{\"ColumnPosition\":3,\"ColumnWidth\":95,\"ColumnName\":\"dateReceived\",\"ColumnText\":\"Date Received\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":4,\"ColumnWidth\":100,\"ColumnName\":\"corporateJobNumber\",\"ColumnText\":\"Corporate Ref #\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":5,\"ColumnWidth\":150,\"ColumnName\":\"projectNumber\",\"ColumnText\":\"Project #\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":6,\"ColumnWidth\":90,\"ColumnName\":\"propertyTypeName\",\"ColumnText\":\"Property Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":7,\"ColumnWidth\":80,\"ColumnName\":\"jobTypeName\",\"ColumnText\":\"Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":8,\"ColumnWidth\":140,\"ColumnName\":\"progress\",\"ColumnText\":\"Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":9,\"ColumnWidth\":80,\"ColumnName\":\"isRedFlagged\",\"ColumnText\":\"Project Flag\",\"IsVisible\":false,\"Type\":\"boolean\"},{\"ColumnPosition\":10,\"ColumnWidth\":140,\"ColumnName\":\"customerFullName\",\"ColumnText\":\"Customer\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":11,\"ColumnWidth\":120,\"ColumnName\":\"PreliminaryEstimate\",\"ColumnText\":\"Preliminary Estimate $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":12,\"ColumnWidth\":120,\"ColumnName\":\"Confidence\",\"ColumnText\":\"Confidence %\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":13,\"ColumnWidth\":140,\"ColumnName\":\"priorityResponderName\",\"ColumnText\":\"Priority Responder\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":14,\"ColumnWidth\":140,\"ColumnName\":\"jobFileCoordinatorName\",\"ColumnText\":\"Job File Coordinator\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":15,\"ColumnWidth\":140,\"ColumnName\":\"projectManagerName\",\"ColumnText\":\"Project Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":16,\"ColumnWidth\":140,\"ColumnName\":\"productionManagerName\",\"ColumnText\":\"Production Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":17,\"ColumnWidth\":140,\"ColumnName\":\"marketingRepName\",\"ColumnText\":\"Marketing Rep.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":18,\"ColumnWidth\":140,\"ColumnName\":\"crewChiefName\",\"ColumnText\":\"Crew Chief\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":19,\"ColumnWidth\":140,\"ColumnName\":\"recpDispatcherName\",\"ColumnText\":\"Recp Dispatcher\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":20,\"ColumnWidth\":140,\"ColumnName\":\"generalManagerName\",\"ColumnText\":\"General Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":21,\"ColumnWidth\":140,\"ColumnName\":\"officeManagerName\",\"ColumnText\":\"Office Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":22,\"ColumnWidth\":140,\"ColumnName\":\"reconSuperintendent\",\"ColumnText\":\"Recon Supt\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":23,\"ColumnWidth\":210,\"ColumnName\":\"statusNotes\",\"ColumnText\":\"Status Notes\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":24,\"ColumnWidth\":80,\"ColumnName\":\"lossPostalCode\",\"ColumnText\":\"Loss Zip\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":25,\"ColumnWidth\":130,\"ColumnName\":\"insuranceCompanyName\",\"ColumnText\":\"Insurance Co.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":26,\"ColumnWidth\":110,\"ColumnName\":\"adjusters\",\"ColumnText\":\"Adjuster\",\"IsVisible\":true,\"Type\":\"array\"},{\"ColumnPosition\":27,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress1\",\"ColumnText\":\"Loss Address 1\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":28,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress2\",\"ColumnText\":\"Loss Address 2\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":29,\"ColumnWidth\":90,\"ColumnName\":\"lossCity\",\"ColumnText\":\"Loss City\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":30,\"ColumnWidth\":80,\"ColumnName\":\"lossStateAbbreviation\",\"ColumnText\":\"Loss State\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":31,\"ColumnWidth\":130,\"ColumnName\":\"leadSource\",\"ColumnText\":\"Lead Source\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":32,\"ColumnWidth\":130,\"ColumnName\":\"reportedByName\",\"ColumnText\":\"Reported By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":33,\"ColumnWidth\":130,\"ColumnName\":\"referredByName\",\"ColumnText\":\"Referred By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":34,\"ColumnWidth\":110,\"ColumnName\":\"durationOpen\",\"ColumnText\":\"Duration Open\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":35,\"ColumnWidth\":110,\"ColumnName\":\"airMoverCount\",\"ColumnText\":\"Air Mover Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":36,\"ColumnWidth\":110,\"ColumnName\":\"activeAlertCount\",\"ColumnText\":\"DB Alerts\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":37,\"ColumnWidth\":110,\"ColumnName\":\"callerFullName\",\"ColumnText\":\"Caller\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":38,\"ColumnWidth\":110,\"ColumnName\":\"dehuCount\",\"ColumnText\":\"Dehu Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":39,\"ColumnWidth\":120,\"ColumnName\":\"airScrubberCount\",\"ColumnText\":\"Air Scrubber Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":40,\"ColumnWidth\":160,\"ColumnName\":\"customerEmailAddresses\",\"ColumnText\":\"Email\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":41,\"ColumnWidth\":160,\"ColumnName\":\"customerPhoneNumbers\",\"ColumnText\":\"Phone Number\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":42,\"ColumnWidth\":110,\"ColumnName\":\"insuranceClaimNumber\",\"ColumnText\":\"Insurance Claim #\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":43,\"ColumnWidth\":110,\"ColumnName\":\"dateOfLoss\",\"ColumnText\":\"Date of Loss\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":44,\"ColumnWidth\":110,\"ColumnName\":\"referralName\",\"ColumnText\":\"Referral\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":45,\"ColumnWidth\":110,\"ColumnName\":\"referralBusiness\",\"ColumnText\":\"Referral Business\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":46,\"ColumnWidth\":120,\"ColumnName\":\"franchiseName\",\"ColumnText\":\"Franchise\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":47,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDetermination\",\"ColumnText\":\"Not Sold/Cancelled Determination\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":48,\"ColumnWidth\":110,\"ColumnName\":\"completedDate\",\"ColumnText\":\"Job Completed date\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":49,\"ColumnWidth\":90,\"ColumnName\":\"amountDue\",\"ColumnText\":\"$ Due\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":50,\"ColumnWidth\":80,\"ColumnName\":\"hasEstimate\",\"ColumnText\":\"Est\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":51,\"ColumnWidth\":80,\"ColumnName\":\"hasInvoice\",\"ColumnText\":\"Inv\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":52,\"ColumnWidth\":130,\"ColumnName\":\"stormName\",\"ColumnText\":\"Event Name\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":53,\"ColumnWidth\":120,\"ColumnName\":\"leadType\",\"ColumnText\":\"Lead Type\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":54,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDate\",\"ColumnText\":\"Not Sold/Cancelled Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":55,\"ColumnWidth\":120,\"ColumnName\":\"notSoldOrCancelledBy\",\"ColumnText\":\"Not Sold/Cancelled By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":56,\"ColumnWidth\":120,\"ColumnName\":\"createdDate\",\"ColumnText\":\"Created Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":57,\"ColumnWidth\":120,\"ColumnName\":\"createdBy\",\"ColumnText\":\"Created By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":58,\"ColumnWidth\":120,\"ColumnName\":\"durationAtCurrentProgress\",\"ColumnText\":\"Duration at Current Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":59,\"ColumnWidth\":120,\"ColumnName\":\"facilityTypeName\",\"ColumnText\":\"Facility Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":60,\"ColumnWidth\":120,\"ColumnName\":\"structureTypeName\",\"ColumnText\":\"Structure Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":61,\"ColumnWidth\":120,\"ColumnName\":\"finalUploadDueDate\",\"ColumnText\":\"Upload Due Dates\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":62,\"ColumnWidth\":120,\"ColumnName\":\"selfAuditStatus\",\"ColumnText\":\"Audit Details\",\"IsVisible\":false,\"Type\":\"numeric\"}]"
                        });
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Session.UserSession", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsExpired")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("SessionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSession");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Storm", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("HostFranchiseId")
                        .HasColumnType("char(36)");

                    b.Property<string>("HostFranchiseName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int>("HostFranchiseNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("HostFranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("StormEventId")
                        .HasColumnType("int");

                    b.Property<string>("StormName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Id");

                    b.HasIndex("StormEventId");

                    b.ToTable("Storm");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.StormEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("FranchiseId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FranchiseName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<int>("StormEventId")
                        .HasColumnType("int");

                    b.Property<string>("StormEventName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("StormId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("StormId");

                    b.HasIndex("FranchiseSetId", "StormId");

                    b.HasIndex("FranchiseSetId", "FranchiseId", "StormEventId")
                        .IsUnique();

                    b.HasIndex("FranchiseSetId", "FranchiseId", "StormId");

                    b.ToTable("StormEvent");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.VersionData", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ReleaseNotesUrl")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<bool>("VersionAcknowledge")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("VersionNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.HasKey("Id");

                    b.ToTable("VersionData");

                    b.HasData(
                        new
                        {
                            Id = new Guid("3b96c140-2f2d-4b51-8c83-82997beeeed0"),
                            CreatedDate = new DateTime(2022, 5, 12, 20, 35, 49, 186, DateTimeKind.Utc).AddTicks(7125),
                            ReleaseNotesUrl = "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906",
                            VersionAcknowledge = true,
                            VersionNumber = "*******"
                        });
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.WipRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("ActiveAlertCount")
                        .HasColumnType("int");

                    b.Property<string>("Adjusters")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int>("AirMoverCount")
                        .HasColumnType("int");

                    b.Property<int>("AirScrubberCount")
                        .HasColumnType("int");

                    b.Property<decimal>("AmountDue")
                        .HasColumnType("decimal(19,4)");

                    b.Property<string>("AuditStage")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CallerBusinessName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CallerFirstName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CallerFullName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("CallerId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CallerLastName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CallerPhoneNumberExtension")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CallerPhoneNumbers")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<double>("Confidence")
                        .HasColumnType("double");

                    b.Property<DateTime?>("CorporateAuditDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CorporateAuditDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CorporateAuditReturnedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("CorporateJobNumber")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CrewChiefName")
                        .HasColumnType("char(36)");

                    b.Property<string>("CustomerBusinessName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CustomerCreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CustomerEmailAddresses")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CustomerFirstName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CustomerFullName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CustomerLastName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CustomerPhoneExtesion")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("CustomerPhoneNumbers")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int>("DailyExtensionCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("DailyExtensionReasonId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DailyUploadDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("DateOfLoss")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DateReceived")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("DefaultMentorId")
                        .HasColumnType("char(36)");

                    b.Property<int>("DehuCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DispatchedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("DocumentCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DurationAtCurrentProgress")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("DurationOpen")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("FacilityTypeName")
                        .HasColumnType("int");

                    b.Property<int>("FinalExtensionCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("FinalExtensionReasonId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("FinalUploadDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("FranchiseId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FranchiseName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("FranchiseSetId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FranchiseState")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("GeneralManagerName")
                        .HasColumnType("char(36)");

                    b.Property<bool>("HasEstimate")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("HasInvoice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IgnoreMergeCandidates")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("InitialExtensionCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("InitialExtensionReasonId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("InitialUploadDueDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("InsuranceClaimNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("InsuranceCompanyName")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAuditComplete")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAuditRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsChaseLead")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDispatched")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsLeadFullySaved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsLocked")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNonStandardJob")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRedFlagged")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsWarmLead")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("JobAuditMasterId")
                        .HasColumnType("int");

                    b.Property<Guid?>("JobFileCoordinatorName")
                        .HasColumnType("char(36)");

                    b.Property<int>("JobProgress")
                        .HasColumnType("int");

                    b.Property<long>("JobRowId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("JobTypeName")
                        .HasColumnType("char(36)");

                    b.Property<int>("JournalNoteCount")
                        .HasColumnType("int");

                    b.Property<decimal?>("LeadEstimate")
                        .HasColumnType("decimal(19,4)");

                    b.Property<Guid>("LeadSource")
                        .HasColumnType("char(36)");

                    b.Property<string>("LockedByApplication")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LockedByDevice")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LockedByUser")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("LockedByUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("LockedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LossAddress1")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LossAddress2")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LossBusinessName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LossBusinessPhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("LossCity")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("LossCountryId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LossCountryName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LossCountyName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("LossPostalCode")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("LossStateAbbreviation")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("LossStateId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LossStateName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("MarketingRepName")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MentorId")
                        .HasColumnType("char(36)");

                    b.Property<string>("MergeCandidates")
                        .HasColumnType("json");

                    b.Property<string>("MergeSource")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("MergeTarget")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("MostRecentDailyUploadCompleted")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("MostRecentFinalUploadCompleted")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("MostRecentInitialUploadCompleted")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("NotSoldOrCancelledBy")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime>("NotSoldOrCancelledDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("NotSoldOrCancelledDetermination")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<int>("NumberOfFinalReturns")
                        .HasColumnType("int");

                    b.Property<Guid?>("OfficeManagerName")
                        .HasColumnType("char(36)");

                    b.Property<int>("PhotoCount")
                        .HasColumnType("int");

                    b.Property<double>("PreliminaryEstimate")
                        .HasColumnType("double");

                    b.Property<string>("PriorityResponderFullName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("PriorityResponderName")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ProductionManagerName")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ProjectManagerName")
                        .HasColumnType("char(36)");

                    b.Property<string>("ProjectNumber")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("PropertyTypeName")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ReconSuperintendent")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RecpDispatcherName")
                        .HasColumnType("char(36)");

                    b.Property<string>("RedFlagNotes")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("ReferralBusiness")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("ReferralName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid?>("ReferredByName")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ReportedByName")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<DateTime?>("SelfAuditDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("SelfAuditReturnedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("StatusNotes")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("StormName")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<Guid>("StructureTypeName")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("TargetCompletionDate")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("DateReceived");

                    b.HasIndex("FranchiseSetId");

                    b.HasIndex("JobProgress");

                    b.HasIndex("FranchiseSetId", "JobProgress");

                    b.HasIndex("MentorId", "JobProgress");

                    b.ToTable("WipRecord");
                });

            modelBuilder.Entity("Servpro.FranchiseSystems.Framework.Messaging.InboxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("CorrelationId")
                        .IsRequired()
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Message")
                        .HasMaxLength(16000000)
                        .HasColumnType("mediumtext");

                    b.Property<string>("MessageId")
                        .IsRequired()
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id")
                        .HasName("PRIMARY");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("Message")
                        .HasAnnotation("MySql:IndexPrefixLength", new[] { 255 });

                    b.HasIndex("MessageId");

                    b.HasIndex("CorrelationId", "MessageType");

                    b.HasIndex("MessageType", "Message")
                        .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 255 });

                    b.ToTable("InboxMessages");
                });

            modelBuilder.Entity("Servpro.FranchiseSystems.Framework.Messaging.OutboxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("CorrelationId")
                        .IsRequired()
                        .HasColumnType("char(36)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("DispatchAttempts")
                        .HasColumnType("int");

                    b.Property<bool>("Dispatched")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("DispatchedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDead")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LockedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(16000000)
                        .HasColumnType("mediumtext");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(240)
                        .HasColumnType("varchar(240)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("Version")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.HasKey("Id")
                        .HasName("PRIMARY");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("Id", "Version");

                    b.HasIndex("Dispatched", "LockedAt", "DispatchAttempts", "IsDead");

                    b.ToTable("OutboxMessages");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Contact", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Business", "Business")
                        .WithMany("Contacts")
                        .HasForeignKey("BusinessId");

                    b.Navigation("Business");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Equipment", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.EquipmentModel", "EquipmentModel")
                        .WithMany("Equipments")
                        .HasForeignKey("EquipmentModelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentModel");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentModel", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.EquipmentType", "EquipmentType")
                        .WithMany("EquipmentModels")
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentType");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentPlacement", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.Equipment", "Equipment")
                        .WithMany("EquipmentPlacements")
                        .HasForeignKey("EquipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobArea", "JobArea")
                        .WithMany("EquipmentPlacements")
                        .HasForeignKey("JobAreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Equipment");

                    b.Navigation("JobArea");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentPlacementReading", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.EquipmentPlacement", "EquipmentPlacement")
                        .WithMany("EquipmentPlacementReadings")
                        .HasForeignKey("EquipmentPlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("EquipmentPlacementReadings")
                        .HasForeignKey("JobVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.Zone", "Zone")
                        .WithMany("EquipmentPlacementReadings")
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EquipmentPlacement");

                    b.Navigation("JobVisit");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobArea", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "BeginJobVisit")
                        .WithMany("BeginVisitJobAreas")
                        .HasForeignKey("BeginJobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "EndJobVisit")
                        .WithMany("EndVisitJobAreas")
                        .HasForeignKey("EndJobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobAreas")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Room", "Room")
                        .WithMany("JobAreas")
                        .HasForeignKey("RoomId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.Zone", "Zone")
                        .WithMany("JobAreas")
                        .HasForeignKey("ZoneId");

                    b.Navigation("BeginJobVisit");

                    b.Navigation("EndJobVisit");

                    b.Navigation("Job");

                    b.Navigation("Room");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobAreaMaterial", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "BeginJobVisit")
                        .WithMany("BeginJobVisitJobAreaMaterials")
                        .HasForeignKey("BeginJobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "GoalMetOnJobVisit")
                        .WithMany("GoalMetOnJobVisitJobAreaMaterials")
                        .HasForeignKey("GoalMetOnJobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobArea", "JobArea")
                        .WithMany("JobAreaMaterials")
                        .HasForeignKey("JobAreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobMaterial", "JobMaterial")
                        .WithMany("JobAreaMaterials")
                        .HasForeignKey("JobMaterialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "RemovedOnJobVisit")
                        .WithMany("RemovedOnJobVisitJobAreaMaterials")
                        .HasForeignKey("RemovedOnJobVisitId");

                    b.Navigation("BeginJobVisit");

                    b.Navigation("GoalMetOnJobVisit");

                    b.Navigation("JobArea");

                    b.Navigation("JobMaterial");

                    b.Navigation("RemovedOnJobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobAreaMaterialReading", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobAreaMaterial", "JobAreaMaterial")
                        .WithMany("JobAreaMaterialReadings")
                        .HasForeignKey("JobAreaMaterialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("JobAreaMaterialReadings")
                        .HasForeignKey("JobVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobAreaMaterial");

                    b.Navigation("JobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobMaterial", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobMaterials")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobTriStateAnswer", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobTriStateAnswers")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobVisit", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobVisits")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobVisitTriStateAnswer", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("JobVisitTriStateAnswers")
                        .HasForeignKey("JobVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.RoomFlooringTypeAffected", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Room", "Room")
                        .WithMany("RoomFlooringTypesAffected")
                        .HasForeignKey("RoomId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Room");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Task", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("Tasks")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("Tasks")
                        .HasForeignKey("JobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.Zone", "Zone")
                        .WithMany("Tasks")
                        .HasForeignKey("ZoneId");

                    b.Navigation("Job");

                    b.Navigation("JobVisit");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Zone", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("Zones")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.ZoneReading", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("ZoneReadings")
                        .HasForeignKey("JobVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.JournalNote", "JournalNote")
                        .WithMany("ZoneReadings")
                        .HasForeignKey("JournalNoteId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.Zone", "Zone")
                        .WithMany("ZoneReadings")
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobVisit");

                    b.Navigation("JournalNote");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.FirstNoticeActivity", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("FirstNoticeActivities")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.FirstNoticeUserActivityTracker", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("FirstNoticeUserActivityTrackers")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Job", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Contact", "Caller")
                        .WithMany()
                        .HasForeignKey("CallerId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Contact", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("Servpro.Franchise.JobService.Models.MobileData", "MobileData")
                        .WithMany("Jobs")
                        .HasForeignKey("MobileDataId");

                    b.Navigation("Caller");

                    b.Navigation("Customer");

                    b.Navigation("MobileData");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobActionLocation", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobActionLocations")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobBusinessMap", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Business", "Business")
                        .WithMany("JobBusinesess")
                        .HasForeignKey("BusinessId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobBusinesses")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Business");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobContactMap", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Contact", "Contact")
                        .WithMany("JobContactMaps")
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobContacts")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contact");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobInvoice", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.MediaMetadata", "MediaMetadata")
                        .WithOne("JobInvoice")
                        .HasForeignKey("Servpro.Franchise.JobService.Models.JobInvoice", "MediaMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MediaMetadata");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobLock", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobLocks")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobProgressHistory", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobProgressHistory")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobSketch", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JobSketches")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.MediaMetadata", "MediaMetadata")
                        .WithOne("JobSketch")
                        .HasForeignKey("Servpro.Franchise.JobService.Models.JobSketch", "MediaMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");

                    b.Navigation("MediaMetadata");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobSketchJobVisit", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.JobSketch", "JobSketch")
                        .WithMany("JobSketchJobVisits")
                        .HasForeignKey("JobSketchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("JobSketchJobVisits")
                        .HasForeignKey("JobVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobSketch");

                    b.Navigation("JobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JournalNote", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("JournalNotes")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.RoomFlooringTypeAffected", "RoomFlooringTypeAffected")
                        .WithMany("JournalNotes")
                        .HasForeignKey("RoomFlooringTypeAffectedId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.Task", "Task")
                        .WithMany("JournalNotes")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Job");

                    b.Navigation("RoomFlooringTypeAffected");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.LineItem", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("LineItems")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("LineItems")
                        .HasForeignKey("JobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Room", "Room")
                        .WithMany("LineItems")
                        .HasForeignKey("RoomId");

                    b.Navigation("Job");

                    b.Navigation("JobVisit");

                    b.Navigation("Room");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.LineItemNote", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.LineItem", "LineItem")
                        .WithMany("Notes")
                        .HasForeignKey("LineItemId");

                    b.HasOne("Servpro.Franchise.JobService.Models.Room", "Room")
                        .WithMany("LineItemNotes")
                        .HasForeignKey("RoomId");

                    b.Navigation("LineItem");

                    b.Navigation("Room");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MediaMetadata", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobArea", "JobArea")
                        .WithMany("MediaMetadata")
                        .HasForeignKey("JobAreaId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Servpro.Franchise.JobService.Models.Job", "Job")
                        .WithMany("MediaMetadata")
                        .HasForeignKey("JobId")
                        .HasPrincipalKey("Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("MediaMetadata")
                        .HasForeignKey("JobVisitId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Job");

                    b.Navigation("JobArea");

                    b.Navigation("JobVisit");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Room", b =>
                {
                    b.HasOne("Servpro.Franchise.JobService.Models.Drybook.JobVisit", "JobVisit")
                        .WithMany("Rooms")
                        .HasForeignKey("DryOnJobVisitId");

                    b.HasOne("Servpro.Franchise.JobService.Models.JournalNote", "JournalNote")
                        .WithMany("Rooms")
                        .HasForeignKey("PreExistingConditionsDiaryNoteId");

                    b.Navigation("JobVisit");

                    b.Navigation("JournalNote");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Business", b =>
                {
                    b.Navigation("Contacts");

                    b.Navigation("JobBusinesess");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Contact", b =>
                {
                    b.Navigation("JobContactMaps");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Equipment", b =>
                {
                    b.Navigation("EquipmentPlacements");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentModel", b =>
                {
                    b.Navigation("Equipments");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentPlacement", b =>
                {
                    b.Navigation("EquipmentPlacementReadings");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.EquipmentType", b =>
                {
                    b.Navigation("EquipmentModels");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobArea", b =>
                {
                    b.Navigation("EquipmentPlacements");

                    b.Navigation("JobAreaMaterials");

                    b.Navigation("MediaMetadata");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobAreaMaterial", b =>
                {
                    b.Navigation("JobAreaMaterialReadings");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobMaterial", b =>
                {
                    b.Navigation("JobAreaMaterials");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.JobVisit", b =>
                {
                    b.Navigation("BeginJobVisitJobAreaMaterials");

                    b.Navigation("BeginVisitJobAreas");

                    b.Navigation("EndVisitJobAreas");

                    b.Navigation("EquipmentPlacementReadings");

                    b.Navigation("GoalMetOnJobVisitJobAreaMaterials");

                    b.Navigation("JobAreaMaterialReadings");

                    b.Navigation("JobSketchJobVisits");

                    b.Navigation("JobVisitTriStateAnswers");

                    b.Navigation("LineItems");

                    b.Navigation("MediaMetadata");

                    b.Navigation("RemovedOnJobVisitJobAreaMaterials");

                    b.Navigation("Rooms");

                    b.Navigation("Tasks");

                    b.Navigation("ZoneReadings");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.RoomFlooringTypeAffected", b =>
                {
                    b.Navigation("JournalNotes");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Task", b =>
                {
                    b.Navigation("JournalNotes");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Drybook.Zone", b =>
                {
                    b.Navigation("EquipmentPlacementReadings");

                    b.Navigation("JobAreas");

                    b.Navigation("Tasks");

                    b.Navigation("ZoneReadings");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Job", b =>
                {
                    b.Navigation("FirstNoticeActivities");

                    b.Navigation("FirstNoticeUserActivityTrackers");

                    b.Navigation("JobActionLocations");

                    b.Navigation("JobAreas");

                    b.Navigation("JobBusinesses");

                    b.Navigation("JobContacts");

                    b.Navigation("JobLocks");

                    b.Navigation("JobMaterials");

                    b.Navigation("JobProgressHistory");

                    b.Navigation("JobSketches");

                    b.Navigation("JobTriStateAnswers");

                    b.Navigation("JobVisits");

                    b.Navigation("JournalNotes");

                    b.Navigation("LineItems");

                    b.Navigation("MediaMetadata");

                    b.Navigation("Tasks");

                    b.Navigation("Zones");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JobSketch", b =>
                {
                    b.Navigation("JobSketchJobVisits");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.JournalNote", b =>
                {
                    b.Navigation("Rooms");

                    b.Navigation("ZoneReadings");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.LineItem", b =>
                {
                    b.Navigation("Notes");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MediaMetadata", b =>
                {
                    b.Navigation("JobInvoice");

                    b.Navigation("JobSketch");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.MobileData", b =>
                {
                    b.Navigation("Jobs");
                });

            modelBuilder.Entity("Servpro.Franchise.JobService.Models.Room", b =>
                {
                    b.Navigation("JobAreas");

                    b.Navigation("LineItemNotes");

                    b.Navigation("LineItems");

                    b.Navigation("RoomFlooringTypesAffected");
                });
#pragma warning restore 612, 618
        }
    }
}
