﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.LinkUnlink
{
    public class LinkProjects
    {
        public class Command : IRequest<Unit>
        {
            public Guid SourceJobId { get; set; }
            public Guid TargetJobId { get; set; }
            public bool CopyPhotos { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.SourceJobId).NotEmpty();
                RuleFor(x => x.TargetJobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _dbContext;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IAmazonS3 _clientS3;
            private readonly IConfiguration _configuration;
            private readonly ILogger<LinkProjects> _logger;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private GetLookups.Dto _lookups;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(
                JobDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient,
                IConfiguration configuration,
                ILogger<LinkProjects> logger,
                IAmazonS3 clientS3,
                ISessionIdAccessor sessionIdAccessor)
            {
                _dbContext = context;
                _lookupServiceClient = lookupServiceClient;
                _configuration = configuration;
                _logger = logger;
                _clientS3 = clientS3;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler began with: {@request}", request);

                var userInfo = _userInfoAccessor.GetUserInfo();
                _lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                Job sourceJob = await _dbContext.Jobs
                    .Include(j => j.MediaMetadata)
                    .FirstOrDefaultAsync(j => j.Id == request.SourceJobId, cancellationToken);

                if (sourceJob == null)
                {
                    throw new ResourceNotFoundException($"Source Job {request.SourceJobId} not found.");
                }

                Job targetJob = await _dbContext.Jobs
                    .Include(j => j.MediaMetadata)
                    .FirstOrDefaultAsync(j => j.Id == request.TargetJobId, cancellationToken);

                if (targetJob == null)
                {
                    throw new ResourceNotFoundException($"Target Job {request.TargetJobId} not found.");
                }

                string sourceJobProjectGroup = Regex.Match(sourceJob.ProjectNumber, ProjectNumberIdentifier.ProjectNumberRegExString).Value;
                string targetJobProjectGroup = Regex.Match(targetJob.ProjectNumber, ProjectNumberIdentifier.ProjectNumberRegExString).Value;

                if (sourceJobProjectGroup == targetJobProjectGroup)
                {
                    throw new Exception($"Jobs are already linked.");
                }

                string sourceJobLossTypeAbbreviation = _lookups.LossTypes.FirstOrDefault(x => x.Id == sourceJob.LossTypeId)?.Abbreviation;
                string newProjectNumber = $"{targetJobProjectGroup}{sourceJobLossTypeAbbreviation}";

                sourceJob.ProjectNumber = newProjectNumber;

                if(request.CopyPhotos)
                {
                    await MapMediaMedatadaAsync(sourceJob, targetJob);
                }

                var jobUpdatedEvent = GenerateJobUpdatedEvent(request, newProjectNumber, userInfo);
                _dbContext.OutboxMessages.Add(jobUpdatedEvent);

                _dbContext.Update(sourceJob);
                await _dbContext.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private OutboxMessage GenerateJobUpdatedEvent(Command request, string newProjectNumber, UserInfo userInfo)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var jobUpdatedDto = new JobUpdatedEvent.JobUpdatedDto
                {
                    Changes = new Dictionary<string, object>() { { nameof(JobUpdatedEvent.JobUpdatedDto.ProjectNumber), newProjectNumber } },
                    JobId = request.SourceJobId,
                    ProjectNumber = newProjectNumber
                };

                var jobUpdatedEvent = new JobUpdatedEvent(jobUpdatedDto, correlationId);
                return new OutboxMessage(jobUpdatedEvent.ToJson(), nameof(JobUpdatedEvent), correlationId, userInfo.Username);
            }

            private async Task MapMediaMedatadaAsync(Job sourceJob, Job targetJob)
            {
                var allowedArtifactTypes = new List<Guid>()
                    {
                        ArtifactTypes.CauseofLoss,
                        ArtifactTypes.FrontofStructure
                    };
                var mediaToCopy = targetJob.MediaMetadata
                    .Where(m => m.MediaTypeId == MediaTypes.Photo && allowedArtifactTypes.Contains(m.ArtifactTypeId))
                    .ToList();

                _logger.LogInformation("Media to Copy: {@media}", mediaToCopy);

                await mediaToCopy.ForEachAsync(async m => 
                {
                    MediaMetadata newMedia = await GenerateNewMediaMetadata(m, sourceJob);
                    sourceJob.MediaMetadata.Add(newMedia);
                });
            }

            private async Task<MediaMetadata> GenerateNewMediaMetadata(MediaMetadata sourceMediaMetadata, Job sourceJob)
            {
                var mediaMetadataId = Guid.NewGuid();

                var newMediaMetadata = new MediaMetadata
                {
                    Id = mediaMetadataId,
                    JobId = sourceJob.Id,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    FranchiseSetId = sourceJob.FranchiseSetId,
                    IsDeleted = sourceMediaMetadata.IsDeleted,
                    IsForUpload = sourceMediaMetadata.IsForUpload,
                    Name = sourceMediaMetadata.Name,
                    MediaTypeId = sourceMediaMetadata.MediaTypeId,
                    ArtifactTypeId = sourceMediaMetadata.ArtifactTypeId,
                    ArtifactDate = DateTime.UtcNow,
                    BucketName = _configuration[S3MediaBucketNameKey],
                    UploadedSuccessfully = true,
                };

                if (!string.IsNullOrEmpty(sourceMediaMetadata.MediaPath.Trim()))
                {
                    newMediaMetadata.MediaPath = newMediaMetadata.GetKey();
                    try
                    {
                        CopyObjectRequest request = new CopyObjectRequest
                        {
                            SourceBucket = sourceMediaMetadata.BucketName, 
                            SourceKey = sourceMediaMetadata.MediaPath,
                            DestinationBucket = newMediaMetadata.BucketName,
                            DestinationKey = newMediaMetadata.MediaPath
                        };
                        CopyObjectResponse response = await _clientS3.CopyObjectAsync(request);
                    }
                    catch (AmazonS3Exception e)
                    {
                        _logger.LogError("Error encountered on server. Message:'{0}' when writing an object", e.Message);
                        throw new Exception($"Error encountered on server. Message:'{e.Message}' when writing an object");
                    }
                    catch (Exception e)
                    {
                        _logger.LogError("Unknown encountered on server. Message:'{0}' when writing an object", e.Message);
                        throw new Exception($"Unknown encountered on server. Message:'{e.Message}' when writing an object");
                    }
                }
                return newMediaMetadata;
            }
        }
    }
}
