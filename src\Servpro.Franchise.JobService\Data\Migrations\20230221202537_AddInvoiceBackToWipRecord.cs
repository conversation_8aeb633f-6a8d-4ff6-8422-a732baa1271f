﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddInvoiceBackToWipRecord : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("0ad4440b-7b4c-41f8-a719-d8b40e6aa688"));

            migrationBuilder.AddColumn<decimal>(
                name: "TotalRevenue",
                table: "WipRecord",
                type: "decimal(19,4)",
                precision: 19,
                scale: 4,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.UpdateData(
                table: "ServproWipColumnCustomization",
                keyColumn: "Id",
                keyValue: new Guid("416020c8-1e92-4cdd-847e-ec7fcee7d333"),
                column: "Data",
                value: "[{\"ColumnPosition\":3,\"ColumnWidth\":95,\"ColumnName\":\"dateReceived\",\"ColumnText\":\"Date Received\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":4,\"ColumnWidth\":100,\"ColumnName\":\"corporateJobNumber\",\"ColumnText\":\"Corporate Ref #\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":5,\"ColumnWidth\":150,\"ColumnName\":\"projectNumber\",\"ColumnText\":\"Project #\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":6,\"ColumnWidth\":90,\"ColumnName\":\"propertyTypeName\",\"ColumnText\":\"Property Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":7,\"ColumnWidth\":80,\"ColumnName\":\"jobTypeName\",\"ColumnText\":\"Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":8,\"ColumnWidth\":140,\"ColumnName\":\"progress\",\"ColumnText\":\"Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":9,\"ColumnWidth\":80,\"ColumnName\":\"isRedFlagged\",\"ColumnText\":\"Project Flag\",\"IsVisible\":false,\"Type\":\"boolean\"},{\"ColumnPosition\":10,\"ColumnWidth\":140,\"ColumnName\":\"customerFullName\",\"ColumnText\":\"Customer\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":11,\"ColumnWidth\":120,\"ColumnName\":\"PreliminaryEstimate\",\"ColumnText\":\"Preliminary Estimate $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":12,\"ColumnWidth\":120,\"ColumnName\":\"FirstSubmitToAdjuster\",\"ColumnText\":\"1st Submission to Adjuster $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":13,\"ColumnWidth\":120,\"ColumnName\":\"Confidence\",\"ColumnText\":\"Confidence %\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":14,\"ColumnWidth\":150,\"ColumnName\":\"totalRevenue\",\"ColumnText\":\"Invoiced Amount $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":15,\"ColumnWidth\":140,\"ColumnName\":\"priorityResponderName\",\"ColumnText\":\"Priority Responder\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":16,\"ColumnWidth\":140,\"ColumnName\":\"jobFileCoordinatorName\",\"ColumnText\":\"Job File Coordinator\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":17,\"ColumnWidth\":140,\"ColumnName\":\"projectManagerName\",\"ColumnText\":\"Project Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":18,\"ColumnWidth\":140,\"ColumnName\":\"estimatorName\",\"ColumnText\":\"Estimator\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":19,\"ColumnWidth\":140,\"ColumnName\":\"productionManagerName\",\"ColumnText\":\"Production Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":20,\"ColumnWidth\":140,\"ColumnName\":\"marketingRepName\",\"ColumnText\":\"Marketing Rep.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":21,\"ColumnWidth\":140,\"ColumnName\":\"agentName\",\"ColumnText\":\"Agent\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":22,\"ColumnWidth\":140,\"ColumnName\":\"assignedServiceRepName\",\"ColumnText\":\"Assigned Service Rep.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":23,\"ColumnWidth\":140,\"ColumnName\":\"crewChiefName\",\"ColumnText\":\"Crew Chief\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":24,\"ColumnWidth\":140,\"ColumnName\":\"recpDispatcherName\",\"ColumnText\":\"Recp Dispatcher\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":25,\"ColumnWidth\":140,\"ColumnName\":\"generalManagerName\",\"ColumnText\":\"General Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":26,\"ColumnWidth\":140,\"ColumnName\":\"officeManagerName\",\"ColumnText\":\"Office Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":27,\"ColumnWidth\":140,\"ColumnName\":\"reconSuperintendent\",\"ColumnText\":\"Recon Supt\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":28,\"ColumnWidth\":210,\"ColumnName\":\"statusNotes\",\"ColumnText\":\"Status Notes\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":29,\"ColumnWidth\":80,\"ColumnName\":\"lossPostalCode\",\"ColumnText\":\"Loss Zip\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":30,\"ColumnWidth\":130,\"ColumnName\":\"insuranceCompanyName\",\"ColumnText\":\"Insurance Co.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":31,\"ColumnWidth\":110,\"ColumnName\":\"adjusters\",\"ColumnText\":\"Adjuster\",\"IsVisible\":true,\"Type\":\"array\"},{\"ColumnPosition\":32,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress1\",\"ColumnText\":\"Loss Address 1\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":33,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress2\",\"ColumnText\":\"Loss Address 2\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":34,\"ColumnWidth\":90,\"ColumnName\":\"lossCity\",\"ColumnText\":\"Loss City\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":35,\"ColumnWidth\":80,\"ColumnName\":\"lossStateAbbreviation\",\"ColumnText\":\"Loss State\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":36,\"ColumnWidth\":130,\"ColumnName\":\"leadSource\",\"ColumnText\":\"Lead Source\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":37,\"ColumnWidth\":130,\"ColumnName\":\"reportedByName\",\"ColumnText\":\"Reported By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":38,\"ColumnWidth\":130,\"ColumnName\":\"referredByName\",\"ColumnText\":\"Referred By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":39,\"ColumnWidth\":110,\"ColumnName\":\"durationOpen\",\"ColumnText\":\"Duration Open\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":40,\"ColumnWidth\":110,\"ColumnName\":\"airMoverCount\",\"ColumnText\":\"Air Mover Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":41,\"ColumnWidth\":110,\"ColumnName\":\"activeAlertCount\",\"ColumnText\":\"DB Alerts\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":42,\"ColumnWidth\":110,\"ColumnName\":\"callerFullName\",\"ColumnText\":\"Caller\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":43,\"ColumnWidth\":110,\"ColumnName\":\"dehuCount\",\"ColumnText\":\"Dehu Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":44,\"ColumnWidth\":120,\"ColumnName\":\"airScrubberCount\",\"ColumnText\":\"Air Scrubber Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":45,\"ColumnWidth\":160,\"ColumnName\":\"customerEmailAddresses\",\"ColumnText\":\"Email\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":46,\"ColumnWidth\":160,\"ColumnName\":\"customerPhoneNumbers\",\"ColumnText\":\"Phone Number\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":47,\"ColumnWidth\":110,\"ColumnName\":\"insuranceClaimNumber\",\"ColumnText\":\"Insurance Claim #\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":48,\"ColumnWidth\":110,\"ColumnName\":\"dateOfLoss\",\"ColumnText\":\"Date of Loss\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":49,\"ColumnWidth\":110,\"ColumnName\":\"referralName\",\"ColumnText\":\"Referral\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":50,\"ColumnWidth\":110,\"ColumnName\":\"referralBusiness\",\"ColumnText\":\"Referral Business\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":51,\"ColumnWidth\":120,\"ColumnName\":\"franchiseName\",\"ColumnText\":\"Franchise\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":52,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDetermination\",\"ColumnText\":\"Not Sold/Cancelled Determination\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":53,\"ColumnWidth\":110,\"ColumnName\":\"completedDate\",\"ColumnText\":\"Job Completed date\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":54,\"ColumnWidth\":90,\"ColumnName\":\"amountDue\",\"ColumnText\":\"$ Due\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":55,\"ColumnWidth\":80,\"ColumnName\":\"hasEstimate\",\"ColumnText\":\"Est\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":56,\"ColumnWidth\":80,\"ColumnName\":\"hasInvoice\",\"ColumnText\":\"Inv\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":57,\"ColumnWidth\":130,\"ColumnName\":\"stormName\",\"ColumnText\":\"Event Name\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":58,\"ColumnWidth\":120,\"ColumnName\":\"leadType\",\"ColumnText\":\"Lead Type\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":59,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDate\",\"ColumnText\":\"Not Sold/Cancelled Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":60,\"ColumnWidth\":120,\"ColumnName\":\"notSoldOrCancelledBy\",\"ColumnText\":\"Not Sold/Cancelled By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":61,\"ColumnWidth\":120,\"ColumnName\":\"createdDate\",\"ColumnText\":\"Created Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":62,\"ColumnWidth\":120,\"ColumnName\":\"createdBy\",\"ColumnText\":\"Created By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":63,\"ColumnWidth\":120,\"ColumnName\":\"durationAtCurrentProgress\",\"ColumnText\":\"Duration at Current Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":64,\"ColumnWidth\":120,\"ColumnName\":\"facilityTypeName\",\"ColumnText\":\"Facility Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":65,\"ColumnWidth\":120,\"ColumnName\":\"structureTypeName\",\"ColumnText\":\"Structure Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":66,\"ColumnWidth\":120,\"ColumnName\":\"finalUploadDueDate\",\"ColumnText\":\"Upload Due Dates\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":67,\"ColumnWidth\":120,\"ColumnName\":\"selfAuditStatus\",\"ColumnText\":\"Audit Details\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":68,\"ColumnWidth\":80,\"ColumnName\":\"referralLinkVerified\",\"ColumnText\":\"Referral Link Verified\",\"IsVisible\":false,\"Type\":\"array\"}]");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("b993a646-79ea-4f1c-a2b4-976f16bcf221"), null, new DateTime(2023, 2, 21, 20, 25, 36, 997, DateTimeKind.Utc).AddTicks(1775), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("b993a646-79ea-4f1c-a2b4-976f16bcf221"));

            migrationBuilder.DropColumn(
                name: "TotalRevenue",
                table: "WipRecord");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.UpdateData(
                table: "ServproWipColumnCustomization",
                keyColumn: "Id",
                keyValue: new Guid("416020c8-1e92-4cdd-847e-ec7fcee7d333"),
                column: "Data",
                value: "[{\"ColumnPosition\":3,\"ColumnWidth\":95,\"ColumnName\":\"dateReceived\",\"ColumnText\":\"Date Received\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":4,\"ColumnWidth\":100,\"ColumnName\":\"corporateJobNumber\",\"ColumnText\":\"Corporate Ref #\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":5,\"ColumnWidth\":150,\"ColumnName\":\"projectNumber\",\"ColumnText\":\"Project #\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":6,\"ColumnWidth\":90,\"ColumnName\":\"propertyTypeName\",\"ColumnText\":\"Property Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":7,\"ColumnWidth\":80,\"ColumnName\":\"jobTypeName\",\"ColumnText\":\"Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":8,\"ColumnWidth\":140,\"ColumnName\":\"progress\",\"ColumnText\":\"Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":9,\"ColumnWidth\":80,\"ColumnName\":\"isRedFlagged\",\"ColumnText\":\"Project Flag\",\"IsVisible\":false,\"Type\":\"boolean\"},{\"ColumnPosition\":10,\"ColumnWidth\":140,\"ColumnName\":\"customerFullName\",\"ColumnText\":\"Customer\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":11,\"ColumnWidth\":120,\"ColumnName\":\"PreliminaryEstimate\",\"ColumnText\":\"Preliminary Estimate $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":12,\"ColumnWidth\":120,\"ColumnName\":\"FirstSubmitToAdjuster\",\"ColumnText\":\"1st Submission to Adjuster $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":13,\"ColumnWidth\":120,\"ColumnName\":\"Confidence\",\"ColumnText\":\"Confidence %\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":14,\"ColumnWidth\":140,\"ColumnName\":\"priorityResponderName\",\"ColumnText\":\"Priority Responder\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":15,\"ColumnWidth\":140,\"ColumnName\":\"jobFileCoordinatorName\",\"ColumnText\":\"Job File Coordinator\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":16,\"ColumnWidth\":140,\"ColumnName\":\"projectManagerName\",\"ColumnText\":\"Project Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":17,\"ColumnWidth\":140,\"ColumnName\":\"estimatorName\",\"ColumnText\":\"Estimator\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":18,\"ColumnWidth\":140,\"ColumnName\":\"productionManagerName\",\"ColumnText\":\"Production Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":19,\"ColumnWidth\":140,\"ColumnName\":\"marketingRepName\",\"ColumnText\":\"Marketing Rep.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":20,\"ColumnWidth\":140,\"ColumnName\":\"agentName\",\"ColumnText\":\"Agent\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":21,\"ColumnWidth\":140,\"ColumnName\":\"assignedServiceRepName\",\"ColumnText\":\"Assigned Service Rep.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":22,\"ColumnWidth\":140,\"ColumnName\":\"crewChiefName\",\"ColumnText\":\"Crew Chief\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":23,\"ColumnWidth\":140,\"ColumnName\":\"recpDispatcherName\",\"ColumnText\":\"Recp Dispatcher\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":24,\"ColumnWidth\":140,\"ColumnName\":\"generalManagerName\",\"ColumnText\":\"General Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":25,\"ColumnWidth\":140,\"ColumnName\":\"officeManagerName\",\"ColumnText\":\"Office Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":26,\"ColumnWidth\":140,\"ColumnName\":\"reconSuperintendent\",\"ColumnText\":\"Recon Supt\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":27,\"ColumnWidth\":210,\"ColumnName\":\"statusNotes\",\"ColumnText\":\"Status Notes\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":28,\"ColumnWidth\":80,\"ColumnName\":\"lossPostalCode\",\"ColumnText\":\"Loss Zip\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":29,\"ColumnWidth\":130,\"ColumnName\":\"insuranceCompanyName\",\"ColumnText\":\"Insurance Co.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":30,\"ColumnWidth\":110,\"ColumnName\":\"adjusters\",\"ColumnText\":\"Adjuster\",\"IsVisible\":true,\"Type\":\"array\"},{\"ColumnPosition\":31,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress1\",\"ColumnText\":\"Loss Address 1\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":32,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress2\",\"ColumnText\":\"Loss Address 2\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":33,\"ColumnWidth\":90,\"ColumnName\":\"lossCity\",\"ColumnText\":\"Loss City\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":34,\"ColumnWidth\":80,\"ColumnName\":\"lossStateAbbreviation\",\"ColumnText\":\"Loss State\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":35,\"ColumnWidth\":130,\"ColumnName\":\"leadSource\",\"ColumnText\":\"Lead Source\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":36,\"ColumnWidth\":130,\"ColumnName\":\"reportedByName\",\"ColumnText\":\"Reported By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":37,\"ColumnWidth\":130,\"ColumnName\":\"referredByName\",\"ColumnText\":\"Referred By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":38,\"ColumnWidth\":110,\"ColumnName\":\"durationOpen\",\"ColumnText\":\"Duration Open\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":39,\"ColumnWidth\":110,\"ColumnName\":\"airMoverCount\",\"ColumnText\":\"Air Mover Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":40,\"ColumnWidth\":110,\"ColumnName\":\"activeAlertCount\",\"ColumnText\":\"DB Alerts\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":41,\"ColumnWidth\":110,\"ColumnName\":\"callerFullName\",\"ColumnText\":\"Caller\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":42,\"ColumnWidth\":110,\"ColumnName\":\"dehuCount\",\"ColumnText\":\"Dehu Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":43,\"ColumnWidth\":120,\"ColumnName\":\"airScrubberCount\",\"ColumnText\":\"Air Scrubber Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":44,\"ColumnWidth\":160,\"ColumnName\":\"customerEmailAddresses\",\"ColumnText\":\"Email\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":45,\"ColumnWidth\":160,\"ColumnName\":\"customerPhoneNumbers\",\"ColumnText\":\"Phone Number\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":46,\"ColumnWidth\":110,\"ColumnName\":\"insuranceClaimNumber\",\"ColumnText\":\"Insurance Claim #\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":47,\"ColumnWidth\":110,\"ColumnName\":\"dateOfLoss\",\"ColumnText\":\"Date of Loss\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":48,\"ColumnWidth\":110,\"ColumnName\":\"referralName\",\"ColumnText\":\"Referral\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":49,\"ColumnWidth\":110,\"ColumnName\":\"referralBusiness\",\"ColumnText\":\"Referral Business\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":50,\"ColumnWidth\":120,\"ColumnName\":\"franchiseName\",\"ColumnText\":\"Franchise\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":51,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDetermination\",\"ColumnText\":\"Not Sold/Cancelled Determination\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":52,\"ColumnWidth\":110,\"ColumnName\":\"completedDate\",\"ColumnText\":\"Job Completed date\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":53,\"ColumnWidth\":90,\"ColumnName\":\"amountDue\",\"ColumnText\":\"$ Due\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":54,\"ColumnWidth\":80,\"ColumnName\":\"hasEstimate\",\"ColumnText\":\"Est\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":55,\"ColumnWidth\":80,\"ColumnName\":\"hasInvoice\",\"ColumnText\":\"Inv\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":56,\"ColumnWidth\":130,\"ColumnName\":\"stormName\",\"ColumnText\":\"Event Name\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":57,\"ColumnWidth\":120,\"ColumnName\":\"leadType\",\"ColumnText\":\"Lead Type\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":58,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDate\",\"ColumnText\":\"Not Sold/Cancelled Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":59,\"ColumnWidth\":120,\"ColumnName\":\"notSoldOrCancelledBy\",\"ColumnText\":\"Not Sold/Cancelled By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":60,\"ColumnWidth\":120,\"ColumnName\":\"createdDate\",\"ColumnText\":\"Created Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":61,\"ColumnWidth\":120,\"ColumnName\":\"createdBy\",\"ColumnText\":\"Created By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":62,\"ColumnWidth\":120,\"ColumnName\":\"durationAtCurrentProgress\",\"ColumnText\":\"Duration at Current Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":63,\"ColumnWidth\":120,\"ColumnName\":\"facilityTypeName\",\"ColumnText\":\"Facility Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":64,\"ColumnWidth\":120,\"ColumnName\":\"structureTypeName\",\"ColumnText\":\"Structure Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":65,\"ColumnWidth\":120,\"ColumnName\":\"finalUploadDueDate\",\"ColumnText\":\"Upload Due Dates\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":66,\"ColumnWidth\":120,\"ColumnName\":\"selfAuditStatus\",\"ColumnText\":\"Audit Details\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":67,\"ColumnWidth\":80,\"ColumnName\":\"referralLinkVerified\",\"ColumnText\":\"Referral Link Verified\",\"IsVisible\":false,\"Type\":\"array\"}]");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("0ad4440b-7b4c-41f8-a719-d8b40e6aa688"), null, new DateTime(2023, 2, 20, 19, 5, 9, 181, DateTimeKind.Utc).AddTicks(1764), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
