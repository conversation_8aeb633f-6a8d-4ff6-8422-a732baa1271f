﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.JobService.Common.ExtensionMethods;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;
using Amazon.DynamoDBv2;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents.Forms
{
    public interface IFormsService
    {
        Task<List<FormTemplateModel>> GetFormsAsync(Job job, CancellationToken cancellationToken);
        Task<MemoryStream> GetMergedFormsAsync(MergeFormsRequest request, CancellationToken cancellationToken);
    }

    public class FormsService : IFormsService
    {
        private readonly IFranchiseServiceClient _franchiseServiceClient;
        private readonly ILookupServiceClient _lookupServiceClient;
        private readonly IConfiguration _config;
        private readonly ILogger<FormsService> _logger;
        private readonly JobDataContext _db;
        private readonly IClientRequirementsService _clientRequirementsService;
        private readonly IFormManager _formManager;
        private readonly IS3ClaimCheckValet _valet;

        public FormsService(IConfiguration config, 
            ILogger<FormsService> logger, 
            JobDataContext db, 
            IClientRequirementsService clientRequirementsService, 
            IFranchiseServiceClient franchiseServiceClient, 
            ILookupServiceClient lookupServiceClient, 
            IFormManager formManager, 
            IS3ClaimCheckValet valet)
        {
            _config = config;
            _logger = logger;
            _db = db;
            _clientRequirementsService = clientRequirementsService;
            _franchiseServiceClient = franchiseServiceClient;
            _lookupServiceClient = lookupServiceClient;
            _formManager = formManager;
            _valet = valet;
        }

        #region Get Forms

        public async Task<List<FormTemplateModel>> GetFormsAsync(Job job, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting GetFormsAsync");

            var stopwatch = Stopwatch.StartNew();

            var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
            var allJobForms = new List<FormTemplateModel>();
            var partialList = new List<FormTemplateModel>();

            var breForms = await GetFormsFromCrs(job, lookups, cancellationToken);
            _logger.LogInformation("Get BRE Forms Completed. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
            _logger.LogInformation("breForms: {@breForms}", breForms);

            var jForms = job.MediaMetadata.Where(x => !x.IsDeleted && x.FormTemplateId.HasValue).ToList();
            _logger.LogInformation("jForms: {@jForms}", jForms);

            var breFormsLookup = breForms.ToDictionary(x => x.FormTemplateId);
            breForms.ForEach(allJobForms.Add);
            AddNonRequiredForms(jForms, breForms, breFormsLookup, allJobForms);

            // Prepare parameters
            var parameters = new GetFormsParameters
            {
                JobUploadRequired = _clientRequirementsService.GetJobUploadRequiredIndicator(job),
                Insurance = await GetInsuranceClientName(job, cancellationToken),
                State = GetState(job),
                Country = GetCountry(job),
                JobType = GetJobType(job, lookups)
            };
            parameters.Language = GetLanguage(parameters.Country);
            var (isWaterForm, isFireForm, isMoldForm) = GetFormTypes(parameters.JobType);
            parameters.IsWaterForm = isWaterForm;
            parameters.IsFireForm = isFireForm;
            parameters.IsMoldForm = isMoldForm;

            _logger.LogInformation("GetFormsParameters: {@parameters}", parameters);

            var countryForms = await GetCountryFormTemplates(parameters, cancellationToken);
            var jobForms = job.MediaMetadata.Where(x => x.FormTemplateId.HasValue && !x.IsDeleted).ToList();
            _logger.LogInformation("jobForms: {@jobForms}", jobForms);

            SetFormUploadStatus(countryForms, jobForms);
            SetUsaSpanishForms(parameters, countryForms, partialList);
            SetClientSpecificForms(job, parameters, countryForms, jobForms, partialList, allJobForms);
            SetCurrentJobTypeRequiredForms(job, parameters, countryForms, jobForms, partialList, allJobForms);
            SetStandardNonRequiredForms(parameters, countryForms, partialList, allJobForms);
            SetOtherForms(parameters, countryForms, partialList, allJobForms);
            SetXactUploadedForms(job, allJobForms);

            var oldUploadedFormTemplates = GetOldUploadedForms(jobForms, allJobForms, breFormsLookup);
            if (oldUploadedFormTemplates.Count <= 0)
                return allJobForms;
            SetOldForms(parameters, oldUploadedFormTemplates, jobForms, allJobForms);

            _logger.LogInformation("GetFormsAsync Completed. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);

            return allJobForms;
        }

        public void SetXactUploadedForms(Job job, List<FormTemplateModel> allJobForms)
        {
            if (job?.MediaMetadata == null)
                return;
            if (allJobForms == null)
                return;
            
            var formIdsUploadedToXact = GetUploadedXactFormIds(job.MediaMetadata);
            allJobForms
                .Where(x => formIdsUploadedToXact.ContainsKey(x.MediaId))
                .ToList()
                .ForEach(f =>
                {
                    f.IsXactUploaded = true;
                });
        }

        public Dictionary<Guid, Guid> GetUploadedXactFormIds(ICollection<MediaMetadata> media)
        {
            if (media == null)
                return new Dictionary<Guid, Guid>();

            var formIdsUploadedToXact = media.Where(x => x.IsUploadedToXact)
                .Select(x => x.Id)
                .DistinctBy(x => x)
                .ToDictionary(x => x);

            return formIdsUploadedToXact;
        }

        public async Task<List<FormTemplateModel>> GetCountryFormTemplates(GetFormsParameters parameters, CancellationToken cancellationToken)
        {
            var country = parameters.Country;
            var formTemplates = await (from ft in _db.FormTemplates.AsNoTracking()
                                       where ft.IsActive && ft.Approved && ft.Country == country
                                       select new FormTemplateModel
                                       {
                                           FormTemplateId = ft.Id,
                                           Name = ft.Name,
                                           Description = ft.Description,
                                           IsActive = ft.IsActive,
                                           IsApproved = ft.Approved,
                                           State = ft.State,
                                           Country = ft.Country,
                                           IsWaterForm = ft.WaterForm,
                                           IsWaterFormRequired = ft.WaterFormRequired,
                                           IsFireForm = ft.FireForm,
                                           IsFireFormRequired = ft.FireFormRequired,
                                           IsMoldForm = ft.MoldForm,
                                           IsMoldFormRequired = ft.MoldFormRequired,
                                           FormalLanguage = ft.FormalLanguage,
                                           Version = ft.FormVersion,
                                           LinkedPage = ft.LinkedPage,
                                           CreatedDate = ft.CreatedDate,
                                           CreatedBy = ft.CreatedBy,
                                           RelatedForm = ft.RelatedForm,
                                           RelatedForm2 = ft.RelatedForm2,
                                           RelatedForm3 = ft.RelatedForm3,
                                           IsRequiredForCommercialJob = ft.IsRequiredForCommercialJob,
                                           IsRequiredForResidentialJob = ft.IsRequiredForResidentialJob,
                                           IsRequiredForAllLossTypes = ft.IsRequiredForAllLossTypes,
                                           IsAvailableInFirstNotice = ft.IsAvailableInFirstNotice,
                                           InsuranceClient = ft.InsuranceClient,
                                           CommercialClient = ft.CommercialClient,
                                           FileType = ft.FileType
                                       }).ToListAsync(cancellationToken);
            return formTemplates;
        }

        private static void SetOldForms(GetFormsParameters parameters, List<FormTemplateModel> oldUploadedFormTemplates, List<MediaMetadata> jobForms, List<FormTemplateModel> allJobForms)
        {
            var oldForms = MapForms(oldUploadedFormTemplates);
            // Media Id of jobforms not added in oldUploadedFormTemplates.Add Media id in oldUploadedFormTemplates from job forms after mapping it to FormTemplateModel object
            foreach (var item in oldForms)
            {
                var jobForm = jobForms.FirstOrDefault(e => e.FormTemplateId == item.FormTemplateId);
                if (jobForm != null)
                {
                    item.MediaId = jobForm.Id;
                    item.SignedDate = jobForm.SignedDate;
                }
            }

            foreach (var oldForm in oldForms)
            {
                oldForm.IsDocumentUploaded = true;

                // Set default FormCategoryDesc
                oldForm.FormCategoryDesc = oldForm.IsWaterForm || oldForm.IsFireForm || oldForm.IsMoldForm
                    ? $"Standard: {parameters.JobType} Forms"
                    : @"Other Forms";

                // Check for required forms by job type and update FormCategoryDesc
                if (parameters.IsFireForm && oldForm.IsFireFormRequired ||
                    parameters.IsWaterForm && oldForm.IsWaterFormRequired ||
                    parameters.IsMoldForm && oldForm.IsMoldFormRequired)
                {
                    oldForm.FormCategoryDesc = parameters.JobUploadRequired ? $"Required: {parameters.JobType} Forms" : $"{parameters.JobType} Forms";
                    oldForm.GroupOrder = 2;
                    oldForm.IsForUpload = true;
                    oldForm.IsRequired = parameters.JobUploadRequired;
                }

                // Check for required insurance forms
                if (oldForm.InsuranceClient != parameters.Insurance)
                    continue;

                oldForm.FormCategoryDesc = parameters.JobUploadRequired
                    ? $"Required: {oldForm.InsuranceClient} Forms"
                    : $"{oldForm.InsuranceClient} Forms";
                oldForm.GroupOrder = 1;
                oldForm.IsForUpload = true;
                oldForm.IsRequired = parameters.JobUploadRequired;
            }

            allJobForms.AddRange(oldForms);
        }

        private static List<FormTemplateModel> GetOldUploadedForms(List<MediaMetadata> jobForms, List<FormTemplateModel> allJobForms, Dictionary<Guid, FormTemplateModel> breFormsLookup)
        {
            // Ensure old forms are loaded if needed.
            var oldUploadedFormTemplates = new List<FormTemplateModel>();
            foreach (var item in jobForms)
            {
                if (!item.FormTemplateId.HasValue)
                    continue;

                var currentFormTemplate = allJobForms.FirstOrDefault(e => e.FormTemplateId == item.FormTemplateId);
                if (currentFormTemplate != null)
                    continue;

                // Item in list is from older form template and no longer current
                if (!breFormsLookup.ContainsKey(item.FormTemplateId.Value))
                    continue;

                var oldFormTemplate = breFormsLookup[item.FormTemplateId.Value];
                oldUploadedFormTemplates.Add(oldFormTemplate);
            }

            return oldUploadedFormTemplates;
        }

        private static void SetOtherForms(GetFormsParameters parameters, List<FormTemplateModel> countryForms, List<FormTemplateModel> partialList, List<FormTemplateModel> allJobForms)
        {
            // For Others: Form category.
            // Get everything that's left over (not specific to a loss type)
            var otherForms = countryForms.Except(partialList).Where(condition =>
                (condition.State == parameters.State || condition.State == null || condition.State.Trim() == string.Empty)
                && (string.IsNullOrWhiteSpace(condition.InsuranceClient) || condition.InsuranceClient == parameters.Insurance)
            ).OrderBy(order => order.Name).ToList();

            foreach (var item in otherForms)
            {
                item.FormCategoryDesc = @"Other Forms";
                item.GroupOrder = 5;
            }

            var items = otherForms
                .Where(item => allJobForms.All(q => q.FormTemplateId != item.FormTemplateId)).ToList();

            allJobForms.AddRange(items);
        }

        private static void SetStandardNonRequiredForms(
            GetFormsParameters parameters,
            List<FormTemplateModel> countryForms,
            List<FormTemplateModel> partialList,
            List<FormTemplateModel> allJobForms)
        {
            // Get standard non-required forms for the current job type
            var query = countryForms.Where(condition =>
                (condition.State == parameters.State || condition.State == null || condition.State.Trim() == string.Empty)
                && (string.IsNullOrWhiteSpace(condition.InsuranceClient) || condition.InsuranceClient == parameters.Insurance)
                && (string.IsNullOrWhiteSpace(condition.CommercialClient) || condition.CommercialClient == parameters.Insurance)
                && condition.FormalLanguage == parameters.Language);

            var formTypeQuery = Enumerable.Empty<FormTemplateModel>();
            if (parameters.IsWaterForm)
            {
                formTypeQuery = query.Where(condition => condition.IsWaterForm && !condition.IsWaterFormRequired);
            }
            else if (parameters.IsFireForm)
            {
                formTypeQuery = query.Where(condition => condition.IsFireForm && !condition.IsFireFormRequired);
            }
            else if (parameters.IsMoldForm)
            {
                formTypeQuery = query.Where(condition => condition.IsMoldForm && !condition.IsMoldFormRequired);
            }

            var standardJobType = formTypeQuery.OrderBy(order => order.Name).ToList();

            foreach (var item in standardJobType)
            {
                item.FormCategoryDesc = $"Standard: {parameters.JobType} Forms";
                item.GroupOrder = 3;
            }

            partialList.AddRange(standardJobType);
            allJobForms.AddRange(standardJobType);
        }

        private static void SetCurrentJobTypeRequiredForms(Job job, GetFormsParameters parameters, List<FormTemplateModel> countryForms,
            List<MediaMetadata> jobForms, List<FormTemplateModel> partialList, List<FormTemplateModel> allJobForms)
        {
            // get required forms for the current job type
            List<FormTemplateModel> requiredJobTypes = countryForms.Where(condition =>
                    condition.FormalLanguage == parameters.Language
                    && string.IsNullOrWhiteSpace(condition.InsuranceClient) && string.IsNullOrWhiteSpace(condition.CommercialClient)
                    && (condition.State == parameters.State || condition.State == null || condition.State.Trim() == string.Empty)
                    && (condition.IsWaterForm == parameters.IsWaterForm && condition.IsWaterFormRequired
                        || condition.IsFireForm == parameters.IsFireForm && condition.IsFireFormRequired
                        || condition.IsMoldForm == parameters.IsMoldForm && condition.IsMoldFormRequired
                        || condition.IsRequiredForAllLossTypes.HasValue && condition.IsRequiredForAllLossTypes.Value)
                    && (job.PropertyTypeId == PropertyTypes.Residential && condition.IsRequiredForResidentialJob.HasValue && condition.IsRequiredForResidentialJob.Value
                        || (job.PropertyTypeId == PropertyTypes.Commercial && condition.IsRequiredForCommercialJob.HasValue && condition.IsRequiredForCommercialJob.Value)))
                .OrderBy(order => order.Name)
                .ToList();

            // Look for any documents not yet completed - if not completed, see if any related docs are completed that should remove this doc
            // Get related form , related form2 and related form3 id of job form from requirejobtype list and then remove forms which has form templated id in id of (related form , related form2 and related form3)
            for (var i = jobForms.Count - 1; i >= 0; i--)
            {
                var uploadedRequiredForm = requiredJobTypes.FirstOrDefault(x => x.FormTemplateId == jobForms[i].FormTemplateId);
                if (uploadedRequiredForm == null)
                    continue;
                uploadedRequiredForm.IsDocumentUploaded = true;
                requiredJobTypes.RemoveAll(x =>
                    x.FormTemplateId == uploadedRequiredForm.RelatedForm ||
                    x.FormTemplateId == uploadedRequiredForm.RelatedForm2 ||
                    x.FormTemplateId == uploadedRequiredForm.RelatedForm3);
            }

            foreach (var item in requiredJobTypes)
            {
                item.FormCategoryDesc = parameters.JobUploadRequired ? $"Required: {parameters.JobType} Forms" : $"{parameters.JobType} Forms:";
                item.GroupOrder = 2;
                item.IsForUpload = true;
                item.IsRequired = parameters.JobUploadRequired;
            }

            var items = requiredJobTypes
                .Where(item => allJobForms.All(q => q.FormTemplateId != item.FormTemplateId)).ToList();

            partialList.AddRange(items);
            allJobForms.AddRange(items);
        }

        private static void SetClientSpecificForms(Job job, GetFormsParameters parameters, List<FormTemplateModel> countryForms, List<MediaMetadata> jobForms, List<FormTemplateModel> partialList, List<FormTemplateModel> allJobForms)
        {
            // Get client specific forms
            if (string.IsNullOrEmpty(parameters.Insurance))
                return;

            var clientForms = countryForms.Where(condition =>
                    condition.FormalLanguage == parameters.Language
                    && (condition.InsuranceClient == parameters.Insurance || condition.CommercialClient == parameters.Insurance)
                    && (condition.State == parameters.State || condition.State == null ||
                    condition.State.Trim() == string.Empty)
                    && (condition.IsWaterForm == parameters.IsWaterForm && condition.IsWaterFormRequired
                        || condition.IsFireForm == parameters.IsFireForm && condition.IsFireFormRequired
                        || condition.IsMoldForm == parameters.IsMoldForm && condition.IsMoldFormRequired
                        || condition.IsRequiredForAllLossTypes.HasValue && condition.IsRequiredForAllLossTypes.Value)
                    && (job.PropertyTypeId == PropertyTypes.Residential && condition.IsRequiredForResidentialJob.HasValue && condition.IsRequiredForResidentialJob.Value
                        || job.PropertyTypeId == PropertyTypes.Commercial && condition.IsRequiredForCommercialJob.HasValue && condition.IsRequiredForCommercialJob.Value))
                .OrderBy(order => order.Name).ToList();

            // Look for any documents not yet completed - if not completed, see if any related docs are completed that should remove this doc
            // Get related form , related form2 and related form3 id of job form from clientForms list and then remove forms which has form templated id  in id of (related form , related form2 and related form3)

            foreach (var uploadedClientForm in jobForms
                .Select(jobForm => clientForms
                    .FirstOrDefault(x => x.FormTemplateId == jobForm.FormTemplateId))
                .Where(uploadedClientForm => uploadedClientForm != null))
            {
                uploadedClientForm.IsDocumentUploaded = true;
                clientForms.RemoveAll(x =>
                    x.FormTemplateId == uploadedClientForm.RelatedForm ||
                    x.FormTemplateId == uploadedClientForm.RelatedForm2 ||
                    x.FormTemplateId == uploadedClientForm.RelatedForm3);
            }

            foreach (var item in clientForms)
            {
                item.FormCategoryDesc = parameters.JobUploadRequired
                    ? $"Required: {item.InsuranceClient ?? item.CommercialClient} Forms"
                    : $"{item.InsuranceClient ?? item.CommercialClient} Forms:";

                item.GroupOrder = 1;
                item.IsForUpload = true;
                item.IsRequired = parameters.JobUploadRequired;
            }

            var items = clientForms
                .Where(item => allJobForms.All(q => q.FormTemplateId != item.FormTemplateId)).ToList();

            partialList.AddRange(items);
            allJobForms.AddRange(items);
        }

        private static void SetUsaSpanishForms(GetFormsParameters parameters, List<FormTemplateModel> countryForms, List<FormTemplateModel> partialList)
        {
            // Only show Spanish forms for USA
            if (!parameters.Country.Equals("United States"))
                return;

            // Get a list of all the Spanish forms
            var spanishForms = countryForms
                .Where(condition => condition.FormalLanguage == "Spanish")
                .OrderBy(order => order.Name)
                .ToList();

            foreach (var item in spanishForms)
            {
                item.FormCategoryDesc = @"Spanish Forms";
                item.GroupOrder = 100;
            }

            partialList.AddRange(spanishForms);
        }

        private static void SetFormUploadStatus(List<FormTemplateModel> countryForms, List<MediaMetadata> jobForms)
        {
            if (!jobForms.Any())
                return;

            foreach (var f in countryForms)
            {
                var jf = jobForms.FirstOrDefault(x => x.FormTemplateId == f.FormTemplateId);
                if (jf == null)
                    continue;
                f.IsDocumentUploaded = true;
                f.IsForUpload = jf.IsForUpload;
                f.MediaId = jf.Id;
                f.SignedDate = jf.SignedDate;
            }
        }

        private static string GetCountry(Job job)
        {
            var country = string.Empty;
            if (job.LossAddress != null)
            {
                country = job.LossAddress?.State?.CountryName ?? string.Empty;
            }
            return country;
        }

        private static string GetState(Job job)
        {
            var state = string.Empty;
            if (job.LossAddress != null)
            {
                state = job.LossAddress?.State?.StateName;
            }
            return state;
        }

        private static string GetJobType(Job job, GetLookups.Dto lookups)
        {
            var jobType = lookups.LossTypes.First(x => x.Id == new Guid(job.LossTypeId.ToString())).Name ?? "";
            return jobType;
        }

        private static string GetLanguage(string country)
            => country switch
            {
                "US" => "English - American",
                "United States" => "English - American",
                "USA" => "English - American",
                _ => "English - Canadian"
            };

        private async Task<string> GetInsuranceClientName(Job job, CancellationToken cancellationToken)
        {
            string insuranceName = string.Empty;
            if (job.InsuranceCarrierId.HasValue)
            {
                var insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(c => c.Id == job.InsuranceCarrierId.Value, cancellationToken);
                if (insuranceClient != null)
                    insuranceName = insuranceClient.Name;
            }

            return insuranceName;
        }

        private static void AddNonRequiredForms(List<MediaMetadata> jForms, List<FormTemplateModel> breForms, Dictionary<Guid, FormTemplateModel> formTemplatesLookup, List<FormTemplateModel> allJobForms)
        {
            jForms
                .Where(x => x.FormTemplateId.HasValue)
                .ToList()
                .ForEach(f =>
                {
                    var breFormExists = breForms.Any(z => z.FormTemplateId == f.FormTemplateId.Value);
                    if (!breFormExists)
                    {
                        if (formTemplatesLookup.ContainsKey(f.FormTemplateId.Value))
                        {
                            var ftm = formTemplatesLookup[f.FormTemplateId.Value];
                            ftm.FormCategoryDesc = @"Other Forms";
                            ftm.GroupOrder = 5;
                            ftm.IsDocumentUploaded = true;
                            ftm.IsForUpload = f.IsForUpload;
                            ftm.MediaId = f.Id;
                            ftm.SignedDate = f.SignedDate;
                            ftm.IsXactUploaded = f.IsUploadedToXact;                     
                            ftm.UploadedSuccessfully = f.UploadedSuccessfully;
                            allJobForms.Add(ftm);
                        }
                    }
                    else
                    {
                        var form = allJobForms.First(x => x.FormTemplateId == f.FormTemplateId);
                        form.MediaId = f.Id;
                        form.SignedDate = f.SignedDate;
                        form.IsXactUploaded = f.IsUploadedToXact;
                        form.UploadedSuccessfully = f.UploadedSuccessfully;
                    }
                });
        }

        private async Task<List<FormTemplateModel>> GetFormsFromCrs(Job job, GetLookups.Dto lookups, CancellationToken cancellationToken)
        {
            var crsCommand = await CreateGetFormRequestDto(job, lookups, cancellationToken);
            var cbreData = await _clientRequirementsService.GetFormsAsync(crsCommand, cancellationToken);
            _logger.LogInformation("cbreData: {@cbreData}", cbreData);

            if (cbreData == null || !cbreData.Any())
                return new List<FormTemplateModel>();

            var breForms = MapForms(cbreData, false);
            _logger.LogInformation("breForms: {@breForms}", breForms);

            return breForms;
        }

        public async Task<GetFormsRequestDto> CreateGetFormRequestDto(Job job, GetLookups.Dto lookups, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Attempting to Create the Get Form Request");

            var franchise = await _franchiseServiceClient.GetFranchiseWithoutUserClaimsAsync(job.FranchiseId, job.FranchiseSetId, cancellationToken: cancellationToken);
            InsuranceClient insuranceCarrier = null;

            if (job.InsuranceCarrierId.HasValue)
            {
                insuranceCarrier = await _db.InsuranceClients.AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == job.InsuranceCarrierId, cancellationToken);

                if (insuranceCarrier is null)
                {
                    _logger.LogWarning("Could not find insurance carrier with the following id: {insuranceCarrierId}, defaulting to unknown.", job.InsuranceCarrierId);
                    insuranceCarrier = await GetUnknownInsuranceCarrier(cancellationToken);
                }
            }
            else
                insuranceCarrier = await GetUnknownInsuranceCarrier(cancellationToken);

            var dto = new GetFormsRequestDto()
            {
                JobId = job.Id,
                InsuranceClientId = insuranceCarrier.InsuranceNumber,
                LossType = lookups.LossTypes.FirstOrDefault(t => t.Id == job.LossTypeId)?.Name,
                StructureType = job.PropertyTypeId == PropertyTypes.Residential
                                    ? "Residential"
                                    : "Commercial", // yes, this is really PropertyType, but it is incorrect in CRS port TODO: https://gitlab.com/servpro/ftg/client-requirements-service/-/merge_requests/160/diffs#diff-content-96670746220c22923c2271f7f12612ae97856845
                State = job.LossAddress?.State?.StateAbbreviation,
                Country = lookups.Countries.FirstOrDefault(c => c.Id == job.LossAddress?.State?.CountryId)?.Alpha2Code,
                FranchiseType = franchise != null ? franchise.FranchiseType : "Unknown",
                JobSource = lookups.JobSources.FirstOrDefault(x => x.Id == job.JobSourceId)?.Name,
                EstimateType = "Xactimate",
                IsStormJob = job.StormId.HasValue,
                IsStreamlined = false, // TODO: Determine how to determine this
            };

            foreach (var mm in job.MediaMetadata.Where(mm => !mm.IsDeleted))
            {
                if (mm.FormTemplateId.HasValue)
                {
                    dto.JobForms.Add(new GetFormsRequestDto.JobFormDto
                    {
                        FormTemplateId = mm.FormTemplateId.Value,
                        Id = mm.Id
                    });
                }
            }

            return dto;
        }

        private async Task<InsuranceClient> GetUnknownInsuranceCarrier(CancellationToken cancellationToken)
            => await _db.InsuranceClients
                    .FirstOrDefaultAsync(ic => ic.Name.ToLower() == "unknown", cancellationToken);

        private List<FormTemplateModel> MapForms(List<GetFormsResultDto> cbreForms, bool filterNonRequired)
        {
            var formTemplates = new List<FormTemplateModel>();

            cbreForms.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));
            foreach (var form in cbreForms)
            {
                // if the status is 0 from CBRE, that means the rule (while applicable) has not yet been triggered due to some job condition
                if (form.ValidationStatus == 0)
                    continue;

                if (filterNonRequired && form.Type != "Required")
                    continue;

                var formTemplate = new FormTemplateModel()
                {
                    Name = form.Name,
                    Description = form.Description,
                    FormCategoryDesc = form.Type,
                    Country = "United States",
                    FormalLanguage = "English",
                    IsWaterForm = true,
                    IsWaterFormRequired = true,
                    FileType = "pdf",
                    DisplayOrder = formTemplates.Count,
                    JobForms = new List<FormTemplateModel.JobFormModel>()
                };

                if (form.FormTemplateId != Guid.Empty)
                {
                    formTemplate.FormTemplateId = form.FormTemplateId;
                }

                formTemplate.IsDocumentUploaded = MapStatus(form.ValidationStatus) == ValidationItemStatus.Complete;
                formTemplate.IsForUpload = true;    // by virtue of coming from the BRE
                formTemplate.IsRequired = true;     // by virtue of coming from the BRE
                formTemplate.IsActive = form.IsActive;

                formTemplate.FormFields = new List<FormTemplateModel.FormFieldModel>();
                formTemplate.JobForms = new List<FormTemplateModel.JobFormModel>();

                formTemplate.IsAvailableInFirstNotice = form.IsAvailableInFirstNotice;

                // Required, Standard then Other
                switch (formTemplate.FormCategoryDesc)
                {
                    case "Required":
                        formTemplate.GroupOrder = 2;
                        formTemplates.Add(formTemplate);
                        break;
                    case "Standard":
                        formTemplate.GroupOrder = 3;
                        formTemplates.Add(formTemplate);
                        break;
                    case "Other":
                        formTemplate.GroupOrder = 5;
                        formTemplates.Add(formTemplate);
                        break;
                }
            }

            return formTemplates;
        }

        private ValidationItemStatus MapStatus(int cbreValidationStatus)
        {
            if (cbreValidationStatus == 1)
                return ValidationItemStatus.Complete;

            return ValidationItemStatus.Active;
        }

        private static List<FormTemplateModel> MapForms(List<FormTemplateModel> forms)
        {
            return forms.Select(MapForm).ToList();
        }

        private static FormTemplateModel MapForm(FormTemplateModel form)
        {
            return new FormTemplateModel
            {
                FormTemplateId = form.FormTemplateId,
                Name = form.Name,
                LinkedPage = form.LinkedPage,
                Description = form.Description,
                RelatedForm = form.RelatedForm,
                RelatedForm2 = form.RelatedForm2,
                RelatedForm3 = form.RelatedForm3,
                IsRequiredForAllLossTypes = form.IsRequiredForAllLossTypes,
                IsWaterFormRequired = form.IsWaterFormRequired,
                IsMoldFormRequired = form.IsMoldFormRequired,
                IsFireFormRequired = form.IsFireFormRequired,
                IsRequiredForResidentialJob = form.IsRequiredForResidentialJob,
                IsRequiredForCommercialJob = form.IsRequiredForCommercialJob,
                IsWaterForm = form.IsWaterForm,
                IsMoldForm = form.IsMoldForm,
                IsFireForm = form.IsFireForm,
                InsuranceClient = form.InsuranceClient,
                CommercialClient = form.CommercialClient,
                IsAvailableInFirstNotice = form.IsAvailableInFirstNotice,
                State = form.State,
                Country = form.Country,
                FormalLanguage = form.FormalLanguage,
                IsActive = form.IsActive,
                IsRequired = form.IsRequired,
                FormCategoryDesc = form.FormCategoryDesc,
                CreatedDate = form.CreatedDate,
                IsXactUploaded = form.IsXactUploaded,
                Copies = form.Copies,
                CreatedBy = form.CreatedBy,
                DisplayOrder = form.DisplayOrder,
                FileType = form.FileType,
                FormFields = form.FormFields,
                GroupOrder = form.GroupOrder,
                IsApproved = form.IsApproved,
                IsDocumentUploaded = form.IsDocumentUploaded,
                IsForUpload = form.IsForUpload,
                JobForms = form.JobForms,
                MediaId = form.MediaId,
                UpdatedBy = form.UpdatedBy,
                UpdatedDate = form.UpdatedDate,
                Version = form.Version,
                SignedDate = form.SignedDate,
            };
        }

        private (bool IsWater, bool IsFire, bool IsMold) GetFormTypes(string jobType)
            => jobType switch
            {
                "Water" => (true, false, false),
                "Sewage" => (true, false, false),
                "Fire" => (false, true, false),
                "Smoke" => (false, true, false),
                "Mold" => (false, false, true),
                "Fire/Water" => (true, true, false),
                _ => (true, false, false)
            };

        public class GetFormsParameters
        {
            public bool JobUploadRequired { get; set; }
            public string Insurance { get; set; }
            public string State { get; set; }
            public string Country { get; set; }
            public string JobType { get; set; }
            public string Language { get; set; }
            public bool IsWaterForm { get; set; }
            public bool IsFireForm { get; set; }
            public bool IsMoldForm { get; set; }
        }

        #endregion

        #region Get Merged Form

        public async Task<MemoryStream> GetMergedFormsAsync(MergeFormsRequest request, CancellationToken cancellationToken)
        {
            var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
            var formFields = GetFormFields(lookups);

            var timeZone = TimeZoneHelper.GetWindowsTimeZoneIdentifier(request.FranchiseSet.PrimaryTimeZoneId);

            await LoadFormsAsync(request.FormTemplates);

            var createFormPackageRequest = new CreateFormPackageRequest(request.Job, request.Franchise, request.FormTemplates, lookups, formFields, timeZone);

            var result = await CreateFormPackage(createFormPackageRequest, cancellationToken);

            return result;
        }

        public async Task<MemoryStream> CreateFormPackage(CreateFormPackageRequest request, CancellationToken cancellationToken)
        {
            try
            {
                const string formDiagnosticModePathConfigKey = "FormDiagnosticModePath";
                var formDiagnosticModePath = _config[formDiagnosticModePathConfigKey];
                var insuranceClients = await _db.InsuranceClients
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                var stream = _formManager.CreateRequestedForms(request, _logger, insuranceClients, formDiagnosticModePath);

                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError("An error occurred merging form templates for {JobId} with exception message {Exception}", request.Job?.Id, ex.Message);
                throw;
            }
        }

        private async Task LoadFormsAsync(IEnumerable<FormTemplate> formTemplates)
        {
            foreach (var formTemplate in formTemplates)
            {
                await LoadFormAsync(formTemplate);
            }
        }

        private async Task LoadFormAsync(FormTemplate formTemplate)
        {
            const string s3FormTemplatesBucketNameConfigKey = "AWS:S3FormTemplatesBucketName";
            var bucketName = _config[s3FormTemplatesBucketNameConfigKey];

            formTemplate.Form = await _valet.ReadObjectStreamAsync(bucketName, formTemplate.MediaPath);
        }

        /// <summary>
        /// Gets form fields, mappings, and types from lookups and maps them to a single object
        /// </summary>
        /// <param name="lookups"></param>
        /// <returns>A list of fields, mappings, and types in a single object</returns>
        private List<FormFieldDto> GetFormFields(GetLookups.Dto lookups)
        {
            var formFields = new List<FormFieldDto>();
            var formFieldMappings = lookups.FormFieldMappings;
            var formFieldTypes = lookups.FormFieldTypes;

            foreach (var formField in lookups.FormFields)
            {
                var formFieldDto = new FormFieldDto()
                {
                    Id = formField.Id,
                    Name = formField.Name,
                    Description = formField.Description,
                    FormatString = formField.FormatString,
                    FormFieldMappingId = formField.FormFieldMappingId,
                    FormFieldTypeId = formField.FormFieldTypeId,
                    FormFieldMapping = Map(formFieldMappings.FirstOrDefault(x => x.Id == formField.FormFieldMappingId)),
                    FormFieldType = Map(formFieldTypes.FirstOrDefault(x => x.Id == formField.FormFieldTypeId))
                };
                formFields.Add(formFieldDto);
            }

            return formFields;
        }

        private FormFieldMappingDto Map(LookupService.Features.LookUps.Lead.Dtos.FormFieldMappingDto formFieldMappingDto)
        {
            if (formFieldMappingDto == null) return null;

            return new FormFieldMappingDto()
            {
                Id = formFieldMappingDto.Id,
                Name = formFieldMappingDto.Name,
                PropertyName = formFieldMappingDto.PropertyName,
                PropertyType = formFieldMappingDto.PropertyType,
                PropertyLength = formFieldMappingDto.PropertyLength,
                Expression = formFieldMappingDto.Expression,
                IosPropertyName = formFieldMappingDto.IOSPropertyName,
                IosPropertyType = formFieldMappingDto.IOSPropertyType,
                IosFormat = formFieldMappingDto.IOSFormat,
                IsNullable = formFieldMappingDto.IsNullable
            };
        }

        private FormFieldTypeDto Map(LookupService.Features.LookUps.Lead.Dtos.FormFieldTypeDto formFieldTypeDto)
        {
            if (formFieldTypeDto == null) return null;

            return new FormFieldTypeDto()
            {
                Id = formFieldTypeDto.Id,
                Name = formFieldTypeDto.Name
            };
        }

        #endregion
    }
}