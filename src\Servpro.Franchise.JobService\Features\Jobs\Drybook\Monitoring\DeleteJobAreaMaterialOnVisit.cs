﻿using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class DeleteJobAreaMaterialOnVisit
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
            public Guid JobAreaMaterialId { get; set; }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(m => m.JobAreaMaterialId).NotEmpty();
                RuleFor(m => m.JobVisitId).NotEmpty();
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            public readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobMaterials)
                        .ThenInclude(y => y.JobAreaMaterials)
                            .ThenInclude(z => z.JobAreaMaterialReadings)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobAreas = await _context.JobAreas.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JournalNotes = await _context.JournalNote.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var validationErrors = ValidateRequest(request, job);
                if (validationErrors.Any())
                    throw new ValidationException(validationErrors);

                var jobAreaMaterials = job.JobMaterials.SelectMany(x => x.JobAreaMaterials);
                var jobAreaMaterial = jobAreaMaterials.FirstOrDefault(x => x.Id == request.JobAreaMaterialId);
                var jobMaterial = GetJobMaterial(job, jobAreaMaterial.JobMaterialId);
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var readingTypeLookups = GetReadingTypeLookups(lookups);
                var journalNoteType = GetJournalNotesType(lookups);
                var visitNumberLookups = GetVisitLookups(job);
                var jobAreaName = GetJobAreaName(job, jobAreaMaterial.JobAreaId);
                var jobVisit = GetJobVisit(job, request.JobVisitId);

                var futureReadings = jobAreaMaterial.JobAreaMaterialReadings
                    .Where(x => x.JobVisit.Date > jobVisit.Date)
                    .ToList();
                
                foreach (var reading in futureReadings)
                    jobAreaMaterial.JobAreaMaterialReadings.Remove(reading);

                job.JournalNotes.Add(new JournalNote
                {
                    ActionDate = DateTime.UtcNow,
                    TypeId = JournalNoteTypes.RecordOfFixPlacementReadingsDeletion,
                    Subject = $"{jobAreaName} - Removed material from room",
                    Note = GetDiaryNoteBody(jobMaterial, readingTypeLookups, visitNumberLookups, futureReadings),
                    CategoryId = journalNoteType.JournalNoteCategoryId,
                    VisibilityId = journalNoteType.DefaultDiaryEntryVisibilityId,
                    Author = userInfo.Name,
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.UtcNow
                });

                RemoveJobAreaMaterialOnVisit(jobAreaMaterial, jobVisit, userInfo.Username);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            #region HelperFunctions
            private JobMaterial GetJobMaterial(Job job, Guid jobMaterialId) =>
                job.JobMaterials.FirstOrDefault(x => x.Id == jobMaterialId);

            private Dictionary<Guid, string> GetReadingTypeLookups(GetLookups.Dto lookups) =>
                lookups.MaterialReadingTypes.ToDictionary(x => x.Id, x => x.Name);

            private JournalNoteTypesDto GetJournalNotesType(GetLookups.Dto lookups) =>
                lookups.JournalNoteTypes.FirstOrDefault(x => x.Id == JournalNoteTypes.RecordOfFixPlacementReadingsDeletion);

            private Dictionary<Guid, int> GetVisitLookups(Job job) =>
                job.JobVisits
                    .OrderBy(x => x.Date)
                    .Select((x, index) => new { x.Id, index = index + 1 })
                    .ToDictionary(x => x.Id, x => x.index);

            private string GetDiaryNoteBody(
                JobMaterial jobMaterial,
                Dictionary<Guid, string> readingTypeLookups,
                Dictionary<Guid, int> visitNumberLookups,
                List<JobAreaMaterialReading> futureReadings)
            {
                var futureReadingsStr = futureReadings
                    .Select(x => new
                    {
                        MaterialName = jobMaterial.Name,
                        Reading = x.Value,
                        ReadingType = readingTypeLookups[x.MaterialReadingTypeId],
                        VisitNumber = visitNumberLookups[x.JobVisitId]
                    })
                    .OrderBy(x => x.VisitNumber)
                    .Select(x => $"\tVisit Number {x.VisitNumber} - {x.MaterialName}, {x.ReadingType} reading: {x.Reading}");
                var body = futureReadings.Any()
                    ? $"Removed Readings{string.Concat(futureReadingsStr)}"
                    : $"Removed Readings - {jobMaterial.Name}";

                return body;
            }

            private string GetJobAreaName(Job job, Guid jobAreaId) =>
                job.JobAreas.FirstOrDefault(x => x.Id == jobAreaId)?.Name;

            private JobVisit GetJobVisit(Job job, Guid visitId) =>
                job.JobVisits.FirstOrDefault(x => x.Id == visitId);

            private void RemoveJobAreaMaterialOnVisit(JobAreaMaterial jobAreaMaterial, JobVisit jobVisit, string username) 
            {
                jobAreaMaterial.RemovedOnJobVisit = jobVisit;
                jobAreaMaterial.ModifiedBy = username;
                jobAreaMaterial.ModifiedDate = DateTime.UtcNow;
            }

            IEnumerable<ValidationFailure> ValidateRequest(Command request, Job job)
            {
                if (!job.JobVisits.Any(x => x.Id == request.JobVisitId))
                    yield return new ValidationFailure(
                        nameof(Command.JobVisitId),
                        "JobVisit not found", request.JobVisitId);

                var jobAreaMaterialLookups = job.JobMaterials
                    .SelectMany(x => x.JobAreaMaterials)
                    .Select(x => x.Id)
                    .ToHashSet();

                if (!jobAreaMaterialLookups.Contains(request.JobAreaMaterialId))
                    yield return new ValidationFailure(
                        nameof(Command.JobAreaMaterialId),
                        "JobAreaMaterial not found", request.JobAreaMaterialId);
            }

            #endregion
        }
        #endregion
    }
}
