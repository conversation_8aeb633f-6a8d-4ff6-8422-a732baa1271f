﻿using MediatR;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    [Route("api/jobs/{jobId}/")]
    public class MicaController : Controller
    {
        private readonly IMediator _mediator;
        public readonly ILogger<MicaController> _logger;

        public MicaController(IMediator mediator,
            ILogger<MicaController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("queries/check-can-submit-to-mica")]
        public async Task<ActionResult> CheckCanSubmitToMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var canSubmit = await _mediator.Send(new CheckCanSubmitToMica.Query(jobId));
            return Ok(canSubmit);
        }

        [HttpPost("commands/submit-to-mica")]
        public async Task<ActionResult> SubmitToMica(Guid jobId, [FromBody] SubmitToMica.Command command)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var submit = await _mediator.Send(command);
            return Ok(submit);
        }

        [HttpGet("queries/get-notes-for-mica")]
        public async Task<ActionResult> GetNotesForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var notes = await _mediator.Send(new GetNotesForMica.Query { JobId = jobId });
            return Ok(notes);
        }

        [HttpGet("queries/get-photos-for-mica")]
        public async Task<ActionResult> GetPhotosForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var photos = await _mediator.Send(new GetPhotosForMica.Query(jobId));
            return Ok(photos);
        }

        [HttpGet("queries/get-documents-for-mica")]
        public async Task<ActionResult> GetDocumentsForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var documents = await _mediator.Send(new GetDocumentsForMica.Query(jobId));
            return Ok(documents);
        }

        [HttpPost("queries/get-documents-to-send-to-mica")]
        public async Task<ActionResult> GetDocumentsToSendToMica(Guid jobId, [FromBody] List<Guid> documents)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            var command = new GetDocumentsToSendToMica.Command()
            {
                JobId = jobId,
                DocumentIds = documents
            };
            var submit = await _mediator.Send(command);
            return Ok(submit);
        }

        [HttpPost("queries/get-photos-to-send-to-mica")]
        public async Task<ActionResult> GetPhotosToSendToMica(Guid jobId, [FromBody] List<Guid> photos)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            var command = new GetPhotosToSendToMica.Command()
            {
                JobId = jobId,
                PhotoIds = photos
            };
            var submit = await _mediator.Send(command);
            return Ok(submit);
        }

        [HttpPost("queries/get-forms-to-send-to-mica")]
        public async Task<ActionResult> GetFormsToSendToMica(Guid jobId, [FromBody] List<Guid> forms)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            var command = new GetFormsToSendToMica.Command()
            {
                JobId = jobId,
                FormIds = forms
            };
            var submit = await _mediator.Send(command);
            return Ok(submit);
        }

        [HttpPost("queries/get-notes-to-send-to-mica")]
        public async Task<ActionResult> GetNotesToSendToMica(Guid jobId, [FromBody] List<Guid> notes)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            var command = new GetNotesToSendToMica.Command()
            {
                JobId = jobId,
                NoteIds = notes
            };
            var submit = await _mediator.Send(command);
            return Ok(submit);
        }

        [HttpGet("queries/get-job-for-mica")]
        public async Task<ActionResult> GetJobForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var job = await _mediator.Send(new GetJobForMica.Query(jobId));
            return Ok(job);
        }

        [HttpGet("queries/get-job-for-mica-update")]
        public async Task<ActionResult> GetJobForMicaUpdate(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var job = await _mediator.Send(new GetJobForMicaUpdate.Query(jobId));
            return Ok(job);
        }

        [HttpGet("queries/get-forms-for-mica")]
        public async Task<ActionResult> GetFormsForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var forms = await _mediator.Send(new GetFormsForMica.Query { JobId = jobId });
            return Ok(forms);
        }

        [HttpGet("queries/get-job-info-for-mica")]
        public async Task<ActionResult> GetJobInfoForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var jobInfo = await _mediator.Send(new GetJobInfoForMica.Query(jobId));
            return Ok(jobInfo);
        }


        [HttpGet("queries/get-job-drying-data-for-mica")]
        public async Task<ActionResult> GetJobDryingDataForMica(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var dryingInfo = await _mediator.Send(new GetJobDryingDataForMica.Query(jobId));
            return Ok(dryingInfo);
        }
    }
}
