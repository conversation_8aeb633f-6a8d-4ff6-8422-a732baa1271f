﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobInvoicesDeleted
    {
        public class Event : InvoicesDeletedEvent, IRequest
        {
            public Event(List<InvoiceDeletedDto> invoices, Guid correlationId)
                : base(invoices, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<JobInvoicesDeleted> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<JobInvoicesDeleted> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler started. Total invoices to delete: {Count}",
                  incomingEvent.Invoices.Count);


                var jobIds = incomingEvent.Invoices
                 .Select(i => i.JobId)
                 .Distinct()
                 .ToList();

                var allMedia = await _context.MediaMetadata
                    .Where(m => jobIds.Contains(m.JobId))
                    .ToListAsync(cancellationToken);

                foreach (var deletedInvoice in incomingEvent.Invoices)
                {
                    var mediaItem = allMedia.FirstOrDefault(m =>
                        m.JobId == deletedInvoice.JobId &&
                        m.JobInvoiceId == deletedInvoice.InvoiceId);

                    if (mediaItem != null)
                    {
                        mediaItem.IsDeleted = true;
                        _logger.LogInformation(
                            "Invoice {InvoiceId} marked as deleted for Job {JobId}",
                            deletedInvoice.InvoiceId, deletedInvoice.JobId);
                    }
                    else
                    {
                        _logger.LogWarning(
                            "Invoice {InvoiceId} not found for Job {JobId}, skipping...",
                            deletedInvoice.InvoiceId, deletedInvoice.JobId);
                    }
                }

                try
                {
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Database updated. All invoices deleted successfully.");
                }
                catch (DbUpdateException ex)
                {
                    _logger.LogError(ex, "Error updating DB for InvoicesDeletedEvent");
                    throw;
                }

                return Unit.Value;
            }
        }
    }
}
