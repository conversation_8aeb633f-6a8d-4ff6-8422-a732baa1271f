﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Scope;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class LineItemUploadFailed
    {
        public class Event : LineItemUploadFailedEvent, IRequest
        {
            public Event(LineItemUploadFailedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<LineItemUploadFailed.Handler> _logger;

            public Handler(JobDataContext db, ILogger<LineItemUploadFailed.Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var eventDto = request.LineItemUpload;
                var jobUploadLock = await _db.JobUploadLocks
                    .FirstOrDefaultAsync(x => x.JobId == eventDto.JobId && x.JobUploadTypeId == JobUploadTypes.XactLineItem, cancellationToken: cancellationToken);

                if (jobUploadLock == null)
                {
                    return Unit.Value;
                }

                _db.JobUploadLocks.Remove(jobUploadLock);
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}