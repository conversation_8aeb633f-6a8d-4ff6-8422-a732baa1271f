﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class EditRoom
    {
        public class Command : SaveRoom.BaseRoomCommand, IRequest
        {
            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
            public ICollection<AffectedFlooringDto> AffectedFlooring { get; set; }
            public ICollection<AdjustmentDto> Adjustments { get; set; }
            public ICollection<MissingSpaceDto> MissingSpaces { get; set; }
            public PreExistingConditionsDto PreExistingConditions { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.RoomId).NotEmpty();
                RuleFor(x => x.RoomShapeId).NotEmpty();
            }
        }
        public class AffectedFlooringDto : SaveRoom.AffectedFlooringDto
        {
            public Guid Id { get; set; }
        }
        public class AdjustmentDto : SaveRoom.AdjustmentDto
        {
            public Guid RoomAdjustmentId { get; set; }
        }
        public class MissingSpaceDto : SaveRoom.MissingSpaceDto
        {
            public Guid MissingSpaceId { get; set; }
        }

        public class PreExistingConditionsDto : SaveRoom.PreExistingConditionsDto
        {
            public Guid Id { get; set; }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo)
            {
                _context = context;
                _userInfo = userInfo;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var username = userInfo.Username;

                var jobArea = await _context.JobAreas
                    .Include(x => x.Job)
                    .Include(x => x.Room)
                        .ThenInclude(x => x.RoomFlooringTypesAffected)
                    .FirstOrDefaultAsync(x => x.RoomId == request.RoomId && x.JobId == request.JobId && !x.IsDeleted, cancellationToken);

                var room = jobArea?.Room;

                if (jobArea is null || room is null)
                    throw new ResourceNotFoundException($"Room not found (Id: {request.RoomId}");

                if (jobArea.Job != null)
                {
                    jobArea.Job.JobLocks = await _context.JobLock.Where(x => x.JobId == jobArea.Job.Id).ToListAsync(cancellationToken);
                    jobArea.Job.Tasks = await _context.Tasks.Where(x => x.JobId == jobArea.Job.Id).ToListAsync(cancellationToken);
                    jobArea.Job.JournalNotes = await _context.JournalNote.Where(x => x.JobId == jobArea.Job.Id).ToListAsync(cancellationToken);
                }
                if (JobLockUtils.HasLockConflict(jobArea.Job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(jobArea.Job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(jobArea.Job.CurrentJobLock));


                // Update jobArea 
                jobArea.ModifiedDate = DateTime.UtcNow;
                jobArea.ModifiedBy = username;
                jobArea.Name = request.RoomName;

                // Update room
                room.ModifiedBy = username;
                room.ModifiedDate = DateTime.UtcNow;
                room.RoomTypeId = request.RoomTypeId;
                room.RoomShapeId = request?.RoomShapeId ?? RoomShapes.Box;
                room.AffectedCeilingAreaSquareInches = request.AffectedCeilingAreaSquareFeet * 144;
                room.AffectedWallAreaSquareInches = request.AffectedWallAreaSquareFeet * 144;
                room.AffectedWallAreaAbove2FeetSquareInches = request.AffectedWallAreaAbove2FeetSquareInches * 144;
                room.AffectedWallAreaBelow2FeetSquareInches = request.AffectedWallAreaBelow2FeetSquareInches * 144;
                room.CeilingAreaSquareInches = request.CeilingAreaSquareInches;
                room.CeilingPerimeterInches = request.CeilingPerimeterInches;
                room.FloorAreaSquareInches = request.FloorAreaSquareInches;
                room.FloorPerimeterInches = request.FloorPerimeterInches;
                room.Height1TotalInches = request.Height1TotalInches;
                room.Height2TotalInches = request.Height2TotalInches;
                room.Height3TotalInches = request.Height3TotalInches;
                room.Length1TotalInches = request.Length1TotalInches;
                room.Length2TotalInches = request.Length2TotalInches;
                room.MissingCeilingAreaSquareInches = request.MissingCeilingAreaSquareInches;
                room.MissingCeilingPerimeterInches = request.MissingCeilingPerimeterInches;
                room.MissingFloorAreaSquareInches = request.MissingFloorAreaSquareInches;
                room.MissingFloorPerimeterInches = request.MissingFloorPerimeterInches;
                room.MissingRoomVolumeCubicInches = request.MissingRoomVolumeCubicInches;
                room.MissingWallAreaSquareInches = request.MissingWallAreaSquareInches;
                room.OffsetCeilingAreaSquareInches = request.OffsetCeilingAreaSquareInches;
                room.OffsetCeilingPerimeterInches = request.OffsetCeilingPerimeterInches;
                room.OffsetFloorAreaSquareInches = request.OffsetFloorAreaSquareInches;
                room.OffsetFloorPerimeterInches = request.OffsetFloorPerimeterInches;
                room.OffsetRoomVolumeCubicInches = request.OffsetRoomVolumeCubicInches;
                room.OffsetWallAreaSquareInches = request.OffsetWallAreaSquareInches;
                room.WallAreaBelow2FeetSquareInches = request.WallAreaBelow2FeetSquareInches;
                room.WallAreaSquareInches = request.WallAreaSquareInches;
                room.Width1TotalInches = request.Width1TotalInches;
                room.Width2TotalInches = request.Width2TotalInches;
                room.Length3TotalInches = request.Length3TotalInches;
                room.Width3TotalInches = request.Width3TotalInches;
                room.NetVolume = request.NetVolume;
                room.NetCeilingSquareFootage = request.NetCeilingSquareFootage;
                room.NetFloorSquareFootage = request.NetFloorSquareFootage;
                room.NetWallSquareFootage = request.NetWallSquareFootage;
                room.NetCeilingPerimeter = request.NetCeilingPerimeter;
                room.NetFloorPerimeter = request.NetFloorPerimeter;
                room.TotalOffsetsSquareFootage = request.TotalOffsetsSquareFootage;
                room.TotalInsetsSquareFootage = request.TotalInsetsSquareFootage;
                room.TotalOpeningsSquareFootage = request.TotalOpeningsSquareFootage;
                room.ExpressDimensionImageKey = request.ExpressDimensionImageKey;
                room.RequiresContainment = request.RequiresContainment;
                room.PercentageContained = request.PercentageContained;

                room.MissingSpaces = MapMissingSpaces(request.MissingSpaces, username).ToList();
                room.RoomVolumeCubicInches = request.RoomVolumeCubicInches;
                room.OffsetSpaces = MapOffsetSpaces(request.Adjustments, username).ToList();

                RemoveFlooringTypesAffected(request.AffectedFlooring,
                                             room.RoomFlooringTypesAffected);

                SaveChangesFlooringTypes(request.AffectedFlooring,
                                     room.RoomFlooringTypesAffected,
                                     username,
                                     room.Id);

                if (request.PreExistingConditions == null)
                {
                    room.PreExistingConditions = "";
                    room.PreExistingConditionsDiaryNoteId = null;

                }
                else
                {
                    var journalNoteId = SaveJournalNote(request.JobId,
                        username,
                        userInfo.Name,
                        request.PreExistingConditions,
                        jobArea.Job.JournalNotes);


                    room.PreExistingConditions = request.PreExistingConditions.Note;
                    room.PreExistingConditionsDiaryNoteId = journalNoteId;

                }

                //Unconfirms the zone if the room belongs to one
                if (jobArea.ZoneId != null)
                {
                    var user = _userInfo.GetUserInfo();

                    var task = jobArea.Job.Tasks
                         .FirstOrDefault(t => t.TaskTypeId == TaskTypes.ZoneNotConfirmed
                         && t.ZoneId == jobArea.ZoneId);

                    if (task != null)
                    {
                        task.ModifiedDate = DateTime.UtcNow;
                        task.ModifiedBy = userInfo.Username;
                        task.PercentComplete = 0;
                        task.TaskStatusId = TaskStatuses.Active;
                    }
                }

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private IEnumerable<MissingSpace> MapMissingSpaces(
                ICollection<MissingSpaceDto> missingSpaces,
                string username)
            {
                if (missingSpaces != null)
                {
                    foreach (var missingSpace in missingSpaces)
                    {
                        Guid missingSpaceId;

                        if (missingSpace.MissingSpaceId == Guid.Empty)
                        {
                            missingSpaceId = Guid.NewGuid();
                        }
                        else
                        {
                            missingSpaceId = missingSpace.MissingSpaceId;
                        }

                        yield return new MissingSpace
                        {
                            Id = missingSpaceId,
                            ModifiedDate = DateTime.UtcNow,
                            ModifiedBy = username,
                            Version = DateTime.UtcNow,
                            MissingSpaceReasonTypeId = missingSpace.MissingSpaceReasonTypeId,
                            Quantity = missingSpace.Quantity,
                            LengthTotalInches = missingSpace.LengthTotalInches,
                            HeightTotalInches = missingSpace.HeightTotalInches,
                            WidthTotalInches = missingSpace.WidthTotalInches,
                            Name = missingSpace.Name,
                            OtherReason = missingSpace.OtherReason,
                            DepthTotalInches = missingSpace.DepthTotalInches,
                            MissingSpaceRemoveFromTypeId = missingSpace.MissingSpaceRemoveFromTypeId
                        };
                    }
                }
            }


            private IEnumerable<OffsetSpace> MapOffsetSpaces(ICollection<AdjustmentDto> adjustments, string username)
            {
                if (adjustments != null)
                {
                    foreach (var adjustment in adjustments)
                    {
                        var offsetInset = new OffsetSpace();

                        if (adjustment.RoomAdjustmentId == Guid.Empty)
                        {
                            offsetInset.Id = Guid.NewGuid();
                            offsetInset.CreatedBy = username;
                            offsetInset.CreatedDate = DateTime.UtcNow;
                            offsetInset.Name = adjustment.Name;
                            offsetInset.OffsetSpaceTypeId = OffsetSpaceTypes.OffsetInset;
                            offsetInset.DepthTotalInches = default;
                            offsetInset.HeightTotalInches = default;
                            offsetInset.WidthTotalInches = default;
                            offsetInset.IsAdjoiningWallRemoved = default;
                            offsetInset.IsOppositeWallRemoved = default;
                            offsetInset.IsLeftWallRemoved = default;
                            offsetInset.IsRightWallRemoved = default;
                            offsetInset.Volume = default;
                            offsetInset.Subtype = default;
                            offsetInset.CeilingSquareFootage = default;
                            offsetInset.IsCeilingAffected = default;
                            offsetInset.FloorSquareFootage = default;
                            offsetInset.WallSquareFootage = default;
                        }
                        else
                        {
                            offsetInset.Id = adjustment.RoomAdjustmentId;
                            offsetInset.ModifiedDate = DateTime.UtcNow;
                            offsetInset.ModifiedBy = username;
                            offsetInset.Version = DateTime.UtcNow;
                            offsetInset.Name = adjustment.Name;
                            offsetInset.OffsetSpaceTypeId = adjustment.RoomAdjustmentTypeId;
                            offsetInset.IsAdjoiningWallRemoved = adjustment.IsAdjoiningWallRemoved;
                            offsetInset.IsOppositeWallRemoved = adjustment.IsOppositeWallRemoved;
                            offsetInset.IsLeftWallRemoved = adjustment.IsLeftWallRemoved;
                            offsetInset.IsRightWallRemoved = adjustment.IsRightWallRemoved;
                            offsetInset.DepthTotalInches = adjustment.DepthTotalInches;
                            offsetInset.HeightTotalInches = adjustment.HeightTotalInches;
                            offsetInset.WidthTotalInches = adjustment.WidthTotalInches;
                            offsetInset.Volume = adjustment.Volume;
                            offsetInset.Subtype = adjustment.Subtype;
                            offsetInset.CeilingSquareFootage = adjustment.CeilingSquareFootage;
                            offsetInset.IsCeilingAffected = adjustment.IsCeilingAffected;
                            offsetInset.FloorSquareFootage = adjustment.FloorSquareFootage;
                            offsetInset.WallSquareFootage = adjustment.WallSquareFootage;
                        }

                        yield return offsetInset;
                    }
                }
            }

            private void SaveChangesFlooringTypes(
                ICollection<AffectedFlooringDto> flooringTypes,
                ICollection<RoomFlooringTypeAffected> oldFlooringTypes,
                string username,
                Guid roomId)
            {
                foreach (var floorigTypeAffected in flooringTypes)
                {

                    if (floorigTypeAffected.Id == Guid.Empty)
                    {
                        _context.RoomFlooringTypesAffected.Add(new RoomFlooringTypeAffected
                        {
                            Id = Guid.NewGuid(),
                            CreatedDate = DateTime.UtcNow,
                            CreatedBy = username,
                            RoomId = roomId,
                            AffectedPercentage = floorigTypeAffected.AffectedPercentage,
                            AffectedSquareFeet = floorigTypeAffected.AffectedSquareFeet,
                            FlooringTypeId = floorigTypeAffected.FlooringTypeId,
                            IsPadRestorable = floorigTypeAffected.IsPadRestorable,
                            IsSalvageable = floorigTypeAffected.IsSalvageable,
                            OtherText = floorigTypeAffected.OtherText ?? "",
                            SavedPercentage = floorigTypeAffected.SavedPercentage,
                            SavedSquareFeet = floorigTypeAffected.SavedSquareFeet,
                            TotalSquareFeet = floorigTypeAffected.TotalSquareFeet
                        });
                    }
                    else
                    {
                        var oldFlooringType = oldFlooringTypes
                                .FirstOrDefault(x => x.Id == floorigTypeAffected.Id);
                        if (oldFlooringType != null)
                        {
                            oldFlooringType.ModifiedDate = DateTime.UtcNow;
                            oldFlooringType.ModifiedBy = username;
                            oldFlooringType.AffectedPercentage = floorigTypeAffected.AffectedPercentage;
                            oldFlooringType.AffectedSquareFeet = floorigTypeAffected.AffectedSquareFeet;
                            oldFlooringType.FlooringTypeId = floorigTypeAffected.FlooringTypeId;
                            oldFlooringType.IsPadRestorable = floorigTypeAffected.IsPadRestorable;
                            oldFlooringType.IsSalvageable = floorigTypeAffected.IsSalvageable;
                            oldFlooringType.OtherText = floorigTypeAffected.OtherText ?? "";
                            oldFlooringType.SavedPercentage = floorigTypeAffected.SavedPercentage;
                            oldFlooringType.SavedSquareFeet = floorigTypeAffected.SavedSquareFeet;
                            oldFlooringType.TotalSquareFeet = floorigTypeAffected.TotalSquareFeet;
                        }
                    }
                }


            }

            private void RemoveFlooringTypesAffected(
                ICollection<AffectedFlooringDto> flooringTypes,
                ICollection<RoomFlooringTypeAffected> oldFlooringTypes)
            {
                foreach (var oldFlooringTypeAffected in oldFlooringTypes)
                {
                    var newFlooringType = flooringTypes
                                .FirstOrDefault(x => x.Id == oldFlooringTypeAffected.Id);

                    if (newFlooringType is null)
                    {
                        _context.Remove(oldFlooringTypeAffected);
                    }
                }

            }

            private Guid SaveJournalNote(
                Guid jobId,
                string username,
                string author,
                PreExistingConditionsDto preExistingConditions,
                ICollection<JournalNote> journalNotes)
            {
                var journalNote = journalNotes
                               .FirstOrDefault(x => x.Id == preExistingConditions.Id);

                Guid journalNoteId;

                if (journalNote == null)
                {
                    journalNoteId = Guid.NewGuid();

                    _context.JournalNote.Add(new JournalNote
                    {
                        Id = journalNoteId,
                        JobId = jobId,
                        CategoryId = JournalNoteCategories.Room,
                        TypeId = TaskTypes.PreExistingCondition,
                        VisibilityId = JournalNoteVisibilities.FranchiseClientAndCustomer,
                        Subject = preExistingConditions.Subject,
                        Note = preExistingConditions.Note,
                        Author = author,
                        ActionDate = DateTime.UtcNow,
                        CreatedBy = username,
                        CreatedDate = DateTime.UtcNow,
                    });
                }
                else
                {
                    journalNoteId = preExistingConditions.Id;

                    journalNote.Subject = preExistingConditions.Subject;
                    journalNote.Note = preExistingConditions.Note;
                    journalNote.ActionDate = DateTime.UtcNow;
                    journalNote.ModifiedBy = username;
                    journalNote.ModifiedDate = DateTime.UtcNow;
                }

                return journalNoteId;
            }

        }

    }
}
