﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetDailyEquipmentCount
    {

        public class Query : IRequest<DailyEquipmentCountDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }

        }

        public class Handler : IRequestHandler<Query, DailyEquipmentCountDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IEquipmentServiceClient _equipmentServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;


            public Handler(
                IFranchiseServiceClient franchiseServiceClient,
                IEquipmentServiceClient equipmentServiceClient,
                ILogger<GenerateDryingReport> logger,
                JobReadOnlyDataContext context)
            {
                _franchiseServiceClient = franchiseServiceClient;
                _equipmentServiceClient = equipmentServiceClient;
                _logger = logger;
                _context = context;
            }

            public async Task<DailyEquipmentCountDto> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobId = request.Job.Id;
                var job = await _context.Jobs
                    .Include(j => j.JobVisits)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                _logger.LogInformation($"DryingReportLog - starting GetDailyEquipmentCount for jobId: {job.Id}");

                var model = new DailyEquipmentCountDto();

                var baseEquipmentTypes = await _equipmentServiceClient.GetBaseEquipmentTypeAsync(job.FranchiseSetId, cancellationToken);
                var baseEquipmentTypeMapping = baseEquipmentTypes.ToDictionary(bet => bet.Id.ToString(), bet => bet.Name);
                var firstVisit = job.JobVisits.OrderBy(x => x.Date).FirstOrDefault();
                var lastVisit = job.JobVisits.OrderByDescending(x => x.Date).FirstOrDefault();
                model.FirstJobVisitDate = firstVisit?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);
                model.LastJobVisitDate = lastVisit?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);
                model.EquipmentPlacementCountByDay = await GetEquipmentCount(job, request.FranchiseTimeZone, baseEquipmentTypeMapping, cancellationToken);
                model.EquipmentSummary = await GetEquipmentPlacementDetail(job, request.FranchiseTimeZone, baseEquipmentTypeMapping, cancellationToken);
                _logger.LogInformation($"DryingReportLog - GetDailyEquipmentCount - completed for jobId: {job.Id}");

                return model;
            }

            public async Task<List<EquipmentCountDetail>> GetEquipmentCount(
                Job job,
                LookupService.Features.LookUps.Lead.TimeZoneDto franchiseTimeZone,
                Dictionary<string, string> baseEquipmentTypes,
                CancellationToken cancellationToken)
            {
                var visitDates = (from jv in job.JobVisits
                                  where jv.JobId == job.Id
                                  select jv.Date);

                _logger.LogInformation("DryingReportLog - GetEquipmentCount - Dates visited for jobId: {Id}, visitDates: {visitDates} ", job.Id, visitDates);
                if (!visitDates.Any()) return new List<EquipmentCountDetail>();

                var firstDay = visitDates.Min().GetLocalFranchiseDateTime(franchiseTimeZone);
                var lastDay = visitDates.Max().GetLocalFranchiseDateTime(franchiseTimeZone);

                var dayCount = (int)Math.Round((lastDay.Date - firstDay.Date).TotalDays, 0) + 1;
                var days = Enumerable.Range(0, dayCount).Select(x => firstDay.AddDays(x));

                var daysWithEod = days
                    .Select(x => new
                    {
                        DayBegin = x.Date,
                        DayEnd = x.Date.AddDays(1).AddMilliseconds(-1)
                    });

                _logger.LogDebug("DryingReportLog - GetEquipmentCount - Dates that will be counted/graphed for jobId: {Id},  {daysWithEod}", job.Id, daysWithEod);
                var placements = await (from ep in _context.EquipmentPlacements
                                        join ja in _context.JobAreas on ep.JobAreaId equals ja.Id
                                        where ja.JobId == job.Id
                                        select new
                                        {
                                            BaseEquipmentType = ep.Equipment.EquipmentModel.EquipmentType.BaseEquipmentTypeId.ToString(),
                                            BeginDate = ((DateTime?)ep.BeginDate).GetLocalFranchiseDateTime(franchiseTimeZone),
                                            EndDate = (ep.EndDate).GetLocalFranchiseDateTime(franchiseTimeZone)
                                        })
                                  .ToListAsync(cancellationToken);

                var placementCount = placements?.Count ?? 0;
                _logger.LogInformation("DryingReportLog - GetEquipmentCount - Total placement count for all equipment - {placementCount}, All placements: {@placements}", placementCount, placements);

                var equipmentCountDetails = new List<EquipmentCountDetail>();
                foreach (var placementDay in daysWithEod)
                {
                    foreach (var placement in placements)
                    {
                        var equipmentCountDetail = equipmentCountDetails
                            .FirstOrDefault(x => x.EquipmentType == placement.BaseEquipmentType
                                && x.Day == placementDay.DayBegin);
                        if (equipmentCountDetail == null)
                        {
                            equipmentCountDetail = new EquipmentCountDetail()
                            {
                                EquipmentType = placement.BaseEquipmentType,
                                Day = placementDay.DayBegin,
                                Count = 0
                            };
                            equipmentCountDetails.Add(equipmentCountDetail);
                        }

                        _logger.LogDebug("DryingReportLog - GetEquipmentCount - Processing placement day: {@placementDay}, for placement: {@placement}", placementDay, placement);
                        if (placement.BeginDate <= placementDay.DayEnd
                            && (!placement.EndDate.HasValue || placement.EndDate.Value.Date > placementDay.DayEnd.Date))
                        {
                            _logger.LogDebug("DryingReportLog - GetEquipmentCount - Placement of equipment type {equipmentType} found - updating count", placement.BaseEquipmentType);
                            equipmentCountDetail.Count++;
                        }
                    }
                }
                foreach (var equipmentCountDetail in equipmentCountDetails)
                {
                    equipmentCountDetail.EquipmentType = baseEquipmentTypes.GetValueOrDefault(equipmentCountDetail.EquipmentType, null);
                }
                var placementCountsSummarizedCount = equipmentCountDetails?.Count() ?? 0;
                _logger.LogDebug("DryingReportLog - GetEquipmentCount - Total count of equipment count details: {placementCountsSummarizedCount}", placementCountsSummarizedCount);
                _logger.LogInformation("DryingReportLog - GetEquipmentCount - Full model for equipmentCountDetails {@equipmentCountDetails}", equipmentCountDetails);

                return equipmentCountDetails.ToList();
            }

            public async Task<List<EquipmentPlacementDetail>> GetEquipmentPlacementDetail(
                Job job,
                LookupService.Features.LookUps.Lead.TimeZoneDto franchiseTimeZone,
                Dictionary<string, string> baseEquipmentTypeMappings,
                CancellationToken cancellationToken)
            {
                _logger.LogDebug("DryingReportLog - GetEquipmentPlacementDetail - get GetEquipmentPlacementDetail on jobId: {Id}", job.Id);
                var equipmentPlacements = await _context.JobAreas
                    .Include(ja => ja.EquipmentPlacements)
                        .ThenInclude(x => x.JobArea)
                    .Include(ja => ja.EquipmentPlacements)
                        .ThenInclude(x => x.Equipment)
                        .ThenInclude(x => x.EquipmentModel)
                        .ThenInclude(x => x.EquipmentType)
                    .Where(ja => ja.JobId == job.Id)
                    .SelectMany(ja => ja.EquipmentPlacements).ToListAsync(cancellationToken);

                var result = new List<EquipmentPlacementDetail>();
                equipmentPlacements.ForEach(ep =>
                {
                    var days = ep.EndDate.HasValue ? Math.Round((decimal)((DateTime)ep.EndDate).Subtract(ep.BeginDate).TotalDays, 2) : Math.Round((decimal)DateTime.UtcNow.Subtract(ep.BeginDate).TotalDays, 2);
                    var baseEquipmentTypeId = ep.Equipment.EquipmentModel.EquipmentType.BaseEquipmentTypeId;
                    var baseEquipmentTypeName = baseEquipmentTypeMappings.GetValueOrDefault(baseEquipmentTypeId.ToString(), null);
                    result.Add(new EquipmentPlacementDetail
                    {
                        // EquipmentType here is actually a BaseEquipmentType
                        EquipmentType = baseEquipmentTypeName,
                        Room = ep.JobArea?.Name,
                        EquipmentModel = ep.Equipment.EquipmentModel.Name,
                        AssetNumber = ep.Equipment.AssetNumber,
                        Placed = ep.BeginDate.GetLocalFranchiseDateTime(franchiseTimeZone),
                        Removed = ep.EndDate.GetLocalFranchiseDateTime(franchiseTimeZone) ?? DateTime.MinValue,
                        Days = days,
                        Hours = Math.Round(days * 24m, 2)
                    });
                });
                return result;
            }
        }
    }
}