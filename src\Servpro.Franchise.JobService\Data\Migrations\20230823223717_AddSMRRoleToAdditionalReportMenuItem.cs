﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddSMRRoleToAdditionalReportMenuItem : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("1945086b-9c8a-4697-8ee6-cde2a89e4803"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.UpdateData(
                table: "MenuItem",
                keyColumn: "Id",
                keyValue: new Guid("8996855a-f826-4052-8f67-ac2a7e463f95"),
                column: "Items",
                value: "[{\"Order\":1,\"Name\":\"KPM Dashboard - Owner View\",\"Url\":\"views/KPMDashboard-Owner/KPMDashboard\",\"Roles\":[\"GM/Owner\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9e46d5e2-5349-4f80-ac0a-84388daa44aa\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":2,\"Name\":\"KPM Dashboard\",\"Url\":\"views/KPMDashboard/KPMDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9a152e44-6a00-4d17-bbf4-0a4dfd509f16\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":3,\"Name\":\"Client KPM Scorecard\",\"Url\":\"views/ClientKPMDashboard/ClientKPMDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"10196706-1a1a-46f5-ad98-7be85c16daf1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":4,\"Name\":\"Financial KPM Scorecard\",\"Url\":\"FinancialKPMDashboard\",\"Roles\":[\"GM/Owner\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e400a392-2bdf-4072-8cfc-36aebb720ec7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":5,\"Name\":\"Office KPM Scorecard\",\"Url\":\"OfficeKPMsDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"202ee198-b0cc-4869-a206-2dff4faf31e1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":6,\"Name\":\"Production KPM Scorecard\",\"Url\":\"ProductionKPMsDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"a85b10c5-0986-4961-86a5-1b00e5b21618\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":7,\"Name\":\"Marketing KPM Dashboard\",\"Url\":\"MarketingKPMs\",\"Roles\":[\"GM/Owner\",\"WCTier2\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseMarketingSupportCoordinatorMSCs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"229618d9-8a8d-4607-9661-59b760fd212e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":8,\"Name\":\"Drive the 5 Scorecard\",\"Url\":\"DrivetheFiveScorecard\",\"Roles\":[\"GM/Owner\",\"WCTier3\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"efb2a0d0-f627-4bcc-8349-80b5041e9335\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":9,\"Name\":\"Revenue Dashboard\",\"Url\":\"views/RevenueDashboard/VolumeDashboard\",\"Roles\":[\"GM/Owner\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ea8daf13-7612-4e49-ab71-83a364f3e570\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":10,\"Name\":\"Additional Reports\",\"Url\":\"projects\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\",\"F_AllFranchiseMarketingRepresentativeSMRs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"11a2ef5c-3ab7-4487-addb-4f40451dd6f5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":11,\"Name\":\"Servpro.com Call Conversion Rate\",\"Url\":\"\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[\"4faa1c4d-2b55-4e76-90c1-e6157b128575\"],\"Items\":[],\"Id\":\"2140d32f-be94-4dd3-b3a3-6aed2602e285\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}]");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("2cf286fd-9269-4192-8c77-9134a6bf191d"), null, new DateTime(2023, 8, 23, 22, 37, 16, 399, DateTimeKind.Utc).AddTicks(1514), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("2cf286fd-9269-4192-8c77-9134a6bf191d"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.UpdateData(
                table: "MenuItem",
                keyColumn: "Id",
                keyValue: new Guid("8996855a-f826-4052-8f67-ac2a7e463f95"),
                column: "Items",
                value: "[{\"Order\":1,\"Name\":\"KPM Dashboard - Owner View\",\"Url\":\"views/KPMDashboard-Owner/KPMDashboard\",\"Roles\":[\"GM/Owner\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9e46d5e2-5349-4f80-ac0a-84388daa44aa\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":2,\"Name\":\"KPM Dashboard\",\"Url\":\"views/KPMDashboard/KPMDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9a152e44-6a00-4d17-bbf4-0a4dfd509f16\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":3,\"Name\":\"Client KPM Scorecard\",\"Url\":\"views/ClientKPMDashboard/ClientKPMDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"10196706-1a1a-46f5-ad98-7be85c16daf1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":4,\"Name\":\"Financial KPM Scorecard\",\"Url\":\"FinancialKPMDashboard\",\"Roles\":[\"GM/Owner\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e400a392-2bdf-4072-8cfc-36aebb720ec7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":5,\"Name\":\"Office KPM Scorecard\",\"Url\":\"OfficeKPMsDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"202ee198-b0cc-4869-a206-2dff4faf31e1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":6,\"Name\":\"Production KPM Scorecard\",\"Url\":\"ProductionKPMsDashboard\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"a85b10c5-0986-4961-86a5-1b00e5b21618\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":7,\"Name\":\"Marketing KPM Dashboard\",\"Url\":\"MarketingKPMs\",\"Roles\":[\"GM/Owner\",\"WCTier2\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseMarketingSupportCoordinatorMSCs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"229618d9-8a8d-4607-9661-59b760fd212e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":8,\"Name\":\"Drive the 5 Scorecard\",\"Url\":\"DrivetheFiveScorecard\",\"Roles\":[\"GM/Owner\",\"WCTier3\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"efb2a0d0-f627-4bcc-8349-80b5041e9335\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":9,\"Name\":\"Revenue Dashboard\",\"Url\":\"views/RevenueDashboard/VolumeDashboard\",\"Roles\":[\"GM/Owner\",\"WCTier3\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ea8daf13-7612-4e49-ab71-83a364f3e570\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":10,\"Name\":\"Additional Reports\",\"Url\":\"projects\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"11a2ef5c-3ab7-4487-addb-4f40451dd6f5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":11,\"Name\":\"Servpro.com Call Conversion Rate\",\"Url\":\"\",\"Roles\":[\"Staff\",\"Manager\",\"GM/Owner\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[\"4faa1c4d-2b55-4e76-90c1-e6157b128575\"],\"Items\":[],\"Id\":\"2140d32f-be94-4dd3-b3a3-6aed2602e285\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}]");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("1945086b-9c8a-4697-8ee6-cde2a89e4803"), null, new DateTime(2023, 7, 31, 17, 52, 7, 80, DateTimeKind.Utc).AddTicks(1018), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }
    }
}
