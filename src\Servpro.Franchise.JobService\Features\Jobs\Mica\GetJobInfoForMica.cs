﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetJobInfoForMica
    {
        public class Query : IRequest<JobInfoDto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }
        public class JobInfoDto
        {
            public Guid Id { get; set; }
            public string CustomerFullName { get; set; }
            public string ProjectNumber { get; set; }
            public string JobProgress { get; set; }
            public DateTime DateReceived { get; set; }
            public string LossAddress { get; set; }
            public string LossCity { get; set; }
            public string LossStateAbbreviation { get; set; }
            public string LossPostalCode { get; set; }
            public string LossTypeName { get; set; }
            public string PropertyTypeName { get; set; }

        }

        public class Handler : IRequestHandler<Query, JobInfoDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupClient;
            private readonly ILogger<Handler> _logger;

            public Handler(
                  JobReadOnlyDataContext context,
                  ILookupServiceClient lookupClient,
                  ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
                _lookupClient = lookupClient;
            }

            public async Task<JobInfoDto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);

                _logger.LogInformation("Getting Job info for Mica.");

                var job = await _context.Jobs
                    .Include(x => x.JobContacts)
                        .ThenInclude(x => x.Contact)
                    .Include(x => x.JobTriStateAnswers)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var lookups = await _lookupClient.GetLookupsAsync(cancellationToken);
                var retDto = Mapper.Map(job, lookups);
                return retDto;
            }
        }

        public class Mapper
        {
            public static JobInfoDto Map(Models.Job job, GetLookups.Dto lookups)
            {
                return new JobInfoDto
                {
                    Id = job.Id,
                    ProjectNumber = job.ProjectNumber,
                    JobProgress = ((JobProgress)job.JobProgress).ToString(),
                    DateReceived = (DateTime)(job.JobDates?.Where(x => x.JobDateTypeId == JobDateTypes.LossReceived)?.FirstOrDefault().Date),
                    CustomerFullName = job.Customer?.FullName,
                    LossAddress = job.LossAddress?.Address1,
                    LossCity = job.LossAddress?.City,
                    LossPostalCode = job.LossAddress?.PostalCode,
                    LossStateAbbreviation = job.LossAddress?.State.StateAbbreviation,
                    LossTypeName = lookups.LossTypes?.FirstOrDefault(x => x.Id == job.LossTypeId)?.Name,
                    PropertyTypeName = lookups.PropertyTypes?.FirstOrDefault(x => x.Id == job.PropertyTypeId)?.Name
                };
            }

        }
    }
}
