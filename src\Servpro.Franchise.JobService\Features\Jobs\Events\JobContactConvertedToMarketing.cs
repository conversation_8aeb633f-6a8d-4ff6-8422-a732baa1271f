﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Contacts;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using EventEnums = Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;
using JobContactTypes = Servpro.Franchise.LookupService.Constants.JobContactTypes;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobContactConvertedToMarketing
    {
        public class Event : ContactConvertedToMarketingEvent, IRequest
        {
            public Event(ContactConvertedToMarketingDto convertedDto, Guid correlationId) : base(convertedDto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<JobContactConvertedToMarketing> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<JobContactConvertedToMarketing> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{contactId}", incomingEvent.ContactConverted.ContactId);
                _logger.LogInformation("Processing event: {@payload}", incomingEvent);

                var contact = await _context.Contacts
                    .FirstOrDefaultAsync(j => j.Id == incomingEvent.ContactConverted.ContactId, cancellationToken);

                if (contact == null)
                {
                    _logger.LogInformation("Contact does not exists, creating a dummy contact");
                    var dummyPhone = new Phone()
                    {
                        PhoneNumber = "7777777777",
                        PhoneExtension = "77",
                        PhoneType = (PhoneType)Enum.ToObject(typeof(PhoneType), PhoneType.Home)
                    };
                    Phone[] phoneNumbers = { dummyPhone };
                    var newContact = new Contact()
                    {
                        Id = incomingEvent.ContactConverted.ContactId,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        FirstName = "Placeholder",
                        LastName = "Placeholder",
                        IsMarketingContact = incomingEvent.ContactConverted.IsMarketingContact,
                        MarketingRepId = incomingEvent.ContactConverted.MarketingRepId,
                        SmsNumber = "SmsNumber",
                        PhoneNumbers = phoneNumbers?.ToHashSet()
                    };

                    _context.Contacts.Add(newContact);

                    _logger.LogInformation("Dummy Contact created");
                }
                else
                {
                    UpdateMarketingContact(incomingEvent, contact);

                    // We are getting the first franchiseset based on the marketing rep already assigned to a job
                    // Since we are updating marketing reps that are already assigned to jobs - then we are grabbing the first one we find
                    // in order to get the franchisesetid.  This franchiseSetId will help us use the index on the next query in order
                    // to avoid performance problems - aka queues backing up.
                    var franchiseSetId = (await _context.JobContactMap
                                                  .Include(x => x.Job)
                                                  .Select(x => new { FranchiseSetId = x.Job.FranchiseSetId, ContactId = x.ContactId })
                                                  .AsNoTracking()
                                                  .FirstOrDefaultAsync(x => x.ContactId == incomingEvent.ContactConverted.ContactId, cancellationToken))?.FranchiseSetId;

                    var jobs =  await _context.Jobs
                                        .Include(x => x.JobContacts)
                                            .ThenInclude(x => x.Contact)
                                                .ThenInclude(x => x.Business)
                                        .Include(x => x.Customer)
                                        .Include(x => x.JobBusinesses)
                                            .ThenInclude(x => x.Business)
                                        .Where(j => j.JobContacts.Any(y => y.ContactId == incomingEvent.ContactConverted.ContactId &&
                                                                           (y.JobContactTypeId == JobContactTypes.ReferralSource ||
                                                                           (y.TitleId.HasValue && y.TitleId == Titles.SourceOfReferral &&
                                                                            y.JobContactTypeId == JobContactTypes.AdditionalContact)))
                                                    && j.FranchiseSetId == franchiseSetId
                                        ).ToListAsync(cancellationToken);

                    foreach (Job job in jobs)
                    {
                        job.MarketingRepId = contact.MarketingRepId;
                    }

                    SendMarketingRefferalJobs(incomingEvent, jobs);

                    _logger.LogInformation("For the Contact: {contactId}, has been converted to Marketing User: {isMarketingContact}", incomingEvent.ContactConverted.ContactId, incomingEvent.ContactConverted.IsMarketingContact);
                }

                try
                {
                    await _context.SaveChangesAsync(cancellationToken);
                }
                catch (DbUpdateException ex)
                {
                    _logger.LogWarning(ex.Message);
                }

                return Unit.Value;
            }

            private List<OutboxMessage> GenerateJobUpdatedEventsList(List<Job> jobs, Guid correlationId)
                => jobs.Select(x => MapJobUpdatedEvent(x, correlationId)).ToList();

            private OutboxMessage MapJobUpdatedEvent(Job job, Guid correlationId)
            {
                var jobUpdatedDto = new JobUpdatedEvent.JobUpdatedDto
                {
                    Changes = new Dictionary<string, object>() { { nameof(JobUpdatedEvent.JobUpdatedDto.MarketingRepId), job.MarketingRepId} },
                    JobId = job.Id,
                    MarketingRepId = job.MarketingRepId
                };
                var jobUpdatedEvent = new JobUpdatedEvent(jobUpdatedDto, correlationId);
                _logger.LogInformation("{method} Creating Outbox Message for {event} for job {@job}", nameof(MapJobUpdatedEvent), nameof(JobUpdatedEvent), jobUpdatedDto);
                return new OutboxMessage(jobUpdatedEvent.ToJson(), nameof(JobUpdatedEvent), correlationId, nameof(JobContactConvertedToMarketing));
            }

            private IEnumerable<OutboxMessage> GenerateReferralJobsToMarketingEvent(IEnumerable<Job> jobs, Guid correlationId)
            {
                var jobDtos = jobs.Select(MapJobtoReferralJobDto);
                // Because of the nature of this event (arbitrary size due to contacts/business lists) we have decided to send a single event per job
                //  If a reasonable number of events could be sent in batches that could also be done, but that seems undeterminable based on the lists on the event
                var jobRefferalCreatedEvents = jobDtos.Select(jobDto => new ReferralJobsToMarketingEvent(new List<ReferralJobsToMarketingEvent.JobDto>() { jobDto }, correlationId));
                var messages = jobRefferalCreatedEvents.Select(x => new OutboxMessage(x.ToJson(), nameof(ReferralJobsToMarketingEvent), correlationId, nameof(ReferralJobsToMarketingEvent)));
                return messages;
            }
            private ReferralJobsToMarketingEvent.JobDto MapJobtoReferralJobDto(Job job)
            {
                var jobDto = new ReferralJobsToMarketingEvent.JobDto();
                jobDto.JobId = job.Id;
                jobDto.ProjectNumber = job.ProjectNumber;
                jobDto.JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)job.JobProgress;
                jobDto.DateOfLoss = job.DateOfLoss;
                jobDto.Customer = job.Customer != null ? MapContact(job.Customer) : null;
                jobDto.PropertyTypeId = job.PropertyTypeId;
                jobDto.LossTypeId = job.LossTypeId;
                jobDto.FranchiseSetId = job.FranchiseSetId;
                var jobReferral = job.JobContacts.Where(x => x.JobContactTypeId == JobContactTypes.ReferralSource).FirstOrDefault();
                jobDto.Referral = jobReferral != null && jobReferral.Contact != null ? MapContact(jobReferral.Contact) : null;
                jobDto.TotalRevenue = job.TotalRevenue;
                jobDto.TotalCollections = job.TotalAmountDue;
                jobDto.DateReceived = job.GetDate(Common.JobDateTypes.ReceivedDate);
                jobDto.CauseOfLossId = job.CauseOfLossId;
                jobDto.StructureTypeId = job.StructureTypeId;
                jobDto.FacilityTypeId = job.FacilityTypeId;
                jobDto.JobBusinesses = job.JobBusinesses.Select(MapJobBusinessDto).ToList();
                jobDto.FranchiseId = job.FranchiseId;
                jobDto.InsuranceCarrierId = job.InsuranceCarrierId;
                jobDto.InsuranceClaimNumber = job.InsuranceClaimNumber;
                jobDto.ProjectManagerId = job.ProjectManagerId;
                jobDto.JobContacts = job.JobContacts.Select(MapJobContactDto).ToList();
                return jobDto;
            }


            public ReferralJobsToMarketingEvent.JobContactDto MapJobContactDto(JobContactMap contact) =>
               new ReferralJobsToMarketingEvent.JobContactDto
               {
                   Contact = MapContact(contact.Contact),
                   JobId = contact.JobId,
                   JobContactType = contact.JobContactTypeId,
                   IsBusinessContact = contact.IsBusinessContact,
                   TitleId = contact.TitleId,
                   Id = contact.Id
               };

            private ReferralJobsToMarketingEvent.ContactDto MapContact(Contact contact)
            {
                if (contact == null)
                    return null;

                return new ReferralJobsToMarketingEvent.ContactDto
                {
                    Id = contact.Id,
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    BusinessName = contact.Business?.Name,
                    BusinessId = contact.BusinessId
                };
            }

            public ReferralJobsToMarketingEvent.StateDto MapStateDto(State state)
            {
                if (state == null)
                    return null;

                return new ReferralJobsToMarketingEvent.StateDto
                {
                    StateId = state.StateId,
                    StateName = state.StateName,
                    StateAbbreviation = state.StateAbbreviation,
                    CountryId = state.CountryId,
                    CountryName = state.CountryName,
                    CountyName = state.CountyName
                };
            }

            public ReferralJobsToMarketingEvent.JobBusinessDto MapJobBusinessDto(JobBusinessMap jobBusinessMap) =>
               new ReferralJobsToMarketingEvent.JobBusinessDto
               {
                   JobId = jobBusinessMap.JobId,
                   Business = MapBusinessDto(jobBusinessMap.Business),
                   JobBusinessTypeId = jobBusinessMap.JobBusinessTypeId,
                   BusinessId = jobBusinessMap.BusinessId,
                   IsPrimary = jobBusinessMap.IsPrimary,
               };

            public ReferralJobsToMarketingEvent.BusinessDto MapBusinessDto(Business business)
            {
                if (business == null)
                    return null;

                return new ReferralJobsToMarketingEvent.BusinessDto()
                {
                    Name = business.Name,
                    Address = MapAddressDto(business.Address),
                    PhoneNumber = MapPhoneDto(business.PhoneNumber),
                    IsSystem = business.IsSystem,
                    BusinessTypeId = business.BusinessTypeId,
                    IsDeleted = business.IsDeleted,
                    HasErnetContract = business.HasErnetContract,
                    IsOther = business.IsOther,
                    RecordSourceId = business.RecordSourceId,
                    FranchiseSetId = business.FranchiseSetId,
                    PreferredName = business.PreferredName,
                    EmailAddress = MapEmailDto(business.EmailAddress),
                    Contacts = business.Contacts.Select(MapContact).ToList()
                };
            }


            public ReferralJobsToMarketingEvent.EmailDto MapEmailDto(Email email)
            {
                if (email == null)
                    return null;

                return new ReferralJobsToMarketingEvent.EmailDto()
                {
                    Id = email.Id,
                    Address = email.Address,
                    EmailAddressTypeId = email.EmailAddressTypeId,
                    SequenceNumber = email.SequenceNumber,
                    IsUsedForNotifications = email.IsUsedForNotifications
                };
            }

            public ReferralJobsToMarketingEvent.AddressDto MapAddressDto(Address address)
            {
                if (address == null)
                    return null;

                return new ReferralJobsToMarketingEvent.AddressDto
                {
                    Address1 = address.Address1,
                    Address2 = address.Address2,
                    City = address.City,
                    PostalCode = address.PostalCode,
                    State = MapStateDto(address.State),
                    Latitude = address.Latitude,
                    Logitude = address.Logitude,
                    AddressType = (EventEnums.AddressType)address.AddressType
                };
            }
            public ReferralJobsToMarketingEvent.PhoneDto MapPhoneDto(Phone phone)
            {
                if (phone == null)
                    return null;

                return new ReferralJobsToMarketingEvent.PhoneDto
                {
                    Id = phone.Id,
                    PhoneNumber = phone.PhoneNumber,
                    PhoneExtension = phone.PhoneExtension,
                    PhoneType = (EventEnums.PhoneType)phone.PhoneType
                };
            }

            private void UpdateMarketingContact(Event incomingEvent, Contact contact)
            {
                if (incomingEvent.ContactConverted.IsMarketingContact)
                {
                    contact.MarketingRepId = incomingEvent.ContactConverted.MarketingRepId;
                }
                else
                {
                    contact.MarketingRepId = null;
                }
                contact.IsMarketingContact = incomingEvent.ContactConverted.IsMarketingContact;
            }

            private void SendMarketingRefferalJobs(Event incomingEvent, List<Job> jobs)
            {
                if (incomingEvent.ContactConverted.SendMarketingReferralJobs)
                {
                    if (jobs.Any())
                    {
                        var referralJobsToMarketingEvents = GenerateReferralJobsToMarketingEvent(jobs, incomingEvent.CorrelationId);
                        _context.OutboxMessages.AddRange(referralJobsToMarketingEvents);
                        var jobUpdatedEvents = GenerateJobUpdatedEventsList(jobs, incomingEvent.CorrelationId);
                        _context.OutboxMessages.AddRange(jobUpdatedEvents);
                    }
                }
            }
        }
    }
}
