﻿using Amazon.SQS;
using Amazon.SQS.Model;
using FluentValidation;

using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class StartDryingReport
    {
        public class Command : IRequest<DryingReportStartedEvent.Dto>
        {
            public Command(Guid franchiseSetId, Guid jobId, string token, string username, Guid? correlationId = null)
            {
                JobId = jobId;
                FranchiseSetId = franchiseSetId;
                Token = token;
                Username = username;
                CorrelationId = correlationId;
            }

            public string Token { get; set; }
            public string Username { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid JobId { get; set; }
            public Guid? CorrelationId { get; set; }
            
        }
        
        public class QueryValidator : AbstractValidator<Command>
        {
            public QueryValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }

        public class Handler: IRequestHandler<Command, DryingReportStartedEvent.Dto>
        {
            private readonly IMediator _mediator;
            private readonly IConfiguration _config;
            private readonly ILogger<GenerateDryingReport> _logger;
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            private readonly IAmazonSQS _sqsClient;
            private readonly string _sqsUrl;

            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";

            public Handler(IMediator mediator,
                           IConfiguration config,
                           ILogger<GenerateDryingReport> logger,
                           ILookupServiceClient lookupServiceClient,
                           IFranchiseServiceClient franchiseServiceClient,
                           ISessionIdAccessor sessionIdAccessor,
                           IAmazonSQS sqsClient,
                           JobReadOnlyDataContext context)
            {
                _mediator = mediator;
                _logger = logger;
                _context = context;
                _lookupServiceClient = lookupServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
                _sqsClient = sqsClient;
                _config = config;
                _sessionIdAccessor = sessionIdAccessor;

                _sqsUrl = config["AWS:SqsUrl"];
            }

            public async Task<DryingReportStartedEvent.Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                var jobId = request.JobId;
                _logger.LogInformation("DryingReportLog - Start drying report to create the intial event", jobId);
                var franchiseSetId = request.FranchiseSetId;
                var correlationId = request.CorrelationId.HasValue ? request.CorrelationId.Value : _sessionIdAccessor.GetCorrelationGuid();
                var token = request.Token.Replace("Bearer","").Replace(" ", "");
                var username = request.Username;

                _logger.LogInformation("DryingReportLog - Getting presigned url", jobId);
                var dto = await GetPresignedDto(jobId, franchiseSetId, cancellationToken);
                _logger.LogInformation("DryingReportLog - Presigned url generated for jobId: {jobId} - {@presignedUrlDto}", jobId, dto);

                //generate DryingReportStartedEvent and place it directly to the job-service queue.  
                // Our policies wont let our services handle their own events via our sns topic - so by placing this event directly on
                // the sqs queue - job-service will immediately process the event
                _logger.LogInformation("DryingReportLog - Sending initial request to generate drying report for jobId:{jobId}", dto);
                var dryingReportStartedEvent = new DryingReportStartedEvent(franchiseSetId, jobId, dto, token, username, correlationId);
                var response = await SendEventToJobServiceQueue(dryingReportStartedEvent, correlationId, cancellationToken);
                if(response.HttpStatusCode != System.Net.HttpStatusCode.OK)
                    throw new Exception($"An error occurred trying to generate the Drying Report for jobId: {jobId}.  While sending the DryingReportStartedEvent to the queue the following reponse occurred: {response}");
                
                _logger.LogInformation("DryingReportLog - Finished initiating Drying Report for jobId: {jobId}", jobId);

                return dto;
            }

            private async Task<DryingReportStartedEvent.Dto> GetPresignedDto(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var generateDocumentUrl = new GenerateDocumentUrl.Command()
                {
                    JobId = jobId,
                    FranchiseSetId = franchiseSetId,
                    OptionalContentType = "application/pdf",
                    Media = new List<GenerateDocumentUrl.MediaDto>() {
                        new GenerateDocumentUrl.MediaDto() {
                            Name = $"DryingReport_{DateTime.Now:MMM-dd-yyyy}.pdf"
                        }
                    }
                };

                var presignedUrls = await _mediator.Send(generateDocumentUrl, cancellationToken);
                var presignedUrl = presignedUrls.ToList().FirstOrDefault();
                if (presignedUrl == null)
                {
                    throw new ResourceNotFoundException("A presigned url could not be generated for the requested Drying report.");
                }

                var dto = new DryingReportStartedEvent.Dto()
                {
                    Id = presignedUrl.Id,
                    Key = presignedUrl.Key,
                    Name = presignedUrl.Name,
                    SignedUrl = presignedUrl.SignedUrl,
                    BucketName = presignedUrl.BucketName
                };

                return dto;
            }

            public async Task<SendMessageResponse> SendEventToJobServiceQueue(DryingReportStartedEvent @event, Guid correlationId, CancellationToken cancellationToken)
            {
                if (@event != null)
                {
                    var servproMessage = new ServproMessage(Guid.NewGuid().ToString(), 
                                                            nameof(DryingReportStartedEvent), 
                                                            nameof(StartDryingReport), 
                                                            correlationId, 
                                                            @event.ToJson(), 
                                                            DateTime.UtcNow);

                    var messageRequest = new SendMessageRequest()
                    {
                        QueueUrl = _sqsUrl,
                        MessageBody = servproMessage.ToJson(),
                        MessageAttributes = new Dictionary<string, MessageAttributeValue>
                        {
                            {
                            "CorrelationId",
                            new MessageAttributeValue
                                {StringValue = correlationId.ToString(), DataType = nameof(String)}
                            },
                            {
                            "MessageType",
                            new MessageAttributeValue
                                {StringValue = nameof(DryingReportStartedEvent), DataType = nameof(String)}
                            }
                        }
                    };

                    var response = await _sqsClient.SendMessageAsync(messageRequest, cancellationToken);
                    return response;
                }

                return null;
            }
        }
    }
}