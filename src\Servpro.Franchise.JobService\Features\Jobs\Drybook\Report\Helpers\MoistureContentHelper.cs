﻿using System.Collections.Generic;
using System.Linq;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class MoistureContentHelper
    {
        public class ZoneVisitBundle
        {
            public string Zone { get; set; }
            public List<GroupOfReadings> VisitReadingGroups { get; set; }
        }

        public class GroupOfReadings
        {
            public int GroupNum { get; set; }
            public List<MoistureContentReading> Readings { get; set; }
        }

        public static List<ZoneVisitBundle> GetVisitReadingBundles(List<MoistureContentReading> readings)
        {
            if (readings == null) return new List<ZoneVisitBundle>();

            const int maxVisitsPerPageWidth = 8;
            var groupedByZone = readings.GroupBy(r => r.Zone);

            var zoneVisitBundles = groupedByZone
                .Select(bz => new ZoneVisitBundle
                {
                    Zone = bz.Key,
                    VisitReadingGroups = bz
                        .GroupBy(r => (r.VisitIndex - 1) / maxVisitsPerPageWidth)
                        .Select(rv => new GroupOfReadings { GroupNum = rv.Key, Readings = rv.ToList() })
                        .ToList()
                })
                .ToList();

            return zoneVisitBundles;
        }
    }
}
