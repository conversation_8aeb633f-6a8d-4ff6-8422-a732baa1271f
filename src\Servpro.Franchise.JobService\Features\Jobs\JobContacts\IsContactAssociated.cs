﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JobContacts
{
    public class IsContactAssociated
    {
        public class Query : IRequest<bool>
        {
            public Query(Guid contactId)
            {
                ContactId = contactId;
            }
            public Guid ContactId { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.ContactId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Query, bool>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobReadOnlyDataContext db, ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<bool> Handle(Query request,
                CancellationToken cancellationToken)
            {
                _logger.LogInformation("Checking for jobs associated to contact: {contactId}", request.ContactId);

                var isAssociated = await _db.JobContactMap.AnyAsync(x => x.ContactId == request.ContactId
                    && (x.JobContactTypeId == JobContactTypes.Customer || x.JobContactTypeId == JobContactTypes.Caller), cancellationToken);

                _logger.LogInformation("Result for associated jobs: {isAssociated}", isAssociated);

                return isAssociated;
            }
        }
    }
}
