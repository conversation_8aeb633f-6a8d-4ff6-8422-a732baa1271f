﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge
{
	public class JobMergeContext
	{
		private IJobMergeSection _jobMergeSection;

		public void SetJobMergeSection(IJobMergeSection jobMergeSection)
		{
			_jobMergeSection = jobMergeSection;
		}

		public void ExecuteJobMergeSection(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
		{
			_jobMergeSection.Merge(sourceJob, targetJob, isDryingDataCopied, hasCallDispositionSourceJob, hasCallDispositionTargetJob);
		}
	}
}
