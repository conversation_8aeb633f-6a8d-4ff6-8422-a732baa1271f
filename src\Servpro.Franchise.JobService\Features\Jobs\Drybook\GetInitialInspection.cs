﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetInitialInspection
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Dto
        {
            public Dto(
                Guid jobId,
                Guid? productionManagerId,
                int? levelsAffected,
                DateTime? onSiteArrival)
            {
                JobId = jobId;
                ProductionManagerId = productionManagerId;
                LevelsAffected = levelsAffected;
                OnSiteArrival = onSiteArrival;
            }

            public Guid JobId { get; }
            public Guid? ProductionManagerId { get; }
            public int? LevelsAffected { get; }
            public DateTime? OnSiteArrival { get; }
            public List<QuestionAnswerDto> QuestionAnswers { get; set; } = new List<QuestionAnswerDto>();

            public class QuestionAnswerDto
            {
                public QuestionAnswerDto(
                    Guid id,
                    Guid jobTriStateQuestionId,
                    bool? answer,
                    int journalNoteCount)
                {
                    Id = id;
                    JobTriStateQuestionId = jobTriStateQuestionId;
                    Answer = answer;
                    JournalNoteCount = journalNoteCount;
                }

                public Guid Id { get; }
                public Guid JobTriStateQuestionId { get; }
                public bool? Answer { get; }
                public int JournalNoteCount { get; }
            }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {

            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetInitialInspection> _logger;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobReadOnlyDataContext context, ILogger<GetInitialInspection> logger,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _logger = logger;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .AsNoTracking()
                    .Include(j => j.JobTriStateAnswers)
                    .Include(j => j.Tasks)
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job is null)
                {
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");
                }

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                var initialJobVisit = await _context.JobVisit
                        .Include(jv => jv.JobVisitTriStateAnswers)
                    .Where(jv => jv.JobId == request.JobId)
                    .OrderBy(x => x.Date)
                    .FirstOrDefaultAsync(cancellationToken);

                var noteCountByQuestionId = job.Tasks
                    .Where(x => x.JobTriStateQuestionId.HasValue)
                    .GroupBy(x => x.JobTriStateQuestionId.Value)
                    .ToDictionary(x => x.Key, x => x.Count());

                var responseDto = Map(job, initialJobVisit, noteCountByQuestionId, lookups);
                _logger.LogDebug("Handler returning response: {@responseDto}", responseDto);
                return responseDto;
            }

            private static Dto Map(
                Job job,
                JobVisit initialJobVisit,
                IReadOnlyDictionary<Guid, int> noteCountByQuestionId,
                GetLookups.Dto lookups)
            {
                var dto = new Dto(
                    job.Id,
                    job.ProductionManagerId,
                    job.LevelsAffected,
                    job.GetDate(JobDateTypes.InitialOnSiteArrival));

                dto.QuestionAnswers.AddRange(job.JobTriStateAnswers
                    // filter out so that we return only the questions asked in the initial
                    // inspection tab where IsAskedPerVisit = false
                    .Where(x => InitialInspectionQuestionIds.NotAskedPerVisitIds(lookups).Contains(x.JobTriStateQuestionId))
                    .Select(x => Map(x, noteCountByQuestionId)));

                if (initialJobVisit?.JobVisitTriStateAnswers != null)
                {
                    dto.QuestionAnswers.AddRange(initialJobVisit.JobVisitTriStateAnswers
                        // filter out so that we return only the questions asked in the initial
                        // inspection (first daily arrival/jobVisit) tab where IsAskedPerVisit = true
                        .Where(y => InitialInspectionQuestionIds.AskedPerVisitIds(lookups).Contains(y.JobTriStateQuestionId))
                        .Select(y => Map(y, noteCountByQuestionId)));
                }

                return dto;
            }

            private static Dto.QuestionAnswerDto Map(
                JobTriStateAnswer answer,
                IReadOnlyDictionary<Guid, int> noteCountByQuestionId)
                => new Dto.QuestionAnswerDto(
                    answer.Id,
                    answer.JobTriStateQuestionId,
                    answer.Answer,
                    noteCountByQuestionId.ContainsKey(answer.JobTriStateQuestionId)
                        ? noteCountByQuestionId[answer.JobTriStateQuestionId]
                        : 0);

            private static Dto.QuestionAnswerDto Map(
                JobVisitTriStateAnswer answer,
                IReadOnlyDictionary<Guid, int> noteCountByQuestionId)
                => new Dto.QuestionAnswerDto(
                    answer.Id,
                    answer.JobTriStateQuestionId,
                    answer.Answer,
                    noteCountByQuestionId.ContainsKey(answer.JobTriStateQuestionId)
                        ? noteCountByQuestionId[answer.JobTriStateQuestionId]
                        : 0);
        }
    }
}