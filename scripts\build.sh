#!/bin/sh

echo "calling script to set up dummy creds for S3Client"
sh app/scripts/test-creds.sh

echo "restoring packages"
dotnet restore app/src/Servpro.Franchise.JobService -s $1 -s https://api.nuget.org/v3/index.json 
dotnet restore app/src/Servpro.Franchise.JobService.Integration.Tests -s $1 -s https://api.nuget.org/v3/index.json 

echo "running integration test"
dotnet test  app/src/Servpro.Franchise.JobService.Integration.Tests --no-restore /p:CollectCoverage=true --filter=$2 --logger:"junit;LogFilePath=Servpro.Franchise.JobService.Integration-test-result.xml;MethodFormat=Default;FailureBodyFormat=Verbose"