﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationMiscellaneous : CrsValidationBase
    {
        public string ValidationEffect { get; set; }
        public string ValidationMessage { get; set; }
        public bool RequiredPerVisit { get; set; }
        public bool RequiredPerRoom { get; set; }
        public bool RequiredLastVisit { get; set; }
        public Guid? JobTriStateQuestionId { get; set; }
        public bool? TriggerAnswer { get; set; }
        public bool BypassAllowed { get; set; }

        public List<CrsValidationMiscItem> MiscItems { get; set; }

        public CrsValidationMiscellaneous()
        {
            this.MiscItems = new List<CrsValidationMiscItem>();
        }
    }
}