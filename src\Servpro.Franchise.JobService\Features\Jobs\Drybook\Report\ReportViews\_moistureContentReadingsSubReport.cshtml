@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.MoistureContentReadingsDto
@using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers

@{
    var reportModel = MoistureContentHelper.GetVisitReadingBundles(Model.MoistureContentReadings);
}
@if (reportModel.Count > 0)
{
    <div class="floatLeft">
        <div class="monitoring-section-heading">Moisture Content Readings</div>
        @foreach (var zoneVisit in reportModel.OrderBy(m => m.Zone))
        {
            @foreach (var groupOfReadings in zoneVisit.VisitReadingGroups)
            {
                var listOfRoomReadings = groupOfReadings.Readings
                .OrderBy(x => x.Room)
                .ThenBy(x => x.Material)
                .ThenBy(x => x.Goal)
                .ThenBy(x => x.PointsOrPercent)
                .Select((x, index) => new { x.Material, x.Room, x.Goal, x.PointsOr<PERSON>er<PERSON>, x.Visit<PERSON>ate, x.Technician, x.<PERSON>alue, x.Removed<PERSON>nVisit, x.GoalMet, x.NotDrying, Index = index })
                .ToList();

                var uniqueReadings = new Dictionary<string, List<dynamic>>();

                foreach (var reading in listOfRoomReadings)
                {
                    var readingKey = $"{reading.Room}-{reading.Material}-{reading.Goal}-{reading.PointsOrPercent}-{reading.VisitDate}-{reading.Technician}";

                    if (!uniqueReadings.ContainsKey(readingKey))
                    {
                        uniqueReadings[readingKey] = new List<dynamic>();
                    }

                    uniqueReadings[readingKey].Add(reading);
                }

                var flattenedReadings = uniqueReadings.Values.SelectMany(r => r).ToList();

                var moistureContentHeaders = groupOfReadings.Readings
                .Select(x => new { x.VisitDate, x.Technician })
                .Distinct()
                .OrderBy(x => x.VisitDate)
                .ToList();

                var validKeys = new HashSet<string>(
                uniqueReadings.Keys.Where(key =>
                uniqueReadings[key].Any(dr => dr.ReadingValue != null || dr.RemovedOnVisit))
                );

                var filteredReadings = flattenedReadings
                .Where(r => validKeys.Contains($"{r.Room}-{r.Material}-{r.Goal}-{r.PointsOrPercent}-{r.VisitDate}-{r.Technician}"))
                .ToList();

                <div class="monitoring-data-content">
                    <p class="monitoring-section-heading2">@zoneVisit.Zone</p>

                    <!--TODO: We removed this bookmark becuase it is causing issues with the styling - the header will wrap if its too long
                    This will need to be addressed.-->
                    <!-- <p class="monitoring-section-heading2">{moistureContentZoneName}@zoneVisit.Zone{/moistureContentZoneName}</p> -->

                    <table class="monitoring-table" summary="monitoring">
                        <thead>
                            @*--------- Printing MoistureContent header ---------*@
                            <tr>
                                <th scope="col" class="text-bottom">Room</th>
                                <th scope="col" class="text-bottom">Material</th>
                                <th scope="col" class="text-bottom">%/Pts</th>
                                <th scope="col" class="text-bottom">Goal</th>
                                @foreach (var header in moistureContentHeaders)
                                {
                                    <th scope="col">@header.VisitDate.ToString("MM/d h:mm tt")<br />@header.Technician</th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            @*--------- Printing MoistureContent content ---------*@
                            @foreach (var roomMaterialReading in filteredReadings.Distinct())
                            {
                                // Check if the current row has any valid values before rendering
                                var hasValues = moistureContentHeaders.Any(header =>
                                {
                                    var readingKey = $"{roomMaterialReading.Room}-{roomMaterialReading.Material}-{roomMaterialReading.Goal}-{roomMaterialReading.PointsOrPercent}-{header.VisitDate}-{header.Technician}";
                                    var dayReading = uniqueReadings.ContainsKey(readingKey) ? uniqueReadings[readingKey].FirstOrDefault() : null;
                                    return dayReading != null;
                                });

                                if (!hasValues)
                                {
                                    continue; // Skip rows without values
                                }

                                <tr>
                                    <td class="text-left">@roomMaterialReading.Room</td>
                                    <td class="text-left">@roomMaterialReading.Material</td>
                                    <td>@roomMaterialReading.PointsOrPercent</td>
                                    <td>@roomMaterialReading.Goal</td>
                                    @foreach (var header in moistureContentHeaders)
                                    {
                                        var readingKey = $"{roomMaterialReading.Room}-{roomMaterialReading.Material}-{roomMaterialReading.Goal}-{roomMaterialReading.PointsOrPercent}-{header.VisitDate}-{header.Technician}";
                                        var dayReading = uniqueReadings.ContainsKey(readingKey) ? uniqueReadings[readingKey].FirstOrDefault() : null;

                                        decimal? value = null;
                                        string valueStyle = "";
                                        if (dayReading != null)
                                        {
                                            value = dayReading.ReadingValue;
                                            if (dayReading.RemovedOnVisit || dayReading.GoalMet)
                                            {
                                                valueStyle = "background-color: green";
                                            }
                                            else if (dayReading.NotDrying)
                                            {
                                                valueStyle = "background-color: red";
                                            }

                                            uniqueReadings[readingKey].Remove(dayReading);
                                        }

                                        if (dayReading != null && dayReading.RemovedOnVisit)
                                        {
                                            <td style="@valueStyle">Removed</td>
                                        }
                                        else
                                        {
                                            <td style="@valueStyle">@value</td>
                                        }
                                    }
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        }
    </div>
}
