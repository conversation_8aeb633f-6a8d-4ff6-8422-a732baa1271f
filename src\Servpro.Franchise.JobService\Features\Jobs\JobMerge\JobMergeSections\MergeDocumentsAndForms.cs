﻿using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using System;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeDocumentsAndForms : IJobMergeSection
    {
        private readonly IFormsService _formsService;
        public MergeDocumentsAndForms()
        {
        }
        public MergeDocumentsAndForms(IFormsService formsService)
        {
            _formsService = formsService;
        }
        public async void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            sourceJob.MediaMetadata
                .Where(x => !x.IsDeleted && x.MediaTypeId == MediaTypes.Document &&
                             x.ArtifactTypeId != ArtifactTypes.Invoice &&
                             x.ArtifactTypeId != ArtifactTypes.MobileSketchESX &&
                             x.ArtifactTypeId != ArtifactTypes.MobileSketchJson &&
                             x.ArtifactTypeId != ArtifactTypes.MobileSketchXML &&
                            !targetJob.MediaMetadata.Any(y => y.Id == x.Id) && 
                            !targetJob.MediaMetadata.Any(y => y.MediaPath == x.MediaPath))
                .Select(x => MapMediaMetadata(x, targetJob.Id))
                .ToList().ForEach(targetJob.MediaMetadata.Add);
		}

        private MediaMetadata MapMediaMetadata(MediaMetadata sourceMedia, Guid jobId) => new MediaMetadata
        {
            BucketName = sourceMedia.BucketName,
            FranchiseSetId = sourceMedia.FranchiseSetId,
            JobId = jobId,
            MediaTypeId = sourceMedia.MediaTypeId,
            ArtifactTypeId = sourceMedia.ArtifactTypeId,
            FormTemplateId = sourceMedia.FormTemplateId,
            Name = sourceMedia.Name,
            Description = sourceMedia.Description,
            MediaPath = sourceMedia.MediaPath,
            IsDeleted = sourceMedia.IsDeleted,
            IsForUpload = sourceMedia.IsForUpload,
            UploadedSuccessfully = sourceMedia.UploadedSuccessfully,
            JobSketchId = sourceMedia.JobSketchId,
            JobSketch = sourceMedia.JobSketch,
            JobInvoiceId = sourceMedia.JobInvoiceId,
            JobInvoice = sourceMedia.JobInvoice,
            SignedDate = sourceMedia.SignedDate,
            FormVersion = sourceMedia.FormVersion,
            SyncDate = sourceMedia.SyncDate,
            ArtifactDate = sourceMedia.ArtifactDate,
            Comment = sourceMedia.Comment
        };
    }
}
