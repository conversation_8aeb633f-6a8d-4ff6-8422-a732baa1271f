﻿using System;
using System.Collections.Generic;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class AtmosphericReadingMockData
    {
        public static AtmosphericReadingsDto GetMockForPreview()
        {
            // Generated by the Object Exporter extension for Visual Studio
            // https://visualstudiogallery.msdn.microsoft.com/c6a21c68-f815-4895-999f-cd0885d8774f
            #region Mock AtmosphericReadingsModel


            return new AtmosphericReadingsDto
            {
                ZoneReadingSets = new List<AtmosphericReadingsDto.ZoneReadingSet>
                {
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        ZoneName = "Zone 3",
                        ZoneType = "Drying",
                        ZoneDesc = "zone 3 description",
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #10",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = -3,
                                                    GPP = 38,
                                                    Hours = 12,
                                                    RH = 10,
                                                    Temp = 110,
                                                    GDepNegative = true,
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #1",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = 35,
                                                    GPP = 34,
                                                    Hours = 15,
                                                    RH = 12,
                                                    Temp = 100,
                                                    GDepTooLow = true,
                                                }
                                            },
                                    GPP = 69,
                                    RH = 50,
                                    Technician = "MT",
                                    Temp = 77,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50),
                                    GppNotDecreasing = true,
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #10",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 19,
                                                    GPP = 19,
                                                    Hours = 36,
                                                    RH = 5,
                                                    Temp = 110,
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #1",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = 32,
                                                    GPP = 6,
                                                    Hours = 35,
                                                    RH = 3,
                                                    Temp = 90
                                                }
                                            },
                                    GPP = 66,
                                    RH = 30,
                                    Technician = "MT",
                                    Temp = 75,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0),
                                    GppTooHigh = true,
                                    RhTooHigh = true,
                                    TempOutsideOptimalRangeForEquip = true,
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #10",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #1",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                }
                                            },
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #10",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 3,
                                                    GPP = 20,
                                                    Hours = 22,
                                                    RH = 7,
                                                    Temp = 101
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #1",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = 9,
                                                    GPP = 14,
                                                    Hours = 22,
                                                    RH = 5,
                                                    Temp = 100
                                                }
                                            },
                                    GPP = 23,
                                    RH = 20,
                                    Technician = "MT",
                                    Temp = 72,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                }
                            },
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #9",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #8",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                }
                                            },
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #9",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 61,
                                                    GPP = 26,
                                                    Hours = 33,
                                                    RH = 8,
                                                    Temp = 105
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #8",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 41,
                                                    GPP = 46,
                                                    Hours = 32,
                                                    RH = 12,
                                                    Temp = 110
                                                }
                                            },
                                    GPP = 87,
                                    RH = 55,
                                    Technician = "MT",
                                    Temp = 81,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #9",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 30,
                                                    GPP = 1,
                                                    Hours = 45,
                                                    RH = 1,
                                                    Temp = 78
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #8",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 17,
                                                    GPP = 14,
                                                    Hours = 50,
                                                    RH = 5,
                                                    Temp = 100
                                                }
                                            },
                                    GPP = 31,
                                    RH = 25,
                                    Technician = "MT",
                                    Temp = 74,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #9",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #8",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                }
                                            },
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                }
                            },
                        ZoneName = "Zone 2",
                        ZoneDesc = "zone 2 description",
                        ZoneType = "Drying"
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 14,
                                    RH = 12,
                                    Technician = "MT",
                                    Temp = 72,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 11,
                                    RH = 12,
                                    Technician = "MT",
                                    Temp = 65,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 61,
                                    RH = 40,
                                    Technician = "MT",
                                    Temp = 80,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                }
                            },
                        ZoneName = "Outside",
                        ZoneDesc = null,
                        ZoneType = "Outside"
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "MTTEST#Dscnt",
                                                    EquipmentModel = "1200",
                                                    GDep = -9,
                                                    GPP = 50,
                                                    Hours = 28,
                                                    RH = 10,
                                                    Temp = 120
                                                }
                                            },
                                    GPP = 41,
                                    RH = 45,
                                    Technician = "MT",
                                    Temp = 65,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "MTTEST#Dscnt",
                                                    EquipmentModel = "1200",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                }
                                            },
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "MTTEST#Dscnt",
                                                    EquipmentModel = "1200",
                                                    GDep = 15,
                                                    GPP = 34,
                                                    Hours = 4,
                                                    RH = 12,
                                                    Temp = 100
                                                }
                                            },
                                    GPP = 49,
                                    RH = 35,
                                    Technician = "MT",
                                    Temp = 78,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                }
                            },
                        ZoneName = "Zone 4",
                        ZoneDesc = "zone 4 description",
                        ZoneType = "Drying"
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 17,
                                    RH = 15,
                                    Technician = "MT",
                                    Temp = 71,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 20,
                                    RH = 18,
                                    Technician = "MT",
                                    Temp = 71,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 19,
                                    RH = 18,
                                    Technician = "MT",
                                    Temp = 70,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                }
                            },
                        ZoneName = "Unaffected",
                        ZoneType = "Unaffected",
                        ZoneDesc = "unaffected desc",
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #6",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 11,
                                                    GPP = 46,
                                                    Hours = 31,
                                                    RH = 14,
                                                    Temp = 105
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #7",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 23,
                                                    GPP = 34,
                                                    Hours = 32,
                                                    RH = 10,
                                                    Temp = 106
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #3",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = -12,
                                                    GPP = 69,
                                                    Hours = 30,
                                                    RH = 18,
                                                    Temp = 110
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #1",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 32,
                                                    GPP = 25,
                                                    Hours = 29,
                                                    RH = 5,
                                                    Temp = 120
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "R200 #1",
                                                    EquipmentModel = "270HTX",
                                                    GDep = 19,
                                                    GPP = 38,
                                                    Hours = 33,
                                                    RH = 11,
                                                    Temp = 107
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #2",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "2000 #1",
                                                    EquipmentModel = "2800i",
                                                    GDep = 35,
                                                    GPP = 22,
                                                    Hours = 28,
                                                    RH = 8,
                                                    Temp = 99
                                                }
                                            },
                                    GPP = 57,
                                    RH = 40,
                                    Technician = "MT",
                                    Temp = 78,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #6",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #7",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #3",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #1",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "R200 #1",
                                                    EquipmentModel = "270HTX",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #2",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "2000 #1",
                                                    EquipmentModel = "2800i",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                }
                                            },
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #6",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 41,
                                                    GPP = 4,
                                                    Hours = 53,
                                                    RH = 2,
                                                    Temp = 88
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #7",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 36,
                                                    GPP = 9,
                                                    Hours = 54,
                                                    RH = 5,
                                                    Temp = 87
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #3",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 34,
                                                    GPP = 11,
                                                    Hours = 52,
                                                    RH = 5.1m,
                                                    Temp = 92
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #1",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 34,
                                                    GPP = 11,
                                                    Hours = 51,
                                                    RH = 5,
                                                    Temp = 91
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "R200 #1",
                                                    EquipmentModel = "270HTX",
                                                    GDep = 22,
                                                    GPP = 23,
                                                    Hours = 55,
                                                    RH = 12,
                                                    Temp = 88
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #2",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "2000 #1",
                                                    EquipmentModel = "2800i",
                                                    GDep = 37,
                                                    GPP = 8,
                                                    Hours = 50,
                                                    RH = 4,
                                                    Temp = 90
                                                }
                                            },
                                    GPP = 45,
                                    RH = 35,
                                    Technician = "MT",
                                    Temp = 75,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>
                                            {
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #6",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 29,
                                                    GPP = 39,
                                                    Hours = 4,
                                                    RH = 16,
                                                    Temp = 95
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #7",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 31,
                                                    GPP = 37,
                                                    Hours = 5,
                                                    RH = 14,
                                                    Temp = 98
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #3",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 27,
                                                    GPP = 41,
                                                    Hours = 3,
                                                    RH = 17,
                                                    Temp = 95
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "DHM #1",
                                                    EquipmentModel = "Evolution LGR",
                                                    GDep = 37,
                                                    GPP = 31,
                                                    Hours = 2,
                                                    RH = 15,
                                                    Temp = 90
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "R200 #1",
                                                    EquipmentModel = "270HTX",
                                                    GDep = 41,
                                                    GPP = 27,
                                                    Hours = 6,
                                                    RH = 10,
                                                    Temp = 99
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "1200 #2",
                                                    EquipmentModel = "DrizAir 1200",
                                                    GDep = null,
                                                    GPP = null,
                                                    Hours = 0,
                                                    RH = null,
                                                    Temp = null
                                                },
                                                new AtmosphericReadingsDto.DehuReading
                                                {
                                                    AssetNo = "2000 #1",
                                                    EquipmentModel = "2800i",
                                                    GDep = 34,
                                                    GPP = 34,
                                                    Hours = 1,
                                                    RH = 12,
                                                    Temp = 100
                                                }
                                            },
                                    GPP = 68,
                                    RH = 45,
                                    Technician = "MT",
                                    Temp = 80,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                }
                            },
                        ZoneName = "Zone 1",
                        ZoneType = "Drying",
                        ZoneDesc = "zone 1 description",
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = null,
                                    RH = null,
                                    Technician = "MT",
                                    Temp = null,
                                    VisitTimestamp = new DateTime(2015, 3, 30, 18, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 23,
                                    RH = 20,
                                    Technician = "MT",
                                    Temp = 72,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 19,
                                    RH = 18,
                                    Technician = "MT",
                                    Temp = 69,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 19,
                                    RH = 18,
                                    Technician = "MT",
                                    Temp = 69,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                }
                            },
                        ZoneName = "HVAC",
                        ZoneType = "HVAC",
                        ZoneDesc = "hvac description",
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 19,
                                    RH = 18,
                                    Technician = "MT",
                                    Temp = 69,
                                    VisitTimestamp = new DateTime(2015, 3, 28, 15, 16, 0)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 27,
                                    RH = 25,
                                    Technician = "MT",
                                    Temp = 70,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                }
                            },
                        ZoneName = "HVAC 1",
                        ZoneType = "HVAC",
                        ZoneDesc = "hvac 1 desc"
                    },
                    new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        VisitReadings =
                            new List<AtmosphericReadingsDto.VisitReadingSet>
                            {
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 20,
                                    RH = 20,
                                    Technician = "MT",
                                    Temp = 68,
                                    VisitTimestamp = new DateTime(2015, 3, 26, 14, 30, 50)
                                },
                                new AtmosphericReadingsDto.VisitReadingSet
                                {
                                    DehuReadings =
                                        new List
                                            <AtmosphericReadingsDto.DehuReading>(),
                                    GPP = 21,
                                    RH = 18,
                                    Technician = "MT",
                                    Temp = 73,
                                    VisitTimestamp = new DateTime(2015, 3, 27, 14, 33, 0)
                                }
                            },
                        ZoneName = "HVAC 2",
                        ZoneType = "HVAC",
                        ZoneDesc = "hvac 2 desc",
                    }
                }

            };

            #endregion

        }
    }
}
