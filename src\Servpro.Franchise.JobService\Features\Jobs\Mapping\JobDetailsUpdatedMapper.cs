﻿using Servpro.Franchise.JobService.Features.Jobs.Events;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.JobDetailsUpdatedEvent;
using Enums = Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;

namespace Servpro.Franchise.JobService.Features.Jobs.Mapping
{
    public static partial class JobDetailsUpdatedMapper
    {
        public static JobDetailsUpdatedDto MapJobDetailsUpdatedDto(Job job, bool IsFinancialUpdated, UserInfo userInfo)
        {
            return new JobDetailsUpdatedEvent.JobDetailsUpdatedDto
            {
                InsuranceClientId = job.InsuranceCarrierId ?? Guid.Empty,
                Id = job.Id,
                IsRedFlagged = job.IsRedFlagged,
                RedFlagNotes = job.RedFlagNotes,
                ClaimNumber = job.InsuranceClaimNumber,
                LossAddress = MapLossAddress(job.LossAddress),
                YearBuilt = job.YearStructureBuilt,
                PriorityResponderId = job.PriorityResponderId,
                FranchiseId = job.FranchiseId,
                ProjectManagerId = job.ProjectManagerId,
                CrewChiefId = job.CrewChiefId,
                AssignedServiceRepId = job.AssignedServiceRepId,
                MarketingRepId = job.MarketingRepId,
                ProductionManagerId = job.ProductionManagerId,
                JobFileCoordId = job.JobFileCoordinatorId,
                DispatcherId = job.RecpDispatcherId,
                GeneralManagerId = job.GeneralManagerId,
                OfficeManagerId = job.OfficeManagerId,
                ReconSupportId = job.ReconSupportId,
                PropertyTypeId = job.PropertyTypeId,
                PolicyNumber = job.InsurancePolicyNumber,
                DeductibleAmount = job.DeductibleAmount,
                IsChase = job.IsChase,
                FacilityTypeId = job.FacilityTypeId,
                StructureTypeId = job.StructureTypeId,
                IsWaterAvailable = job.IsWaterAvailable,
                LossNotes = job.LossNote,
                IsElectricAvailable = job.IsElectricAvailable,
                IsStandingWater = job.IsThereStandingWater,
                LevelsAffected = job.LevelsAffected,
                IsMultiUnitStructure = job.IsMultiUnitStructure,
                SquareFeet = job.SquareFeet,
                WorkOrderNumber = job.WorkOrderNumber,
                PurchaseOrderNumber = job.PurchaseOrderNumber,
                SiteReferenceNumber = job.SiteReferenceNumber,
                NteAmount = job.NteAmount,
                TotalRevenue = job.TotalRevenue.GetValueOrDefault(),
                TotalAmountDue = job.TotalAmountDue,
                IsFinancialUpdated = IsFinancialUpdated,
                ReportedById = job.ReportedById ?? Guid.Empty,
                ReferredById = job.ReferredById ?? Guid.Empty,
                UpdatedBy = userInfo.Username,
                UpdatedById = userInfo.Id,
                PercentageAffected = job.MobileData.PercentAffected,
                SquareFeetAffected = job.SquareFeetAffected,
                LossSeverityTypeId = job.LossSeverityId,
                FloorOfOrigin = job.LossOccurredOnLevel,
                RoomsAffected = job.RoomsAffected,
                CauseOfLossId = job.CauseOfLossId,
                LossTypeId = job.LossTypeId
            };
        }


        private static JobDetailsUpdatedEvent.AddressDto MapLossAddress(Address address)
        {
            return new JobDetailsUpdatedEvent.AddressDto()
            {
                Address1 = address.Address1,
                Address2 = address.Address2,
                City = address.City,
                Latitude = address.Latitude,
                Longitude = address.Logitude,
                PostalCode = address.PostalCode,
                AddressType = (Enums.AddressType)address.AddressType,
                State = new JobDetailsUpdatedEvent.StateDto()
                {
                    StateName = address.State.StateName,
                    CountyName = address.State.CountyName,
                    StateAbbreviation = address.State.StateAbbreviation,
                    CountryId = address.State.CountryId,
                    CountryName = address.State.CountryName,
                    StateId = address.State.StateId
                }
            };
        }
    }
}
