﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddingJobMergeHistoryTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("bb8b3fbc-dd17-4595-b550-49181ec2b069"));

            migrationBuilder.AlterColumn<long>(
                name: "ReferenceNumber",
                table: "Job",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.CreateTable(
                name: "JobMergeHistory",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    SourceJobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    TargetJobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    MergeOptions = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobMergeHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobMergeHistory_Job_SourceJobId",
                        column: x => x.SourceJobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobMergeHistory_Job_TargetJobId",
                        column: x => x.TargetJobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("604705d6-ad66-4547-9978-253200db7d30"), null, new DateTime(2024, 9, 26, 20, 28, 26, 962, DateTimeKind.Utc).AddTicks(9163), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_JobMergeHistory_SourceJobId",
                table: "JobMergeHistory",
                column: "SourceJobId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobMergeHistory_TargetJobId",
                table: "JobMergeHistory",
                column: "TargetJobId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "JobMergeHistory");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("604705d6-ad66-4547-9978-253200db7d30"));

            migrationBuilder.AlterColumn<long>(
                name: "ReferenceNumber",
                table: "Job",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint")
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("bb8b3fbc-dd17-4595-b550-49181ec2b069"), null, new DateTime(2024, 9, 23, 22, 32, 52, 476, DateTimeKind.Utc).AddTicks(6067), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
