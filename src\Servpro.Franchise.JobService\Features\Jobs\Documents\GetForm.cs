﻿using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GetForm
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid id)
            {
                JobFormId = id;
            }
            public Guid JobFormId { get; }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public Guid? TemplateId { get; set; }
            public Guid? Id { get; set; }
            public DateTime? SignedDate { get; set; }
            public Guid? RecordSourceId { get; set; }
            public bool? IsForUpload { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobReadOnlyDataContext db, ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting form with JobFormId: {JobFormId}", request.JobFormId);
                var form = await _db.MediaMetadata
                     .Include(j => j.Job)
                    .FirstOrDefaultAsync(x => x.Id == request.JobFormId && !x.IsDeleted, cancellationToken);

                if (form is null)
                    throw new ResourceNotFoundException("Form not found");

                return MapDtos(form);
            }

            private static Dto MapDtos(MediaMetadata form)
            {
                return new Dto
                {
                    Id = form.Id,
                    JobId = form.JobId,
                    IsForUpload = form.IsForUpload,
                    SignedDate = form.SignedDate,
                    TemplateId = form.FormTemplateId,
                    RecordSourceId = form.Job.RecordSourceId,
                    Name = form.Name,
                };
            }
        }
    }
}
