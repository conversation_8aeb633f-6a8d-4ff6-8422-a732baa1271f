﻿using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationResult
    {

        public List<CrsValidationEstimaticRequirement> EstimaticsRequirements { get; set; }

        public List<ValidationPhotoRequirements> PhotoRequirements { get; set; }

        public List<CrsValidationForms> Forms { get; set; }

        public List<CrsValidationClientNotificationRequirement> ClientNotificationRequirements { get; set; }

        public List<CrsValidationMiscellaneous> MiscellaneousRequirements { get; set; }

        public CrsValidationJobInfo JobInfo { get; set; }

        public CrsValidationResult()
        {
            this.EstimaticsRequirements = new List<CrsValidationEstimaticRequirement>();
            this.Forms = new List<CrsValidationForms>();
            this.PhotoRequirements = new List<ValidationPhotoRequirements>();
            this.ClientNotificationRequirements = new List<CrsValidationClientNotificationRequirement>();
            this.MiscellaneousRequirements = new List<CrsValidationMiscellaneous>();
            this.JobInfo = new CrsValidationJobInfo();
        }
    }
}