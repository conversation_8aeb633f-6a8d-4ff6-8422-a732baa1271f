﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Erp;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Erp.ErpService
{
    public class ErpService : IErpService
    {
        private readonly JobDataContext _db;
        private readonly ILogger<ErpService> _logger;
        private readonly ISessionIdAccessor _sessionIdAccessor;
        private readonly IUserInfoAccessor _userInfoAccessor;

        public ErpService(
            JobDataContext db,
            ILogger<ErpService> logger,
            ISessionIdAccessor sessionIdAccessor,
            IUserInfoAccessor userInfoAccessor)
        {
            _db = db;
            _logger = logger;
            _sessionIdAccessor = sessionIdAccessor;
            _userInfoAccessor = userInfoAccessor;
        }

        public async Task AssociateErpToJobAsync(Guid jobId, int erpId, CancellationToken cancellationToken)
        {
            var user = _userInfoAccessor.GetUserInfo();
            var correlationId = GetCorrelationId();

            Job job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

            if (job != null)
            {
                _logger.LogInformation("{method}: Setting ERP {erpId} to Job {jobId}",
                    nameof(AssociateErpToJobAsync), erpId, jobId);

                job.AssociatedErpId = erpId;

                var erpAssociatedToJob = GenerateErpAssociatedToJobEvent(jobId, erpId, user, correlationId);
                _db.OutboxMessages.Add(erpAssociatedToJob);

                await _db.SaveChangesAsync(cancellationToken);
            }
            else
            {
                _logger.LogWarning("{method}: Job {jobId} not found",
                    nameof(AssociateErpToJobAsync), jobId);
            }
        }

        public async Task AssociateErpToIntakeLeadAsync(Job job, int erpId)
        {
            var user = _userInfoAccessor.GetUserInfo();
            var correlationId = GetCorrelationId();

            if (job != null)
            {
                _logger.LogInformation("{method}: Setting ERP {erpId} to Job {jobId}",
                    nameof(AssociateErpToIntakeLeadAsync), erpId, job.Id);
                
                job.AssociatedErpId = erpId;

                var erpAssociatedToJob = GenerateErpAssociatedToJobEvent(job.Id, erpId, user, correlationId);
                await _db.OutboxMessages.AddAsync(erpAssociatedToJob);
            }
            else
            {
                _logger.LogWarning("{method}: Job {jobId} not found",
                    nameof(AssociateErpToIntakeLeadAsync), job?.Id);
            }
        }

        public async Task RemoveErpFromJobAsync(Guid jobId, CancellationToken cancellationToken)
        {
            var user = _userInfoAccessor.GetUserInfo();
            var correlationId = GetCorrelationId();

            Job job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

            if (job != null)
            {
                _logger.LogInformation("{method}: Removing ERP from Job {jobId}",
                    nameof(RemoveErpFromJobAsync), jobId);

                job.AssociatedErpId = null;

                var erpRemovedFromJob = GenerateErpRemovedFromJobEvent(jobId, user, correlationId);
                _db.OutboxMessages.Add(erpRemovedFromJob);

                await _db.SaveChangesAsync(cancellationToken);
            }
            else
            {
                _logger.LogWarning("{method}: Job {jobId} not found",
                    nameof(RemoveErpFromJobAsync), jobId);
            }
        }

        public async Task<int?> GetErpFromJobAsync(Guid jobId, CancellationToken cancellationToken)
        {
            var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

            return job?.AssociatedErpId;
        }

        private OutboxMessage GenerateErpAssociatedToJobEvent(Guid jobId, int erpId, UserInfo user, Guid correlationId)
        {
            return new OutboxMessage(new ErpAssociatedToJobEvent(jobId, erpId, correlationId).ToJson(),
                nameof(ErpAssociatedToJobEvent), correlationId, user.Username);
        }

        private OutboxMessage GenerateErpRemovedFromJobEvent(Guid jobId, UserInfo user, Guid correlationId)
        {
            return new OutboxMessage(new ErpRemovedFromJobEvent(jobId, correlationId).ToJson(),
                nameof(ErpRemovedFromJobEvent), correlationId, user.Username);
        }

        private Guid GetCorrelationId()
        {
            if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                correlationId = Guid.NewGuid();

            return correlationId;
        }
    }
}
