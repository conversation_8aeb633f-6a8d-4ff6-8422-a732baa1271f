﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.CorporateService;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Client
{
    public class InitializeClientGroupMasterFile
    {
        public class Event : IRequest { }

        public class Handler : IRequestHandler<Event>
        {
            private const string ClientGroupTypeIdKey = "ClientGroupTypeId";
            private const int DefaultClientGroupTypeId = 14;
            private readonly JobDataContext _context;
            private readonly ILogger<Handler> _logger;
            private readonly ICorporateServiceClient _corporateServiceClient;
            private readonly IConfiguration _config;

            public Handler(JobDataContext context,
                ILogger<Handler> logger,
                ICorporateServiceClient corporateServiceClient,
                IConfiguration config)
            {
                _context = context;
                _logger = logger;
                _corporateServiceClient = corporateServiceClient;
                _config = config;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Feature {feature} invoked", nameof(InitializeClientGroupMasterFile));

                var clientGroupTypeId = _config.GetValue(ClientGroupTypeIdKey, DefaultClientGroupTypeId);
                var clientGroupMasterFileRecords = (await _corporateServiceClient.GetClientGroupMasterFileRecords(cancellationToken))
                    .Where(cg => cg.IsActive && cg.GroupTypeId == clientGroupTypeId)
                    .ToList();

                foreach (var clientGroupMasterFileRecord in clientGroupMasterFileRecords)
                {
                    var insuranceClient = await _context.InsuranceClients.FirstOrDefaultAsync(ic => ic.InsuranceNumber == clientGroupMasterFileRecord.MemberId, cancellationToken);

                    if (insuranceClient == null)
                    {
                        insuranceClient = new InsuranceClient
                        {
                            Id = Guid.NewGuid(),
                            Name = "Placeholder Corporate Insurance",
                            InsuranceNumber = clientGroupMasterFileRecord.MemberId,
                            ParentInsuranceNumber = clientGroupMasterFileRecord.ParentId,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow
                        };

                        await _context.InsuranceClients.AddAsync(insuranceClient, cancellationToken);
                    }
                    else
                    {
                        insuranceClient.ParentInsuranceNumber = clientGroupMasterFileRecord.ParentId;
                    }
                }

                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{Feature} completed - processed {count} records", nameof(InitializeClientGroupMasterFile), clientGroupMasterFileRecords?.Count());

                return Unit.Value;
            }
        }
    }
}
