﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JournalNoteUpdated
    {
        public class Event : JournalNoteUpdatedEvent, IRequest
        {
            public Event(JournalNoteDto journalNote, Guid correlationId) : base(journalNote, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JournalNoteCreated> _logger;

            public Handler(JobDataContext db, ILogger<JournalNoteCreated> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with request: {@request}", request);

                var noteUpdatedDto = request.JournalNote;
                var journalNote = await _db.JournalNote.FirstOrDefaultAsync(x => x.Id == noteUpdatedDto.Id, cancellationToken);

                if (journalNote == null)
                {
                    _logger.LogInformation("JournalNote not found: {noteUpdatedDto.Id}, attempting to add a new one", noteUpdatedDto.Id);
                    await _db.JournalNote.AddAsync(Map(noteUpdatedDto), cancellationToken);
                }
                else
                {
                    _logger.LogInformation("JournalNote found: {noteUpdatedDto.Id}, attempting to update it", noteUpdatedDto.Id);
                    journalNote.Note = noteUpdatedDto.Message;
                    journalNote.ActionDate = noteUpdatedDto.ActionDate;
                    journalNote.Subject = noteUpdatedDto.Subject;
                    journalNote.VisibilityId = noteUpdatedDto.VisibilityId;
                    if (noteUpdatedDto.CategoryId.HasValue) journalNote.CategoryId = noteUpdatedDto.CategoryId.Value;
                    journalNote.TypeId = noteUpdatedDto.TypeId;
                    if (!string.IsNullOrEmpty(noteUpdatedDto.Author)) journalNote.Author = noteUpdatedDto.Author;
                    journalNote.ModifiedDate = noteUpdatedDto.UpdatedDate;
                }

                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private JournalNote Map(JournalNoteUpdatedEvent.JournalNoteDto journalNote) =>
                new JournalNote
                {
                    Id = journalNote.Id,
                    ActionDate = journalNote.ActionDate,
                    Subject = journalNote.Subject,
                    CreatedDate = DateTime.UtcNow,
                    Note = journalNote.Message,
                    VisibilityId = journalNote.VisibilityId,
                    TypeId = journalNote.TypeId,
                    Author = journalNote.Author,
                    CategoryId = journalNote.CategoryId ?? Guid.Empty,
                    IsDeleted = false,
                    JobId = journalNote.JobId,
                    ModifiedDate = DateTime.UtcNow
                };
        }
    }
}
