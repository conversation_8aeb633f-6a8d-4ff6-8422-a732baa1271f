﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyMediaMetadata
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobVisitResult { get; set; }
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult AreaResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> MediaIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyMediaMetadata>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyMediaMetadata> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyMediaMetadata> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(MediaMetadata));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.MediaIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var mediaTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedMediaIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(mediaTargetIds, GetMediaIdsAsync, cancellationToken);
                return await ProcessEntitiesAsync<MediaMetadata, ResaleMediaMetadata>(
                    request.ResaleId,
                    media =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(media.JobId))
                            failedDependencies.Add((nameof(Job), media.JobId));
                        if (media.JobAreaId.HasValue && request.AreaResult.FailedEntities.Contains(media.JobAreaId.Value))
                            failedDependencies.Add((nameof(JobArea), media.JobAreaId.Value));
                        if (media.JobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(media.JobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), media.JobVisitId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedMediaIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.MediaMetadata.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<MediaMetadata>> GetSourceEntitiesAsync(List<Guid> mediaMetaDataIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var mediaMetaData = await _context.MediaMetadata
                    .Where(jv => mediaMetaDataIds.Contains(jv.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", mediaMetaData.Count);
                return mediaMetaData;
            }

            private async Task<List<Guid>> GetMediaIdsAsync(List<Guid?> mediaTargetIds, CancellationToken cancellationToken)
            {
                return await _context.MediaMetadata
                    .Where(x => mediaTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
