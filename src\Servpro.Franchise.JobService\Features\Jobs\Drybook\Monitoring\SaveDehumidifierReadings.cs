﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models.Drybook;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class SaveDehumidifierReadings
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
            public IEnumerable<DehumidifierReadingDto> DehumidifierReadings { get; set; }

            public class DehumidifierReadingDto
            {
                public Guid ZoneId { get; set; }
                public Guid EquipmentPlacementId { get; set; }
                public int Hours { get; set; }
                public decimal? Temperature { get; set; }
                public decimal? RelativeHumidity { get; set; }
            }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.JobVisitId).NotEmpty();
                RuleForEach(x => x.DehumidifierReadings)
                    .Must(y => y.ZoneId != Guid.Empty)
                    .Must(y => y.EquipmentPlacementId != Guid.Empty);
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILogger<SaveDehumidifierReadings> _logger;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                  ILogger<SaveDehumidifierReadings> logger)
            {
                _context = context;
                _userInfo = userInfo;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Begin handler with: {@request}", request);

                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.Zones)
                        .ThenInclude(y => y.EquipmentPlacementReadings)
                    .Include(x => x.Zones)
                        .ThenInclude(y => y.JobAreas)
                            .ThenInclude(z => z.EquipmentPlacements)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var visit = job.JobVisits
                    .FirstOrDefault(x => x.Id == request.JobVisitId);

                if (visit is null && request.JobVisitId == Guid.Empty)
                    throw new ResourceNotFoundException($"Visit not found (Id: {request.JobVisitId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                foreach (var dehumidifierReading in request.DehumidifierReadings) 
                {
                    var zone = job.Zones.FirstOrDefault(x => x.Id == dehumidifierReading.ZoneId);

                    if (zone is null) 
                    {
                        _logger.LogWarning(LoggingEvents.GetItemsNotFound, "BadRequest: Zone not found.");
                        continue;
                    }


                    var equipmentPlacement = zone.JobAreas
                        .SelectMany(x => x.EquipmentPlacements)
                        .Any(x => x.Id == dehumidifierReading.EquipmentPlacementId);

                    if (!equipmentPlacement) 
                    {
                        _logger.LogWarning(LoggingEvents.GetItemsNotFound, "BadRequest: Equipment Placement not found."); 
                        continue;
                    }
                    
                    var reading = zone.EquipmentPlacementReadings.FirstOrDefault(x => x.JobVisitId == request.JobVisitId 
                        && x.EquipmentPlacementId == dehumidifierReading.EquipmentPlacementId);

                    if (reading is null)
                    {
                        zone.EquipmentPlacementReadings.Add(new EquipmentPlacementReading
                        {
                            CreatedBy = userInfo.Username,
                            CreatedDate = DateTime.UtcNow,
                            EquipmentPlacementId = dehumidifierReading.EquipmentPlacementId,
                            JobVisitId = request.JobVisitId,
                            ZoneId = dehumidifierReading.ZoneId,
                            RelativeHumidity = dehumidifierReading.RelativeHumidity,
                            Temperature = dehumidifierReading.Temperature,
                            HourCount = dehumidifierReading.Hours,
                        });
                    }
                    else 
                    {
                        reading.ModifiedBy = userInfo.Name;
                        reading.ModifiedDate = DateTime.UtcNow;
                        reading.RelativeHumidity = dehumidifierReading.RelativeHumidity;
                        reading.Temperature = dehumidifierReading.Temperature;
                        reading.HourCount = dehumidifierReading.Hours;
                    }
                }

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogDebug("Handler Completed Successfully");

                return Unit.Value;
            }
        }
        #endregion
    }
}
