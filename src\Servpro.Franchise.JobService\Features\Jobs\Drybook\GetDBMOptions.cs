﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetDBMOptions
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public bool IsDryBookMobileAddEquipmentOptedIn { get; set; }
            public bool RequireInitialMaterialPhotos { get; set; }
            public bool RequireDailyMaterialPhotos { get; set; }
            public bool RequireFinalMaterialPhotos { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(JobReadOnlyDataContext context, 
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();

                var jobConfigExt = await _context.JobConfigExtensions
                    .FirstOrDefaultAsync(x => x.JobId == request.JobId, cancellationToken);

                return Map(jobConfigExt, userInfo.FranchiseSetId.Value);
            }

            private static Dto Map(JobConfigExtension jobConfExt, Guid franchiseSetId)
            {
                var dto = new Dto
                {
                    Id = franchiseSetId,
                    RequireDailyMaterialPhotos = jobConfExt?.RequireDailyMaterialPhotos ?? false,
                    RequireFinalMaterialPhotos = jobConfExt?.RequireFinalMaterialPhotos ?? false,
                    RequireInitialMaterialPhotos = jobConfExt?.RequireInitialMaterialPhotos ?? false
                };

                return dto;
            }
        }
    }
}