﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Sketches
{
    public class DeleteSketch
    {
        public class Command : IRequest<bool>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid Id { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.Id).NotEmpty();
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, bool>
        {
            private readonly JobDataContext _db;
            private readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(
                JobDataContext db,
                IUserInfoAccessor userInfoAccessor)
            {
                _db = db;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<bool> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _db.Jobs
                     .Include(j => j.JobLocks)
                     .FirstOrDefaultAsync(q => q.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var jobSketch = await _db.JobSketch
                    .Include(j => j.MediaMetadata)
                    .Where(x => x.JobId == request.JobId &&
                             x.MediaMetadata.FranchiseSetId == request.FranchiseSetId &&
                             x.MediaMetadata.MediaTypeId == MediaTypes.Sketch &&
                             request.Id == x.Id &&
                             !x.MediaMetadata.IsDeleted)
                    .FirstOrDefaultAsync(cancellationToken);

                if(jobSketch != null)
                {
                    jobSketch.MediaMetadata.IsDeleted = true;
                }

                await _db.SaveChangesAsync(cancellationToken);

                return true;
            }
        }
    }
}
