﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class JobDispatchStatusTypes
    {
        public static readonly Guid Pending = new Guid("46487273-5E7A-4008-868C-58E03071BF7C");
        public static readonly Guid Dispatched = new Guid("49AB9FD3-5EFE-4EAF-BEBF-E1C4F4BAFEF8");
        public static readonly Guid Reassigned = new Guid("A2705D3B-8699-490F-8E47-FBC7B913E31B");
        public static readonly Guid Submitted = new Guid("5A7AB4FE-9C4A-47CF-9D6C-461039E44DA9");
        public static readonly Guid SubmissionRequired = new Guid("2CC308D1-5BA8-4C02-9274-7FD9F7D272B1");
        public static readonly Guid PendingHold = new Guid("D4412687-F9DA-4AB3-864B-14C73F1E1BAF");
        public static readonly Guid Cancel = new Guid("32C860ED-A1D0-4B63-97EF-2E4999A1969D");
    }
}
