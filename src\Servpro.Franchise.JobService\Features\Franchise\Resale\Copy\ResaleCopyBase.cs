﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyBase<T> where T : class
    {
        private readonly ILogger<T> _logger;
        private readonly IMapper _mapper;
        private readonly int _thresholdToSaveItems = 1000;

        public ResaleCopyBase(
            ILogger<T> logger,
            IMapper mapper)
        {
            _logger = logger;
            _mapper = mapper;
        }

        public async Task<ProcessEntityResult> ProcessEntitiesAsync<J, K>(
            Guid resaleId,
            Func<J, List<(string Name, Guid Id)>> failedDependencies,
            HashSet<Guid> previouslyCopiedEntityIds,
            IEnumerable<J> sourceEntities,
            Dictionary<string, object> mappingOptions,
            Action<J> sourceEntityAction,
            Action<K> targetEntityAction,
            JobDataContext context,
            Action<K, JobDataContext, CancellationToken> trackChangesAsync,
            CancellationToken cancellationToken) where J : Entity<Guid>
        {
            var sourceEntitiesToCopy = sourceEntities.DistinctBy(x => x.Id).ToHashSet();
            var totalCount = sourceEntitiesToCopy.Count;
            _logger.LogTrace("({copiedCount}/{totalCount}) entities were previously copied.", previouslyCopiedEntityIds.Count, totalCount);
            var errors = new List<ErrorInfo>();
            var failedEntities = new HashSet<Guid>();
            var entityName = typeof(J).Name;
            var numOfItemsToSave = 0;
            var targetIdsToSourceIdsMap = new Dictionary<Guid, Guid>();
            foreach (var sourceEntity in sourceEntitiesToCopy)
            {
                try
                {
                    var targetId = GuidTransformHelpers.TransformToManipulatedGuid(sourceEntity.Id, resaleId);
                    using var itemScope = _logger.BeginScope("{@entity}",
                        new { SourceId = sourceEntity.Id, TargetId = targetId, type = entityName });
                    _logger.LogInformation("Processing item.");
                    if (targetId.HasValue && previouslyCopiedEntityIds.Contains(targetId.Value))
                    {
                        _logger.LogWarning("Item has already been copied during a previous resale attempt.");
                        continue;
                    }

                    // If dependency function was supplied, and has a failed dependency
                    if (failedDependencies != null)
                    {
                        var dependencyErrors = failedDependencies(sourceEntity);
                        if (dependencyErrors.Any())
                        {
                            foreach (var error in dependencyErrors)
                            {
                                _logger.LogWarning("Unable to copy entity due to failed dependency, name: {dependencyName} id: {dependencyId}",
                                    error.Name, error.Id);
                                errors.Add(
                                new ErrorInfo(
                                    sourceEntity.Id.ToString(),
                                    entityName,
                                    $"Unable to copy entity due to failed dependency, name: {error.Name}, id: {error.Id}",
                                    string.Empty));
                            }
                            failedEntities.Add(sourceEntity.Id);
                            continue;
                        }
                    }
                    var targetEntity = _mapper.Map<K>(
                        sourceEntity,
                        opt => opt.Items.AddMany(mappingOptions));

                    if (sourceEntityAction != null)
                    {
                        sourceEntityAction(sourceEntity);
                    }

                    if (targetEntityAction != null)
                    {
                        targetEntityAction(targetEntity);
                    }

                    if(trackChangesAsync != null)
                    {
                        trackChangesAsync.Invoke(targetEntity, context, cancellationToken);
                        numOfItemsToSave++;

                        if (!targetIdsToSourceIdsMap.ContainsKey(targetId.Value))
                            targetIdsToSourceIdsMap.Add(targetId.Value, sourceEntity.Id);

                        _logger.LogInformation("Item updated locally and being tracked to be saved.");
                        //catch each set of 1000 items to save and save them, any remainder is delt with after the loop
                        if (numOfItemsToSave >= _thresholdToSaveItems)
                        {
                            await SaveAndHandleAnyErrorsAsync<J>(context, failedEntities, errors, targetIdsToSourceIdsMap, entityName, cancellationToken);
                            numOfItemsToSave = 0;
                            targetIdsToSourceIdsMap.Clear();
                        }
                    }
                }
                catch (Exception ex) when (ex.GetType() != typeof(OperationCanceledException))
                {
                    failedEntities.Add(sourceEntity.Id);
                    errors.Add(new ErrorInfo(
                        sourceEntity.Id.ToString(),
                        entityName,
                        ex.Message, ex.StackTrace));
                    _logger.LogError(ex, "Unknown error processing item.");
                }
            }

            //Save the remainder of the items after the loop is completed
            if (numOfItemsToSave > 0)
            {
                await SaveAndHandleAnyErrorsAsync<J>(context, failedEntities, errors, targetIdsToSourceIdsMap, entityName, cancellationToken);
                targetIdsToSourceIdsMap.Clear();
            }

            return new ProcessEntityResult(failedEntities, errors, entityName);
        }

        private async Task SaveAndHandleAnyErrorsAsync<J>(JobDataContext context, 
            HashSet<Guid> failedEntities, 
            List<ErrorInfo> errors, 
            Dictionary<Guid, Guid> targetIdsToSourceIdsMap, 
            string entityName, 
            CancellationToken cancellationToken)
            where J : Entity<Guid>
        {
            try
            {
                await context.SaveChangesIndependentlyAsync(cancellationToken);
            }
            catch (DbUpdateException dbuex)
            {
                HandleDatabaseErrors<J>(context, failedEntities, errors, targetIdsToSourceIdsMap, dbuex, entityName);
            }
        }

        private void HandleDatabaseErrors<J>(JobDataContext context, 
            HashSet<Guid> failedEntities, 
            List<ErrorInfo> errors,
            Dictionary<Guid, Guid> targetIdsToSourceIdsMap,
            DbUpdateException ex,
            string entityName)
            where J : Entity<Guid>
        {
            foreach (var failedEntry in ex.Entries)
            {
                var failedEntity = failedEntry.Entity as J;

                if(failedEntity != null)
                    context.Remove(failedEntity);
                else
                {
                    if (failedEntity == null)
                    {
                        if (failedEntry == null)
                        {
                            _logger.LogWarning("Failed Entry was null, causing the FailedEntity to be null, skipping processing.");
                            return;
                        }
                        _logger.LogWarning("Failed Entity was null, although FailedEntry was not, this assumes a cast error. Entry: {@entry}", failedEntry);
                        return;
                    }
                }
                    
                var sourceId = targetIdsToSourceIdsMap.GetValueOrDefault(failedEntity.Id);
                failedEntities.Add(sourceId);
                errors.Add(new ErrorInfo(
                    sourceId.ToString(),
                    entityName,
                    ex.InnerException?.Message ?? ex.Message, 
                    ex.StackTrace));
                using var errorScope = _logger.BeginScope("{errorEntity}", new { sourceId, targetId = failedEntity.Id });
                _logger.LogError(ex, "Database Error occurred when trying to save items.");
            }
        }
    }
}
