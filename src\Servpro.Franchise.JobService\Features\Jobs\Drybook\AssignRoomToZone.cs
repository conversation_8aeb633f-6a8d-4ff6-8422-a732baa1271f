﻿#nullable disable
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.RoomAssignedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class AssignRoomToZone
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
            public ICollection<RoomDto> Rooms { get; set; }

            public Command(Guid jobId, Guid zoneId, ICollection<RoomDto> rooms)
            {
                JobId = jobId;
                ZoneId = zoneId;
                Rooms = rooms;
            }
        }

        public class RoomDto
        {
            public Guid RoomId { get; set; }
            public Guid? ZoneId { get; set; }
            public string RoomName { get; set; }
            public string ZoneName { get; set; }
            public DateTime BeginDate { get; set; }
            public Guid BeginJobVisitId { get; set; }
            public bool IsSelected { get; set; }
            public bool IsCurrentRow { get; set; }
            public bool HasMissingFlooring { get; set; }
            public bool HasMissingDimensions { get; set; }
            public bool HasEquipmentPlacements { get; set; }
            public int EquipmentPlacementCount { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.ZoneId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo)
            {
                _context = context;
                _userInfo = userInfo;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                    .Include(x => x.Tasks)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                var jobAreaIds = job.JobAreas.Select(x => x.Id).ToList();

                var equipmentPlacements = await _context.EquipmentPlacements
                    .Include(x => x.EquipmentPlacementReadings)
                    .Where(x => jobAreaIds.Contains(x.JobAreaId))
                    .ToListAsync(cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.Zones = await _context.Zones.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var userInfo = _userInfo.GetUserInfo();

                var targetZone = job.Zones.Select(x => x.Id == request.ZoneId);
                if (targetZone is null)
                    throw new ResourceNotFoundException($"Zone not found: {request.ZoneId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                //Unconfirms the zone
                var task = job.Tasks.FirstOrDefault(t => t.TaskTypeId == TaskTypes.ZoneNotConfirmed
                     && t.ZoneId == request.ZoneId);

                if (task != null)
                {
                    task.ModifiedDate = DateTime.UtcNow;
                    task.ModifiedBy = userInfo.Username;
                    task.PercentComplete = 0;
                    task.TaskStatusId = TaskStatuses.Active;
                }
                Map(job, request.ZoneId, userInfo.Username, request.Rooms, equipmentPlacements);
                var roomAssignedEvents = GenerateRoomAssignedEvent(Guid.NewGuid(), userInfo);
                await _context.OutboxMessages.AddRangeAsync(roomAssignedEvents, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private IEnumerable<OutboxMessage> GenerateRoomAssignedEvent(
                Guid correlationId,
                UserInfo userInfo)
            {
                var jobAreaCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobArea
                        && x.State == EntityState.Modified)
                    .Select(x => x.Entity as JobArea)
                    .Select(x => new RoomAssignedEvent(MapRoomAssignedDto(x), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(RoomAssignedEvent), correlationId, userInfo.Username));

                return jobAreaCreatedEvents;
            }

            private RoomAssignedDto MapRoomAssignedDto(JobArea jobArea) =>
                new RoomAssignedDto()
                {
                    JobAreaId = jobArea.Id,
                    ZoneId = jobArea.ZoneId.Value,
                    BeginJobVisitId = jobArea.BeginJobVisitId,
                    EndJobVisitId = jobArea.EndJobVisitId,
                    IsUsedInValidation = jobArea.IsUsedInValidation
                };

            private void Map(Job job,
                Guid zoneId,
                string username,
                ICollection<RoomDto> rooms,
                ICollection<EquipmentPlacement> equipmentPlacements)
            {
                if (job.JobAreas != null && job.JobVisits != null)
                {
                    foreach (var room in rooms)
                    {
                        var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == room.RoomId);
                        var jobVisit = job.JobVisits.FirstOrDefault(x => x.Id == room.BeginJobVisitId);

                        if (jobArea != null && jobVisit != null && jobArea.ZoneId == null && jobArea.EndJobVisitId == null)
                        {
                            jobArea.ZoneId = zoneId;
                            jobArea.BeginJobVisitId = room.BeginJobVisitId;
                            jobArea.ModifiedBy = username;
                            jobArea.ModifiedDate = DateTime.UtcNow;

                            var jobAreaEquipmentPlacements = equipmentPlacements
                                .Where(x => x.JobAreaId == jobArea.Id)
                                .ToList();

                            foreach (var ep in jobAreaEquipmentPlacements)
                            {
                                ep.BeginDate = room.BeginDate;

                                foreach (var epr in ep.EquipmentPlacementReadings)
                                {
                                    epr.ZoneId = zoneId;
                                }
                            }
                        }
                    }
                }
            }
        }

    }
}
