﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GetInvoice
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid id)
            {
                JobId = id;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string InvoiceNumber { get; set; }
            public string Description { get; set; }
            public string Source { get; set; }
            public DateTime Date { get; set; }
            public double Amount { get; set; }
            public Guid MediaMetadataId { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext jobDataContext)
            {
                _db = jobDataContext;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var invoiceList = await _db.MediaMetadata
                    .Include(mm => mm.JobInvoice)
                    .Where(mm => mm.ArtifactTypeId == ArtifactTypes.Invoice 
                                 && mm.JobId == request.JobId && !mm.IsDeleted)
                    .Select(mm => mm.JobInvoice).ToListAsync(cancellationToken);
                
                return invoiceList.Any() ? Map(invoiceList) : new List<Dto>();
            }

            private static List<Dto> Map(List<JobInvoice> invoiceList)
            {
                return invoiceList.Select(invoice => new Dto()
                {
                    MediaMetadataId = invoice.MediaMetadataId,
                    InvoiceNumber = invoice.InvoiceNumber,
                    Description = invoice.Description,
                    Source = invoice.Source,
                    Date = invoice.Date,
                    Amount = invoice.Amount,
                    Id = invoice.Id
                }).ToList();
            }
        }
    }
}
