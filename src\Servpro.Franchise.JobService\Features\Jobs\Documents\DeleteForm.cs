﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;

using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class DeleteForm
    {
        public class Command : IRequest<bool>
        {
            public Guid JobFormId { get; set; }

            public Command(Guid jobFormId)
            {
                JobFormId = jobFormId;
            }
        }

        public class Handler : IRequestHandler<Command, bool>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IDocuSignUtility _docuSignUtility;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext db, ILogger<Handler> logger, IDocuSignUtility docuSignUtility, ISessionIdAccessor sessionIdAccessor)
            {
                _db = db;
                _logger = logger;
                _docuSignUtility = docuSignUtility;
                _sessionIdAccessor = sessionIdAccessor;
            }
            public async Task<bool> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                _logger.BeginScope("{jobFormId}{correlationId}", request.JobFormId, correlationId);
                _logger.LogInformation("Getting form to delete", request.JobFormId);
                var form = await _db.MediaMetadata
                    .FirstOrDefaultAsync(x => x.Id == request.JobFormId, cancellationToken);

                if (form is null)
                    throw new ResourceNotFoundException("Form not found");

                form.IsDeleted = true;
                form.ModifiedDate = DateTime.UtcNow;
                await RaiseDocumentDeletedEventAsync(form, correlationId, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);
                
                await _docuSignUtility.DeleteFormIfExistsInDocuSignAsync(form.JobId, form.Id, form.FranchiseSetId, cancellationToken);

                return true;
            }

            private async Task RaiseDocumentDeletedEventAsync(MediaMetadata form, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising DocumentDeletedEvent for form: {formId}", form.Id);
                var documentDto = new DocumentDeletedEvent.DocumentDto(form.Id, form.JobId, form.FranchiseSetId, form.ModifiedDate ?? DateTime.UtcNow, form.FormTemplateId);

                var documentsDeleted = new List<DocumentDeletedEvent.DocumentDto> { documentDto };
                var documentDeletedEvent = new DocumentDeletedEvent(documentsDeleted, correlationId);
                var outboxMessage = new OutboxMessage(documentDeletedEvent.ToJson(), nameof(DocumentDeletedEvent), correlationId, nameof(DeleteForm));
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }
        }
    }
}
