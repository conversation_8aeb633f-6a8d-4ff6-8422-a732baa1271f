﻿using MediatR;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Features.Leads.MobileSync;
using System.Text.Json.Serialization;

namespace Servpro.Franchise.JobService.Features.Artifacts
{
    public class MobileLeadArtifactSync
    {
        public class Command : IRequest<ResponseDto>
        {
            public Guid Id { get; set; }
            public string DeviceId { get; set; }
            [JsonIgnore]
            public UserInfo User { get; set; }
            public Guid FranchiseSetId { get; set; }
            public IEnumerable<MediaDto> Documents { get; set; } = Enumerable.Empty<MediaDto>();
            public IEnumerable<MediaDto> Photos { get; set; } = Enumerable.Empty<MediaDto>();
            public IEnumerable<MediaDto> Sketches { get; set; } = Enumerable.Empty<MediaDto>();
        }
        public class ResponseDto
        {
            public bool UpdatedSuccessfully { get; set; }
            public string ExceptionMessage { get; set; }
        }
        public class MediaDto
        {
            public Guid Id { get; set; }
            public SyncWrapper<Guid> ArtifactTypeId { get; set; }
            public SyncWrapper<string> Name { get; set; }
            public SyncWrapper<string> Description { get; set; }
            public bool IsDeleted { get; set; }
            public SyncWrapper<string> BucketName { get; set; }
            public SyncWrapper<bool> IsForUpload { get; set; } = false;
            public SyncWrapper<bool> UploadedSuccessfully { get; set; }
            public SyncWrapper<DateTime?> ArtifactDate { get; set; }
            public SyncWrapper<string> Comment { get; set; }
            public SyncWrapper<Guid?> JobAreaId { get; set; }
            public SyncWrapper<Guid?> JobVisitId { get; set; }
            public SyncWrapper<Guid?> ZoneId { get; set; }
            public SyncWrapper<Guid?> JobAreaMaterialId { get; set; }
            public SyncWrapper<string> MediaPath { get; set; }
        }

        public class Handler : IRequestHandler<Command, ResponseDto>
        {
            private readonly JobDataContext _db;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private readonly ILogger<MobileLeadArtifactSync> _logger;
            public Handler(
              JobDataContext context,
              ILogger<MobileLeadArtifactSync> logger, IUserInfoAccessor userInfoAccessor,
              ILookupServiceClient lookupServiceClient, ISessionIdAccessor sessionIdAccessor,
              IMediaEventGenerator mediaEventGenerator)
            {
                _db = context;
                _logger = logger;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
                _sessionIdAccessor = sessionIdAccessor;
                _mediaEventGenerator = mediaEventGenerator;
            }

            public async Task<ResponseDto> Handle(Command request, CancellationToken cancellationToken)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                _logger.LogInformation("MobileLeadArtifactSync started. Payload: {@Request}", request);

                try
                {
                    var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                    var userInfo = _userInfoAccessor.GetUserInfo();

                    var mediaMetadata = await Map(request, lookups, userInfo, cancellationToken);

                    if (mediaMetadata == null)
                    {
                        return new ResponseDto
                        {
                            UpdatedSuccessfully = false,
                            ExceptionMessage = "New media is marked as deleted"
                        };
                    }

                    var currentMedia = mediaMetadata.FirstOrDefault();
                    var mediaInfo = await _db.MediaMetadata.FirstOrDefaultAsync(x => x.Id == currentMedia.Id, cancellationToken);

                    if (mediaInfo == null)
                    {
                        _logger.LogDebug("Adding new artifact");
                        await _db.MediaMetadata.AddRangeAsync(mediaMetadata, cancellationToken);
                    }
                    else
                    {
                        _logger.LogDebug("Updating existing artifact");
                        _db.MediaMetadata.UpdateRange(mediaMetadata);
                    }

                    var outBoxMessage = GenerateMediaAddedEvents(correlationId, userInfo, mediaMetadata);
                    await _db.OutboxMessages.AddRangeAsync(outBoxMessage);

                    await SaveChangesWithRetryAsync(request.Id, cancellationToken);

                    return new ResponseDto
                    {
                        UpdatedSuccessfully = true
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError("MobileLeadArtifactSync failed:", ex);
                    return new ResponseDto
                    {
                        UpdatedSuccessfully = false,
                        ExceptionMessage = ex.StackTrace
                    };
                }
            }

            private async Task SaveChangesWithRetryAsync(Guid jobId, CancellationToken cancellationToken)
            {
                const int maxRetries = 3;
                int attempt = 0;
                Exception lastException = null;

                while (attempt < maxRetries)
                {
                    try
                    {
                        attempt++;
                        await _db.SaveChangesAsync(cancellationToken);
                        return;
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        var delay = TimeSpan.FromSeconds(Math.Pow(3, attempt));

                        _logger.LogWarning(ex,
                            "JobId {jobId} - SaveChanges attempt {attempt} failed. Retrying in {delay}s...",
                            jobId, attempt, delay.TotalSeconds);

                        await Task.Delay(delay, cancellationToken);
                    }
                }

                _logger.LogError(lastException,
                    "JobId {jobId} - Failed to save changes after {maxRetries} retries. Last exception: {message}",
                    jobId, maxRetries, lastException?.Message);

                throw lastException;
            }


            private OutboxMessage GenerateMediaAddedEvents(Guid correlationId, UserInfo userInfo, List<MediaMetadata> mediaMetadata)
            {
                return _mediaEventGenerator.GenerateMediaAddedEvent(
                    mediaMetadata, correlationId, userInfo.Username);
            }

            private static bool ValidateGuid(SyncWrapper<Guid?> id)
            {
                return id != null && id.HasValue;
            }

            private static async Task<bool> ValidateMedia(MediaDto media, JobDataContext db, CancellationToken cancellationToken)
            {
                // Check if JobArea exists
                if (ValidateGuid(media.JobAreaId))
                    return await db.JobAreas.AnyAsync(x => x.Id == media.JobAreaId.Value, cancellationToken);

                // Check if JobVisit exists
                if (ValidateGuid(media.JobVisitId))
                    return await db.JobVisit.AnyAsync(x => x.Id == media.JobVisitId.Value, cancellationToken);

                // Check if Zone exists
                if (ValidateGuid(media.ZoneId))
                    return await db.Zones.AnyAsync(x => x.Id == media.ZoneId.Value, cancellationToken);

                // Check if JobAreaMaterial exists
                if (ValidateGuid(media.JobAreaMaterialId))
                    return await db.JobAreaMaterials.AnyAsync(x => x.Id == media.JobAreaMaterialId.Value, cancellationToken);

                return true;
            }

            private async Task<List<MediaMetadata>> Map(Command command, GetLookups.Dto lookups, UserInfo userInfo, CancellationToken cancellationToken)
            {
                var mediaMetadata = new List<MediaMetadata>();

                var mediaType = command.Documents.ToList().Count > 0 ? MediaTypes.Document : command.Photos.ToList().Count > 0 ? MediaTypes.Photo
                    : command.Sketches.ToList().Count > 0 ? MediaTypes.Sketch : Guid.Empty;

                if (mediaType.IsNullOrEmpty()) return null;

                var mediaCollection = mediaType == MediaTypes.Document ? command.Documents : new List<MediaDto>();
                mediaCollection = mediaType == MediaTypes.Photo ? command.Photos : mediaCollection;
                mediaCollection = mediaType == MediaTypes.Sketch ? command.Sketches : mediaCollection;

                var artifactTypeRequiredForUploadMap = lookups.ArtifactTypes
                                .ToDictionary(x => x.Id, x => x.Required);

                if (mediaCollection == null) { return mediaMetadata; }

                foreach (var media in mediaCollection)
                {
                    var isValidMedia = await ValidateMedia(media, _db, cancellationToken);

                    if (!isValidMedia) continue;

                    var currentMedia = await _db.MediaMetadata.FirstOrDefaultAsync(c => c.Id == media.Id, cancellationToken);
                    if (currentMedia == null && !media.IsDeleted)
                    {
                        mediaMetadata.Add(new MediaMetadata
                        {
                            Name = media.Name.Value,
                            Description = media.Description.Value != null && media.Description.HasValue ? media.Description.Value : string.Empty,
                            ArtifactTypeId = media.ArtifactTypeId.Value,
                            MediaTypeId = mediaType,
                            Id = media.Id != Guid.Empty ? media.Id : Guid.NewGuid(),
                            IsDeleted = media.IsDeleted,
                            BucketName = media.BucketName.DefaultIfEmpty(),
                            IsForUpload = (artifactTypeRequiredForUploadMap.ContainsKey(media.ArtifactTypeId.Value)
                                           && artifactTypeRequiredForUploadMap[media.ArtifactTypeId.Value]) || media.IsForUpload.Value,
                            JobId = command.Id,
                            FranchiseSetId = command.FranchiseSetId,
                            ArtifactDate = media.ArtifactDate.DefaultIfEmpty(),
                            Comment = media.Comment.DefaultIfEmpty(),
                            JobAreaId = media.JobAreaId.DefaultIfEmpty(),
                            JobVisitId = media.JobVisitId.DefaultIfEmpty(),
                            ZoneId = media.ZoneId.DefaultIfEmpty(),
                            JobAreaMaterialId = media.JobAreaMaterialId.DefaultIfEmpty(),
                            MediaPath = media.MediaPath.DefaultIfEmpty(),
                            CreatedBy = userInfo.Username,
                            UploadedSuccessfully = media.UploadedSuccessfully.DefaultIfEmpty()
                        });
                    }
                    else if (currentMedia != null)
                    {
                        currentMedia.IsDeleted = media.IsDeleted ? media.IsDeleted : currentMedia.IsDeleted;
                        currentMedia.Name = media.Name.Value;
                        currentMedia.Description = media.Description.Value != null && media.Description.HasValue ? media.Description.Value : string.Empty;
                        currentMedia.ArtifactTypeId = media.ArtifactTypeId.Value;
                        currentMedia.MediaTypeId = mediaType;
                        currentMedia.Id = media.Id != Guid.Empty ? media.Id : Guid.NewGuid();
                        currentMedia.IsDeleted = media.IsDeleted;
                        currentMedia.BucketName = media.BucketName.DefaultIfEmpty();
                        currentMedia.IsForUpload = (artifactTypeRequiredForUploadMap.ContainsKey(media.ArtifactTypeId.Value)
                                                    && artifactTypeRequiredForUploadMap[media.ArtifactTypeId.Value]) || media.IsForUpload.Value;
                        currentMedia.JobId = command.Id;
                        currentMedia.FranchiseSetId = command.FranchiseSetId;
                        currentMedia.ArtifactDate = media.ArtifactDate.DefaultIfEmpty();
                        currentMedia.Comment = media.Comment.DefaultIfEmpty();
                        currentMedia.JobAreaId = media.JobAreaId.DefaultIfEmpty();
                        currentMedia.JobVisitId = media.JobVisitId.DefaultIfEmpty();
                        currentMedia.ZoneId = media.ZoneId.DefaultIfEmpty();
                        currentMedia.JobAreaMaterialId = media.JobAreaMaterialId.DefaultIfEmpty();
                        currentMedia.MediaPath = media.MediaPath.DefaultIfEmpty();
                        currentMedia.ModifiedBy = userInfo.Username;
                        currentMedia.ModifiedDate = DateTime.UtcNow;
                        currentMedia.UploadedSuccessfully = media.UploadedSuccessfully.DefaultIfEmpty();
                        mediaMetadata.Add(currentMedia);
                    }
                }

                return mediaMetadata;
            }
        }
    }
}