﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class SetIsForUploadPhotos
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public List<Guid> MediaMetadataIds { get; set; }
            public bool IsForUpload { get; set; }
        }
        public class Validator : AbstractValidator<SetIsForUploadPhotos.Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public bool UpdatedSuccessfully { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _db;

            public Handler(JobDataContext db)
            {
                _db = db;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                var jobMedias = await _db.MediaMetadata
                    .Where(q => q.JobId  == request.JobId 
                    && q.MediaTypeId == MediaTypes.Photo && !q.IsDeleted).AsQueryable().ToListAsync(cancellationToken);

                var medias = jobMedias.Where(x => request.MediaMetadataIds.Contains(x.Id));

                foreach (var item in medias)
                {
                    item.IsForUpload = request.IsForUpload;
                }

                await _db.SaveChangesAsync(cancellationToken);

                return new Dto
                {
                    UpdatedSuccessfully = true
                };
            }
        }


    }
}
