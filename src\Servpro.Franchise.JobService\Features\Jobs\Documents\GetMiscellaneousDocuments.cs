﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using AutoMapper;
using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Invoices;
using Servpro.Franchise.JobService.Infrastructure.ClientRequirementsService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;
using ArtifactTypes = Servpro.Franchise.JobService.Common.Constants.ArtifactTypes;
using MediaTypes = Servpro.Franchise.JobService.Common.MediaTypes;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GetMiscellaneousDocuments
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Query(Guid id, Guid franchiseSetId)
            {
                JobId = id;
                FranchiseSetId = franchiseSetId;
            }

            public Guid JobId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public DateTime? ArtifactDate { get; set; }
            public string Comments { get; set; }
            public string Description { get; set; }
            public string Name { get; set; }
            public Guid MediaTypeId { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public string MediaPath { get; set; }
            public DateTime InsertionDate { get; set; }
            public DateTime? LastModified { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobVisitId { get; set; }
            public DateTime? VisitDate { get; set; }
            public string DocumentCategory { get; set; }
            public string DocumentType { get; set; }
            public bool IsForUpload { get; set; }
            public bool IsRequired { get; set; }
            public JobInvoiceDto JobInvoice { get; set; }
            public bool IsXactUploaded { get; set; }
        }

        public class JobInvoiceDto
        {
            public Guid Id { get; set; }
            public string InvoiceNumber { get; set; }
            public string Description { get; set; }
            public string Source { get; set; }
            public DateTime Date { get; set; }
            public double Amount { get; set; }
        }


        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private readonly IClientRequirementsService _clientRequirementsService;
            private readonly IInvoiceService _invoiceService;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<Handler> _logger;
            private const string SelfPay = "SELF PAY";
            private const string DocCategoryMiscRequired = "Required: Miscellaneous Documents";
            private const string DocCategoryMiscOther = "Other: Miscellaneous Documents";
            public const string EstimateMediaFileName = "Non_ScanERXL_Estimate.pdf";
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";
            private const int subContractJobRule = 73;
            private const int invoiceJobRule = 90;
            public Handler(JobDataContext db, IConfiguration config, IAmazonS3 clientS3, IClientRequirementsService clientRequirementsService, IInvoiceService invoiceService, ILookupServiceClient lookupServiceClient, ILogger<Handler> logger)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
                _clientRequirementsService = clientRequirementsService;
                _invoiceService = invoiceService;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            public async Task<ICollection<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("Query: {@request}", request);

                _logger.LogInformation("Getting miscellaneous documents");
                _logger.LogDebug("Querying misc docs for: {@request}", request);
                var result = new List<Dto>();
                try
                {
                    var job = await GetJobAsync(request.JobId, request.FranchiseSetId, cancellationToken);
                    
                    if (job == null)
                    {
                        _logger.LogWarning("Job not found with Id: {jobId}", request.JobId);
                        return result;
                    }

                    _logger.LogDebug("Job data found: {@job}", job);
                    var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                    result = (await GetMiscellaneousDocuments(job, lookups, cancellationToken)).ToList();
                }
                catch (Exception exception)
                {
                    _logger.LogError(exception, "Error while processing GetMiscellaneousDocuments request");
                }
                return result;
            }


            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.Include(j => j.JournalNotes).FirstOrDefaultAsync(j => j.Id == jobId && j.FranchiseSetId == franchiseSetId, cancellationToken);
                job.JobTriStateAnswers = await _db.JobTriStateAnswers.Where(jsa => jsa.JobId == jobId).ToListAsync(cancellationToken);
                job.MediaMetadata = await _db.MediaMetadata.Include(mm => mm.JobInvoice).Where(mm => mm.JobId == jobId).ToListAsync(cancellationToken);
                job.JobContacts = await _db.JobContactMap.Include(jc => jc.Contact).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);

                job.JobAreas = await _db.JobAreas.Include(x => x.Room)
                                                 .ThenInclude(x => x.RoomFlooringTypesAffected).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);

                var jobAreasWithMaterialsAndVisits = await _db.JobAreas.Include(x => x.JobAreaMaterials)
                                                              .Include(x => x.BeginJobVisit)
                                                              .Where(jc => jc.JobId == jobId).ToDictionaryAsync(x => x.Id, cancellationToken);

                foreach (var jobArea in job.JobAreas)
                {
                    jobArea.JobAreaMaterials = jobAreasWithMaterialsAndVisits[jobArea.Id].JobAreaMaterials;
                    jobArea.BeginJobVisit = jobAreasWithMaterialsAndVisits[jobArea.Id].BeginJobVisit;
                }

                return job;
            }

            private string GetPreSignedUrl(MediaMetadata metadata)
            {
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = metadata.BucketName.IsNullOrWhiteSpace() ? _config[S3MediaBucketNameKey] : metadata.BucketName,
                    Key = metadata.MediaPath,
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };

                var preSignedUrlDto = _clientS3.GetPreSignedURL(preSignedUrlRequest);

                return preSignedUrlDto;
            }

            private async Task<ICollection<Dto>> GetMiscellaneousDocuments(Job job, GetLookups.Dto lookups, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("Method: {method}", nameof(GetMiscellaneousDocuments));
                if (job == null) return new List<Dto>();

                _logger.LogInformation("Getting job upload required indicator");
                var isJobUploadRequired = _clientRequirementsService.GetJobUploadRequiredIndicator(job);
                var documentModels = new List<Dto>();

                const bool isCorpJob = false; // Corp Job does not exist in Job Service yet.

                _logger.LogInformation("Getting job requirements");
                var requirements = await _clientRequirementsService.GetJobRequirementsAsync(job, cancellationToken);

                var isSubcontractInvoiceRequired = false;
                _logger.LogInformation("Getting FilteredRequirements");
                var subContractRequirement = _clientRequirementsService.FilteredRequirements(requirements, subContractJobRule);
                if (subContractRequirement?.Result.MiscellaneousRequirements?.FirstOrDefault(x => x.ValidationEffect.ToLower() == "require") != null)
                {
                    isSubcontractInvoiceRequired = true;
                }

                _logger.LogInformation("Getting Invoice requirements");

                var isInvoiceRequired = false;
                var invoiceRequirement = _clientRequirementsService.FilteredRequirements(requirements, invoiceJobRule);
                if (invoiceRequirement?.Result.MiscellaneousRequirements?.FirstOrDefault(x => x.ValidationStatus == 2) != null)
                {
                    isInvoiceRequired = true;
                }

                var isSelfPay = false;
                var isUnknown = false;
                _logger.LogInformation("Determining insurance carrier");

                if (job.InsuranceCarrierId != null)
                {
                    var insuranceInfo = await _db.InsuranceClients.FirstOrDefaultAsync(x => x.Id == job.InsuranceCarrierId, cancellationToken);
                    if (insuranceInfo != null)
                        isSelfPay = insuranceInfo.Name.ToUpper().Equals(SelfPay);
                }
                else
                {
                    isUnknown = true;
                }

                _logger.LogInformation("Loading document types");
                var documentTypes = GetDocumentTypes(lookups);

                var allDocumentTypes = documentTypes.Select(i => i.Value).ToList();

                //TODO
                _logger.LogInformation("Getting media metadata documents");
                var miscDocuments = job.MediaMetadata
                    .Where(x => x.MediaTypeId == MediaTypes.Document && !x.FormTemplateId.HasValue && !x.IsDeleted && x.MediaPath != "ProjectRevenuePlaceholder")
                    .ToList();

                // Calculation for "Drying Report" Artifact Type.
                var reqDryingReportJobTypes = new List<string> { "Sewage/Flood", "Water" };

                var lossType = lookups.LossTypes.FirstOrDefault(x => x.Id == job.LossTypeId);
                var isReqDryingReportJobType = lossType != null && reqDryingReportJobTypes.Contains(lossType.Name);

                var isXactimateEstimateRequired = true;

                _logger.LogInformation("Any Misc. Documents?", miscDocuments?.Any());

                if (miscDocuments.Any())
                {
                    foreach (var item in miscDocuments)
                    {
                        var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == item.ArtifactTypeId);
                        var documentModel = MapDocumentModel(item, artifactType);
                        if (documentModel == null)
                            continue;

                        if (isReqDryingReportJobType && (item.ArtifactTypeId == ArtifactTypes.DryingWorkbook)
                            || item.ArtifactTypeId == ArtifactTypes.Invoice)
                        {
                            documentModel.DocumentCategory = isInvoiceRequired ? DocCategoryMiscRequired : DocCategoryMiscOther;
                            await SetDocumentIsForUploadInfo(documentModel, cancellationToken);

                            documentModel.IsRequired = isJobUploadRequired;

                            var jobInvoice = await _db.JobInvoices.FirstOrDefaultAsync(x => x.MediaMetadataId == item.Id, cancellationToken);

                            documentModel.JobInvoice = MapJobInvoiceModel(jobInvoice);

                        }
                        else if (item.ArtifactTypeId == ArtifactTypes.XactimateEstimate)
                        {
                            documentModel.DocumentCategory = DocCategoryMiscRequired;

                            await SetDocumentIsForUploadInfo(documentModel, cancellationToken);

                            documentModel.IsRequired = isJobUploadRequired;
                        }
                        else
                        {
                            documentModel.DocumentCategory = DocCategoryMiscOther;
                            documentModel.IsRequired = false;
                        }

                        var artifactTypeName = lookups.ArtifactTypes
                            .Where(x => x.Id == item.ArtifactTypeId)
                            .Select(x => x.Name)
                            .FirstOrDefault();
                        if (artifactTypeName != null)
                        {
                            allDocumentTypes.Remove(artifactTypeName);
                        }
                        documentModels.Add(documentModel);
                    }

                    foreach (var item in allDocumentTypes)
                    {
                        AddEmptyDocumentModel(isCorpJob, isSelfPay, isUnknown, isJobUploadRequired,
                            documentModels, item, isXactimateEstimateRequired,
                            isSubcontractInvoiceRequired, isInvoiceRequired, isReqDryingReportJobType, job.LossTypeId);
                    }
                }
                else
                {
                    foreach (var item in allDocumentTypes)
                    {
                        AddEmptyDocumentModel(isCorpJob, isSelfPay, isUnknown, isJobUploadRequired,
                            documentModels, item, isXactimateEstimateRequired,
                            isSubcontractInvoiceRequired, isInvoiceRequired, isReqDryingReportJobType, job.LossTypeId);
                    }
                }

                _logger.LogInformation("Returning from {method}.", nameof(GetMiscellaneousDocuments));
                return documentModels
                    .OrderByDescending(order => order.DocumentCategory)
                    .ThenBy(fx => fx.InsertionDate)
                    .ToList(); ;
            }

            private Dto MapDocumentModel(MediaMetadata item, ArtifactTypeDto artifactType)
            {
                if (item == null)
                    return null;

                return new Dto()
                {
                    Id = item.Id,
                    JobId = item.JobId,
                    ArtifactDate = item.CreatedDate,
                    Name = item.Name,
                    ArtifactTypeId = item.ArtifactTypeId,
                    InsertionDate = item.CreatedDate,
                    LastModified = item.ModifiedDate,
                    DocumentType = artifactType?.Name,
                    IsForUpload = item.IsForUpload,
                    IsRequired = artifactType?.Required ?? false,
                    MediaPath = GetPreSignedUrl(item),
                    JobInvoice = new JobInvoiceDto(),
                    IsXactUploaded = item.IsUploadedToXact,
                    Comments = item.Comment
                };
            }

#pragma warning disable IDE0060 // Remove unused parameter
            private static JobInvoiceDto MapJobInvoiceModel(JobInvoice invoice)
#pragma warning restore IDE0060 // Remove unused parameter
            {
                if (invoice == null)
                    return null;
                // A future user story will handle invoices
                return new JobInvoiceDto
                {
                    Id = invoice.Id,
                    InvoiceNumber = invoice.InvoiceNumber,
                    Description = invoice.Description,
                    Source = invoice.Source,
                    Date = invoice.Date,
                    Amount = invoice.Amount
                };
            }

            private async Task SetDocumentIsForUploadInfo(Dto documentModel, CancellationToken cancellationToken)
            {
                if (!documentModel.IsForUpload)
                {
                    _logger.LogInformation("SetDocumentIsForUploadInfo {@documentModel}.", documentModel);
                    var mediaMetadata = await _db.MediaMetadata.FirstOrDefaultAsync(x => x.Id == documentModel.Id, cancellationToken);
                    if (mediaMetadata != null && !string.IsNullOrEmpty(mediaMetadata.MediaPath))
                    {
                        mediaMetadata.IsForUpload = true;
                        await _db.SaveChangesAsync(cancellationToken);
                    }
                    else
                    {
                        _logger.LogWarning("MediaMetadata not found for {documentModelId}", documentModel);
                    }

                    if (!string.IsNullOrEmpty(mediaMetadata.MediaPath))
                        documentModel.IsForUpload = true;
                }
            }

            private static void AddEmptyDocumentModel(bool isCorpJob, bool isSelfPay, bool isUnknown, bool JobUploadRequired,
                                                  List<Dto> dtos,
                                                  string artifactType,
                                                  bool isXactimateRequired,
                                                  bool isSubcontractInvoiceRequired,
                                                  bool isInvoiceRequired,
                                                  bool isReqDryingWorkbookJobType,
                                                  Guid lossTypeId)
            {
                switch (artifactType)
                {
                    case "Drying Report":
                        var jobDocDryingReport = new Dto
                        {
                            ArtifactTypeId = ArtifactTypes.DryingReport,
                            DocumentCategory =
                                (isReqDryingWorkbookJobType && JobUploadRequired)
                                    ? @"Required: Miscellaneous Documents"
                                    : @"Other: Miscellaneous Documents",
                            DocumentType = artifactType,
                            Name = string.Empty,
                            IsForUpload = true,
                            IsRequired = true
                        };
                        dtos.Add(jobDocDryingReport);
                        break;

                    case "External Document":
                        var externalDocument = new Dto
                        {
                            ArtifactTypeId = ArtifactTypes.ExternalDocument,
                            DocumentCategory = @"Other: Miscellaneous Documents",
                            DocumentType = artifactType,
                            Name = string.Empty
                        };
                        dtos.Add(externalDocument);
                        break;

                    case "Xactimate Estimate":
                        var xactimateEstimate = new Dto();
                        if (isXactimateRequired)
                        {
                            xactimateEstimate.ArtifactTypeId = ArtifactTypes.XactimateEstimate;
                            xactimateEstimate.DocumentCategory =
                                (lossTypeId == LossTypes.Water || lossTypeId == LossTypes.Sewage) && !isSelfPay && !isUnknown
                                    ? @"Required: Miscellaneous Documents"
                                    : @"Other: Miscellaneous Documents";
                            xactimateEstimate.DocumentType = artifactType;
                            xactimateEstimate.Name = string.Empty;
                            xactimateEstimate.IsForUpload = true;
                            xactimateEstimate.IsRequired = true;
                            dtos.Add(xactimateEstimate);
                        }
                        break;

                    case "Invoice":
                        var jobDocInvoice = new Dto
                        {
                            ArtifactTypeId = ArtifactTypes.Invoice,
                            DocumentCategory =
                               (isInvoiceRequired)
                                    ? @"Required: Miscellaneous Documents"
                                    : @"Other: Miscellaneous Documents",
                            DocumentType = artifactType,
                            Name = string.Empty,
                            IsForUpload = true,
                            IsRequired = !isSelfPay || isCorpJob
                        };
                        dtos.Add(jobDocInvoice);
                        break;

                    case "Subcontract Invoice":
                        var jobDocSubcontractInvoice = new Dto
                        {
                            ArtifactTypeId = ArtifactTypes.SubcontractInvoice,
                            DocumentCategory =
                                (isSubcontractInvoiceRequired)
                                    ? @"Required: Miscellaneous Documents"
                                    : @"Other: Miscellaneous Documents",
                            DocumentType = artifactType,
                            Name = string.Empty,
                            IsForUpload = true,
                            IsRequired = !isSelfPay || isCorpJob
                        };
                        dtos.Add(jobDocSubcontractInvoice);

                        break;
                }
            }

            private static Dictionary<Guid, string> GetDocumentTypes(GetLookups.Dto lookups)
            {
                var documentTypesLookup = new Dictionary<Guid, string>();
                var allowDocumentTypes = new List<string>
                {
                    "External Document",
                    "Drying Report",
                    "Xactimate Estimate",
                    "Subcontract Invoice",
                    "Invoice"
                };

                var documentTypes = lookups.ArtifactTypes.Where(condition => allowDocumentTypes.Contains(condition.Name)).ToList();
                documentTypes.ForEach(item => { documentTypesLookup.Add(item.Id, item.Name); });

                return documentTypesLookup;
            }

            public bool IsCommercialDirect(Job job)
            {
                // Job Program Type does not exist yet.
                //if (job?.OfficeJob?.JobSummary?.JobProgramTypeId != null
                //    && job?.OfficeJob?.JobSummary?.JobProgramTypeId.Value == (int)JobProgramTypeEnum.CommercialDirect)
                //{
                //    return true;
                //}
                return false;
            }

            public bool IsCommercialManaged(Job job)
            {
                // Job Program Type does not exist yet.
                //if (job?.OfficeJob?.JobSummary?.JobProgramTypeId != null
                //    && job?.OfficeJob?.JobSummary?.JobProgramTypeId.Value == (int)JobProgramTypeEnum.CommercialManaged)
                //{
                //    return true;
                //}
                return false;
            }
        }
    }
}