﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones
{
    public class UnconfirmZone
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
            public JournalNoteInfo JournalNote { get; set; }

            public class JournalNoteInfo
            {
                public Guid JournalNoteVisibilityId { get; set; }
                public DateTime ActionDate { get; set; }
                public string Note { get; set; }
            }
        }
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.ZoneId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient)
            {
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
                _context = context;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();

                var job = await _context.Jobs
                    .Include(x => x.Zones)
                        .ThenInclude(x => x.Tasks.Where(t => t.JobId == request.JobId))
                    .FirstOrDefaultAsync(x => x.FranchiseSetId == userInfo.FranchiseSetId.Value
                        && x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var zone = job.Zones.FirstOrDefault(x => x.Id == request.ZoneId);

                if (zone is null)
                    throw new ResourceNotFoundException($"Zone not found: {request.ZoneId}");

                //Clear
                zone.CanValidateDehuCapacity = false;
                zone.RequiredDehuCapacity = 0;
                zone.AchievedDehuCapacity = 0;

                var zoneNotConfirmedTask = zone.Tasks.
                    First(x => x.TaskTypeId == TaskTypes.ZoneNotConfirmed);
                zoneNotConfirmedTask.CompletionDate = null;
                zoneNotConfirmedTask.PercentComplete = 0;
                zoneNotConfirmedTask.ModifiedBy = userInfo.Username;
                zoneNotConfirmedTask.ModifiedDate = DateTime.UtcNow;
                zoneNotConfirmedTask.TaskStatusId = TaskStatuses.Active;

                if (request.JournalNote != null)
                {
                    var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                    var equipmentValidationAlert = MapToTask(request, zone, lookups, userInfo);
                    zone.Tasks.Add(equipmentValidationAlert);
                }
                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            Models.Drybook.Task MapToTask(
                Command request,
                Models.Drybook.Zone zone,
                GetLookups.Dto lookups,
                UserInfo userInfo)
            {
                var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == TaskTypes.EquipmentValidationException);
                var equipmentValidationAlert = new Models.Drybook.Task
                {
                    Id = Guid.NewGuid(),
                    FranchiseSetId = userInfo.FranchiseSetId,
                    PercentComplete = 100,
                    TaskStatusId = TaskStatuses.Completed,
                    CompletionDate = DateTime.UtcNow,
                    JobId = zone.JobId,
                    Subject = $"{taskType.DefaultSubject}: {zone.Name}",
                    TaskTypeId = TaskTypes.EquipmentValidationException,
                    TaskPriorityId = TaskPriorities.Medium,
                    JournalNotes = new List<Models.JournalNote>
                    {
                        new Models.JournalNote
                        {
                            ActionDate = request.JournalNote.ActionDate,
                            Author = userInfo.Name,
                            CategoryId = JournalNoteCategories.Validation,
                            VisibilityId = request.JournalNote.JournalNoteVisibilityId,
                            TypeId = JournalNoteTypes.Day1EquipmentValidationException,
                            Subject = $"{taskType.DefaultDiaryEntrySubject}: {zone.Name}",
                            Note = request.JournalNote.Note,
                            CreatedBy = userInfo.Username,
                            CreatedDate = DateTime.UtcNow,
                            JobId = request.JobId,
                        }
                    }
                };
                return equipmentValidationAlert;
            }
        }
    }
}
