﻿using AutoMapper;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale
{
    public class ResaleMappingProfile : Profile
    {
        public static readonly string EmployeeMappingKey = "EmployeeMapping";
        public static readonly string OwnerKey = "Owner";
        public static readonly string ServproOwnedBaseEquipmentKey = "ServproOwnedEquipmentTypeIds";
        public static readonly string XactJobsKey = "XactJobs";
        /// <summary>
        /// This is used to specify which equipment types are owned by the franchise, these are used to determine
        /// if a equipment model needs to transform its EquipmentTypeId or not
        /// </summary>
        public static readonly string EquipmentTypeToCopyIdsKey = "EquipmentTypeToCopyIds";
        /// <summary>
        /// This is used to specify which equipment models are owned by the franchise, these are used to determine
        /// if a piece of equipment needs to transform its EquipmentModelId or not
        /// </summary>
        public static readonly string EquipmentModelToCopyIdsKey = "EquipmentModelToCopyIds";
        public ResaleMappingProfile()
        {
            CreateMap<Job, ResaleJob>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Job.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseSetId)]))
                .ForMember(dest => dest.ReferenceNumber, opt => opt.Ignore())
                .ForMember(dest => dest.FranchiseId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseId)]))
                .ForMember(dest => dest.FranchiseName,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseName)]))
                .ForMember(dest => dest.FranchiseState,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseState)]))
                .ForMember(dest => dest.CallerId,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.CallerId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.CallerId, ctx.Items[nameof(Job.Id)]) :
                        src.CallerId))
                .ForMember(dest => dest.Caller, opt => opt.Ignore())
                .ForMember(dest => dest.CustomerId,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.CustomerId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.CustomerId, ctx.Items[nameof(Job.Id)]) :
                        src.CustomerId))
                .ForMember(dest => dest.Customer, opt => opt.Ignore())
                .ForMember(dest => dest.MobileData, opt => opt.Ignore())
                .ForMember(dest => dest.MobileDataId,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.MobileDataId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.MobileDataId, ctx.Items[nameof(Job.Id)]) :
                        src.MobileDataId))
                .ForMember(dest => dest.SiteAppointmentById,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.SiteAppointmentById.HasValue ?
                    GetNewEmployeeId(src.SiteAppointmentById.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.SiteAppointmentById))
                .ForMember(dest => dest.PriorityResponderId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GetNewEmployeeId(src.PriorityResponderId,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey])))
                .ForMember(dest => dest.JobFileCoordinatorId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.JobFileCoordinatorId.HasValue ?
                    GetNewEmployeeId(src.JobFileCoordinatorId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.JobFileCoordinatorId))
                .ForMember(dest => dest.ProjectManagerId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ProjectManagerId.HasValue ?
                    GetNewEmployeeId(src.ProjectManagerId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ProjectManagerId))
                .ForMember(dest => dest.ProductionManagerId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ProductionManagerId.HasValue ?
                    GetNewEmployeeId(src.ProductionManagerId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ProductionManagerId))
                .ForMember(dest => dest.CrewChiefId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.CrewChiefId.HasValue ?
                    GetNewEmployeeId(src.CrewChiefId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.CrewChiefId))
                .ForMember(dest => dest.RecpDispatcherId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.RecpDispatcherId.HasValue ?
                    GetNewEmployeeId(src.RecpDispatcherId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.RecpDispatcherId))
                .ForMember(dest => dest.GeneralManagerId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.GeneralManagerId.HasValue ?
                    GetNewEmployeeId(src.GeneralManagerId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.GeneralManagerId))
                .ForMember(dest => dest.OfficeManagerId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.OfficeManagerId.HasValue ?
                    GetNewEmployeeId(src.OfficeManagerId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.OfficeManagerId))
                .ForMember(dest => dest.ReconSupportId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ReconSupportId.HasValue ?
                    GetNewEmployeeId(src.ReconSupportId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ReconSupportId))
                .ForMember(dest => dest.MarketingRepId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.MarketingRepId.HasValue ?
                    GetNewEmployeeId(src.MarketingRepId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.MarketingRepId))
                // if the job is a xactimate job then add "." as a suffix to the claim number
                .ForMember(dest => dest.InsuranceClaimNumber,
                    opt => opt.MapFrom((src, dest, _, ctx) => !string.IsNullOrEmpty(src.InsuranceClaimNumber) && ((HashSet<Guid>)ctx.Items[XactJobsKey]).Contains(src.Id) ?
                        $"{src.InsuranceClaimNumber}." : src.InsuranceClaimNumber));
            CreateMap<WipRecord, ResaleWipRecord>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Job.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseSetId)]))
                .ForMember(dest => dest.FranchiseId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseId)]))
                .ForMember(dest => dest.FranchiseName,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseName)]))
                .ForMember(dest => dest.FranchiseState,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Job.FranchiseState)]))
                .ForMember(dest => dest.CallerId,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.CallerId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.CallerId, ctx.Items[nameof(Job.Id)]) :
                        src.CallerId))
                .ForMember(dest => dest.CustomerId,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.CustomerId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.CustomerId, ctx.Items[nameof(Job.Id)]) :
                        src.CustomerId))
                .ForMember(dest => dest.PriorityResponderName,
                    opt => opt.MapFrom((src, dest, _, ctx) => GetNewEmployeeId(src.PriorityResponderName,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey])))
                .ForMember(dest => dest.JobFileCoordinatorName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.JobFileCoordinatorName.HasValue ?
                    GetNewEmployeeId(src.JobFileCoordinatorName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.JobFileCoordinatorName))
                .ForMember(dest => dest.ProjectManagerName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ProjectManagerName.HasValue ?
                    GetNewEmployeeId(src.ProjectManagerName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ProjectManagerName))
                .ForMember(dest => dest.ProductionManagerName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ProductionManagerName.HasValue ?
                    GetNewEmployeeId(src.ProductionManagerName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ProductionManagerName))
                .ForMember(dest => dest.CrewChiefName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.CrewChiefName.HasValue ?
                    GetNewEmployeeId(src.CrewChiefName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.CrewChiefName))
                .ForMember(dest => dest.RecpDispatcherName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.RecpDispatcherName.HasValue ?
                    GetNewEmployeeId(src.RecpDispatcherName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.RecpDispatcherName))
                .ForMember(dest => dest.GeneralManagerName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.GeneralManagerName.HasValue ?
                    GetNewEmployeeId(src.GeneralManagerName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.GeneralManagerName))
                .ForMember(dest => dest.OfficeManagerName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.OfficeManagerName.HasValue ?
                    GetNewEmployeeId(src.OfficeManagerName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.OfficeManagerName))
                .ForMember(dest => dest.ReconSuperintendent,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ReconSuperintendent.HasValue ?
                    GetNewEmployeeId(src.ReconSuperintendent.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ReconSuperintendent))
                .ForMember(dest => dest.ReferredByName,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.ReferredByName.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.ReferredByName, ctx.Items[nameof(Job.Id)]) :
                        src.ReferredByName))
                .ForMember(dest => dest.ReportedByName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.ReportedByName.HasValue ?
                    GetNewEmployeeId(src.ReportedByName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.ReportedByName))
                .ForMember(dest => dest.MarketingRepName,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.MarketingRepName.HasValue ?
                    GetNewEmployeeId(src.MarketingRepName.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.MarketingRepName))
                // if the job is a xactimate job then add "." as a suffix to the claim number
                .ForMember(dest => dest.InsuranceClaimNumber,
                    opt => opt.MapFrom((src, dest, _, ctx) => !string.IsNullOrEmpty(src.InsuranceClaimNumber) && ((HashSet<Guid>)ctx.Items[XactJobsKey]).Contains(src.Id) ?
                        $"{src.InsuranceClaimNumber}." : src.InsuranceClaimNumber));
            CreateMap<JobVisit, ResaleJobVisit>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobVisit.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobVisit.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.JobVisitTriStateAnswers, opt => opt.Ignore());
            CreateMap<JobVisitTriStateAnswer, ResaleJobVisitTriStateAnswer>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobVisitTriStateAnswer.Id)])))
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(JobVisitTriStateAnswer.Id)])))
                .ForMember(dest => dest.JobVisit, opt => opt.Ignore());
            CreateMap<Zone, ResaleZone>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Zone.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(Zone.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore());
            CreateMap<ZoneReading, ResaleZoneReading>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(ZoneReading.Id)])))
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(ZoneReading.Id)])))
                .ForMember(dest => dest.JournalNoteId,
                    opt => opt.MapFrom((src, dest, _, ctx)
                        => !src.JournalNoteId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.JournalNoteId, ctx.Items[nameof(Job.Id)]) :
                        src.JournalNoteId))
                .ForMember(dest => dest.ZoneId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.ZoneId, ctx.Items[nameof(ZoneReading.Id)])));
            CreateMap<JobArea, ResaleJobArea>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobArea.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobArea.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.ZoneId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.ZoneId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.ZoneId, ctx.Items[nameof(JobArea.Id)]) : src.ZoneId))
                .ForMember(dest => dest.RoomId,
                    opt => opt.MapFrom((src, dest, _, ctx) 
                        => !src.RoomId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.RoomId, ctx.Items[nameof(JobArea.Id)]) 
                        : src.RoomId))
                .ForMember(dest => dest.BeginJobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.BeginJobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.BeginJobVisitId, ctx.Items[nameof(JobArea.Id)]) : src.BeginJobVisitId))
                .ForMember(dest => dest.EndJobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.EndJobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.EndJobVisitId, ctx.Items[nameof(JobArea.Id)]) : src.EndJobVisitId))
                .ForMember(dest => dest.Room, opt => opt.Ignore());
            CreateMap<JobMaterial, ResaleJobMaterial>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobMaterial.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobMaterial.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore());
            CreateMap<JobAreaMaterial, ResaleJobAreaMaterial>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobAreaMaterial.Id)])))
                .ForMember(dest => dest.JobAreaId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaId, ctx.Items[nameof(JobAreaMaterial.Id)])))
                .ForMember(dest => dest.GoalMetOnJobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.GoalMetOnJobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.GoalMetOnJobVisitId, ctx.Items[nameof(JobAreaMaterial.Id)]) : src.GoalMetOnJobVisitId))
                .ForMember(dest => dest.BeginJobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.BeginJobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.BeginJobVisitId, ctx.Items[nameof(JobAreaMaterial.Id)]) : src.BeginJobVisitId))
                .ForMember(dest => dest.RemovedOnJobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.RemovedOnJobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.RemovedOnJobVisitId, ctx.Items[nameof(JobAreaMaterial.Id)]) : src.RemovedOnJobVisitId))
                .ForMember(dest => dest.JobMaterialId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobMaterialId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobMaterialId, ctx.Items[nameof(JobAreaMaterial.Id)]) : src.JobMaterialId));
            CreateMap<JobAreaMaterialReading, ResaleJobAreaMaterialReading>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobAreaMaterialReading.Id)])))
                .ForMember(dest => dest.JobAreaMaterialId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaMaterialId, ctx.Items[nameof(JobAreaMaterialReading.Id)])))
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(JobAreaMaterialReading.Id)])))
                .ForMember(dest => dest.MediaMetadataId, 
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.MediaMetadataId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.MediaMetadataId, ctx.Items[nameof(JobAreaMaterialReading.Id)]) : src.MediaMetadataId));
            CreateMap<Contact, ResaleContact>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Contact.Id)])))
                .ForMember(dest => dest.BusinessId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.BusinessId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.BusinessId, ctx.Items[nameof(Contact.Id)]) : src.BusinessId))
                .ForMember(dest => dest.MarketingRepId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.MarketingRepId.HasValue ?
                    GetNewEmployeeId(src.MarketingRepId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.MarketingRepId))
                .ForMember(dest => dest.Business, opt => opt.Ignore());
            CreateMap<Business, ResaleBusiness>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Business.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.FranchiseSetId.IsNullOrEmpty() ? ctx.Items[nameof(Business.FranchiseSetId)] : src.FranchiseSetId))
                .ForMember(dest => dest.Contacts, opt => opt.Ignore());
            CreateMap<JobContactMap, ResaleJobContactMap>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobContactMap.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobContactMap.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.ContactId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.ContactId, ctx.Items[nameof(JobContactMap.Id)])));
            CreateMap<JobBusinessMap, ResaleJobBusinessMap>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobBusinessMap.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobBusinessMap.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.BusinessId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.BusinessId, ctx.Items[nameof(JobBusinessMap.Id)])));
            CreateMap<MediaMetadata, ResaleMediaMetadata>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(MediaMetadata.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(MediaMetadata.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(MediaMetadata.FranchiseSetId)]))
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(MediaMetadata.Id)]) : src.JobVisitId))
                .ForMember(dest => dest.JobSketchId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobSketchId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobSketchId, ctx.Items[nameof(MediaMetadata.Id)]) : src.JobSketchId))
                .ForMember(dest => dest.JobInvoiceId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobInvoiceId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobInvoiceId, ctx.Items[nameof(MediaMetadata.Id)]) : src.JobInvoiceId))
                .ForMember(dest => dest.JobAreaId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobAreaId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaId, ctx.Items[nameof(MediaMetadata.Id)]) : src.JobAreaId))
                .ForMember(dest => dest.JobAreaMaterialId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobAreaMaterialId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaMaterialId, ctx.Items[nameof(MediaMetadata.Id)]) : src.JobAreaMaterialId))
                .ForMember(dest => dest.ZoneId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.ZoneId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.ZoneId, ctx.Items[nameof(MediaMetadata.Id)]) : src.ZoneId));
            CreateMap<JobSketch, ResaleJobSketch>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobSketch.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobSketch.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.MediaMetadataId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.MediaMetadataId, ctx.Items[nameof(JobSketch.Id)])));
            CreateMap<JobInvoice, ResaleJobInvoice>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobInvoice.Id)])))
                .ForMember(dest => dest.MediaMetadataId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.MediaMetadataId, ctx.Items[nameof(JobInvoice.Id)])));
            CreateMap<Task, ResaleTask>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Task.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(Task.Id)]) : src.JobId))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(Task.Id)]) : src.JobVisitId))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.FranchiseSetId.IsNullOrEmpty() ? ctx.Items[nameof(Task.FranchiseSetId)] : src.FranchiseSetId))
                .ForMember(dest => dest.ZoneId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.ZoneId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.ZoneId, ctx.Items[nameof(Task.Id)]) : src.ZoneId));
            CreateMap<Room, ResaleRoom>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Room.Id)])))
                .ForMember(dest => dest.PreExistingConditionsDiaryNoteId,
                    opt => opt.MapFrom((src, dest, _, ctx) 
                        => !src.PreExistingConditionsDiaryNoteId.IsNullOrEmpty() ?
                        GuidTransformHelpers.TransformToManipulatedGuid(src.PreExistingConditionsDiaryNoteId, ctx.Items[nameof(Room.Id)]) :
                        src.PreExistingConditionsDiaryNoteId))
                .ForMember(dest => dest.DryOnJobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.DryOnJobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.DryOnJobVisitId, ctx.Items[nameof(Room.Id)]) : src.DryOnJobVisitId));
            CreateMap<RoomFlooringTypeAffected, ResaleRoomFlooringTypeAffected>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(RoomFlooringTypeAffected.Id)])))
                .ForMember(dest => dest.RoomId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.RoomId, ctx.Items[nameof(RoomFlooringTypeAffected.Id)])));
            CreateMap<JournalNote, ResaleJournalNote>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JournalNote.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JournalNote.Id)]) : src.JobId))
                .ForMember(dest => dest.Job, opt => opt.Ignore())
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobVisitId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(JournalNote.Id)]) : src.JobVisitId))
                .ForMember(dest => dest.JobAreaId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobAreaId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaId, ctx.Items[nameof(JournalNote.Id)]) : src.JobAreaId))
                .ForMember(dest => dest.JobAreaMaterialId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.JobAreaMaterialId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaMaterialId, ctx.Items[nameof(JournalNote.Id)]) : src.JobAreaMaterialId))
                .ForMember(dest => dest.ZoneId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.ZoneId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.ZoneId, ctx.Items[nameof(JournalNote.Id)]) : src.ZoneId))
                .ForMember(dest => dest.TaskId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.TaskId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.TaskId, ctx.Items[nameof(JournalNote.Id)]) : src.TaskId))
                .ForMember(dest => dest.RoomFlooringTypeAffectedId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.RoomFlooringTypeAffectedId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.RoomFlooringTypeAffectedId, ctx.Items[nameof(JournalNote.Id)]) : src.RoomFlooringTypeAffectedId))
                .ForMember(dest => dest.Rules,
                    opt => opt.MapFrom((src, dest, _, ctx) => MapBreRules(src.Rules, (Guid)ctx.Items[nameof(JournalNote.Id)])));
            CreateMap<EquipmentType, ResaleEquipmentType>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(EquipmentType.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(EquipmentType.FranchiseSetId)]))
                .ForMember(dest => dest.EquipmentModels, opt => opt.Ignore())
                // Only transform BaseEquipmentType if it it not a Servpro Owned BaseEquipmentType
                .ForMember(dest => dest.BaseEquipmentTypeId,
                    opt => opt.MapFrom((src, dest, _, ctx) => 
                        !src.BaseEquipmentTypeId.IsNullOrEmpty() && !IsServproOwnedBaseEquipment((ImmutableHashSet<Guid>)ctx.Items[ServproOwnedBaseEquipmentKey], src.BaseEquipmentTypeId.Value) 
                        ? GuidTransformHelpers.TransformToManipulatedGuid(src.BaseEquipmentTypeId, ctx.Items[nameof(EquipmentType.Id)]) 
                        : src.BaseEquipmentTypeId));
            CreateMap<EquipmentModel, ResaleEquipmentModel>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(EquipmentModel.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(EquipmentModel.FranchiseSetId)]))
                // Only transform EquipmentTypeId if it is owned by the franchise (not Servpro Owned, aka has FranchiseSetId)
                .ForMember(dest => dest.EquipmentTypeId,
                    opt => opt.MapFrom((src, dest, _, ctx) => IsFranchiseOwnedEquipmentType((HashSet<Guid>)ctx.Items[EquipmentTypeToCopyIdsKey], src.EquipmentTypeId)
                        ? GuidTransformHelpers.TransformToManipulatedGuid(src.EquipmentTypeId, ctx.Items[nameof(EquipmentModel.Id)])
                        : src.EquipmentTypeId))
                .ForMember(dest => dest.EquipmentType, opt => opt.Ignore())
                .ForMember(dest => dest.Equipments, opt => opt.Ignore());
            CreateMap<Equipment, ResaleEquipment>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Equipment.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Equipment.FranchiseSetId)]))
                // Only transform EquipmentModelId if it is owned by the franchise (not Servpro Owned, aka has FranchiseSet)
                .ForMember(dest => dest.EquipmentModelId,
                    opt => opt.MapFrom((src, dest, _, ctx) => IsFranchiseOwnedEquipmentModel((HashSet<Guid>)ctx.Items[EquipmentModelToCopyIdsKey], src.EquipmentModelId)
                        ? GuidTransformHelpers.TransformToManipulatedGuid(src.EquipmentModelId, ctx.Items[nameof(Equipment.Id)])
                        : src.EquipmentModelId))
                .ForMember(dest => dest.EquipmentModel, opt => opt.Ignore());
            CreateMap<EquipmentPlacement, ResaleEquipmentPlacement>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(EquipmentPlacement.Id)])))
                .ForMember(dest => dest.JobAreaId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobAreaId, ctx.Items[nameof(EquipmentPlacement.Id)])))
                .ForMember(dest => dest.EquipmentId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.EquipmentId, ctx.Items[nameof(EquipmentPlacement.Id)])));
            CreateMap<EquipmentPlacementReading, ResaleEquipmentPlacementReading>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(EquipmentPlacementReading.Id)])))
                .ForMember(dest => dest.JobVisitId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobVisitId, ctx.Items[nameof(EquipmentPlacementReading.Id)])))
                .ForMember(dest => dest.ZoneId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.ZoneId, ctx.Items[nameof(EquipmentPlacementReading.Id)])))
                .ForMember(dest => dest.EquipmentPlacementId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.EquipmentPlacementId, ctx.Items[nameof(EquipmentPlacementReading.Id)])));
            CreateMap<JobTriStateAnswer, ResaleJobTriStateAnswer>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(JobTriStateAnswer.Id)])))
                .ForMember(dest => dest.JobId,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.JobId, ctx.Items[nameof(JobTriStateAnswer.Id)])))
                .ForMember(dest => dest.Job, opt => opt.Ignore());
            CreateMap<MobileData, ResaleMobileData>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(MobileData.Id)])))
                .ForMember(dest => dest.Jobs, opt => opt.Ignore());
        }

        private static List<JournalNote.BreRule> MapBreRules(List<JournalNote.BreRule> sourceBreRules, Guid resaleId)
        {
            if (sourceBreRules is null || !sourceBreRules.Any())
                return sourceBreRules;

            foreach (var breRule in sourceBreRules)
            {
                breRule.JobAreaId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.JobAreaId, resaleId);
                breRule.JobAreaMaterialId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.JobAreaMaterialId, resaleId);
                breRule.JobVisitId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.JobVisitId, resaleId);
                breRule.ZoneId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.ZoneId, resaleId);
            }

            return sourceBreRules;
        }

        public static bool IsFranchiseOwnedEquipmentType(HashSet<Guid> equipmentTypeIds, Guid equipmentTypeId)
            => equipmentTypeIds.Contains(equipmentTypeId);

        public static bool IsFranchiseOwnedEquipmentModel(HashSet<Guid> equipmentModelIds, Guid equipmentModelId)
            => equipmentModelIds.Contains(equipmentModelId);

        public static bool IsServproOwnedBaseEquipment(ImmutableHashSet<Guid> servproOwnedBaseEquipmentTypeIds, Guid baseEquipmentTypeId)
            => servproOwnedBaseEquipmentTypeIds.Contains(baseEquipmentTypeId);

        private static Guid? GetNewEmployeeId(Guid oldEmployeeId, ImmutableDictionary<Guid, Guid> employeeMapping, EmployeeMapping.Employee owner)
        {
            if (oldEmployeeId == Guid.Empty)
            {
                return null;
            }

            //check if the franchise provided a mapping, if a mapping was not provided, default to the owner
            return employeeMapping.ContainsKey(oldEmployeeId) ? employeeMapping[oldEmployeeId] : owner.Id;
        }

        private static Guid? GetNewEmployeeId(Guid? oldEmployeeId, object employeeMapping)
            => GetNewEmployeeId(oldEmployeeId, (ImmutableDictionary<Guid, Guid>)employeeMapping);
    }
}
