﻿using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Split
{
    public class SplitEntity
    {
        public SplitEntity(
            IEnumerable<Job> jobs,
            IEnumerable<Business> businesses,
            IEnumerable<Contact> contacts,
            IEnumerable<Equipment> equipment,
            IEnumerable<EquipmentModel> equipmentModels,
            IEnumerable<EquipmentType> equipmentTypes,
            IEnumerable<EquipmentPlacement> equipmentPlacements)
        {
            Jobs = jobs?.ToHashSet() ?? throw new ArgumentNullException(nameof(jobs));
            Businesses = businesses?.ToHashSet() ?? throw new ArgumentNullException(nameof(businesses));
            Contacts = contacts?.ToHashSet() ?? throw new ArgumentNullException(nameof(contacts));
            Equipment = equipment?.ToHashSet() ?? throw new ArgumentNullException(nameof(equipment));
            EquipmentModels = equipmentModels?.ToHashSet() ?? throw new ArgumentNullException(nameof(equipmentModels));
            EquipmentTypes = equipmentTypes?.ToHashSet() ?? throw new ArgumentNullException(nameof(equipmentTypes));
            EquipmentPlacements = equipmentPlacements?.ToHashSet() ?? throw new ArgumentNullException(nameof(equipmentPlacements));
        }

        public HashSet<Job> Jobs { get; }
        public HashSet<Business> Businesses { get; }
        public HashSet<Contact> Contacts { get; }
        public HashSet<Equipment> Equipment { get; }
        public HashSet<EquipmentModel> EquipmentModels { get; }
        public HashSet<EquipmentType> EquipmentTypes { get; }
        public HashSet<EquipmentPlacement> EquipmentPlacements { get; }
    }
    public class SplitJob : Job { }
    public class SplitBusiness : Business { }
    public class SplitJobBusinessMap : JobBusinessMap { }
    public class SplitContact : Contact { }
    public class SplitJobContactMap : JobContactMap { }
    public class SplitEquipment : Equipment { }
    public class SplitEquipmentModel : EquipmentModel { }
    public class SplitEquipmentType : EquipmentType { }
}
