﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;
using static Servpro.Franchise.JobService.Features.Leads.Events.Mapping.LeadCreatedEventMapper;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    /// <summary>
    /// This event was created in order to publish leadCreated events to RM that for some reason failed
    /// and we no longer have the event or the original event would be out of date.
    /// </summary>
    public class ReplayLeadCreatedEvents
    {
        public class Event : ReplayLeadCreatedEvent, IRequest
        {
            public Event(ReplayLeadAdded dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<ReplayLeadCreatedEvents> _logger;
            private readonly JobDataContext _db;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(
                ILogger<ReplayLeadCreatedEvents> logger,
                JobDataContext db,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _logger = logger;
                _db = db;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing event: {@request}", request);
                foreach (var jobId in request.ReplayLead.JobIds)
                {
                    _logger.BeginScope("{jobId}", jobId);
                    try
                    {
                        _logger.LogInformation("Processing job");
                        var job = await _db.Jobs
                            .Include(x => x.Customer)
                            .Include(x => x.Caller)
                            .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);
                        if (job is null)
                        {
                            _logger.LogWarning("Job not found.");
                            continue;
                        }
                        job.JobContacts = await _db.JobContactMap
                            .Include(x => x.Contact)
                            .Where(x => x.JobId == jobId)
                            .ToListAsync(cancellationToken);
                        job.JobBusinesses = await _db.JobBusinessMaps
                            .Include(x => x.Business)
                            .Where(x => x.JobId == jobId)
                            .ToListAsync(cancellationToken);
                        var franchiseSetInfo = await _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, cancellationToken);
                        var franchiseInfo = franchiseSetInfo.Franchises.FirstOrDefault(x => x.Id == job.FranchiseId);
                        _logger.LogDebug("FranchiseInfo: {@franchise}", franchiseInfo);
                        var userInfo = await GetUserInfoAsync(job, cancellationToken);
                        // todo: need to get the userId
                        var leadCreatedEvent = new LeadCreatedEvent(MapJobDto(job, userInfo.userId), request.CorrelationId);
                        leadCreatedEvent.Job.FranchiseNumber = (int)franchiseInfo?.FranchiseNumber;
                        var outboxMessage = new OutboxMessage(leadCreatedEvent.ToJson(),
                                       nameof(LeadCreatedEvent), request.CorrelationId, userInfo.username);
                        await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                        await _db.SaveChangesAsync(cancellationToken);
                        _logger.LogInformation("Finished processing job");
                    }
                    catch(ServiceException ex)
                    {
                        _logger.LogError(ex, "Service error: {@response}",
                            new { ex.ServiceName, ex.StatusCode, ex.RequestUri, ex.Response });
                    }
                    catch(Exception ex)
                    {
                        _logger.LogError(ex, "Error processing job: {jobId}", jobId);
                    }
                }
                return Unit.Value;
            }

            private async Task<(Guid userId, string username)> GetUserInfoAsync(
                Models.Job job,
                CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting employee information");
                if (job.RecpDispatcherId.HasValue)
                {
                    _logger.LogDebug("Employee info found in job");
                    return (job.RecpDispatcherId.Value, job.CreatedBy);
                }
                // get info from franchise service
                _logger.LogInformation("Getting employee info from franchise-service");
                var employees = await _franchiseServiceClient.GetEmployees(
                    new GetEmployees.Request(job.FranchiseSetId), cancellationToken);
                var employee = employees.FirstOrDefault(x => x.UserName == job.CreatedBy);
                if (employee is null)
                {
                    _logger.LogWarning("{Username} not found in franchise employees: {@employees}",
                        job.CreatedBy, employees.Select(x => new { x.Id, x.FirstName, x.LastName, x.UserName }));
                    return (Guid.NewGuid(), "");
                }
                return (employee.Id, employee.UserName);
            }
        }
    }
}
