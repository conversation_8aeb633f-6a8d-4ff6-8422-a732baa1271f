﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetJobForMicaUpdate
    {
        public class Query : IRequest<JobDto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }

        public class JobDto
        {
            public Guid Id { get; set; }
            public int JobProgress { get; set; }
            public Guid FranchiseId { get; set; }
            public string FranchiseName { get; set; }
            public Guid PropertyTypeId { get; set; }
            public int LevelsAffected { get; set; }
            public long? YearStructureBuilt { get; set; }
            public string CarrierName { get; set; }
            public string InsuranceClaimNumber { get; set; }
            public ContactDto Adjuster { get; set; }
            public ContactDto Customer { get; set; }
            public List<JobDateDto> JobDates { get; set; }
            public AddressDto Address { get; set; }
        }

        public class ContactDto
        {
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string Email { get; set; }
            public List<PhoneDto> Phones { get; set; }
        }

        public class PhoneDto
        {
            public int Type { get; set; }
            public string Number { get; set; }
            public string Extension { get; set; }
        }

        public class JobDateDto
        {
            public Guid JobDateTypeId { get; set; }
            public DateTime Date { get; set; }
        }

        public class AddressDto
        {
            public string Address1 { get; set; }
            public string Address2 { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string PostalCode { get; set; }
            public Guid CountryId { get; set; }
        }

        public class Handler : IRequestHandler<Query, JobDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<Handler> _logger;
            private readonly HashSet<Guid> _jobDatesToSend = new HashSet<Guid>()
            {
                JobDateTypes.LossOccurred,
                JobDateTypes.LossReceived,
                JobDateTypes.CustomerCalled,
                JobDateTypes.InitialOnSiteArrival,
                JobDateTypes.WorkAuthorizationSigned,
                JobDateTypes.DryingComplete,
                JobDateTypes.Complete
            };

            public Handler(
                  JobReadOnlyDataContext context,
                  ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<JobDto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);

                _logger.LogInformation("Getting Job data for Mica Update.");

                var job = await _context.Jobs
                    .Include(x => x.JobContacts)
                        .ThenInclude(x => x.Contact)
                    .Include(x => x.Customer)
                        .ThenInclude(c => c.Business)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var insurance = await _context.InsuranceClients.FirstOrDefaultAsync(x => x.Id == job.InsuranceCarrierId, cancellationToken);

                return Mapper.Map(job, insurance, _jobDatesToSend);
            }
        }

        public class Mapper
        {
            public static JobDto Map(Models.Job job, InsuranceClient insuranceClient, HashSet<Guid> jobDatesToSend)
            {
                return new JobDto
                {
                    Id = job.Id,
                    JobProgress = (int)job.JobProgress,
                    CarrierName = insuranceClient?.Name,
                    InsuranceClaimNumber = job.InsuranceClaimNumber,
                    FranchiseId = job.FranchiseId,
                    FranchiseName = job.FranchiseName,
                    PropertyTypeId = job.PropertyTypeId,
                    YearStructureBuilt = job.YearStructureBuilt,
                    LevelsAffected = job.LevelsAffected,
                    Address = Map(job.LossAddress),
                    Adjuster = MapAdjuster(job.JobContacts?.FirstOrDefault(x => x.JobContactTypeId == JobContactTypes.Adjuster)),
                    Customer = MapCustomer(job.Customer, job.PropertyTypeId),
                    JobDates = job.JobDates?.Where(x => jobDatesToSend.Contains(x.JobDateTypeId))
                                ?.Select(x => Map(x)).ToList() ?? new List<JobDateDto>(),
                };
            }

            private static ContactDto MapAdjuster(Models.JobContactMap contactMap)
            {
                if (contactMap == null)
                    return null;

                return MapResidentialContact(contactMap.Contact);
            }

            private static ContactDto MapCustomer(Models.Contact contact, Guid propertyTypeId)
            {
                if (contact == null)
                    return null;

                if (propertyTypeId == LookupService.Constants.PropertyTypes.Commercial)
                {
                    return MapCommercialContact(contact);
                }

                return MapResidentialContact(contact);
            }

            private static ContactDto MapResidentialContact(Models.Contact contact)
            {
                return new ContactDto
                {
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    Email = contact.EmailAddress,
                    Phones = contact.PhoneNumbers?.Select(x => Map(x)).ToList() ?? new List<PhoneDto>(),
                };
            }

            private static ContactDto MapCommercialContact(Models.Contact contact)
            {
                var businessPhoneNumber = Map(contact.Business?.PhoneNumber);
                var phoneNumbers = new List<PhoneDto>();

                if (businessPhoneNumber != null)
                    phoneNumbers.Add(businessPhoneNumber);

                return new ContactDto
                {
                    LastName = contact.Business?.Name,
                    Email = contact.Business?.EmailAddress?.Address,
                    Phones = phoneNumbers,
                };
            }

            private static PhoneDto Map(Models.Phone phone)
            {
                if (phone == null)
                    return null;

                return new PhoneDto
                {
                    Number = phone.PhoneNumber,
                    Extension = phone.PhoneExtension,
                    Type = (int)phone.PhoneType
                };
            }

            private static JobDateDto Map(JobDate jobDate)
            {
                if (jobDate == null)
                    return null;

                return new JobDateDto
                {
                    JobDateTypeId = jobDate.JobDateTypeId,
                    Date = jobDate.Date
                };
            }

            private static AddressDto Map(Models.Address address)
            {
                if (address == null)
                    return null;

                var addressDto = new AddressDto
                {
                    Address1 = address.Address1,
                    Address2 = address.Address2,
                    City = address.City,
                    PostalCode = address.PostalCode,
                };

                if (address.State != null)
                {
                    addressDto.State = address.State.StateName;
                    addressDto.CountryId = address.State.CountryId;
                }

                return addressDto;
            }
        }
    }
}
