﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyEquipmentModel
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult EquipmentTypeResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> EquipmentModelIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyEquipmentModel>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyEquipmentModel> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                IServiceProvider services,
                ILogger<ResaleCopyEquipmentModel> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _services = services;
                _logger = logger;
                _mapper = mapper;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(EquipmentModel));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.EquipmentModelIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var equipmentModelTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedEquipmentModelIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(equipmentModelTargetIds, 
                    GetEquipmentModelIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<EquipmentModel, ResaleEquipmentModel>(
                    request.ResaleId,
                    equipmentModel =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.EquipmentTypeResult.FailedEntities.Contains(equipmentModel.EquipmentTypeId))
                            failedDependencies.Add((nameof(EquipmentType), equipmentModel.EquipmentTypeId));

                        return failedDependencies;
                    },
                    alreadyCopiedEquipmentModelIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.EquipmentModels.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<EquipmentModel>> GetSourceEntitiesAsync(List<Guid> equipmentModelIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var equipmentModels = await _context.EquipmentModels
                    .Where(e => equipmentModelIds.Contains(e.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", equipmentModels.Count);
                return equipmentModels;
            }

            private async Task<List<Guid>> GetEquipmentModelIdsAsync(List<Guid?> equipmentModelTargetIds, CancellationToken cancellationToken)
            {
                return await _context.EquipmentModels
                    .Where(x => equipmentModelTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
