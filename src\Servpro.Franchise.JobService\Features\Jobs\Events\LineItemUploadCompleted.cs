﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Scope;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Servpro.Franchise.JobService.Common.Constants;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class LineItemUploadCompleted
    {
        public class Event : LineItemUploadCompletedEvent, IRequest
        {
            public Event(LineItemUploadCompletedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly IServiceProvider _services;

            public Handler(IServiceProvider serviceProvider)
            {
                _services = serviceProvider;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();

                var db = serviceScope.ServiceProvider.GetService<JobDataContext>();
                var logger = serviceScope.ServiceProvider.GetService<ILogger<LineItemUploadCompleted>>();

                var eventDto = incomingEvent.LineItemUpload;
                var jobUploadLock = await db.JobUploadLocks
                    .FirstOrDefaultAsync(x => x.JobId == eventDto.JobId && x.JobUploadTypeId == JobUploadTypes.XactLineItem, cancellationToken: cancellationToken);

                if (jobUploadLock != null)
                {
                    db.JobUploadLocks.Remove(jobUploadLock);
                }

                var job = await db.Jobs
                    .Include(x => x.LineItems)
                    .FirstOrDefaultAsync(j => j.Id == incomingEvent.LineItemUpload.JobId, cancellationToken);

                incomingEvent.LineItemUpload.ScopeLineItemIds?.ForEach(i =>
                {
                    var jobLineItem = job.LineItems.FirstOrDefault(x => x.Id == i);
                    if (jobLineItem != null)
                    {
                        jobLineItem.XactUploadTransactionId = incomingEvent.LineItemUpload.XactUploadTransactionId;
                    }
                });

                db.Update(job);
                await db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}