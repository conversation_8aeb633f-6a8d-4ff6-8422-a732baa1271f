﻿using System;
using System.Runtime.Serialization;

namespace Servpro.Franchise.JobService.Features.Jobs.Locks
{
    public class JobConflictException : Exception
    {
        public GetJobLock.Dto JobLock { get; }
        public JobConflictException(GetJobLock.Dto jobLockDto)
            : base(GenerateMessage(jobLockDto))
        {
            JobLock = jobLockDto;
        }

        protected JobConflictException(string message, Exception inner) : base(message, inner)
        {
        }

        private static string GenerateMessage(GetJobLock.Dto jobLockDto)
        {
            var message = jobLockDto is null || !jobLockDto.IsLocked ?
                     "Job is not locked. Please lock the job." :
                     "Job is locked by " + jobLockDto.LockedByUserFullName;

            return message;
        }
    }
}