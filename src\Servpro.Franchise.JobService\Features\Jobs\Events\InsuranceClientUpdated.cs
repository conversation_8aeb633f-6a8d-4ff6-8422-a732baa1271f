﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Client;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class InsuranceClientUpdated
    {
        #region Event
        public class Event : InsuranceClientUpdatedEvent, IRequest
        {
            public Event(InsuranceDto insuranceClient, Guid correlationId) : base(insuranceClient, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db,
                ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} receieved", nameof(InsuranceClientUpdatedEvent));

                var insuranceClientDto = request.InsuranceUpdated;

                var insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(x => x.Id == insuranceClientDto.Id, cancellationToken: cancellationToken);

                if (insuranceClient == null) //Implementation in case ClientGroupMaster added the placeholder client with the insuranceNumber;
                {
                    insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(x => x.InsuranceNumber == insuranceClientDto.InsuranceNumber, cancellationToken: cancellationToken);
                }

                if (insuranceClient == null)
                {
                    insuranceClient = MapClient(insuranceClientDto);
                    _db.InsuranceClients.Add(insuranceClient);
                    _logger.LogInformation("{event}: Inserting with Id {insuranceClientId}, {@insuranceClient}", nameof(InsuranceClientUpdatedEvent), insuranceClientDto.Id, insuranceClient);
                }
                else if (insuranceClient.Id != insuranceClientDto.Id)// This is to handle when a placeholder insurance client was added by ClientGroupMaster.
                {
                    var parentInsuranceNumber = insuranceClient.ParentInsuranceNumber;
                    var insuranceNumber = insuranceClient.InsuranceNumber;
                    _db.InsuranceClients.Remove(insuranceClient);
                    insuranceClient = MapClient(insuranceClientDto, parentInsuranceNumber);
                    _db.InsuranceClients.Add(insuranceClient);
                    _logger.LogWarning("{event}: Updating Placeholder Insurance with InsuranceNumber {insuranceNumber} and Id {insuranceClientId}, {@insuranceClient}", nameof(InsuranceClientUpdatedEvent), insuranceNumber, insuranceClientDto.Id, insuranceClient);
                }
                else
                {
                    insuranceClient.Name = insuranceClientDto.Name;
                    insuranceClient.InsuranceNumber = insuranceClientDto.InsuranceNumber;
                    insuranceClient.IsActive = insuranceClientDto.IsActive;
                    insuranceClient.IsLocalAuditRequired = insuranceClientDto.AuditLocalJobs;
                    insuranceClient.IsAuditRequired = insuranceClientDto.AuditJobs;
                    insuranceClient.ModifiedDate = insuranceClientDto.LastModified;
                    _logger.LogInformation("{event}: Updating with Id {insuranceClientId}, {@insuranceClient}", nameof(InsuranceClientUpdatedEvent), insuranceClientDto.Id, insuranceClient);
                }

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{event} saved: Id {id}", nameof(InsuranceClientUpdatedEvent), insuranceClientDto.Id);

                return Unit.Value;
            }

            private InsuranceClient MapClient(InsuranceClientUpdatedEvent.InsuranceDto insuranceClientDto, int? parentInsuranceNumber = null)
                => new InsuranceClient
                {
                    Id = insuranceClientDto.Id,
                    Name = insuranceClientDto.Name,
                    InsuranceNumber = insuranceClientDto.InsuranceNumber,
                    ParentInsuranceNumber = parentInsuranceNumber ?? insuranceClientDto.InsuranceNumber,
                    IsActive = insuranceClientDto.IsActive,
                    IsLocalAuditRequired = insuranceClientDto.AuditLocalJobs,
                    IsAuditRequired = insuranceClientDto.AuditJobs,
                    CreatedDate = insuranceClientDto.CreatedDate
                };
        }
        #endregion Handler
    }
}
