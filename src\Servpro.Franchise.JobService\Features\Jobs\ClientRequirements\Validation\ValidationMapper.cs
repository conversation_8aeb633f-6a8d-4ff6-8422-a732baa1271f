﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Validation
{
    public interface IMapJobValidations
    {
        GetJobValidation.Dto.ValidationPhotosDto MapPhotos(CrsValidationResponse crsValidation,
            List<JobAreaMaterial> jobAreaMaterials,
            List<MediaMetadata> jobPhotos,
            IReadOnlyDictionary<Guid, string> jobAreaRoomZones,
            IReadOnlyDictionary<Guid, int> jobVisits,
            IReadOnlyCollection<ArtifactTypeDto> artifactTypes,
            string timeZone,
            ref int idx);

        GetJobValidation.Dto.ValidationFormsDto MapForms(CrsValidationResponse crsValidation, 
            List<MediaMetadata> jobForms, 
            bool filterNonRequired, 
            string timeZone,
            ref int idx, 
            bool initialRequirementsMustBeClientRequired, 
            Dictionary<Guid, bool> formTemplatesOutForSignatureMap = null);

        GetJobValidation.Dto.ValidationClientNotificationsDto MapClientNotifications(CrsValidationResponse crsValidation,
            IReadOnlyDictionary<Guid, int> jobVisits, IReadOnlyDictionary<Guid, string> zones, ref int idx);

        GetJobValidation.Dto.ValidationMiscellaneousDto MapMiscellaneous(CrsValidationResponse crsValidation,
            IReadOnlyDictionary<Guid, string> jobAreaRoomZones, IReadOnlyDictionary<Guid, Guid> jobAreaIdRoomIdDictionary, ref int idx);

        GetJobValidation.Dto.ValidationEstimaticsDto MapEstimatics(Guid jobId, CrsValidationResponse crsValidation, ICollection<LineItem> lineItems, ref int idx);
    }

    public class ValidationMapper : IMapJobValidations
    {
        private readonly ILogger<ValidationMapper> _logger;
        private static readonly List<int> IgnoredCrsValidationStatusIds = new List<int> {0, 3}; // Inactive & Error

        public ValidationMapper(ILogger<ValidationMapper> logger)
        {
            _logger = logger;
        }

        private static GetJobValidation.Dto.ValidationItemDto MapEstimatic(CrsValidationEstimaticRequirement crsEstimaticsRequirement, ref int idx)
        {
            var validationItem = new GetJobValidation.Dto.ValidationItemDto()
            {
                Name = crsEstimaticsRequirement.Name,
                Status = MapStatus(crsEstimaticsRequirement.ValidationStatus),
                AllowBypass = crsEstimaticsRequirement.BypassAllowed,
                AllowAcknowledgement = crsEstimaticsRequirement.ValidationEffect?.ToLower() == "prompt",
                Description = crsEstimaticsRequirement.Description,
                Notification = crsEstimaticsRequirement.Notification,
                RuleId = crsEstimaticsRequirement.RuleId,
                Id = idx,
                RequiredForInitialUpload = crsEstimaticsRequirement.RequiredForInitialUpload
            };

            foreach (var (key, value) in crsEstimaticsRequirement.RuleParameters)
                validationItem.RuleParameters.Add(new KeyValuePair<string, string>(key, value));

            if (validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Active)
                validationItem.Description = string.IsNullOrEmpty(crsEstimaticsRequirement.FailMessage)
                    ? crsEstimaticsRequirement.Description
                    : crsEstimaticsRequirement.FailMessage;

            validationItem.ValidationStatus = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Active
                ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

            return validationItem;
        }

        public GetJobValidation.Dto.ValidationEstimaticsDto MapEstimatics(Guid jobId, CrsValidationResponse crsValidation, ICollection<LineItem> lineItems, ref int idx)
        {
            var estimatics = new GetJobValidation.Dto.ValidationEstimaticsDto
            {
                Requirements = new List<GetJobValidation.Dto.ValidationItemDto>()
            };

            foreach (var clientReq in crsValidation.Result.EstimaticsRequirements)
            {

                if (IgnoredCrsValidationStatusIds.Contains(clientReq.ValidationStatus))
                    continue;

                var validationItem = MapEstimatic(clientReq, ref idx);

                if (clientReq.Estimatics == null || clientReq.Estimatics.Count == 0)
                {
                    estimatics.Requirements.Add(validationItem);
                    idx++;
                    continue;
                }

                foreach (var estimatic in clientReq.Estimatics)
                {
                    var clone = new GetJobValidation.Dto.ValidationItemDto(validationItem)
                    {
                        JobDiaryEntryId = estimatic.JobDiaryEntryId,
                        Id = idx
                    };

                    if (!string.IsNullOrEmpty(estimatic.CategoryId) && !string.IsNullOrEmpty(estimatic.SelectorId))
                    {
                        var lineItem = lineItems.FirstOrDefault(li =>
                            li.ActivityCode == estimatic.ActivityCode && li.Category == estimatic.CategoryId &&
                            li.Code == estimatic.SelectorId && li.IsDeleted == false);
                        
                        if (lineItem != null)
                        {
                            clone.ScopeLineItemId = lineItem.Id;
                            clone.ScopeLineItemInfo = 
                                $"{lineItem.Category} {lineItem.Code} {lineItem.ActivityCode} - {lineItem.Description} QTY: {lineItem.Quantity}";
                            clone.Name = 
                                $"{validationItem.Name}: {lineItem.Category}{lineItem.Code}{lineItem.ActivityCode}";
                        }
                        else
                        {
                            clone.ScopeLineItemInfo = 
                                $"{estimatic.CategoryId} {estimatic.SelectorId} {estimatic.ActivityCode}";
                            clone.Name = 
                                $"{validationItem.Name}: {estimatic.CategoryId}{estimatic.SelectorId}{estimatic.ActivityCode}";
                        }
                    }

                    clone.Status = estimatic.Valid ? ValidationRequestMapper.ValidationItemStatus.Complete : ValidationRequestMapper.ValidationItemStatus.Active;
                    clone.ValidationStatus = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Active
                        ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                        : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    if (clone.JobDiaryEntryId.HasValue)
                        clone.Status = ValidationRequestMapper.ValidationItemStatus.Exception;

                    estimatics.Requirements.Add(clone);

                    idx++;
                }
            }
            estimatics.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));

            return estimatics;
        }

        private static ValidationRequestMapper.ValidationItemStatus MapStatus(int crsValidationStatus)
        {
            return crsValidationStatus == 1 ? ValidationRequestMapper.ValidationItemStatus.Complete : ValidationRequestMapper.ValidationItemStatus.Active;
        }

        public GetJobValidation.Dto.ValidationPhotosDto MapPhotos(CrsValidationResponse crsValidation,
                                                List<JobAreaMaterial> jobAreaMaterials,
                                                List<MediaMetadata> jobPhotos,
                                                IReadOnlyDictionary<Guid, string> jobAreaRoomZones,
                                                IReadOnlyDictionary<Guid, int> jobVisits,
                                                IReadOnlyCollection<ArtifactTypeDto> artifactTypes,
                                                string timeZone,
                                                ref int idx)
        {
            const string grpNamePre = "Pre";
            const string grpNamePost = "Post";
            const string grpNameDaily = "Daily";
            const string preConstruction = "Pre-Construction";
            const string preMitigation = "Pre-Mitigation";
            const string dailyDeparture = "Daily Departure";
            const string postMitigation = "Post-Mitigation";

            var preGroup = new GetJobValidation.Dto.ValidationPhotosGroupDto() { Name = preMitigation, GroupType = ValidationRequestMapper.ValidationPhotosGroupType.PreMitigation };
            var dailyGroup = new GetJobValidation.Dto.ValidationPhotosGroupDto() { Name = dailyDeparture, GroupType = ValidationRequestMapper.ValidationPhotosGroupType.PostMitigation };
            var postGroup = new GetJobValidation.Dto.ValidationPhotosGroupDto() { Name = postMitigation, GroupType = ValidationRequestMapper.ValidationPhotosGroupType.PostMitigation };
            var visitGroups = new Dictionary<Guid, GetJobValidation.Dto.ValidationPhotosGroupDto>();

            var photos = new GetJobValidation.Dto.ValidationPhotosDto
            {
                Requirements = new List<GetJobValidation.Dto.ValidationItemDto>(),
                Groups = new List<GetJobValidation.Dto.ValidationPhotosGroupDto>()
            };

            foreach (var crsPhoto in crsValidation.Result.PhotoRequirements)
            {
                if (IgnoredCrsValidationStatusIds.Contains(crsPhoto.ValidationStatus))
                    continue;

                if (crsPhoto.Photos.Count > 0)
                {
                    var validationItemBase = new GetJobValidation.Dto.ValidationItemDto()
                    {
                        Name = crsPhoto.Name,
                        Status = MapStatus(crsPhoto.ValidationStatus),
                        Description = crsPhoto.Description,
                        Notification = crsPhoto.Notification,
                        AllowBypass = crsPhoto.BypassAllowed,
                        AllowAcknowledgement = crsPhoto.ValidationEffect?.ToLower() == "prompt",
                        ValidationEffect = crsPhoto.ValidationEffect,
                        RuleId = crsPhoto.RuleId,
                        RequiredForInitialUpload = crsPhoto.RequiredForInitialUpload,
                        ArtifactTypeId = crsPhoto.ArtifactTypeId
                    };

                    validationItemBase.IsMet = validationItemBase.Status == ValidationRequestMapper.ValidationItemStatus.Complete ||
                       (validationItemBase.AllowBypass &&
                        validationItemBase.Status == ValidationRequestMapper.ValidationItemStatus.Exception);

                    validationItemBase.ValidationStatus = validationItemBase.Status == ValidationRequestMapper.ValidationItemStatus.Active
                        ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                        : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    foreach (var subPhoto in crsPhoto.Photos)
                    {
                        // make a copy of validationItemBase
                        // the intent here is to create a validation item for EACH required photo within a single requirement from CBRE
                        var validationItem = new GetJobValidation.Dto.ValidationItemDto()
                        {
                            Name = validationItemBase.Name,
                            Status = validationItemBase.Status,
                            Description = validationItemBase.Description,
                            Notification = validationItemBase.Notification,
                            AllowBypass = validationItemBase.AllowBypass,
                            AllowAcknowledgement = validationItemBase.ValidationEffect?.ToLower() == "prompt",
                            RuleId = validationItemBase.RuleId,
                            ValidationEffect = validationItemBase.ValidationEffect,
                            Id = idx,
                            RequiredForInitialUpload = validationItemBase.RequiredForInitialUpload,
                            JobDiaryEntryId = validationItemBase.JobDiaryEntryId,
                            JobArtifactId = validationItemBase.JobArtifactId,
                            ArtifactTypeId = validationItemBase.ArtifactTypeId
                        };

                        // add in the pertinent information from the subPhoto object
                        validationItem.JobVisitId = subPhoto.JobVisitId;
                        validationItem.JobAreaId = subPhoto.JobAreaId;
                        validationItem.JobDiaryEntryId = subPhoto.JobDiaryEntryId;
                        validationItem.JobArtifactId = subPhoto.JobArtifactId;

                        if (validationItem.JobArtifactId.HasValue)
                            validationItem.JobArtifactDate = jobPhotos
                                .FirstOrDefault(p => p.Id == validationItem.JobArtifactId)?.CreatedDate
                                .ConvertDateFromUtc(timeZone);

                        validationItem.JobAreaMaterialId = subPhoto.JobAreaMaterialId;

                        validationItem.Status = !validationItem.JobArtifactId.HasValue && validationItem.JobDiaryEntryId.HasValue && validationItemBase.AllowBypass
                            ? validationItem.Status = ValidationRequestMapper.ValidationItemStatus.Exception
                            : validationItem.Status = subPhoto.Valid ? ValidationRequestMapper.ValidationItemStatus.Complete : ValidationRequestMapper.ValidationItemStatus.Active;

                        validationItem.ValidationStatus = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Active
                            ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                            : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                        validationItem.IsMet = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Complete ||
                                                (validationItem.AllowBypass && validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Exception);
                        if (validationItem.IsMet)
                            validationItem.Description = crsPhoto.PassMessage;

                        // determine the group (where it belongs: no group, pre, daily or post)
                        var added = false;
                        //var pre = false;
                        var daily = false;
                        var post = false;

                        if (validationItem.Name.Contains(grpNamePost) && validationItem.RuleId != (int)ValidationRequestMapper.ValidationRuleType.PhotosPostDemolition)
                        {
                            validationItem.GroupName = postGroup.Name;
                            postGroup.Requirements.Add(validationItem);
                            added = true;
                            post = true;
                        }

                        if (!added && validationItem.Name.Contains(preConstruction))
                        {
                            photos.Requirements.Add(validationItem);
                            added = true;
                        }

                        if (!added && validationItem.Name.Contains(grpNamePre))
                        {
                            validationItem.GroupName = preGroup.Name;
                            preGroup.Requirements.Add(validationItem);
                            added = true;
                            //pre = true;
                        }

                        if (!added)
                        {
                            if (validationItem.JobVisitId.HasValue)
                            {
                                if (jobVisits.ContainsKey(validationItem.JobVisitId.Value))
                                {
                                    var visitGroup = new GetJobValidation.Dto.ValidationPhotosGroupDto()
                                    {
                                        Name = $"Visit {jobVisits[validationItem.JobVisitId.Value] + 1}",
                                        GroupType = ValidationRequestMapper.ValidationPhotosGroupType.Visit
                                    };

                                    if (visitGroups.ContainsKey(validationItem.JobVisitId.Value))
                                    {
                                        visitGroup = visitGroups[validationItem.JobVisitId.Value];
                                    }
                                    else
                                    {
                                        visitGroups[validationItem.JobVisitId.Value] = visitGroup;
                                    }

                                    if (validationItem.ArtifactTypeId.HasValue)
                                    {
                                        var artifactType = jobPhotos.FirstOrDefault(x => validationItem.ArtifactTypeId != null && x.Id == validationItem.ArtifactTypeId.Value);
                                        if (artifactType != null 
                                            && validationItem.RuleId != (int)ValidationRequestMapper.ValidationRuleType.PhotosPostDemolition 
                                            && validationItem.RuleId != (int)ValidationRequestMapper.ValidationRuleType.PhotosDumpstersDebris 
                                            && validationItem.RuleId != (int)ValidationRequestMapper.ValidationRuleType.PhotosCabinetsCounterTops)
                                        {
                                            validationItem.Name = artifactType.Name;
                                        }
                                    }
                                    validationItem.GroupName = visitGroup.Name;
                                    visitGroup.Requirements.Add(validationItem);
                                    added = true;
                                }
                            }
                        }

                        if (!added)
                            photos.Requirements.Add(validationItem);

                        if (validationItem.JobAreaId.HasValue)
                        {
                            // we'll be modifying the name and description
                            if (jobAreaRoomZones.ContainsKey(validationItem.JobAreaId.Value))
                            {
                                validationItem.Name = jobAreaRoomZones[validationItem.JobAreaId.Value];
                                if (validationItem.ArtifactTypeId.HasValue)
                                {
                                    var artifactType = artifactTypes.FirstOrDefault(x => validationItem.ArtifactTypeId != null && x.Id == validationItem.ArtifactTypeId.Value);
                                    if (artifactType != null && artifactType.Name != "Pre-Mitigation" && artifactType.Name != "Post Mitigation")
                                        validationItem.Name = $"{artifactType.Name} - {validationItem.Name}";
                                }
                            }
                            else
                            {
                                _logger.LogInformation("jobAreaRoomZones did not contain: " + validationItem.JobAreaId.Value);
                            }

                            if (daily || post)
                            {
                                if (jobVisits.ContainsKey(validationItem.JobVisitId.Value))
                                {
                                    validationItem.Notification = $"End of Visit {jobVisits[validationItem.JobVisitId.Value] + 1}";
                                }
                                else
                                {
                                    _logger.LogInformation("jobVisits did not contain: " + validationItem.JobVisitId.Value);
                                }
                            }
                            else
                            {
                                if (validationItem.JobVisitId.HasValue)
                                {
                                    if (jobVisits.ContainsKey(validationItem.JobVisitId.Value))
                                    {
                                        validationItem.Notification = $"Initial Inspection: Visit {jobVisits[validationItem.JobVisitId.Value] + 1}";
                                    }
                                    else
                                    {
                                        _logger.LogInformation("jobVisits did not contain: " + validationItem.JobVisitId.Value);
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (validationItem.JobAreaMaterialId.HasValue)
                            {
                                var jobAreaMaterial = jobAreaMaterials.FirstOrDefault(jm => jm.Id == validationItem.JobAreaMaterialId.Value);

                                var materialName = jobAreaMaterial?.JobMaterial?.Name;
                                var roomName = jobAreaMaterial?.JobArea?.Name;

                                if (validationItem.ArtifactTypeId.HasValue && !string.IsNullOrEmpty(materialName))
                                {
                                    var artifactType = jobPhotos.FirstOrDefault(x => x.ArtifactTypeId == validationItem.ArtifactTypeId.Value);
                                    if (artifactType != null)
                                        validationItem.Name = $"{validationItem.Name} - {roomName} - {materialName}";
                                }
                            }
                        }
                        idx++;
                    }
                }
                else
                {
                    var validationItem = new GetJobValidation.Dto.ValidationItemDto()
                    {
                        Name = crsPhoto.Name,
                        ArtifactTypeId = crsPhoto.ArtifactTypeId,
                        Status = MapStatus(crsPhoto.ValidationStatus),
                        Description = crsPhoto.Description,
                        Notification = crsPhoto.Notification,
                        AllowBypass = crsPhoto.BypassAllowed,
                        AllowAcknowledgement = crsPhoto.ValidationEffect?.ToLower() == "prompt",
                        RuleId = crsPhoto.RuleId,
                        ValidationEffect = crsPhoto.ValidationEffect,
                        Id = idx,
                        RequiredForInitialUpload = crsPhoto.RequiredForInitialUpload
                    };

                    validationItem.IsMet = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Complete ||
                                           (validationItem.AllowBypass && validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Exception);
                    if (validationItem.IsMet)
                        validationItem.Description = crsPhoto.PassMessage;

                    validationItem.ValidationStatus = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Active
                        ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                        : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    // determine the group (where it belongs: no group, pre, daily or post)
                    if (validationItem.Name.Contains(grpNamePost))
                    {
                        validationItem.GroupName = postGroup.Name;
                        postGroup.Requirements.Add(validationItem);
                    }
                    else if (validationItem.Name.Contains(preConstruction))
                    {
                        photos.Requirements.Add(validationItem);
                    }
                    else if (validationItem.Name.Contains(grpNamePre))
                    {
                        validationItem.GroupName = preGroup.Name;
                        preGroup.Requirements.Add(validationItem);
                    }
                    else if (validationItem.Name.Contains(grpNameDaily))
                    {
                        validationItem.GroupName = dailyGroup.Name;
                        dailyGroup.Requirements.Add(validationItem);
                    }
                    else
                    {
                        photos.Requirements.Add(validationItem);
                    }
                    idx++;
                }
            }

            // if a group has requirements in it, then add the group
            if (preGroup.Requirements?.Count > 0)
            {
                preGroup = NumberPhotosIfNeeded(preGroup);
                preGroup.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));
                photos.Groups.Add(preGroup);
            }

            if (dailyGroup.Requirements?.Count > 0)
            {
                dailyGroup = NumberPhotosIfNeeded(dailyGroup);
                dailyGroup.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));
                photos.Groups.Add(dailyGroup);
            }

            foreach (var kvp in visitGroups)
            {
                var tmpGroup = NumberPhotosIfNeeded(kvp.Value);
                tmpGroup.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));
                photos.Groups.Add(tmpGroup);
            }
            photos.Groups.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));

            if (postGroup.Requirements?.Count > 0)
            {
                postGroup.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));
                photos.Groups.Add(postGroup);
            }

            photos.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));

            return photos;
        }

        private static GetJobValidation.Dto.ValidationPhotosGroupDto NumberPhotosIfNeeded(GetJobValidation.Dto.ValidationPhotosGroupDto photosGroup)
        {

            var rooms = new Dictionary<string, int>();
            photosGroup.Requirements.ForEach(p =>
            {
                if (!rooms.ContainsKey(p.Name))
                    rooms.Add(p.Name, 0);
                rooms[p.Name] = rooms[p.Name] + 1;
            });

            foreach (var kvp in rooms)
            {
                if (kvp.Value <= 1)
                    continue;

                var index = 1;

                // find each of the matching requirements and change the name
                photosGroup.Requirements.Where(r => r.Name == kvp.Key).ToList().ForEach(r =>
                {
                    r.Name = $"{r.Name} (Photo {index})";
                    index++;
                });
            }

            return photosGroup;
        }

        public GetJobValidation.Dto.ValidationFormsDto MapForms(CrsValidationResponse crsValidation, 
            List<MediaMetadata> jobForms, 
            bool filterNonRequired, 
            string timeZone, 
            ref int idx, 
            bool initialRequirementsMustBeClientRequired,
            Dictionary<Guid, bool> formTemplatesOutForSignatureMap = null)
        {
            var forms = new GetJobValidation.Dto.ValidationFormsDto
            {
                Requirements = new List<GetJobValidation.Dto.ValidationItemDto>()
            };

            if (crsValidation?.Result != null)
            {
                foreach (var crsForm in crsValidation.Result.Forms)
                {
                    if (IgnoredCrsValidationStatusIds.Contains(crsForm.ValidationStatus))
                        continue;

                    if (filterNonRequired && crsForm.Type != "Required")
                        continue;

                    var validationItem = new GetJobValidation.Dto.ValidationItemDto()
                    {
                        Name = crsForm.Name ?? string.Empty,
                        Status = MapStatus(crsForm.ValidationStatus),
                        AllowBypass = false,
                        AllowAcknowledgement = crsForm.ValidationEffect?.ToLower() == "prompt",
                        Description = crsForm.Description ?? string.Empty,
                        Notification = crsForm.Notification ?? string.Empty,
                        FormTemplateEgId = crsForm.FormTemplateId,
                        FormType = crsForm.Type ?? "",
                        JobFormId = crsForm.JobFormId,
                        RelatedFormId = crsForm.RelatedForm,
                        Id = idx,
                        RequiredForInitialUpload = crsForm.RequiredForInitialUpload,
                        IsClientRequirement = crsForm.IsClientRequirement,
                        IsOutForSignature = formTemplatesOutForSignatureMap != null && formTemplatesOutForSignatureMap.ContainsKey(crsForm.FormTemplateId)
                    };

                    if (initialRequirementsMustBeClientRequired)
                        validationItem.RequiredForInitialUpload =
                            validationItem.RequiredForInitialUpload.HasValue &&
                            validationItem.RequiredForInitialUpload.Value &&
                            validationItem.IsClientRequirement == true;

                    validationItem.ValidationStatus = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Active
                        ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                        : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    validationItem.IsMet = validationItem.ValidationStatus == ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    if (string.IsNullOrEmpty(validationItem.Description))
                        validationItem.Description = "This is a required form.";

                    if (validationItem.JobFormId.HasValue)
                        validationItem.JobArtifactDate = jobForms
                            .FirstOrDefault(f => f.FormTemplateId == validationItem.FormTemplateEgId)?.CreatedDate
                            .ConvertDateFromUtc(timeZone);

                    forms.Requirements.Add(validationItem);
                    idx++;
                }
            }
            forms.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));

            return forms;
        }

        private static GetJobValidation.Dto.ValidationItemDto MapClientNotification(CrsValidationClientNotificationRequirement crsRequirement, int idx)
        {
            var validationItem = new GetJobValidation.Dto.ValidationItemDto()
            {
                Name = crsRequirement.Name,
                Status = MapStatus(crsRequirement.ValidationStatus),
                AllowBypass = crsRequirement.BypassAllowed,
                AllowAcknowledgement = crsRequirement.ValidationEffect?.ToLower() == "prompt",
                Description = crsRequirement.Description,
                Notification = crsRequirement.Notification,
                RuleId = crsRequirement.RuleId,
                AskedPerVisit = crsRequirement.AskedPerVisit,
                Id = idx,
                RequiredForInitialUpload = crsRequirement.RequiredForInitialUpload
            };

            validationItem.ValidationStatus = validationItem.Status == ValidationRequestMapper.ValidationItemStatus.Complete
                ? ValidationRequestMapper.ValidationItemValidationStatus.Complete
                : ValidationRequestMapper.ValidationItemValidationStatus.Active;

            return validationItem;
        }

        public GetJobValidation.Dto.ValidationClientNotificationsDto MapClientNotifications(CrsValidationResponse crsValidation,
            IReadOnlyDictionary<Guid, int> jobVisits, IReadOnlyDictionary<Guid, string> zones, ref int idx)
        {
            var clientNotifications = new GetJobValidation.Dto.ValidationClientNotificationsDto
            {
                Requirements = new List<GetJobValidation.Dto.ValidationItemDto>()
            };

            foreach (var clientReq in crsValidation.Result.ClientNotificationRequirements)
            {

                if (IgnoredCrsValidationStatusIds.Contains(clientReq.ValidationStatus))
                    continue;

                var validationItem = MapClientNotification(clientReq, idx);

                if (clientReq.ClientNotifications == null || clientReq.ClientNotifications.Count == 0)
                {
                    clientNotifications.Requirements.Add(validationItem);
                    idx++;
                    continue;
                }

                foreach (var clientNotification in clientReq.ClientNotifications)
                {
                    var clone = new GetJobValidation.Dto.ValidationItemDto(validationItem)
                    {
                        JobVisitId = clientNotification.JobVisitId,
                        JobAreaId = clientNotification.JobAreaId,
                        JobDiaryEntryId = clientNotification.JobDiaryEntryId,
                        ZoneId = clientNotification.ZoneId,
                        RequiredForInitialUpload = validationItem.RequiredForInitialUpload
                    };

                    if (clientNotification.Valid)
                        clone.Status = ValidationRequestMapper.ValidationItemStatus.Complete;

                    if (clientNotification.ClearedWithException)
                        clone.Status = ValidationRequestMapper.ValidationItemStatus.Exception;

                    clone.ValidationStatus = clone.Status == ValidationRequestMapper.ValidationItemStatus.Active
                        ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                        : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    clone.IsMet = clone.ValidationStatus == ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    if (clone.AskedPerVisit && clone.JobVisitId.HasValue && jobVisits.ContainsKey(clone.JobVisitId.Value))
                        clone.Name = $"{validationItem.Name} - Visit {jobVisits[clone.JobVisitId.Value] + 1}";

                    if (clone.ZoneId.HasValue && zones.ContainsKey(clone.ZoneId.Value))
                        clone.Name = $"{clone.Name} - {zones[clone.ZoneId.Value]}";

                    clientNotifications.Requirements.Add(clone);

                    idx++;
                }
            }

            clientNotifications.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));

            return clientNotifications;
        }

        private GetJobValidation.Dto.ValidationItemDto MapMiscReq(CrsValidationMiscellaneous crsValidationMiscellaneous, int idx)
        {
            var item = new GetJobValidation.Dto.ValidationItemDto()
            {
                Name = crsValidationMiscellaneous.Name,
                Status = MapStatus(crsValidationMiscellaneous.ValidationStatus),
                AllowBypass = crsValidationMiscellaneous.BypassAllowed,
                AllowAcknowledgement = crsValidationMiscellaneous.ValidationEffect?.ToLower() == "prompt",
                ValidationEffect = crsValidationMiscellaneous.ValidationEffect,
                Description = crsValidationMiscellaneous.Description,
                Notification = crsValidationMiscellaneous.Description,
                RuleId = crsValidationMiscellaneous.RuleId,
                Id = idx,
                JobTriStateQuestionId = crsValidationMiscellaneous.JobTriStateQuestionId,
                RequiredForInitialUpload = crsValidationMiscellaneous.RequiredForInitialUpload
            };

            item.IsMet = item.Status != ValidationRequestMapper.ValidationItemStatus.Active;

            switch (item.Status)
            {
                case ValidationRequestMapper.ValidationItemStatus.Active:
                    item.Description = crsValidationMiscellaneous.Description;
                    break;
                case ValidationRequestMapper.ValidationItemStatus.Complete:
                case ValidationRequestMapper.ValidationItemStatus.Exception:
                    item.Description = crsValidationMiscellaneous.PassMessage;
                    break;
                default:
                    item.Description = crsValidationMiscellaneous.Description;
                    break;
            }

            item.OriginalValidationStatus = crsValidationMiscellaneous.ValidationStatus;

            item.ValidationStatus = item.Status == ValidationRequestMapper.ValidationItemStatus.Active
                ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

            return item;
        }

        public GetJobValidation.Dto.ValidationMiscellaneousDto MapMiscellaneous(CrsValidationResponse crsValidation,
            IReadOnlyDictionary<Guid, string> jobAreaRoomZones, IReadOnlyDictionary<Guid, Guid> jobAreaIdRoomIdDictionary, ref int idx)
        {
            var value = new GetJobValidation.Dto.ValidationMiscellaneousDto
            {
                Requirements = new List<GetJobValidation.Dto.ValidationItemDto>()
            };

            foreach (var req in crsValidation.Result.MiscellaneousRequirements)
            {
                if (IgnoredCrsValidationStatusIds.Contains(req.ValidationStatus))
                    continue;

                var validationItem = MapMiscReq(req, idx);

                if (req.MiscItems == null || req.MiscItems.Count == 0)
                {
                    value.Requirements.Add(validationItem);
                    idx++;
                    continue;
                }

                foreach (var miscItem in req.MiscItems)
                {
                    var clone = new GetJobValidation.Dto.ValidationItemDto(validationItem)
                    {
                        JobAreaId = miscItem.JobAreaId,
                        JobVisitId = miscItem.JobVisitId,
                        JobDiaryEntryId = miscItem.JobDiaryEntryId,
                        MaterialType = miscItem.MaterialType,
                        JobArtifactId = miscItem.JobArtifactId,
                        Id = idx,
                        RequiredForInitialUpload = validationItem.RequiredForInitialUpload
                    };

                    if (clone.AllowAcknowledgement == false && miscItem.JobDiaryEntryId.HasValue)
                        clone.Status = ValidationRequestMapper.ValidationItemStatus.Exception;

                    if (miscItem.Valid)
                    {
                        clone.Status = clone.JobDiaryEntryId.HasValue ? ValidationRequestMapper.ValidationItemStatus.Exception : ValidationRequestMapper.ValidationItemStatus.Complete;
                    }

                    clone.ValidationStatus = clone.Status == ValidationRequestMapper.ValidationItemStatus.Active
                        ? ValidationRequestMapper.ValidationItemValidationStatus.Active
                        : ValidationRequestMapper.ValidationItemValidationStatus.Complete;

                    clone.IsMet = clone.Status != ValidationRequestMapper.ValidationItemStatus.Active;
                    if (clone.IsMet)
                        clone.Description = req.PassMessage;

                    if (clone.JobAreaId.HasValue)
                    {
                        // we'll be modifying the name and description
                        if (jobAreaRoomZones.ContainsKey(clone.JobAreaId.Value))
                        {
                            clone.Name = $"{clone.Name}: {jobAreaRoomZones[clone.JobAreaId.Value]}";
                            if (jobAreaIdRoomIdDictionary.ContainsKey(clone.JobAreaId.Value))
                                clone.RoomId = jobAreaIdRoomIdDictionary[clone.JobAreaId.Value];
                        }
                        else
                        {
                            _logger.LogInformation("jobAreaRoomZones did not contain: " + clone.JobAreaId.Value);
                        }
                    }

                    if (!string.IsNullOrEmpty(clone.MaterialType))
                        clone.Name = $"{clone.Name}: {clone.MaterialType}";

                    value.Requirements.Add(clone);
                    idx++;
                }
            }
            value.Requirements.Sort((req1, req2) => string.Compare(req1.Name, req2.Name, StringComparison.Ordinal));
            return value;
        }
    }
}