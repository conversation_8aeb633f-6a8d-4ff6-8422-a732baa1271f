﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.JobDates
{
    public class GetJobDate
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId, Guid jobDateTypeId)
            {
                JobId = jobId;
                JobDateTypeId = jobDateTypeId;
            }

            public Guid JobId { get; }
            public Guid JobDateTypeId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.JobDateTypeId).NotEmpty();
            }
        }

        public class Dto
        {
            public Dto(DateTime? jobDate, Guid? exceptionReasonId)
            {
                JobDate = jobDate;
                ExceptionReasonId = exceptionReasonId;
            }

            public DateTime? JobDate { get; set; }
            public Guid? ExceptionReasonId { get; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _jobDataContext;

            public Handler(JobReadOnlyDataContext jobDataContext)
            {
                _jobDataContext = jobDataContext;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _jobDataContext.Jobs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var jobDate = job.JobDates?.FirstOrDefault(x => x.JobDateTypeId == request.JobDateTypeId);

                if (jobDate == null)
                    throw new ResourceNotFoundException($"Job Date not found: {request.JobDateTypeId}");

                return new Dto(jobDate.Date, jobDate.ExceptionReasonId);
            }
        }
    }
}
