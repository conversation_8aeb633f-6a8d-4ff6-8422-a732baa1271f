﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyZoneReading
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult ZoneResult { get; set; }
            public ProcessEntityResult JobVisitResult { get; set; }
            public ProcessEntityResult JournalNoteWithRFTResult { get; set; }
            public ProcessEntityResult JournalNoteWithoutRFTResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> ZoneIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyZoneReading>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyZoneReading> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyZoneReading> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(ZoneReading));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.ZoneIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var zoneReadingTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedZoneReadingIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(zoneReadingTargetIds, 
                    GetZoneReadingIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<ZoneReading, ResaleZoneReading>(
                    request.ResaleId,
                    zoneReading =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.ZoneResult.FailedEntities.Contains(zoneReading.ZoneId))
                            failedDependencies.Add((nameof(Zone), zoneReading.ZoneId));
                        if (request.JobVisitResult.FailedEntities.Contains(zoneReading.JobVisitId))
                            failedDependencies.Add((nameof(JobVisit), zoneReading.JobVisitId));
                        if (zoneReading.JournalNoteId.HasValue
                            && (request.JournalNoteWithRFTResult.FailedEntities.Contains(zoneReading.JournalNoteId.Value)
                            || request.JournalNoteWithoutRFTResult.FailedEntities.Contains(zoneReading.JournalNoteId.Value)))
                        {
                            failedDependencies.Add((nameof(JournalNote), zoneReading.JournalNoteId.Value));
                        }

                        return failedDependencies;
                    },
                    alreadyCopiedZoneReadingIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.ZoneReadings.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<ZoneReading>> GetSourceEntitiesAsync(List<Guid> zoneIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var zoneReadings = await _context.ZoneReadings
                    .Where(zr => zoneIds.Contains(zr.ZoneId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", zoneReadings.Count);
                return zoneReadings;
            }

            private async Task<List<Guid>> GetZoneReadingIdsAsync(List<Guid?> zoneReadingTargetIds, CancellationToken cancellationToken)
            {
                return await _context.ZoneReadings
                    .Where(x => zoneReadingTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
