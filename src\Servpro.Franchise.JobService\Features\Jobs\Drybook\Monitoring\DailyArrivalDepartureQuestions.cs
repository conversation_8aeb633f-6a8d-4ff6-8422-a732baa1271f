﻿using System;
using System.Collections.Generic;
using System.Collections.Immutable;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public static class DailyArrivalDepartureQuestions
    {
        public static readonly ImmutableHashSet<Guid> DailyArrivalQuestionIds = new HashSet<Guid>
        {
            new Guid("934a038b-ec8d-48e7-80c4-b83ee4f827e1"),
            new Guid("2e1ee5a9-5662-4135-8ef4-828b23a6df63"),
            new Guid("201be6ab-b3e0-49a9-8b4f-243f884e38a0"),
            new Guid("ae1788ad-84be-4ac1-8b44-7946e32b4858"),
            new Guid("f8d0231a-55ed-4b1e-a498-f2b9115192c4"),
            new Guid("7b7be9b5-b6f1-4937-b32c-d901c51e036a"),
            new Guid("61a3147d-4f00-41a6-bf47-80b0682019af"),
            new Guid("694a2dee-2bef-44f2-bc9a-780b21fdc796")
        }.ToImmutableHashSet();

        public static readonly ImmutableHashSet<Guid> DailyDepartureQuestionIds = new HashSet<Guid>
        {
            new Guid("997dbfac-3371-48db-8eba-e9c57666035b"),
            new Guid("358241db-23f8-4cfa-a767-67ff3523ea26"),
            new Guid("c27de0c0-dbc1-452f-afcb-e5ab1e7bc315"),
            new Guid("86d8bdaa-8e05-40d6-8f1e-527793cbe6d9"),
            new Guid("ca872269-d19d-4dd1-a6d4-51d101bed112"),
            new Guid("322ec9d5-a641-49dc-a3e1-cabd37b25221"),
            new Guid("4cd42ba9-fc34-4f3d-81d9-4ac37e1162e1"),
            new Guid("571b2089-6e32-4ce0-a703-97d87c09f25a"),
            new Guid("7888d560-88bf-414c-b489-659113eb1df4"),
            new Guid("9af410c5-92ca-4937-8a4b-1a737d0d90f7"),
            new Guid("d8a370b7-c0bb-408e-a7fc-576b3f049497"),
            new Guid("dc8390ab-590c-4d0d-9d5c-e4a84a814d1d"),
            new Guid("1914f728-c4b1-4642-a5b4-ada742a8b6c5"),
            new Guid("fd081fb7-883f-48d6-aa89-667f7daf7037"), //DoesMoldExceedSF
            new Guid("e838f797-3ffd-49f8-8eba-3a566b9af034") //MoldLessThanSF
        }.ToImmutableHashSet();

        public static readonly ImmutableHashSet<Guid> All = new HashSet<Guid>
        {
            new Guid("1914f728-c4b1-4642-a5b4-ada742a8b6c5"),
            new Guid("201be6ab-b3e0-49a9-8b4f-243f884e38a0"),
            new Guid("2e1ee5a9-5662-4135-8ef4-828b23a6df63"),
            new Guid("322ec9d5-a641-49dc-a3e1-cabd37b25221"),
            new Guid("358241db-23f8-4cfa-a767-67ff3523ea26"),
            new Guid("4cd42ba9-fc34-4f3d-81d9-4ac37e1162e1"),
            new Guid("571b2089-6e32-4ce0-a703-97d87c09f25a"),
            new Guid("61a3147d-4f00-41a6-bf47-80b0682019af"),
            new Guid("694a2dee-2bef-44f2-bc9a-780b21fdc796"),
            new Guid("7888d560-88bf-414c-b489-659113eb1df4"),
            new Guid("7b7be9b5-b6f1-4937-b32c-d901c51e036a"),
            new Guid("86d8bdaa-8e05-40d6-8f1e-527793cbe6d9"),
            new Guid("934a038b-ec8d-48e7-80c4-b83ee4f827e1"),
            new Guid("997dbfac-3371-48db-8eba-e9c57666035b"),
            new Guid("9af410c5-92ca-4937-8a4b-1a737d0d90f7"),
            new Guid("ae1788ad-84be-4ac1-8b44-7946e32b4858"),
            new Guid("c27de0c0-dbc1-452f-afcb-e5ab1e7bc315"),
            new Guid("ca872269-d19d-4dd1-a6d4-51d101bed112"),
            new Guid("d8a370b7-c0bb-408e-a7fc-576b3f049497"),
            new Guid("dc8390ab-590c-4d0d-9d5c-e4a84a814d1d"),
            new Guid("f8d0231a-55ed-4b1e-a498-f2b9115192c4"),
            new Guid("fd081fb7-883f-48d6-aa89-667f7daf7037"), //DoesMoldExceedSF
            new Guid("e838f797-3ffd-49f8-8eba-3a566b9af034") //MoldLessThanSF
        }.ToImmutableHashSet();
    }
}