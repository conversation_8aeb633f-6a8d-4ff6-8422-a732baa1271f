﻿using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class SaveMaterialReadings
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
            public IEnumerable<MaterialReadingDto> MaterialReadings { get; set; }

            public class MaterialReadingDto
            {
                public Guid JobAreaId { get; set; }
                public Guid JobMaterialId { get; set; }
                public int? Reading { get; set; }
                public bool IsRemoved { get; set; }
                public Guid? ReadingId { get; set; }
            }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.JobVisitId).NotEmpty();
                RuleForEach(x => x.MaterialReadings)
                    .Must(y => y.JobAreaId != Guid.Empty)
                    .Must(y => y.JobMaterialId != Guid.Empty);
            }
        }
        #endregion

        #region Command
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupService;
            private readonly ILogger<SaveMaterialReadings> _logger;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupService,
                ILogger<SaveMaterialReadings> logger)
            {
                _context = context;
                _userInfo = userInfo;
                _lookupService = lookupService;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Begin handler with: {@request}", request);

                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.JobAreaMaterials)
                        .ThenInclude(x => x.JobAreaMaterialReadings)
                        .ThenInclude(x => x.JobVisit)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobMaterials = await _context.JobMaterials.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobAreas = await _context.JobAreas.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JournalNotes = await _context.JournalNote.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var validationErrors = ValidateRequest(request, job);
                if (validationErrors.Any())
                    throw new ValidationException(validationErrors);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var lookups = await _lookupService.GetLookupsAsync(cancellationToken);
                var readingTypeLookups = lookups.MaterialReadingTypes.ToDictionary(x => x.Id, x => x.Name);
                var journalNoteType = lookups.JournalNoteTypes.FirstOrDefault(x => x.Id == JournalNoteTypes.RecordOfFixPlacementReadingsDeletion);
                var jobVisit = job.JobVisits.First(x => x.Id == request.JobVisitId);
                var jobAreaLookups = job.JobAreas.ToDictionary(x => x.Id);
                var jobMaterialLookups = job.JobMaterials.ToDictionary(x => x.Id);
                var visitNumberLookups = job.JobVisits
                    .OrderBy(x => x.Date)
                    .Select((x, index) => new { x.Id, index = index + 1 })
                    .ToDictionary(x => x.Id, x => x.index);
                foreach (var reading in request.MaterialReadings)
                {
                    var jobArea = jobAreaLookups[reading.JobAreaId];
                    var jobMaterial = jobMaterialLookups[reading.JobMaterialId];
                    var jobAreaMaterial = AddOrUpdateJobAreaMaterial(jobArea, jobMaterial, jobVisit, userInfo);
                    var jobAreaMaterialReading = AddorUpdateJobAreaMaterialReading(
                        jobAreaMaterial, jobVisit, jobMaterial, reading.Reading, reading.ReadingId, userInfo);

                    if (reading.IsRemoved)
                    {
                        RemoveJobAreaMaterial(job, jobArea, jobAreaMaterial, jobMaterial, jobVisit, journalNoteType, readingTypeLookups, visitNumberLookups, userInfo);
                    }
                    // if the goal was met on this visit then
                    // set the GoalMetOnJobVisit property
                    if (IsGoalMet(jobMaterial, jobAreaMaterialReading))
                    {
                        jobAreaMaterial.GoalMetOnJobVisit = jobVisit;
                        jobAreaMaterial.ModifiedBy = userInfo.Username;
                        jobAreaMaterial.ModifiedDate = DateTime.UtcNow;
                        var futureReadings = jobAreaMaterial.JobAreaMaterialReadings
                            .Where(x => x.JobVisit.Date > jobVisit.Date)
                            .ToList();
                        foreach (var futureReading in futureReadings)
                        {
                            _logger.LogDebug("Deleting Material reading: {futureReading}", futureReading);
                            jobAreaMaterial.JobAreaMaterialReadings.Remove(futureReading);
                        }
                    }
                    // if the goal is no longer met on this visit then
                    // clear out the visit
                    else if (jobAreaMaterial.GoalMetOnJobVisitId == jobVisit.Id)
                    {
                        jobAreaMaterial.GoalMetOnJobVisitId = null;
                    }
                }

                _logger.LogDebug("Handler Completed Successfully");

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            void RemoveJobAreaMaterial(
                Job job,
                JobArea jobArea,
                JobAreaMaterial jobAreaMaterial,
                JobMaterial jobMaterial,
                JobVisit jobVisit,
                JournalNoteTypesDto journalNoteType,
                IDictionary<Guid, string> readingTypeLookups,
                IDictionary<Guid, int> visitNumberLookups,
                UserInfo userInfo)
            {
                jobAreaMaterial.RemovedOnJobVisit = jobVisit;
                jobAreaMaterial.ModifiedBy = userInfo.Username;
                jobAreaMaterial.ModifiedDate = DateTime.UtcNow;
                var futureReadings = jobAreaMaterial.JobAreaMaterialReadings
                    .Where(x => x.JobVisit.Date > jobVisit.Date)
                    .ToList();

                // remove future readings
                foreach (var reading in futureReadings)
                {
                    _logger.LogDebug("Deleting Material reading: {reading}", reading);
                    jobAreaMaterial.JobAreaMaterialReadings.Remove(reading);
                }

                var futureReadingsStr = futureReadings
                    .Select(x => new
                    {
                        MaterialName = jobMaterial.Name,
                        Reading = x.Value,
                        ReadingType = readingTypeLookups[x.MaterialReadingTypeId],
                        VisitNumber = visitNumberLookups[x.JobVisitId]
                    })
                    .OrderBy(x => x.VisitNumber)
                    .Select(x => $"\tVisit Number {x.VisitNumber} - {x.MaterialName}, {x.ReadingType} reading: {x.Reading}");
                var body = futureReadings.Any()
                    ? $"Removed Readings{string.Concat(futureReadingsStr)}"
                    : $"Removed Readings - {jobMaterial.Name}";

                job.JournalNotes.Add(new JournalNote
                {
                    ActionDate = DateTime.UtcNow,
                    TypeId = JournalNoteTypes.RecordOfFixPlacementReadingsDeletion,
                    Subject = $"{jobArea.Name} - Removed material from room",
                    Note = body,
                    CategoryId = journalNoteType.JournalNoteCategoryId,
                    VisibilityId = journalNoteType.DefaultDiaryEntryVisibilityId,
                    Author = userInfo.Name,
                    CreatedBy = userInfo.Username
                });
            }

            /// <summary>
            /// Gets JobAreaMaterial for the given jobArea/jobMaterial
            /// if the jobAreaMaterial doesn't exist it creates it and
            /// adds it to the jobArea.
            /// </summary>
            /// <param name="jobArea">JobArea on which to look for JobAreaMaterial</param>
            /// <param name="jobMaterial">JobMaterial to find/add</param>
            /// <param name="beginJobVisit">JobVisit on which the material was added to jobArea</param>
            /// <param name="userInfo">User information</param>
            /// <returns>Existing JobAreaMaterial or newly created one</returns>
            JobAreaMaterial AddOrUpdateJobAreaMaterial(
                JobArea jobArea,
                JobMaterial jobMaterial,
                JobVisit beginJobVisit,
                UserInfo userInfo)
            {
                var jobAreaMaterial = jobArea.JobAreaMaterials.FirstOrDefault(x => x.JobAreaId == jobArea.Id && x.JobMaterial.Id == jobMaterial.Id);
                // if jobAreaMaterial doesn't exist then we need to create it
                if (jobAreaMaterial is null)
                {
                    jobAreaMaterial = new JobAreaMaterial
                    {
                        Id = Guid.NewGuid(),
                        JobMaterial = jobMaterial,
                        JobArea = jobArea,
                        BeginJobVisit = beginJobVisit,
                        CreatedBy = userInfo.Username
                    };
                    jobArea.JobAreaMaterials.Add(jobAreaMaterial);
                }
                return jobAreaMaterial;
            }

            /// <summary>
            /// Adds or updates existing reading for the given jobAreaMaterial/Visit
            /// </summary>
            /// <param name="jobAreaMaterial">JobAreaMaterial</param>
            /// <param name="visit">JobVisit for which to add/update reading</param>
            /// <param name="jobMaterial">JobMaterial</param>
            /// <param name="reading">Reading value</param>
            /// <param name="userInfo">User information</param>
            /// <returns></returns>
            JobAreaMaterialReading AddorUpdateJobAreaMaterialReading(
                JobAreaMaterial jobAreaMaterial,
                JobVisit visit,
                JobMaterial jobMaterial,
                int? reading,
                Guid? readingId,
                UserInfo userInfo)
            {
                // find the reading for this visit
                JobAreaMaterialReading jobAreaMaterialReading;

                if (readingId.HasValue)
                {
                    jobAreaMaterialReading = jobAreaMaterial.JobAreaMaterialReadings
                        .FirstOrDefault(x => x.Id == readingId.Value);
                }
                else
                {
                    jobAreaMaterialReading = jobAreaMaterial.JobAreaMaterialReadings
                        .FirstOrDefault(x => x.JobVisitId == visit.Id
                                          && x.JobAreaMaterial.JobMaterial.Id == jobMaterial.Id
                                          && x.JobAreaMaterial.Id == jobAreaMaterial.Id);
                }

                // no reading was found so we need to create one
                if (jobAreaMaterialReading is null)
                {
                    jobAreaMaterialReading = new JobAreaMaterialReading
                    {
                        JobVisit = visit,
                        JobAreaMaterial = jobAreaMaterial,
                        MaterialReadingTypeId = jobMaterial.MaterialReadingTypeId,
                        CreatedBy = userInfo.Username,
                        Value = reading
                    };
                    jobAreaMaterial.JobAreaMaterialReadings.Add(jobAreaMaterialReading);
                }
                else
                {
                    jobAreaMaterialReading.Value = reading;
                    jobAreaMaterialReading.ModifiedBy = userInfo.Username;
                    jobAreaMaterialReading.ModifiedDate = DateTime.UtcNow;
                }
                return jobAreaMaterialReading;
            }

            private bool IsGoalMet(JobMaterial jobMaterial, JobAreaMaterialReading jobAreaMaterialReading)
            {
                var readingGoal = (double)jobMaterial.Goal;
                if (jobMaterial.MaterialReadingTypeId == MaterialReadingTypes.Percentage)
                {
                    readingGoal *= 1.04;
                }
                return jobAreaMaterialReading.Value.HasValue
                    && jobAreaMaterialReading.Value <= readingGoal;

            }

            /// <summary>
            /// Validates JobVisit and JobAreaId/JobMaterialId for each reading
            /// </summary>
            /// <param name="request">Request to validate</param>
            /// <param name="job">Job associated to request</param>
            /// <returns>Collection of validation errors</returns>
            IEnumerable<ValidationFailure> ValidateRequest(Command request, Job job)
            {
                if (!job.JobVisits.Any(x => x.Id == request.JobVisitId))
                    yield return new ValidationFailure(
                        nameof(Command.JobVisitId),
                        "JobVisit not found", request.JobVisitId);

                var jobAreaLookups = job.JobAreas.Select(x => x.Id).ToHashSet();
                var jobMaterialsLookups = job.JobMaterials.Select(x => x.Id).ToHashSet();
                foreach (var reading in request.MaterialReadings)
                {
                    if (!jobAreaLookups.Contains(reading.JobAreaId))
                        yield return new ValidationFailure(
                            nameof(Command.MaterialReadingDto.JobAreaId),
                            "JobArea not found", reading.JobAreaId);

                    if (!jobMaterialsLookups.Contains(reading.JobMaterialId))
                        yield return new ValidationFailure(
                            nameof(Command.MaterialReadingDto.JobMaterialId),
                            "JobMaterial not found", reading.JobMaterialId);
                }
            }
        }
        #endregion
    }
}
