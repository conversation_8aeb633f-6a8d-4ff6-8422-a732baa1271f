﻿using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;

namespace Servpro.Franchise.JobService
{
    public static class ClaimsPrincipalExtensions
    {
        /// <summary>
        /// Gets the username for the user
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>Username</returns>
        public static string Username(this ClaimsPrincipal user)
        {
            var username = user.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
            if (username == null)
                return username;
            if (username.Contains("@"))
            {
                var parts = username.Split('@');
                var domain = parts.Last();
                var userId = parts.First();
                return $"{domain}\\{userId}";
            }
            return username;
        }

        /// <summary>
        /// Gets franchise set id for the user
        /// </summary>
        /// <param name="user">User</param>
        /// <returns>FranchiseSetId</returns>
        public static Guid? FranchiseSetId(this ClaimsPrincipal user)
        {
            var claim = user.Claims.FirstOrDefault(c => c.Type == ServproClaimTypes.FranchiseSetId);
            return claim == null ? (Guid?)null : new Guid(claim.Value);
        }

        /// <summary>
        /// Gets the UserId from the claims for the user
        /// </summary>
        /// <param name="user">Claims</param>
        /// <returns>UserId. Returns empty Guid if not found</returns>
        public static Guid EmployeeId(this ClaimsPrincipal user)
        {
            var idString = user.Claims.FirstOrDefault(c => c.Type == ServproClaimTypes.UserId)?.Value;
            if (Guid.TryParse(idString, out Guid userId))
                return userId;
            return Guid.Empty;
        }

        /// <summary>
        /// Creates a UserInfo 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public static UserInfo UserInfo(this ClaimsPrincipal user)
            => new UserInfo(
                user.EmployeeId(),
                user.FranchiseSetId(),
                user.Username(),
                user.FullName());

        /// <summary>
        /// Gets the name from the user's claims
        /// </summary>
        /// <param name="user">User's claims</param>
        /// <returns>User's name or empty string if not found</returns>
        public static string FullName(this ClaimsPrincipal user)
        {
            var username = user.FindFirst(c => c.Type == ServproClaimTypes.Name)?.Value;
            return username ?? string.Empty;
        }

        public static ICollection<Guid> FeatureSets(this ClaimsPrincipal user)
        {
            var featureSets = user.FindAll("http://www.servpronet.com/identity/claims/featureset").Select(c => new Guid(c.Value.Split("|").First())).ToHashSet();
            return featureSets;
        }

        public static ICollection<string> Roles(this ClaimsPrincipal user)
        {
            var roles = user.FindAll("role").Select(c => c.Value).ToHashSet();
            return roles;
        }
    }
}
