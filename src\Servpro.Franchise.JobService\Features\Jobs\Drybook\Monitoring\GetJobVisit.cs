﻿using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetJobVisit
    {

        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId, Guid jobVisitId)
            {
                JobId = jobId;
                JobVisitId = jobVisitId;
            }
            public Guid JobId { get; }
            public Guid JobVisitId { get; }
        }

        public class Dto
        {
            public Dto(
                Guid jobId,
                Guid jobVisitId,
                string initials,
                DateTime date,
                DateTime? departureDate,
                IEnumerable<QuestionAnswerDto> questionAnswers)
            {
                JobId = jobId;
                JobVisitId = jobVisitId;
                Initials = initials;
                Date = date;
                DepartureDate = departureDate;
                QuestionAnswers = questionAnswers;
            }

            public Guid JobId { get; }
            public Guid JobVisitId { get; }
            public string Initials { get; }
            public DateTime Date { get; }
            public DateTime? DepartureDate { get; }
            public IEnumerable<QuestionAnswerDto> QuestionAnswers { get; }

            public class QuestionAnswerDto
            {
                public QuestionAnswerDto(
                    Guid questionId,
                    bool? answer,
                    Guid? journalNoteId)
                {
                    QuestionId = questionId;
                    Answer = answer;
                    JournalNoteId = journalNoteId;
                }

                public Guid QuestionId { get; }
                public bool? Answer { get; }
                public Guid? JournalNoteId { get; }
            }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await GetJobAsync(request.JobId, userInfo.FranchiseSetId.Value, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.Tasks = await _context.Tasks
                                             .Include(x => x.JournalNotes)
                                             .AsNoTracking()
                                             .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var jobVisit = job.JobVisits.FirstOrDefault(j => j.Id == request.JobVisitId);

                if (jobVisit is null)
                    throw new ResourceNotFoundException($"Job Visit not found: {request.JobVisitId}");

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                var jobAnswerJournalNoteIds = job.Tasks
                    .Where(x => x.JobTriStateQuestionId.HasValue
                        && !x.JobVisitId.HasValue && x.JournalNotes.Any())
                    .GroupBy(x => x.JobTriStateQuestionId.Value)
                    .ToDictionary(x => x.Key, x => x.First().JournalNotes.First().Id);

                // job visit questions that are asked not asked per visit
                var jobJournalNoteIds = lookups.JobTriStateQuestions
                    .Where(x => DailyArrivalDepartureQuestions.All.Contains(x.Id)
                        && !x.IsAskedPerVisit && jobAnswerJournalNoteIds.ContainsKey(x.Id))
                    .ToDictionary(x => x.Id, x => jobAnswerJournalNoteIds[x.Id]);

                return Map(jobVisit, job.JobTriStateAnswers, jobJournalNoteIds);
            }

            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                return await _context.Jobs
                    .Include(x => x.JobTriStateAnswers)
                    .Include(x => x.JobVisits)
                        .ThenInclude(x => x.JobVisitTriStateAnswers)
                    .Include(x => x.JobVisits)
                        .ThenInclude(x => x.Tasks)
                        .ThenInclude(x => x.JournalNotes)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == jobId && x.FranchiseSetId == franchiseSetId, cancellationToken);
            }

            private Dto Map(
                JobVisit jobVisit,
                IEnumerable<JobTriStateAnswer> jobTriStateAnswers,
                Dictionary<Guid, Guid> jobJournalNoteIds)
            {
                var visitJournalNoteIds = GetVisitJournalNoteIds(jobVisit);

                var answers = MapQuestionAnswer(
                    jobVisit.JobVisitTriStateAnswers,
                    jobTriStateAnswers,
                    visitJournalNoteIds,
                    jobJournalNoteIds);

                return new Dto(
                    jobVisit.JobId,
                    jobVisit.Id,
                    jobVisit.EmployeeInitials,
                    jobVisit.Date,
                    jobVisit.DepartureDate,
                    answers);
            }

            private Dictionary<Guid, Guid> GetVisitJournalNoteIds(JobVisit jobVisit)
            {
                return jobVisit.Tasks
                    .Where(x => x.JobTriStateQuestionId.HasValue && x.JournalNotes.Any())
                    .GroupBy(x => x.JobTriStateQuestionId.Value)
                    .ToDictionary(x => x.Key, x => x.First().JournalNotes.First().Id);
            }

            private IEnumerable<Dto.QuestionAnswerDto> MapQuestionAnswer(
                IEnumerable<JobVisitTriStateAnswer> jobVisitTriStateAnswers,
                IEnumerable<JobTriStateAnswer> jobTriStateAnswers,
                Dictionary<Guid, Guid> visitJournalNoteIds,
                Dictionary<Guid, Guid> jobJournalNoteIds)
            {
                List<Dto.QuestionAnswerDto> result = new List<Dto.QuestionAnswerDto>();
                foreach (var answer in jobTriStateAnswers.Where(jtsa => !jobJournalNoteIds.ContainsKey(jtsa.JobTriStateQuestionId)))
                {
                    var visitAnswer = result.FirstOrDefault(qa => qa.QuestionId == answer.JobTriStateQuestionId);
                    var answerToUpsert = new Dto.QuestionAnswerDto(answer.JobTriStateQuestionId, answer?.Answer, null);
                    if (visitAnswer != null)
                    {
                        result.RemoveAll(qa => qa.QuestionId == answer.JobTriStateQuestionId);
                    }
                    result.Add(answerToUpsert);
                }

                foreach (var questionId in jobJournalNoteIds)
                {
                    var answer = jobTriStateAnswers.FirstOrDefault(x => x.JobTriStateQuestionId == questionId.Key);
                    var visitAnswer = result.FirstOrDefault(qa => qa.QuestionId == answer.JobTriStateQuestionId);
                    var answerToUpsert = new Dto.QuestionAnswerDto(questionId.Key, answer?.Answer, questionId.Value);
                    if (visitAnswer != null)
                    {
                        result.RemoveAll(qa => qa.QuestionId == answer.JobTriStateQuestionId);
                    }
                    result.Add(answerToUpsert);
                }

                foreach (var answer in jobVisitTriStateAnswers.Where(x => !jobJournalNoteIds.ContainsKey(x.JobTriStateQuestionId)))
                {
                    var noteId = visitJournalNoteIds.GetOrDefault(answer.JobTriStateQuestionId);
                    var visitAnswer = result.FirstOrDefault(qa => qa.QuestionId == answer.JobTriStateQuestionId);
                    var answerToUpsert = new Dto.QuestionAnswerDto(
                        answer.JobTriStateQuestionId,
                        answer.Answer,
                        noteId == Guid.Empty ? (Guid?)null : noteId);
                    if (visitAnswer != null)
                    {
                        result.RemoveAll(qa => qa.QuestionId == answer.JobTriStateQuestionId);
                    }
                    result.Add(answerToUpsert);
                }

                return result;
            }
        }
    }
}