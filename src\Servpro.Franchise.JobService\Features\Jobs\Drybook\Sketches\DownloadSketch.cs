﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Sketches
{
    public class DownloadSketch
    {
        public class Query : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid Id { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.Id).NotEmpty();
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(JobReadOnlyDataContext db, IConfiguration config, IAmazonS3 clientS3)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
            }

            public async Task<Dto> Handle(Query request,
                CancellationToken cancellationToken)
            {
                    var jobSketch = await _db.JobSketch
                                            .Include(c => c.MediaMetadata)
                                            .AsNoTracking()
                                            .Where(x => (x.JobId == request.JobId ||
                                                        x.MediaMetadata.JobId == request.JobId) &&
                                                        x.MediaMetadata.FranchiseSetId == request.FranchiseSetId &&
                                                        x.MediaMetadata.MediaTypeId == MediaTypes.Sketch &&
                                                        (x.Id == request.Id || x.MediaMetadata.Id == request.Id) &&
                                                        !x.MediaMetadata.IsDeleted)
                                            .FirstOrDefaultAsync(cancellationToken);

                if (jobSketch is null)
                    throw new ResourceNotFoundException("Job sketch not found");

                return GetPreSignedUrl(jobSketch.MediaMetadata);           
            }

            private Dto GetPreSignedUrl(MediaMetadata metadata)
            {
                var key = metadata.MediaPath;
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = !metadata.BucketName.IsNullOrWhiteSpace() ?metadata.BucketName : _config[S3MediaBucketNameKey],
                    Key = key,
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };
                var preSignedUrlDto = new Dto
                {
                    Id = metadata.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = metadata.Name
                };

                return preSignedUrlDto;
            }
        }
    }
}
