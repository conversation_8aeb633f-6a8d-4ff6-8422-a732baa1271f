﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models.Customization.Dtos;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.WipBoards.Customization
{
   
    public class GetMyCustomViewsWipCustomization
    {
        public class Query : IRequest<ICollection<MyCustomViewDto>>
        {
            public Guid FranchiseSetId { get; set; }
            public Guid EmployeeId { get; set; }

        }

        public class Handler : IRequestHandler<Query, ICollection<MyCustomViewDto>>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext db)
            {
                _db = db;
            }

            public async Task<ICollection<MyCustomViewDto>> Handle(Query request,
                CancellationToken cancellationToken)
            {

                return await _db.MyCustomViews
                   .Where(u => u.EmployeeId == request.EmployeeId)
                   .OrderBy(c => c.Sequence)
                   .AsNoTracking()
                   .Select(a => new MyCustomViewDto()
                   {
                       Id = a.Id,
                       ViewName = a.ViewName,
                       Sequence = a.Sequence,
                       Data = a.Data
                   }).ToListAsync(cancellationToken);
            }
        }
    }
}
