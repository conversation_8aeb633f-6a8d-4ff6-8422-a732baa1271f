# Job Merge 2.0

## Overview:

1. A _WorkCenter 2.0 User_ can select a project which has _Merge Candidates_ on the _WorkCenter 2.0 WIP Board_.
1. Upon making the selection, the user will be presented with a modal which will allow them to choose which parts of the data to merge from the _Source Project_, to the _Target Project_.
1. When the user confirms the job merge, _WorkCenter 2.0 WIP Board_ sends a _Merge Jobs Command_ to the _Franchise Jobs Service_.
1. The [_Merge Jobs Command Handler_](../src/Servpro.Franchise.JobService/Features/Jobs/JobMerge/MergeJobs.cs) will perform the job merge
1. The handler then publishes a _Job Merged Event_ (named JobMergeEvent currently in code.)
1. The Restoration Manager and Corporate Services react to this event by initiating job merges in their respective downstream systems.

```mermaid
graph TD;
wcui(WorkCenter UI 2.0) -- calls Merge Jobs Command --> gw(API Gateway);
gw -- forwards request to --> js(Franchise Job Service)
js -- merges jobs internally --> js
js -. publishes Job Merged Event .-> sns(["franchise-system-events<br/>[SNS Topic]"])
sns -. forwards Job Merged Event to .-> rms(Restoration Manager Service)
rms -. forwards Job Merged Event to .-> rm["Restoration Manager<br>[Third Party Software System]"]
sns -. forwards Job Merged Event to .-> cs(Corporate Service)
cs -- call job merge endpoint --> wc["WorkCenter Phase 2<br>[Legacy Software System]"]
```