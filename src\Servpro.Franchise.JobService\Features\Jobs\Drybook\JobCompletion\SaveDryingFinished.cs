﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using System.Linq;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion
{
    public class SaveDryingFinished
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public bool DryingFinished { get; set; }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILogger<SaveDryingFinished> _logger;

            public Handler(JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo,
                ILogger<SaveDryingFinished> logger)
            {
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
                _userInfo = userInfo;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobVisits)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var dryingCompleteDate = job.GetDate(JobDateTypes.DryingComplete);
                var completeDate = job.GetDate(JobDateTypes.Complete);

                if (!request.DryingFinished)
                {
                    if (job.JobProgress == JobProgress.FinalReview)
                    {
                        job.JobProgress = JobProgress.FinalWalkThrough;
                        job.JobProgressModifiedDate = DateTime.UtcNow;
                        var jobProgressUdpatedEvent = GenerateJobProgressUpdatedEvent(request, userInfo.Username, _sessionIdAccessor.GetCorrelationGuid());
                        _context.OutboxMessages.Add(jobProgressUdpatedEvent);
                    }

                    job.RemoveDate(JobDateTypes.DryingComplete);
                    job.RemoveDate(JobDateTypes.Complete);

                    GenerateJobDateEvent(dryingCompleteDate.HasValue, job.Id, JobDateTypes.DryingComplete, null);
                    GenerateJobDateEvent(completeDate.HasValue, job.Id, JobDateTypes.Complete, null);
                }
                else
                {
                    var lastVisit = job.JobVisits.Any()
                        ? job.JobVisits.Max(x => x.Date)
                        : (DateTime?)null;
                    if (lastVisit.HasValue)
                    {
                        job.SetOrUpdateDate(JobDateTypes.DryingComplete, lastVisit.Value);
                        job.SetOrUpdateDate(JobDateTypes.Complete, lastVisit.Value);

                        GenerateJobDateEvent(dryingCompleteDate.HasValue, job.Id, JobDateTypes.DryingComplete, lastVisit.Value);
                        GenerateJobDateEvent(completeDate.HasValue, job.Id, JobDateTypes.Complete, lastVisit.Value);
                    }
                    else
                    {
                        _logger.LogInformation("Completion dates were not set or updated because the job has no visits.");
                    }
                }

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private OutboxMessage GenerateJobProgressUpdatedEvent(Command request, string userName, Guid correlationId)
            {
                var jobProgressUpdatedDto = new JobProgressUpdatedEvent.JobProgressUpdatedDto()
                {
                    JobId = request.JobId,
                    ModifiedBy = userName,
                    JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)JobProgress.FinalWalkThrough
                };
                var jobProgressUpdatedEvent = new JobProgressUpdatedEvent(jobProgressUpdatedDto, correlationId);
                return new OutboxMessage(jobProgressUpdatedEvent.ToJson(), nameof(JobProgressUpdatedEvent),
                    correlationId, userName);
            }

            private void GenerateJobDateEvent(bool isUpdate, Guid jobId, Guid jobDateTypeId, DateTime? date)
            {
                var user = _userInfo.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                if (isUpdate)
                {
                    var eventDto = new JobDateUpdatedEvent.JobDateUpdatedDto(jobId, jobDateTypeId, date, user.Name);
                    var newEvent = new JobDateUpdatedEvent(eventDto, correlationId);

                    var updateEvent = new OutboxMessage(newEvent.ToJson(), nameof(JobDateUpdatedEvent),
                        correlationId, eventDto.ModifiedBy);
                    _context.OutboxMessages.Add(updateEvent);

                    _logger.LogDebug("Raising JobDateUpdatedEvent: {@newEvent}", newEvent);
                }
                else if(date.HasValue)
                {
                    var eventDto = new JobDateCreatedEvent.JobDateCreatedDto(jobId, jobDateTypeId, date.Value, user.Username);
                    var newEvent = new JobDateCreatedEvent(eventDto, correlationId);

                    var createEvent = new OutboxMessage(newEvent.ToJson(), nameof(JobDateCreatedEvent),
                        correlationId, eventDto.CreatedBy);
                    _context.OutboxMessages.Add(createEvent);

                    _logger.LogDebug("Raising JobDateCreatedEvent: {@newEvent}", newEvent);
                }
            }

        }
        #endregion
    }
}
