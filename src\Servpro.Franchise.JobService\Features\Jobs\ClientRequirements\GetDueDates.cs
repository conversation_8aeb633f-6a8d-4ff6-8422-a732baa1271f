﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using JobDateTypes = Servpro.Franchise.LookupService.Constants.JobDateTypes;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements
{
    public class GetDueDates
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public DateTime? InitialUploadDate { get; set; }
            public DateTime? InitialUploadDueDate { get; set; }
            public UploadStatus InitialUploadStatus { get; set; }
            public DateTime? DailyUploadDate { get; set; }
            public DateTime? DailyUploadDueDate { get; set; }
            public UploadStatus DailyUploadStatus { get; set; }
            public DateTime? FinalUploadDate { get; set; }
            public DateTime? FinalUploadDueDate { get; set; }
            public UploadStatus FinalUploadStatus { get; set; }
            public bool IsInitialExtensionPending { get; set; }
            public bool IsFinalExtensionPending { get; set; }
            public bool IsInitialUploadPending { get; set; }
            public bool IsDailyUploadPending { get; set; }
            public bool IsFinalUploadPending { get; set; }
            public bool IsFinalUploadAllowed { get; set; }
            public bool IsInitialUploadExtensionAllowed { get; set; }
            public bool IsFinalUploadExtensionAllowed { get; set; }
            public bool IsValidJobProgressStep { get; set; }
            public bool IsDryingFinished { get; set; }
            public bool HasCustomerCalledTimestamp { get; set; }
            public bool HasInitialOnSiteArrivalTimestamp { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _jobDataContext;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(JobReadOnlyDataContext jobDataContext,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _jobDataContext = jobDataContext;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _jobDataContext.Jobs
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                var jobUploadLocks = await _jobDataContext.JobUploadLocks
                    .Where(l => l.JobId == job.Id)
                    .ToListAsync(cancellationToken: cancellationToken);

                var isValidJobProgressStep = job.JobProgress >= JobProgress.Billing && job.JobProgress <= JobProgress.Closed;
                var isDryingFinished = job.GetDate(JobDateTypes.DryingComplete).HasValue && job.GetDate(JobDateTypes.Complete).HasValue;

                var hasCustomerCalledTimestamp = job.GetDate(JobDateTypes.CustomerCalled).HasValue;
                var hasInitialOnSiteArrivalTimestamp = job.GetDate(JobDateTypes.InitialOnSiteArrival).HasValue;

                var isFinalUploadAllowed = isValidJobProgressStep;
                if (isFinalUploadAllowed && (job.LossTypeId == LossTypes.FireWater
                                             || job.LossTypeId == LossTypes.Water
                                             || job.LossTypeId == LossTypes.Mold
                                             || job.LossTypeId == LossTypes.Sewage))
                {
                    isFinalUploadAllowed = isDryingFinished;
                }

                var timeZone = await _franchiseServiceClient.GetFranchiseSetPrimaryTimeZoneAsync(job.FranchiseSetId, cancellationToken: cancellationToken);

                var dto = new Dto()
                {
                    InitialUploadDueDate = ConvertDateFromUtc(timeZone, job.InitialUploadDueDate),
                    InitialUploadStatus = MapUploadStatus(job.InitialUploadDueDate, job.MostRecentInitialUploadCompleted, job.OrigInitialUploadDueDate, timeZone),
                    DailyUploadDueDate = ConvertDateFromUtc(timeZone, job.DailyUploadDueDate),
                    DailyUploadStatus = MapUploadStatus(job.DailyUploadDueDate, job.MostRecentDailyUploadCompleted, null, timeZone),
                    FinalUploadDueDate = ConvertDateFromUtc(timeZone, job.FinalUploadDueDate),
                    FinalUploadStatus = MapUploadStatus(job.FinalUploadDueDate, job.MostRecentFinalUploadCompleted, job.OrigFinalUploadDueDate, timeZone),
                    IsInitialExtensionPending = jobUploadLocks.Any(l => l.JobUploadTypeId == JobUploadTypes.InitialExtension),
                    IsFinalExtensionPending = jobUploadLocks.Any(l => l.JobUploadTypeId == JobUploadTypes.FinalExtension),
                    IsInitialUploadPending = jobUploadLocks.Any(l => l.JobUploadTypeId == JobUploadTypes.XactJobInitial || l.JobUploadTypeId == JobUploadTypes.JobInitial),
                    IsDailyUploadPending = jobUploadLocks.Any(l => l.JobUploadTypeId == JobUploadTypes.XactJobDaily || l.JobUploadTypeId == JobUploadTypes.JobDaily),
                    IsFinalUploadPending = jobUploadLocks.Any(l => l.JobUploadTypeId == JobUploadTypes.XactJobFinal || l.JobUploadTypeId == JobUploadTypes.JobFinal),
                    InitialUploadDate = ConvertDateFromUtc(timeZone, job.MostRecentInitialUploadCompleted),
                    DailyUploadDate = ConvertDateFromUtc(timeZone, job.MostRecentDailyUploadCompleted),
                    FinalUploadDate = ConvertDateFromUtc(timeZone, job.MostRecentFinalUploadCompleted),
                    IsFinalUploadAllowed = isFinalUploadAllowed,
                    IsValidJobProgressStep = isValidJobProgressStep,
                    IsDryingFinished = isDryingFinished,
                    IsInitialUploadExtensionAllowed = job.InsuranceCarrierId == InsuranceCarrierTypes.StateFarm
                        && (job.LossTypeId == LossTypes.Water || job.LossTypeId == LossTypes.Sewage) ? job.InitialExtensionCount < 2 : true,
                    IsFinalUploadExtensionAllowed = true,
                    HasCustomerCalledTimestamp = hasCustomerCalledTimestamp,
                    HasInitialOnSiteArrivalTimestamp = hasInitialOnSiteArrivalTimestamp
                };

                if (dto.InitialUploadStatus != UploadStatus.NotApplicable
                    && dto.FinalUploadStatus == UploadStatus.PreviouslyUploaded)
                {
                    dto.InitialUploadStatus = UploadStatus.PreviouslyUploaded;
                }

                return dto;
            }

            public static UploadStatus MapUploadStatus(DateTime? uploadDueDate, 
                DateTime? uploadCompletionDate, DateTime? origUploadDueDate, TimeZoneInfo timeZone)
            {
                if (!uploadDueDate.HasValue)
                {
                    return uploadCompletionDate.HasValue || origUploadDueDate.HasValue ?
                        UploadStatus.PreviouslyUploaded :
                        UploadStatus.NotApplicable;
                }

                var dateTime = ConvertDateFromUtc(timeZone, DateTime.UtcNow).Value;
                uploadDueDate = ConvertDateFromUtc(timeZone, uploadDueDate);
                uploadCompletionDate = ConvertDateFromUtc(timeZone, uploadCompletionDate);

                if (uploadCompletionDate.HasValue)
                    return UploadStatus.PreviouslyUploaded;
                if (uploadDueDate < dateTime)
                    return UploadStatus.PastDueDate;
                if (dateTime.AddDays(1) > uploadDueDate.Value)
                    return UploadStatus.WithinOneDayOfDueDate;
                return UploadStatus.NotApplicable;
            }

            private static DateTime? ConvertDateFromUtc(TimeZoneInfo timeZoneInfo, DateTime? dateTime)
            {
                if (!dateTime.HasValue)
                    return null;

                return TimeZoneInfo.ConvertTimeFromUtc(dateTime.Value, timeZoneInfo);
            }
        }
    }
}