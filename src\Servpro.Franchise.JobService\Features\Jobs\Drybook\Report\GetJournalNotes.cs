﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetJournalNotes
    {
        public class Query : IRequest<JournalNotesDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }

        public class Handler : IRequestHandler<Query, JournalNotesDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;


            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                ILookupServiceClient lookupServiceClient, ILogger<GenerateDryingReport> logger)
            {
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            public async Task<JournalNotesDto> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting journal notes.");
                var jobId = request.Job.Id;
                var journalNotes = await _context.JournalNote
                    .AsNoTracking()
                    .Where(x => x.JobId == jobId
                        && x.Note != null
                        && (x.VisibilityId == JournalNoteVisibilityTypes.FranchiseAndClient
                        || x.VisibilityId == JournalNoteVisibilityTypes.FranchiseClientandCustomer))
                    .Select(x => new JournalNoteDto
                    {
                        JobId = x.JobId.Value,
                        VisibilityId = x.VisibilityId,
                        ActionDate = x.ActionDate,
                        Author = x.Author,
                        CategoryId = x.CategoryId,
                        Note = x.Note,
                        Subject = x.Subject,
                        TypeId = x.TypeId
                    })
                    .ToListAsync(cancellationToken);

                _logger.LogDebug("DryingReportLog - found {journalNoteCount} journal note(s)", journalNotes.Count);

                //Assign the timezone to the model
                _logger.LogDebug($"DryingReportLog - Calling FranchiseService to GetTimeZoneString");

                var franchiseSetTimeZone = string.IsNullOrEmpty(request.FranchiseTimeZone?.ShortName) ? "" : request.FranchiseTimeZone?.ShortName;

                var model = new JournalNotesDto
                {
                    FranchiseSetTimeZone = franchiseSetTimeZone,
                    DiaryNotes = journalNotes
                        .OrderBy(x => x.ActionDate ?? DateTime.MaxValue)
                        .Select(x => new JournalNoteDto
                        {
                            ActionDate = x.ActionDate.GetLocalFranchiseDateTime(request.FranchiseTimeZone),
                            Subject = x.Subject,
                            Note = x.Note,
                            Author = x.Author,
                            CategoryId = x.CategoryId,
                            JobId = x.JobId,
                            TypeId = x.TypeId,
                            VisibilityId = x.VisibilityId
                        })
                        .ToList()
                };
                _logger.LogInformation("DryingReportLog - GetJournalNotes - completed");
                return model;
            }
        }
    }
}
