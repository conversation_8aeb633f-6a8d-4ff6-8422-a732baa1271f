# Intake

## CloudWatch Logs

1. Get the CorrelationId

```js
    fields @timestamp,app_log.message, app_log.logger, kubernetes.container_name
    | sort @timestamp asc
    | limit 200
    | filter app_log.message like /(i?)Received RM Lead Created Response/
    #| filter app_log.message like /(i?)<jobid>/
```

2. Check the logs for issues
```js
    fields @timestamp,app_log.message, app_log.logger, kubernetes.container_name
    | sort @timestamp asc
    | limit 2000
    | filter `app_log.correlation-id` = "<correlation-id>"
    | filter kubernetes.container_name != "corporate-service" # This guy is noisy.
    | filter app_log.level not in ["DEBUG", "TRACE"]
```

## Diagrams

### RM Integration

![RM Integration](https://lucid.app/publicSegments/view/eefbba9e-299e-4131-a9cd-b861aa7bfc42/image.png)


### Data Flow

![Data Flow](https://lucid.app/publicSegments/view/2f60e746-173d-43e4-96bf-1c25bcb6d1d7/image.png)

### Event Storming Output

![Intake Events](https://lucid.app/publicSegments/view/bbf3f4f9-4635-41c8-9dfd-cfb008ec7ed9/image.png)
