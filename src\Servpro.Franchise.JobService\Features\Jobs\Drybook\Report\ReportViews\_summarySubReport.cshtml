@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.SummaryDto
<div class="title">{jobSummary}Job Summary{/jobSummary}</div>
<div class="subtitle">{customerAndJobInfo}Customer and Job Information{/customerAndJobInfo}</div>
<div class="container">
    <table class="box" role="presentation" >
        <thead></thead>
        <tr>
            <td role="presentation" class="header">Customer Name:</td>
            <td class="description">@Model.CustomerName</td>
        </tr>
        <tr>
            <td class="header">Claim / PO Number:</td>
            <td class="description">@Model.ClaimPoNumber</td>
        </tr>
        <tr>
            <td class="header">SERVPRO Project #:</td>
            <td class="description">@Model.ProjectNumber</td>
        </tr>
        <tr>
            <td class="header">Job Address 1:</td>
            <td class="description">@Model.JobAddress1</td>
        </tr>
        <tr>
            <td class="header">Job Address 2:</td>
            <td class="description">@Model.JobAddress2</td>
        </tr>
        <tr>
            <td class="header">City, State, Zip:</td>
            <td class="description">@Model.CityStateZip</td>
        </tr>
    </table>
    <table class="box" role="presentation" >
        <thead></thead>
        <tr>
            <td class="header">Insurance / Client:</td>
            <td class="description">@Model.InsuranceClient</td>
        </tr>
        <tr>
            <td class="header">Policy / WO Number:</td>
            <td class="description">@Model.PolicyWoNumber</td>
        </tr>
        <tr>
            <td class="header">Customer Phone:</td>
            <td class="description">@Model.CustomerPhone</td>
        </tr>
        <tr>
            <td class="header">Customer Email:</td>
            <td class="description">@Model.CustomerEmail</td>
        </tr>
    </table>
</div>
<div class="subtitle">{timestamps}Timestamps{/timestamps}</div>
<div class="container">
    <table class="box" role="presentation" >
        <thead></thead>
        <tr>
            @{
                var lossReceivedTimestamp = string.Empty;
                if (Model.LossReceived.HasValue)
                {
                    lossReceivedTimestamp = $"{Model.LossReceived:M/d/yyyy h:mm tt}" + $" ({Model.FranchiseSetTimeZone})";
                }
            }
            <td class="header">Loss Received:</td>
            <td class="description">@lossReceivedTimestamp</td>
        </tr>
        <tr>
            @{
                var dateOfLossTimestamp = string.Empty;
                if (Model.DateOfLoss.HasValue)
                {
                    dateOfLossTimestamp = $"{Model.DateOfLoss:M/d/yyyy h:mm tt}" + $" ({Model.FranchiseSetTimeZone})";
                }
            }
            <td class="header">Date of Loss:</td>
            <td class="description">@dateOfLossTimestamp</td>
        </tr>
        <tr>
            @{
                var dryingCompleteTimestamp = string.Empty;
                if (Model.DryingComplete.HasValue)
                {
                    dryingCompleteTimestamp = $"{Model.DryingComplete:M/d/yyyy h:mm tt}" + $" ({Model.FranchiseSetTimeZone})";
                }
            }
            <td class="header">Drying Complete:</td>
            <td class="description">@dryingCompleteTimestamp</td>
        </tr>
    </table>
    <table class="box" role="presentation" >
        <thead></thead>
        <tr>
            @{
                var customerCalledTimestamp = string.Empty;
                if (Model.CustomerCalled.HasValue)
                {
                    customerCalledTimestamp = $"{Model.CustomerCalled:M/d/yyyy h:mm tt}" + $" ({Model.FranchiseSetTimeZone})";
                }
            }
            <td class="header">Customer Called:</td>
            <td class="description">@customerCalledTimestamp</td>
        </tr>
        <tr>
            @{
                var arrivalOnSiteTimestamp = string.Empty;
                if (Model.ArrivalOnSite.HasValue)
                {
                    arrivalOnSiteTimestamp = $"{Model.ArrivalOnSite:M/d/yyyy h:mm tt}" + $" ({Model.FranchiseSetTimeZone})";
                }
            }
            <td class="header">Site Inspected:</td>
            <td class="description">@arrivalOnSiteTimestamp</td>
        </tr>
    </table>
</div>

<div class="subtitle">{lossInfo}Loss Information{/lossInfo}</div>
<div class="container">
    <table class="box" role="presentation">
        <thead></thead>
        <tr>
            <td class="header">Type of Loss:</td>
            <td class="description">@Model.TypeOfLoss</td>
        </tr>
        <tr>
            <td class="header">Structure Type:</td>
            <td class="description">@Model.StructureType</td>
        </tr>
        <tr>
            <td class="header">Electricity Available:</td>
            <td class="description">@Model.ElectricityAvailable</td>
        </tr>
    </table>
    <table class="box" role="presentation" >
        <thead></thead>
        <tr>
            <td class="header">Cause of Loss:</td>
            <td class="description">@Model.CauseOfLoss</td>
        </tr>
        <tr>
            <td class="header">Property Type:</td>
            <td class="description">@Model.PropertyType</td>
        </tr>
        <tr>
            <td class="header">Year Structure Built:</td>
            <td class="description">@Model.YearStructureBuilt</td>
        </tr>
    </table>
</div>
<div class="subtitle">{franchiseInfo}Franchise Information{/franchiseInfo}</div>
<div class="container">
    <table class="single-box" role="presentation">
        <thead></thead>
        <tr>
            <td class="header">Name:</td>
            <td class="description">@Model.FranchiseName</td>
        </tr>
        <tr>
            <td class="header">Address:</td>
            <td class="description">@Model.FranchiseAddress1</td>
            <td class="description">@Model.FranchiseCityStateZip</td>
        </tr>
        <tr>
            <td class="header">Phone:</td>
            <td class="description">@Model.FranchisePhone</td>
        </tr>
    </table>
</div>


<div class="subtitle">{additionalLossInfo}Additional Loss Information{/additionalLossInfo}</div>
<div class="container">
    <table class="box" role="presentation">
        <thead></thead>
        <tr>
            <td class="header">Category of Water:</td>
            <td class="description">@Model.CategoryOfWater</td>
        </tr>
        <tr>
            <td class="header">Days to Achieve Dry Standard:</td>
            <td class="description">@Model.DaysToAchieveDryStandard</td>
        </tr>
        <tr>
            <td class="header">Drying Zones:</td>
            <td class="description">@Model.DryingZones</td>
        </tr>
    </table>
    <table class="box" role="presentation" >
        <thead></thead>
        <tr>
            <td class="header">Class of Water:</td>
            <td class="description">@Model.ClassOfWater</td>
        </tr>
        <tr>
            <td class="header">Total Affected SF:</td>
            <td class="description">@Model.TotalSquareFeet</td>
        </tr>
    </table>
</div>
<!--work around header issue render image, is needed to rendering in the body-->
<img alt="Servpro" width="1" src="data:image/png;base64,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" />

<div style='page-break-before: always;'></div>
