﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Invoca;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class GetInvocaFranchiseCalls
    {
        public class Query : IRequest<List<Dto>>
        {
            public Guid? FranchiseId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
        }

        public class Dto
        {
            public Guid CallId { get; set; }
            public DateTime? ReceivedDate { get; set; }
            public DateTime FranchiseLocalTimeReceived { get; set; }
            public string Phone { get; set; }
            public decimal CallDuration { get; set; }
            public Guid?  Disposition { get; set; }
            public DateTime? DispositionSelectedDate { get; set; }
            public string DispositionUserName { get; set; }
            public string RecordingId { get; set; }
            public string CallUrl { get; set; }
            public string LinkedProjectNumber { get; set; }
            public Guid? LinkedProjectId { get; set; }
            public int DaysSinceCall { get; set; }
            public long FranchiseNumber { get; set; }
            public Guid? FranchiseId { get; set; }

        }

        public class Handler : IRequestHandler<Query, List<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetInvocaFranchiseCalls> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IConfiguration _config; 
            private const string InvocaSearchDays = "Invoca:SearchDays";

            public Handler(IConfiguration config,                
                ILogger<GetInvocaFranchiseCalls> logger,
                IFranchiseServiceClient franchiseServiceClient,
                JobReadOnlyDataContext context)
            {
                _config = config;
                _logger = logger;
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;   
            }

            public async Task<List<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{franchiseId}", request.FranchiseId);

                _logger.LogInformation("Getting Invoca Calls for Franchise: {franchise}", request.FranchiseId);
                var searchDays = _config.GetValue(InvocaSearchDays, 30);
                DateTime endDate = request.EndDate ?? DateTime.UtcNow;
                DateTime startDate = request.StartDate ?? endDate.AddDays(-(searchDays));

                bool noFranchise = false;
                FranchiseDto franchise = null;
                if (request.FranchiseId != null && request.FranchiseId != Guid.Empty)
                    franchise = await _franchiseServiceClient.GetFranchiseAsync(request.FranchiseId.Value, cancellationToken);
                else
                    noFranchise = true;
                var fransetPrimaryTime = await _franchiseServiceClient.GetFranchiseSetPrimaryTimeZoneAsync(request.FranchiseSetId, cancellationToken: cancellationToken);
                var franchiseCallsQuery = _context.ExternalMarketingCalls
                    .Where(k => k.FranchiseSetId == request.FranchiseSetId
                            && k.CallReceivedDateTime >= request.StartDate
                            && k.CallReceivedDateTime <= request.EndDate);

                if (!noFranchise)
                    franchiseCallsQuery = franchiseCallsQuery.Where(k => k.FranchiseId == franchise.Id);

                var franchiseCalls = await franchiseCallsQuery
                    .OrderByDescending(k => k.CallReceivedDateTime)
                    .ToListAsync(cancellationToken);

                franchiseCalls = franchiseCalls
                    .Where(x => x.CallDuration > 0 || (
                        x.CallDuration == 0 && (DateTime.UtcNow - x.CallReceivedDateTime).TotalHours < 3) || (
                        x.CallDuration == 0 && x.Disposition != Guid.Empty))
                    .ToList();

                var foundFranchises = await GetFranchises(franchiseCalls, cancellationToken); 
                var foundJobs = await GetJobs(franchiseCalls, cancellationToken);

                var mappedFranchiseCalls = new Dictionary<string, Dto>();
                foreach(var franchiseCall in franchiseCalls)                
                {
                    if (franchiseCall.CallRecordingId == null)
                        continue;
                    var mappedFranchiseCall = Map(franchiseCall, fransetPrimaryTime, foundFranchises, foundJobs, cancellationToken);
                    var repeatedCall = mappedFranchiseCalls.ContainsKey(franchiseCall.CallRecordingId);
                    if (repeatedCall)
                    {
                        if (mappedFranchiseCalls[franchiseCall.CallRecordingId].Disposition != Guid.Empty)
                            continue;
                        if (franchiseCall.Disposition != Guid.Empty)
                        {
                            mappedFranchiseCalls[franchiseCall.CallRecordingId] = mappedFranchiseCall;
                        }
                        else if (franchiseCall.CallDuration != 0)
                        {
                            mappedFranchiseCalls[franchiseCall.CallRecordingId] = mappedFranchiseCall;
                        }
                    }
                    else
                    {
                        mappedFranchiseCalls.Add(franchiseCall.CallRecordingId, mappedFranchiseCall);
                    }

                }
                return mappedFranchiseCalls.Values.ToList();

            }
            private Dto Map(ExternalMarketingCall call, 
                                        TimeZoneInfo timezone,
                                        List<FranchiseDto> foundFranchises,
                                         Dictionary<Guid, string> foundJobs,
                                        CancellationToken cancellationToken)
            {
                string jobNumber = null;
                if (call.AssociatedJobId != Guid.Empty
                    && call.AssociatedJobId != null)
                    jobNumber = foundJobs.GetValueOrDefault(call.AssociatedJobId.Value,String.Empty);
                var franchise = foundFranchises.FirstOrDefault(k => k.Id == call.FranchiseId) ?? new FranchiseDto { FranchiseNumber = 0 };

                return new Dto
                {
                    CallId = call.Id,
                    ReceivedDate = call.CallReceivedDateTime,
                    FranchiseLocalTimeReceived = TimeZoneInfo.ConvertTimeFromUtc(call.CallStartTime, timezone),
                    Phone = call.CallerPhoneNumber,
                    DaysSinceCall = DateTime.UtcNow.Subtract(call.CallReceivedDateTime).Days,
                    Disposition = call.Disposition,
                    DispositionSelectedDate = call.DispositionSelectionDateTime,
                    CallDuration = call.CallDuration,
                    CallUrl = call.RecordingLink,
                    LinkedProjectNumber = jobNumber,
                    RecordingId = call.CallRecordingId,
                    DispositionUserName = call.DispositionSelectionUserName,
                    LinkedProjectId = call.AssociatedJobId,
                    FranchiseNumber = franchise.FranchiseNumber,
                    FranchiseId = call.FranchiseId
                };
            }

            private async Task<List<FranchiseDto>> GetFranchises(List<ExternalMarketingCall> calls, CancellationToken cancellationToken)
            {
                List<Guid> foundFranchiseIds = calls.Select(k => k.FranchiseId).Distinct().ToList();
                List<FranchiseDto> foundFranchises = new List<FranchiseDto>();

                foreach (var item in foundFranchiseIds)
                {
                    try
                    {
                        var foundFranchise = await _franchiseServiceClient.GetFranchiseAsync(item, cancellationToken);
                        if (foundFranchise != null)
                            foundFranchises.Add(foundFranchise);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInformation("Error getting the franchise: {item}, {ex}", item, ex);
                    }
                }
                return foundFranchises;

            }

            private async Task<Dictionary<Guid, string>> GetJobs(List<ExternalMarketingCall> calls, CancellationToken cancellationToken)
            {
                List<Guid> foundJobIds = new List<Guid>();

                foreach (var franchiseCall in calls)
                {
                    if(franchiseCall.AssociatedJobId!=null)
                      foundJobIds.Add(franchiseCall.AssociatedJobId.Value);
                }
                foundJobIds = foundJobIds.Distinct().ToList();
                Dictionary<Guid,string> foundJobs = new Dictionary<Guid,string>();

                foreach (var item in foundJobIds)
                {
                    try
                    {
                        var job = await _context.WipRecords.FirstOrDefaultAsync(x => x.Id == item, cancellationToken);
                        if (job != null)
                            foundJobs.Add(item,job.ProjectNumber);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInformation("Error getting the job: {item}, {ex}", item, ex);
                    }
                }
                return foundJobs;
            }
        }
    }
}
