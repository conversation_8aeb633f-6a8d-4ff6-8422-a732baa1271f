﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetEquipmentPlacementLookup
    {
        public class Query : IRequest<Dto>
        {
            public DateTime BeginDate { get; set; }
            public Guid JobId { get; set; }
        }

        public class Dto
        {
            public Dto(
                IEnumerable<Guid> placedEquipment,
                IEnumerable<Guid> placedEquipmentOnJob)
            {
                PlacedEquipment = placedEquipment;
                PlacedEquipmentOnJob = placedEquipmentOnJob;
            }
            public IEnumerable<Guid> PlacedEquipment { get; }
            public IEnumerable<Guid> PlacedEquipmentOnJob { get; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccesor;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccesor = userInfoAccessor;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccesor.GetUserInfo();
                var equipmentPlaced = await (
                    from job in _context.Jobs
                    join ja in _context.JobAreas on job.Id equals ja.JobId
                    join ep in _context.EquipmentPlacements on ja.Id equals ep.JobAreaId
                    let isPlaced = ep.BeginDate <= request.BeginDate
                        && (!ep.EndDate.HasValue || ep.EndDate > request.BeginDate)
                    where job.FranchiseSetId == userInfo.FranchiseSetId.Value && isPlaced
                    select new { JobId = job.Id, ep.EquipmentId }).ToListAsync(cancellationToken);
                var placedEquipment = equipmentPlaced
                    .Select(x => x.EquipmentId)
                    .Distinct();
                var placedEquipmentOnJob = equipmentPlaced
                    .Where(x => x.JobId == request.JobId)
                    .Select(x => x.EquipmentId)
                    .Distinct();
                return new Dto(placedEquipment, placedEquipmentOnJob);
            }
        }
    }
}
