﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddDeleteInboxAndOutMessaagesScheduleEvent : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var scheduleEvent = @"
                Use JobService;

                CREATE EVENT IF NOT EXISTS DeleteOutboxMessages
                ON SCHEDULE EVERY 1 WEEK
                STARTS '2023-01-15 00:00:00'
                DO
	                DELETE FROM JobService.OutboxMessages
	                WHERE CreatedDate <= DATE_ADD(UTC_TIMESTAMP(), INTERVAL -7 DAY);

                CREATE EVENT IF NOT EXISTS DeleteInboxMessages
                ON SCHEDULE EVERY 1 WEEK
                STARTS '2023-01-15 00:01:00'
                DO
	                DELETE FROM JobService.InboxMessages
	                WHERE CreatedDate <= DATE_ADD(UTC_TIMESTAMP(), INTERVAL -7 DAY);

            ";
            migrationBuilder.Sql(scheduleEvent);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            var scheduleEvent = @"
                Use JobService;
                DROP EVENT IF EXISTS DeleteOutboxMessages;

                DROP EVENT IF EXISTS DeleteInboxMessages;
            ";
            migrationBuilder.Sql(scheduleEvent);
        }
    }
}
