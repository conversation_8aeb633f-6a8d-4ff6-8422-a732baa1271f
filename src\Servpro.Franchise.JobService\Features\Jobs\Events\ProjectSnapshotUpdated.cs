using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.JobUpload;
using Servpro.Franchise.JobService.Features.Leads.Events.Mapping;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.ProjectNumber;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Setup.FeatureFlags;
using JobDateTypes = Servpro.Franchise.JobService.Common.JobDateTypes;
using LookupDateTypes = Servpro.Franchise.LookupService.Constants.JobDateTypes;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ProjectSnapshotUpdated
    {
        public class Event : ProjectSnapshotUpdatedEvent, IRequest
        {
            public Event(ProjectSnapshotUpdatedDto projectSnapshotUpdated, Guid correlationId) : base(projectSnapshotUpdated, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<ProjectSnapshotUpdated> _logger;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IProjectNumberClient _projectNumberClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IFeatureFlagUtility _featureFlags;

            public Handler(JobDataContext db,
                ILookupServiceClient lookupServiceClient,
                IProjectNumberClient projectNumberClient,
                IFranchiseServiceClient franchiseServiceClient,
                IFeatureFlagUtility featureFlags,
                ILogger<ProjectSnapshotUpdated> logger)
            {
                _db = db;
                _logger = logger;
                _lookupServiceClient = lookupServiceClient;
                _projectNumberClient = projectNumberClient;
                _franchiseServiceClient = franchiseServiceClient;
                _featureFlags = featureFlags;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {

                _logger.LogInformation("Started with: {@request}", request);

                var projectSnapshotUpdated = request.ProjectSnapshotUpdated;

                _logger.LogDebug("Parsed event {event} {rmIntegration}, received dto: {@job}", nameof(ProjectSnapshotUpdated), true, projectSnapshotUpdated);

                var job = await _db.Jobs
                    .Include(j => j.MediaMetadata)
                    .Include(j => j.Caller)
                    .Include(j => j.Customer)
                        .ThenInclude(b => b.Business)
                    .FirstOrDefaultAsync(j => j.Id == request.ProjectSnapshotUpdated.JobId, cancellationToken);

                job.JobContacts = await _db.JobContactMap
                   .Include(x => x.Contact)
                       .ThenInclude(y => y.Business)
                   .Where(x => x.JobId == request.ProjectSnapshotUpdated.JobId)
                   .ToListAsync(cancellationToken);

                if (job != null)
                {

                    var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                    if (projectSnapshotUpdated.CauseOfLossId.HasValue && projectSnapshotUpdated.CauseOfLossId != Guid.Empty)
                    {
                        _logger.LogInformation("Detected cause of loss update");
                        job.CauseOfLossId = projectSnapshotUpdated.CauseOfLossId.Value;
                    }

                    if (projectSnapshotUpdated.LossTypeId.HasValue && projectSnapshotUpdated.LossTypeId != Guid.Empty)
                    {
                        //If it's a new LossType, update the Job's LossType and create a new ProjectNumber
                        if (job.LossTypeId != projectSnapshotUpdated.LossTypeId)
                        {
                            job.LossTypeId = projectSnapshotUpdated.LossTypeId.Value;
                            job.ProjectNumber = await GetProjectNumberAsync(job.ProjectNumber, job.LossTypeId, lookups);
                            await GenerateJobUpdatedEvent(job, request, cancellationToken);
                        }
                    }

                    job.SetOrUpdateDate(LookupDateTypes.WorkStartDate, projectSnapshotUpdated.WorkStart);
                    job.SetOrUpdateDate(LookupDateTypes.WorkEndDate, projectSnapshotUpdated.WorkEnd);
                    job.SetOrUpdateDate(LookupDateTypes.SiteAppointmentStartDate, projectSnapshotUpdated.SiteApptStart);
                    job.SetOrUpdateDate(LookupDateTypes.SiteAppointmentEndDate, projectSnapshotUpdated.SiteApptEnd);
                    job.SiteAppointmentById = projectSnapshotUpdated.ReviewById;

                    // Don't save SiteAppointmentStartDateTime if feature not enabled
                    var call = _db.ExternalMarketingCalls.FirstOrDefault(x => x.AssociatedJobId == job.Id);
                    if (call != null)
                    {
                        if (call.SiteAppointmentStartDateTime == null)
                            call.SiteAppointmentStartDateTime = projectSnapshotUpdated.SiteApptStart;
                        call.SiteAppointmentStartDateTimeLastUpdated = DateTime.UtcNow;
                    }

                    if (projectSnapshotUpdated.SiteApptEnd.HasValue)
                        job.SetOrUpdateDate(JobDateTypes.FinalInspection, projectSnapshotUpdated.SiteApptEnd.Value);
                    else
                        job.RemoveDate(JobDateTypes.FinalInspection);

                    var previousJobProgress = job.JobProgress;
                    job.JobProgress = (JobProgress)projectSnapshotUpdated.JobProgress;

                    if (previousJobProgress != (JobProgress)projectSnapshotUpdated.JobProgress)
                    {
                        job.JobProgressModifiedDate = projectSnapshotUpdated.ModifiedDate;
                    }

                    if ((JobProgress)projectSnapshotUpdated.JobProgress == JobProgress.NotSoldCancelled &&
                        previousJobProgress != JobProgress.NotSoldCancelled)
                    {
                        job.JobCancelReasonId = projectSnapshotUpdated.JobCancelReasonId == Guid.Empty
                            ? JobCancelReasons.Other : request.ProjectSnapshotUpdated.JobCancelReasonId;

                        await GenerateCancelledEvent(request, cancellationToken);

                        if (job.JobDispatchTypeId != DispatchTypes.NonScanEr
                            && job.CorporateJobNumber.HasValue)
                        {
                            _logger.LogInformation("Detected non-xact job cancellation");
                            // non-xact job.  handled by legacy SOA upload replacement
                            await GenerateJobUploadCorporateSyncEvent(job, request, lookups, cancellationToken);
                        }

                        await GenerateProjectCancelledEvents(job, request, cancellationToken);

                        if (projectSnapshotUpdated.ModifiedById is null)
                        {
                            _logger.LogWarning("Incoming event did not have a ModifiedById. Can't set Wipboard NotSoldOrCancelledBy");
                        }
                        else
                        {
                            await SetNotSoldOrCancelledBy(job, projectSnapshotUpdated.JobId, projectSnapshotUpdated.ModifiedById.Value, projectSnapshotUpdated.ModifiedDate, cancellationToken);
                            job.SetOrUpdateDate(LookupDateTypes.JobNotSold, projectSnapshotUpdated.ModifiedDate);
                        }
                    }

                    if ((JobProgress)projectSnapshotUpdated.JobProgress != JobProgress.NotSoldCancelled &&
                        previousJobProgress == JobProgress.NotSoldCancelled)
                    {
                        job.JobCancelReasonId = Guid.Empty; // wipe out the previous cancellation reason
                        _logger.LogInformation("Detected job reactivation");
                    }

                    if ((JobProgress)projectSnapshotUpdated.JobProgress == JobProgress.InitialServices &&
                        previousJobProgress != JobProgress.InitialServices)
                    {
                        await GenerateProjectSoldEvents(job, request, cancellationToken);
                    }

                    if ((JobProgress)projectSnapshotUpdated.JobProgress == JobProgress.Closed &&
                       previousJobProgress != JobProgress.Closed)
                    {
                        job.SetOrUpdateDate(LookupDateTypes.DateClosed, projectSnapshotUpdated.ModifiedDate);
                        job.SetOrUpdateDate(JobDateTypes.Complete, projectSnapshotUpdated.ModifiedDate);
                    }

                    if (!_db.ChangeTracker.HasChanges())
                    {
                        return Unit.Value;
                    }

                    job.ModifiedBy = projectSnapshotUpdated.ModifiedBy;
                    job.ModifiedDate = projectSnapshotUpdated.ModifiedDate;
                    await _db.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    _logger.LogWarning("The Job with Id({ID}) Does Not Exist", projectSnapshotUpdated.JobId);
                }

                return Unit.Value;
            }

            private async Task SetNotSoldOrCancelledBy(Job job, Guid jobId, Guid modifiedById, DateTime modifiedDate, CancellationToken cancellationToken)
            {
                var user = await GetUserInfoAsync(modifiedById, cancellationToken);

                if (!string.IsNullOrEmpty(user.fullName))
                {
                    var wipRecord = await _db.WipRecords
                        .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
                    if (wipRecord != null)
                    {
                        wipRecord.NotSoldOrCancelledBy = user.fullName;

                        if (job.JobDates.FirstOrDefault(x => x.JobDateTypeId == LookupDateTypes.JobNotSold) == null)
                        {
                            wipRecord.NotSoldOrCancelledDate = modifiedDate;
                        }
                    }
                }
            }

            private async Task GenerateJobUpdatedEvent(Job job, Event request, CancellationToken cancellationToken)
            {
                var jobUpdatedDto = new JobUpdatedEvent.JobUpdatedDto
                {
                    Changes = new Dictionary<string, object>() { { nameof(JobUpdatedEvent.JobUpdatedDto.ProjectNumber), job.ProjectNumber } },
                    JobId = job.Id,
                    ProjectNumber = job.ProjectNumber
                };
                var jobUpdatedEvent = new JobUpdatedEvent(jobUpdatedDto, request.CorrelationId);
                var outboxMessage = new OutboxMessage(jobUpdatedEvent.ToJson(),
                                        nameof(JobUpdatedEvent),
                                        request.CorrelationId,
                                        request.ProjectSnapshotUpdated.ModifiedBy);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);

            }

            private async Task GenerateProjectSoldEvents(Job job, Event request, CancellationToken cancellationToken)
            {
                var eventDto = new ProjectSoldEvent.ProjectSoldDto
                {
                    JobId = job.Id,
                    FranchiseId = job.FranchiseId,
                    FranchiseSetId = job.FranchiseSetId,
                    PropertyTypeId = job.PropertyTypeId,
                    MentorId = job.MentorId,
                    LossTypeId = job.LossTypeId,
                    ProjectNumber = job.ProjectNumber,
                    ProjectRangeId = job.ProjectRangeId,
                    LossSeverityId = job.LossSeverityId,
                    SquareFeetAffected = job.SquareFeetAffected,
                    SquareFeet = job.SquareFeet,
                    CreatedUtc = DateTime.UtcNow,
                    CreatedBy = nameof(ProjectSnapshotUpdated),
                    BusinessName = job.BusinessName,
                    Customer = MapProjectSoldContactDto(job.Customer)
                };

                var projectSoldEvent = new ProjectSoldEvent(eventDto, request.CorrelationId);
                var outboxMessage = new OutboxMessage(projectSoldEvent.ToJson(), nameof(ProjectSoldEvent), request.CorrelationId, request.ProjectSnapshotUpdated.ModifiedBy);

                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private ProjectSoldEvent.ContactDto MapProjectSoldContactDto(Contact contact)
            {
                var contactDto = new ProjectSoldEvent.ContactDto();

                if (contact == null)
                    return contactDto;
                contactDto.Id = contact.Id;
                contactDto.FirstName = contact.FirstName;
                contactDto.LastName = contact.LastName;
                contactDto.EmailAddress = contact.EmailAddress;
                return contactDto;
            }

            private async Task<string> GetProjectNumberAsync(string projectNum, Guid lossTypeId, GetLookups.Dto lookups)
            {
                var lossTypeAbbreviation = lookups.LossTypes.FirstOrDefault(x => x.Id == lossTypeId)?.Abbreviation;
                var projectNumber = $"{Regex.Match(projectNum, ProjectNumberIdentifier.ProjectNumberRegExString).Value}{lossTypeAbbreviation}";
                return projectNumber;
            }

            private async Task GenerateCancelledEvent(Event request, CancellationToken cancellationToken)
            {
                var cancelledEvent = new JobCancelledEvent(request.ProjectSnapshotUpdated.JobId,
                    request.ProjectSnapshotUpdated.ModifiedBy,
                    request.ProjectSnapshotUpdated.ModifiedDate,
                    request.ProjectSnapshotUpdated.JobCancelReasonId == Guid.Empty
                        ? JobCancelReasons.Other : request.ProjectSnapshotUpdated.JobCancelReasonId,
                    request.CorrelationId);
                var outboxMessage = new OutboxMessage(cancelledEvent.ToJson(),
                    nameof(JobCancelledEvent),
                    request.CorrelationId,
                    request.ProjectSnapshotUpdated.ModifiedBy);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task GenerateJobUploadCorporateSyncEvent(Job job, Event request, GetLookups.Dto lookups, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Entered GenerateJobUploadCorporateSyncEvent with jobId: {jobId}", job.Id);

                InsuranceClient insuranceClient = null;
                if (job.InsuranceCarrierId.HasValue)
                {
                    insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(c => c.Id == job.InsuranceCarrierId.Value, cancellationToken);
                }

                job.JournalNotes = await _db.JournalNote
                    .Where(x => x.JobId == request.ProjectSnapshotUpdated.JobId)
                    .ToListAsync(cancellationToken);

                _logger.LogInformation("Getting Franchise info for Job {jobId}", job.Id);
                var franchiseInfo = await _franchiseServiceClient.GetFranchiseWithoutUserClaimsAsync(job.FranchiseId, job.FranchiseSetId, cancellationToken: cancellationToken);

                if (franchiseInfo == null)
                {
                    throw new Exception($"Franchise {job.FranchiseId} doesn't exists.");
                }

                if (!request.ProjectSnapshotUpdated.ModifiedById.HasValue)
                {
                    _logger.LogWarning("Incoming event did not have a ModifiedById. This is likely due to RM not having sent the data. Look at rm-svc.");
                }

                var jobUploadDto = UploadEventGenerator.GetJobUploadForCorporateJobSync(
                    job,
                    insuranceClient,
                    UploadType.Daily,
                    request.ProjectSnapshotUpdated.ModifiedBy,
                    request.ProjectSnapshotUpdated.ModifiedById ?? Guid.Empty,
                    (int)franchiseInfo.FranchiseNumber,
                    false,
                    true,
                    false,
                    null
                    );

                _logger.LogInformation("Generating CorporateJobSyncStartedEvent.");
                var corpSyncEvent = new CorporateJobSyncStartedEvent(jobUploadDto, request.CorrelationId);

                var outboxMessage = new OutboxMessage(corpSyncEvent.ToJson(), nameof(CorporateJobSyncStartedEvent), request.CorrelationId, request.ProjectSnapshotUpdated.ModifiedBy);

                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task GenerateProjectCancelledEvents(Job job, Event request, CancellationToken cancellationToken)
            {
                var eventDto = ProjectCancelledEventMapper.MapProjectCancelledDto(job, nameof(ProjectSnapshotUpdated));
                var projectCancelledEvent = new ProjectCancelledEvent(request.CorrelationId, eventDto);

                var outboxMessage = new OutboxMessage(projectCancelledEvent.ToJson(), nameof(ProjectCancelledEvent), request.CorrelationId, request.ProjectSnapshotUpdated.ModifiedBy);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task<(Guid userId, string fullName)> GetUserInfoAsync(
             Guid userId,
             CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting employee info from franchise-service");
                var employee = await _franchiseServiceClient.GetEmployee(userId, cancellationToken);

                if (employee is null)
                {
                    _logger.LogWarning("Employee {userId} not found", userId);
                    return (Guid.NewGuid(), "");
                }
                return (employee.Id, $"{employee.FirstName} {employee.LastName}");
            }
        }
    }
}