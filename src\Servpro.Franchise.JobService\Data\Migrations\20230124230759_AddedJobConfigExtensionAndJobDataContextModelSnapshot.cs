﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddedJobConfigExtensionAndJobDataContextModelSnapshot : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobAreaMaterialReading_MediaMetadata_MediaMetadataId",
                table: "JobAreaMaterialReading");

            migrationBuilder.DropTable(
                name: "JobConfigExtension");

            migrationBuilder.DropIndex(
                name: "IX_JobAreaMaterialReading_MediaMetadataId",
                table: "JobAreaMaterialReading");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("1c4803dd-4478-4ca5-b01b-58cc947f334c"));

            migrationBuilder.DropColumn(
                name: "MediaMetadataId",
                table: "JobAreaMaterialReading");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("96a375f2-f582-4d21-8135-8a7c99eebce5"), null, new DateTime(2023, 1, 24, 23, 7, 58, 401, DateTimeKind.Utc).AddTicks(3415), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("96a375f2-f582-4d21-8135-8a7c99eebce5"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AddColumn<Guid>(
                name: "MediaMetadataId",
                table: "JobAreaMaterialReading",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.CreateTable(
                name: "JobConfigExtension",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    RequireDailyMaterialPhotos = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RequireFinalMaterialPhotos = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RequireInitialMaterialPhotos = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobConfigExtension", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobConfigExtension_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("1c4803dd-4478-4ca5-b01b-58cc947f334c"), null, new DateTime(2023, 1, 18, 18, 35, 56, 850, DateTimeKind.Utc).AddTicks(1952), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterialReading_MediaMetadataId",
                table: "JobAreaMaterialReading",
                column: "MediaMetadataId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobConfigExtension_JobId",
                table: "JobConfigExtension",
                column: "JobId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobAreaMaterialReading_MediaMetadata_MediaMetadataId",
                table: "JobAreaMaterialReading",
                column: "MediaMetadataId",
                principalTable: "MediaMetadata",
                principalColumn: "Id");
        }
    }
}
