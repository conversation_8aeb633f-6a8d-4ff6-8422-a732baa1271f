﻿using Amazon.SimpleNotificationService.Model;
using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ValidationException = FluentValidation.ValidationException;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class SavePhoto
    {
        public class Command : IRequest<List<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public ICollection<MediaDto> Media { get; set; }
        }

        public class MediaDto
        {
            public Guid ArtifactTypeId { get; set; }
            public bool IsForUpload { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public Guid Id { get; set; }
            public Guid? JobVisitId { get; set; }
            public Guid? ZoneId { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Command, List<Dto>>
        {
            private readonly JobDataContext _db;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private readonly IConfiguration _config;
            private readonly ILogger<Handler> _logger;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";


            public Handler(
                JobDataContext db,
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor,
                IMediaEventGenerator mediaEventGenerator,
                IConfiguration config,
                ILogger<Handler> logger)
            {
                _db = db;
                _lookupServiceClient = lookupServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _mediaEventGenerator = mediaEventGenerator;
                _config = config;
                _logger = logger;
            }

            public async Task<List<Dto>> Handle(Command request,
                CancellationToken cancellationToken)
            {
                _logger.LogInformation("Starting SavePhoto handler. JobId: {JobId}, FranchiseSetId: {FranchiseSetId}, MediaCount: {MediaCount}",
                    request.JobId, request.FranchiseSetId, request.Media?.Count ?? 0);

                if (request.Media == null || !request.Media.Any())
                {
                    _logger.LogWarning("No media provided in request for JobId: {JobId}", request.JobId);
                    throw new FluentValidation.ValidationException(new[]
                    {
                        new FluentValidation.Results.ValidationFailure("media", "At least one media item is required.")
                    });
                }

                var jobExists = await _db.Jobs
                    .AnyAsync(j => j.Id == request.JobId, cancellationToken);

                if (!jobExists)
                {
                    _logger.LogWarning("Job not found. JobId: {JobId}, FranchiseSetId: {FranchiseSetId}",
                        request.JobId, request.FranchiseSetId);
                    throw new ValidationException(new[]
                     {
                            new ValidationFailure("jobId", $"Job '{request.JobId}' was not found.")
                     });
                }

                if (request.Media == null || !request.Media.Any())
                {
                    _logger.LogWarning("No media provided in request for JobId: {JobId}", request.JobId);
                    return new List<Dto>();
                }

                await ValidateMediaIdsAsync(request.Media.Select(m => m.Id), cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                var mediaMetadata = request.Media
                    .Select(x => Map(request, x, lookups))
                    .Where(x => x != null)
                    .ToList();

                await _db.MediaMetadata.AddRangeAsync(mediaMetadata, cancellationToken);

                _logger.LogDebug("Mapped {Count} valid media items for JobId: {JobId}", mediaMetadata.Count, request.JobId);

                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                //Generate MediaAddedEvent
                var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(mediaMetadata, correlationId, userInfo);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);

                //Generate Save photo event
                var photoSavedOutboxMessage =
                    await _mediaEventGenerator.GeneratePhotoSavedEvent(mediaMetadata, correlationId, userInfo);
                await _db.OutboxMessages.AddAsync(photoSavedOutboxMessage, cancellationToken);

                _logger.LogDebug("Saving media metadata and outbox messages to DB for JobId: {JobId}", request.JobId);

                await _db.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Media saved successfully for JobId: {JobId}. Media IDs: {MediaIds}",
                    request.JobId,
                    string.Join(",", mediaMetadata.Select(m => m.Id)));

                return mediaMetadata.Select(x => new Dto() { Id = x.Id, Name = x.Name }).ToList();
            }

            private MediaMetadata Map(Command command, MediaDto media, GetLookups.Dto lookups)
            {
                var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == media.ArtifactTypeId);

                if (artifactType == null)
                {
                    _logger.LogWarning("Unknown ArtifactTypeId: {ArtifactTypeId} for media: {MediaName}", media.ArtifactTypeId, media.Name);
                    return null;
                }

                return new MediaMetadata
                {
                    Id = media.Id,
                    JobId = command.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    FranchiseSetId = command.FranchiseSetId,
                    IsDeleted = false,
                    IsForUpload = artifactType.Required || media.IsForUpload,
                    Name = media.Name,
                    MediaTypeId = MediaTypes.Photo,
                    ArtifactTypeId = media.ArtifactTypeId,
                    MediaPath = media.MediaPath,
                    ZoneId = media.ZoneId.ValueOrNullIfEmpty(),
                    JobAreaId = media.JobAreaId.ValueOrNullIfEmpty(),
                    JobVisitId = media.JobVisitId.ValueOrNullIfEmpty(),
                    JobAreaMaterialId = media.JobAreaMaterialId.ValueOrNullIfEmpty(),
                    ArtifactDate = DateTime.UtcNow,
                    BucketName = _config.GetValue<string>(S3MediaBucketNameKey),
                    UploadedSuccessfully = true
                };
            }

            private async Task ValidateMediaIdsAsync(IEnumerable<Guid> incomingIds, CancellationToken ct)
            {
                var ids = incomingIds?.ToList() ?? new List<Guid>();
                var failures = new List<ValidationFailure>();

                if (ids.Count == 0)
                    failures.Add(new ValidationFailure("media", "At least one media item is required."));

                if (ids.Any(id => id == Guid.Empty))
                    failures.Add(new ValidationFailure("media[].id", "Media 'id' must be a non-empty GUID."));

                var duplicates = ids.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
                if (duplicates.Any())
                    failures.Add(new ValidationFailure("media[].id",
                        $"Duplicate media 'id' values in the request: {string.Join(", ", duplicates)}."));

                var existing = await _db.MediaMetadata
                    .Where(m => ids.Contains(m.Id))
                    .Select(m => m.Id)
                    .ToListAsync(ct);

                if (existing.Any())
                    failures.Add(new ValidationFailure("media[].id",
                        $"These media 'id' values already exist and cannot be reused: {string.Join(", ", existing)}. Please generate new GUIDs."));

                if (failures.Any())
                    throw new ValidationException(failures);
            }
        }
    }
}