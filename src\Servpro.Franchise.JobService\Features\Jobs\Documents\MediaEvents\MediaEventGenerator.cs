﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Events;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents
{
    public class MediaEventGenerator : IMediaEventGenerator
    {
        private readonly JobDataContext _db;
        private readonly ILogger<MediaEventGenerator> _logger;

        public MediaEventGenerator(JobDataContext db, ILogger<MediaEventGenerator> logger)
        {
            _db = db;
            _logger = logger;
        }

        public OutboxMessage GenerateMediaAddedEvent(IEnumerable<MediaMetadata> metadata,
            Guid correlationId,
            UserInfo userInfo)
            => GenerateMediaAddedEvent(metadata, correlationId, userInfo?.Username);

        public OutboxMessage GenerateMediaAddedEvent(IEnumerable<MediaMetadata> metadata,
            Guid correlationId,
            string username,
            bool isMobileSync = false)
        {
            
            if (metadata is null)
                throw new ArgumentNullException(nameof(metadata));
            if (correlationId == Guid.Empty)
                throw new ArgumentException(nameof(correlationId));
            if(username is null)
                throw new ArgumentNullException(nameof(username));
            if (string.IsNullOrEmpty(username))
                throw new ArgumentException(nameof(username));

            var eventDtoList = metadata.Select(MapMediaAddedDto).ToList();
            if (!eventDtoList.Any())
                return null;

            var outboxEvent = new MediaAddedEvent(eventDtoList, correlationId);
            if(isMobileSync)
            {
                outboxEvent.Source = EventSource.MobileSync;
            }
            var outboxMessage = new OutboxMessage(outboxEvent.ToJson(), nameof(MediaAddedEvent),
                correlationId, username);

            _logger.LogInformation("The Media Added Event {@mediaEvent} was generated successfully", outboxMessage);
            return outboxMessage;
        }

        public async Task<OutboxMessage> GeneratePhotoSavedEvent(List<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo)
        {
            var eventDtoList = metadata.Select(MapPhotoSavedDto).ToList();
            if (!eventDtoList.Any())
                return null;

            var outboxEvent = new PhotoSavedEvent(eventDtoList, correlationId);

            var outboxMessage = new OutboxMessage(outboxEvent.ToJson(), nameof(PhotoSavedEvent),
                correlationId, userInfo.Username);

            _logger.LogInformation("The Photo Saved Event {photoSavedEvent} was generated successfully", outboxMessage);
            return outboxMessage;
        }

        public async Task<OutboxMessage> GeneratePhotoCreateEvent(List<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo)
        {
            var eventDtoList = metadata.Select(MapPhotoCreatedDto).ToList();
            if (!eventDtoList.Any())
                return null;

            var outboxEvent = new PhotoCreatedEvent(eventDtoList, correlationId);

            var outboxMessage = new OutboxMessage(outboxEvent.ToJson(), nameof(PhotoCreatedEvent),
                correlationId, userInfo.Username);

            _logger.LogInformation("The Photo Created Event {photoCreatedEvent} was generated successfully", outboxMessage);
            return outboxMessage;
        }

        public async Task<OutboxMessage> GeneratePhotoDeletedEvent(List<MediaMetadata> metadata, Guid correlationId, UserInfo userInfo)
        {
            var eventDtoList = metadata.Select(MapPhotoDeletedDto).ToList();
            if (!eventDtoList.Any())
                return null;

            var outboxEvent = new PhotoDeletedEvent(eventDtoList, correlationId);

            var outboxMessage = new OutboxMessage(outboxEvent.ToJson(), nameof(PhotoDeletedEvent),
                correlationId, userInfo.Username);

            _logger.LogInformation("The Photo Deleted Event {PhotoDeletedEvent} was generated successfully", outboxMessage);
            return outboxMessage;
        }


        private PhotoSavedEvent.MediaDto MapPhotoSavedDto(MediaMetadata metadata)
        {
            return new PhotoSavedEvent.MediaDto
            {
                Id = metadata.Id,
                JobId = metadata.JobId,
                FranchiseSetId = metadata.FranchiseSetId,
                Name = metadata.Name,
                ArtifactTypeId = metadata.ArtifactTypeId,
                MediaPath = metadata.MediaPath,
                BucketName = metadata.BucketName,
                JobAreaId = metadata.JobAreaId,
                JobAreaMaterialId = metadata.JobAreaMaterialId,
                JobVisitId = metadata.JobVisitId,
                ZoneId = metadata.ZoneId,
            };
        }

        private PhotoCreatedEvent.MediaDto MapPhotoCreatedDto(MediaMetadata metadata)
        {
            return new PhotoCreatedEvent.MediaDto
            {
                Id = metadata.Id,
                JobId = metadata.JobId,
                FranchiseSetId = metadata.FranchiseSetId,
                Name = metadata.Name,
                ArtifactTypeId = metadata.ArtifactTypeId,
                MediaPath = metadata.MediaPath,
                BucketName = metadata.BucketName,
                JobAreaId = metadata.JobAreaId,
                JobAreaMaterialId = metadata.JobAreaMaterialId,
                JobVisitId = metadata.JobVisitId,
                ZoneId = metadata.ZoneId,
            };
        }


        private PhotoDeletedEvent.MediaDto MapPhotoDeletedDto(MediaMetadata metadata)
        {
            return new PhotoDeletedEvent.MediaDto
            {
                Id = metadata.Id,
                MediaPath = metadata.MediaPath,
                BucketName = metadata.BucketName,
                FranchiseSetId = metadata.FranchiseSetId
            };
        }



        private MediaAddedEvent.MediaAddedDto MapMediaAddedDto(MediaMetadata metadata)
        {
            return new MediaAddedEvent.MediaAddedDto
            {
                MediaId = metadata.Id,
                JobId = metadata.JobId,
                FranchiseSetId = metadata.FranchiseSetId,
                Name = metadata.Name,
                Description = metadata.Description,
                MediaTypeId = metadata.MediaTypeId,
                ArtifactTypeId = metadata.ArtifactTypeId,
                MediaPath = metadata.MediaPath,
                BucketName = metadata.BucketName,
                FormTemplateId = metadata.FormTemplateId,
                ArtifactDate = metadata.ArtifactDate,
                JobAreaId = metadata.JobAreaId,
                JobAreaMaterialId = metadata.JobAreaMaterialId,
                JobVisitId = metadata.JobVisitId,
                ZoneId = metadata.ZoneId
            };
        }
    }


}
