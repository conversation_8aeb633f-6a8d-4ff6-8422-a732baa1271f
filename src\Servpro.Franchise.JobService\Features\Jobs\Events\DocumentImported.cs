﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class DocumentImported
    {
        public class Event : DocumentImportedEvent, IRequest
        {
            public Event(DocumentImportedDto dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            public Handler(JobDataContext dataContext, ILogger<Handler> logger, ISessionIdAccessor sessionIdAccessor,
                IMediaEventGenerator mediaEventGenerator)
            {
                _db = dataContext;
                _logger = logger;
                _mediaEventGenerator = mediaEventGenerator;
                _sessionIdAccessor = sessionIdAccessor;

            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} Handler: activated", nameof(DocumentImportedEvent));
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var documentDto = request.DocumentImported;

                var media = await _db.MediaMetadata.FirstOrDefaultAsync(x => x.Id == request.DocumentImported.DocumentId, cancellationToken);
                if(media != null)
                {
                    _logger.LogWarning("Document with Id: {id} already exists.", request.DocumentImported.DocumentId);
                    return Unit.Value;
                }

                if (!documentDto.BucketName.IsNullOrWhiteSpace() && !documentDto.MediaPath.IsNullOrWhiteSpace())
                {
                    var mediaMetadata = await Map(request.DocumentImported, cancellationToken);
                    var mediasMetaData = new List<MediaMetadata>
                    {
                        mediaMetadata
                    };
                    _logger.LogInformation("Processing MediaMetadata with Id: {id}", mediaMetadata.Id);
                    await _db.MediaMetadata.AddAsync(mediaMetadata, cancellationToken);
                    var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(mediasMetaData, correlationId, documentDto.CreatedBy);
                    await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                    await _db.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("{event} Handler: completed successfully", nameof(DocumentImportedEvent));
                }
                else
                {
                    _logger.LogWarning("{event} Handler: BucketName and MediaPath should not be Null or Empty", nameof(DocumentImportedEvent));
                }

                return Unit.Value;
            }

            private async Task<MediaMetadata> Map(DocumentImportedDto dto, CancellationToken cancellationToken) =>
                new MediaMetadata
                {
                    Id = dto.DocumentId,
                    JobId = dto.JobId,
                    FranchiseSetId = await GetFranchiseSetId(dto.JobId, cancellationToken),
                    MediaTypeId = MediaTypes.Document,
                    ArtifactTypeId = dto.ArtifactTypeId,
                    Name = dto.Name,
                    Description = dto.Description,
                    BucketName = dto.BucketName,
                    ArtifactDate = dto.ArtifactDate,
                    MediaPath= dto.MediaPath
                    
                };

            private async Task<Guid> GetFranchiseSetId(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.Select(job => new { job.Id, job.FranchiseSetId }).FirstOrDefaultAsync(job => job.Id == jobId, cancellationToken);
                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {jobId}");

                return job.FranchiseSetId;
            }
        }
    }
}