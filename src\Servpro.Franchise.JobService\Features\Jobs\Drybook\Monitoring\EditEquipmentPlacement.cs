﻿using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class EditEquipmentPlacement
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public IEnumerable<EquipmentPlacementDto> EquipmentPlacements { get; set; }

            public class EquipmentPlacementDto
            {
                public Guid EquipmentPlacementId { get; set; }
                public Guid? BeginVisitId { get; set; }
                public Guid? EndVisitId { get; set; }
            }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleForEach(x => x.EquipmentPlacements)
                    .Must(x => x.EquipmentPlacementId != Guid.Empty);
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            public readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo)
            {
                _userInfo = userInfo;
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();

                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                    .Include(x => x.JobVisits)
                    .FirstOrDefaultAsync(x => x.FranchiseSetId == userInfo.FranchiseSetId.Value
                        && x.Id == request.JobId, cancellationToken: cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var jobLocks = await _context.JobLock.Where(jl => jl.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobLocks = jobLocks;

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var validationErrors = ValidateRequest(request, job);
                if (validationErrors.Any())
                    throw new ValidationException(validationErrors);

                var equipmentPlacementLookups = job.JobAreas
                            .SelectMany(x => x.EquipmentPlacements)
                            .ToDictionary(x => x.Id);

                var updatedEquipments = new List<EquipmentPlacedUpdatedDto.PlacementUpdatedDto>();

                foreach (var equipmentPlacement in request.EquipmentPlacements)
                {
                    var jobEquipmentPlacement = equipmentPlacementLookups[equipmentPlacement.EquipmentPlacementId];

                    var beginDate =  DateTime.UtcNow;
                    var endDate =  DateTime.UtcNow;
                    
                    if (equipmentPlacement.BeginVisitId.HasValue)
                    {
                        beginDate = job.JobVisits.FirstOrDefault(x => x.Id == equipmentPlacement.BeginVisitId).Date;
                        jobEquipmentPlacement.BeginDate = beginDate;
                    }
                    if (equipmentPlacement.EndVisitId.HasValue)
                    {
                        endDate = job.JobVisits.FirstOrDefault(x => x.Id == equipmentPlacement.EndVisitId).Date;
                        jobEquipmentPlacement.EndDate = endDate;
                    }

                    jobEquipmentPlacement.ModifiedBy = userInfo.Username;
                    jobEquipmentPlacement.ModifiedDate = DateTime.UtcNow;

                    updatedEquipments.Add(new EquipmentPlacedUpdatedDto.PlacementUpdatedDto
                    {
                        Id = equipmentPlacement.EquipmentPlacementId,
                        EquipmentId = jobEquipmentPlacement.EquipmentId,
                        BeginDate = beginDate,
                        EndDate = endDate
                    });
                }

                var equipmentAvailable = equipmentPlacementLookups.FirstOrDefault(x => !x.Value.EndDate.HasValue);

                if (equipmentAvailable.Value == null && request.EquipmentPlacements.Any())
                {
                    CompleteDrying(request, job, userInfo.Username);
                }

                await GenerateEquipmentUpdatedEvent(userInfo, request, job, updatedEquipments, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task GenerateEquipmentUpdatedEvent(UserInfo userInfo, Command request, Job job,
                List<EquipmentPlacedUpdatedDto.PlacementUpdatedDto> placementDtos, CancellationToken cancellationToken)
            {

                var defaultEquipment = await _context.EquipmentPlacements.FirstOrDefaultAsync(q =>
                                            q.EquipmentId == placementDtos.FirstOrDefault().EquipmentId, cancellationToken);
                
                var dto = new EquipmentPlacedUpdatedDto()
                {
                    FranchiseSetId = new Guid(userInfo.FranchiseSetId.ToString()),
                    JobId = request.JobId,
                    Placements = placementDtos,
                    UserId = userInfo.Id,
                    Username = userInfo.Username,
                    JobAreaId = defaultEquipment.JobAreaId
                };

                var correlationId = Guid.NewGuid();
                var placementEvent = new EquipmentPlacementUpdatedEvent(dto, correlationId);
                var outboxMessage = new OutboxMessage(placementEvent.ToJson(), nameof(EquipmentPlacementUpdatedEvent), correlationId,userInfo.Username);
                await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private void CompleteDrying(Command request, Job job, string userName)
            {
                var lastVisitDate = job.JobVisits.FirstOrDefault(x => x.Id == request.EquipmentPlacements.First().EndVisitId)?.Date;
                if (lastVisitDate.HasValue)
                {
                    job.SetOrUpdateDate(JobDateTypes.DryingComplete, lastVisitDate.Value);
                    var dryingCompletedEvent = GenerateDryingCompletedEvent(request, lastVisitDate.Value, userName, _sessionIdAccessor.GetCorrelationGuid());
                    _context.OutboxMessages.Add(dryingCompletedEvent);
                }
            }

            private OutboxMessage GenerateDryingCompletedEvent(Command request, DateTime date, string userName, Guid correlationId)
            {
                var dryingCompleteDto = new DryingCompletedEvent.DryingCompletedDto(request.JobId, date, userName);
                var dryingCompleteEvent = new DryingCompletedEvent(dryingCompleteDto, correlationId);
                return new OutboxMessage(dryingCompleteEvent.ToJson(), nameof(DryingCompletedEvent),
                    correlationId, userName);
            }

            IEnumerable<ValidationFailure> ValidateRequest(Command request, Job job)
            {

                foreach (var equipmentPlacementDto in request.EquipmentPlacements)
                {
                    if (!equipmentPlacementDto.BeginVisitId.HasValue && !equipmentPlacementDto.EndVisitId.HasValue)
                        yield return new ValidationFailure(
                           nameof(Command.EquipmentPlacements),
                           "BeginVisit and EndVisit are not valid");

                    else
                    {
                        var jobVisitLookups = job.JobVisits.Select(x => x.Id).ToHashSet();
                        var equipmentPlacementLookups = job.JobAreas
                            .SelectMany(x => x.EquipmentPlacements)
                            .ToDictionary(x => x.Id);

                        if (equipmentPlacementDto.BeginVisitId.HasValue &&
                            !jobVisitLookups.Contains(equipmentPlacementDto.BeginVisitId.Value))
                            yield return new ValidationFailure(
                                nameof(Command.EquipmentPlacements),
                                "JobVisit not found", equipmentPlacementDto.BeginVisitId.Value);

                        if (equipmentPlacementDto.EndVisitId.HasValue &&
                            !jobVisitLookups.Contains(equipmentPlacementDto.EndVisitId.Value))
                            yield return new ValidationFailure(
                                nameof(Command.EquipmentPlacements),
                                "JobVisit not found", equipmentPlacementDto.EndVisitId.Value);

                        if (!equipmentPlacementLookups.ContainsKey(equipmentPlacementDto.EquipmentPlacementId))
                            yield return new ValidationFailure(
                                nameof(Command.EquipmentPlacements),
                                "EquipmentPlacement not found", equipmentPlacementDto.EquipmentPlacementId);
                        else 
                        {
                            var equipmentPlacement = equipmentPlacementLookups[equipmentPlacementDto.EquipmentPlacementId];

                            var beginDate = GetVisitDateOrDefault(job.JobVisits, equipmentPlacementDto.BeginVisitId, equipmentPlacement.BeginDate);
                            var endDate = GetVisitDateOrDefault(job.JobVisits, equipmentPlacementDto.EndVisitId, equipmentPlacement.EndDate);

                            if (endDate.HasValue && endDate < beginDate)
                                yield return new ValidationFailure(
                                nameof(Command.EquipmentPlacements),
                                "EndDate must be greater than BeginDate", endDate);
                        }
                    }
                }
            }

            private DateTime? GetVisitDateOrDefault(ICollection<JobVisit> jobVisits, Guid? visitId, DateTime? defaultDate)
            {
                if (visitId.HasValue)
                    return jobVisits.FirstOrDefault(x => x.Id == visitId).Date;

                return defaultDate;
            }
        }
    }
}
