﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models.Customization.Dtos;
using Servpro.FranchiseSystems.Framework.Setup.FeatureFlags;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.WipBoards
{
    public class GetEmployeeWipCustomization
    {
        public class Query : IRequest<ICollection<GridColumnDto>>
        {
            public Guid EmployeeId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public string Name { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
            }
        }

        public class Handler : IRequestHandler<Query, ICollection<GridColumnDto>>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IConfiguration _config;
            private readonly IFeatureFlagUtility _featureFlags;

            public Handler(JobReadOnlyDataContext db, 
                IConfiguration config,
                IFeatureFlagUtility featureFlags)
            {
                _db = db;
                _config = config;
                _featureFlags = featureFlags;
            }

            public async Task<ICollection<GridColumnDto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var employeeCustomization = await _db.EmployeeWipColumnCustomization.
                    FirstOrDefaultAsync(u => u.EmployeeId == request.EmployeeId, cancellationToken);

                if (employeeCustomization == null)
                {
                    var franchiseCustomization = await _db.FranchiseSetWipColumnCustomization.
                        FirstOrDefaultAsync(u => u.FranchiseSetId == request.FranchiseSetId, cancellationToken);

                    if (franchiseCustomization == null)
                    {
                        var customization = await _db.ServproWipColumnCustomization.FirstOrDefaultAsync(cancellationToken);

                        if (!_featureFlags.IsFeatureEnabled("StormPreliminaryEstimate"))
                        {
                            var stormEstimateColumn = customization.Data.FirstOrDefault(x => x.ColumnName == "StormPreliminaryEstimate");
                            customization.Data.Remove(stormEstimateColumn);
                        }
                        return customization.Data;
                    }
                    return franchiseCustomization.Data;
                }
              

                return employeeCustomization.Data;
            }
        }
    }
}
