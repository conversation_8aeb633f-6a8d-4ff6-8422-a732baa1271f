﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobDateCreatedEvent;

using JobDateTypes = Servpro.Franchise.LookupService.Constants.JobDateTypes;
using JournalNoteCategories = Servpro.Franchise.LookupService.Constants.JournalNoteCategories;
using JournalNoteTypes = Servpro.Franchise.LookupService.Constants.JournalNoteTypes;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobDateCreated
    {
        public class Event : JobDateCreatedEvent, IRequest
        {
            public Event(JobDateCreatedDto jobDateCreatedDto, Guid correlationId) : base(jobDateCreatedDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobDateCreated> _logger;

            public Handler(JobDataContext db, ILogger<JobDateCreated> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with: {@request}", request);

                var jobDateCreatedDto = request.JobDateCreated;

                var job = await _db.Jobs.Include(x => x.JobVisits).FirstOrDefaultAsync(x => x.Id == jobDateCreatedDto.JobId, cancellationToken: cancellationToken);
                if (job != null)
                {
                    job.SetOrUpdateDate(jobDateCreatedDto.JobDateTypeId, jobDateCreatedDto.Date);

                    SetExtraDates(request.JobDateCreated, job);

                    if (jobDateCreatedDto.JobDateTypeId == JobDateTypes.CustomerCalled)
                    {
                        var receivedDate = job.GetDate(JobDateTypes.LossReceived);
                        var dispatchDate = job.GetDate(JobDateTypes.Dispatch);

                        if (job.JobSourceId == JobSources.Franchise && receivedDate.HasValue
                            && (DateTime.UtcNow - receivedDate.Value).TotalMinutes > 61)
                        {
                            await CreateCustomerCalledJournalNote(jobDateCreatedDto, request.CorrelationId, cancellationToken);
                        }
                        else if (dispatchDate.HasValue && (DateTime.UtcNow - dispatchDate.Value).TotalMinutes > 31)
                        {
                            await CreateCustomerCalledJournalNote(jobDateCreatedDto, request.CorrelationId, cancellationToken);
                        }
                    }

                    if (jobDateCreatedDto.JobDateTypeId == Servpro.Franchise.LookupService.Constants.JobDateTypes.JobNotSold)
                    {
                        var wipRecord = await _db.WipRecords.FirstOrDefaultAsync(x => x.Id == jobDateCreatedDto.JobId, cancellationToken);
                        wipRecord.NotSoldOrCancelledDate = jobDateCreatedDto.Date;
                    }

                    await _db.SaveChangesAsync(cancellationToken);

                    _logger.LogDebug("Job date successfully saved");
                }
                else
                {
                    _logger.LogWarning("The Job With Id({ID}) Does Not Exist", jobDateCreatedDto.JobId);
                }

                return Unit.Value;
            }

            private void SetExtraDates(JobDateCreatedDto jobDateCreated, Job job)
            {
                if (jobDateCreated.JobDateTypeId == JobDateTypes.InitialOnSiteArrival)
                {
                    _logger.LogInformation("Set Extra Dates, Site inspected date: {siteinspected}", jobDateCreated.Date);
                    job.SetOrUpdateDate(JobDateTypes.SiteAppointmentCreated, DateTime.UtcNow);

                    //If user sets 'Site inspection date' in RM, we need to create a visit if it
                    //already does not exist
                    try
                    {
                        var jobVisit = job.JobVisits
                        .OrderBy(x => x.Date)
                        .FirstOrDefault();

                        _logger.LogInformation("Set Extra Dates, Job Visit: {@jobVisit}", jobVisit);
                        if (jobVisit is null)
                        {
                            _logger.LogInformation("Set Extra Dates, Creating a new job visit");
                            jobVisit = new JobVisit
                            {
                                Id = Guid.NewGuid(),
                                Date = jobDateCreated.Date,
                                JobId = job.Id,
                                CreatedDate = DateTime.UtcNow,
                                CreatedBy = jobDateCreated.CreatedBy,
                                JobVisitTriStateAnswers = new List<JobVisitTriStateAnswer>()
                            };
                            job.JobVisits.Add(jobVisit);
                        }
                        else
                        {
                            _logger.LogInformation("Set Extra Dates, Updating existing job visit date");
                            jobVisit.Date = jobDateCreated.Date;
                            jobVisit.ModifiedBy = jobDateCreated.CreatedBy;
                            jobVisit.ModifiedDate = DateTime.UtcNow;
                        }
                    }
                    catch (Exception exc)
                    {
                        _logger.LogError(exc, "Could not find Job visit");
                    }
                }
            }

            private async System.Threading.Tasks.Task CreateCustomerCalledJournalNote(
                JobDateCreatedDto jobDateCreated, Guid correlationId, CancellationToken cancellationToken)
            {
                var journalNote = new JournalNote
                {
                    Id = Guid.NewGuid(),
                    JobId = jobDateCreated.JobId,
                    CategoryId = JournalNoteCategories.Notes,
                    TypeId = JournalNoteTypes.CustomerInitialContact,
                    VisibilityId = JournalNoteVisibilityTypes.FranchiseAndClient,
                    Subject = "Customer Called Time",
                    Note = "Customer Called Time Exception Note",
                    Author = jobDateCreated.CreatedBy,
                    ActionDate = DateTime.UtcNow,
                    CreatedBy = jobDateCreated.CreatedBy,
                    CreatedDate = DateTime.UtcNow,
                };

                await _db.JournalNote.AddAsync(journalNote, cancellationToken);

                var journalNoteCreatedEvent = GenerateJournalNoteCreatedEvent(journalNote, correlationId);
                await _db.OutboxMessages.AddAsync(journalNoteCreatedEvent, cancellationToken);
            }

            private OutboxMessage GenerateJournalNoteCreatedEvent(JournalNote journalNote, Guid correlationId)
            {
                var journalNoteCreatedDto = MapJournalNoteDto(journalNote);
                var journalNoteCreatedEvent = new JournalNoteCreatedEvent(journalNoteCreatedDto, correlationId);
                return new OutboxMessage(journalNoteCreatedEvent.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, journalNote.CreatedBy);
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteDto(JournalNote journalNote) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId ?? Guid.Empty,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = journalNote.CreatedBy
                };
        }
    }
}