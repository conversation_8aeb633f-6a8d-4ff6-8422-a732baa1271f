﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Models;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddMarketingKPMDashboardMenu : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var Items = new HashSet<MenuItem>() {
                        new MenuItem()
                        {
                            Id = new Guid("9e46d5e2-5349-4f80-ac0a-84388daa44aa"),
                            Order = 1,
                            Name = "KPM Dashboard - Owner View",
                            Url = "views/KPMDashboard-Owner/KPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFinancialRibbonAccess"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("9a152e44-6a00-4d17-bbf4-0a4dfd509f16"),
                            Order = 2,
                            Name = "KPM Dashboard",
                            Url = "views/KPMDashboard/KPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("10196706-1a1a-46f5-ad98-7be85c16daf1"),
                            Order = 3,
                            Name = "Client KPM Scorecard",
                            Url = "views/ClientKPMDashboard/ClientKPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("e400a392-2bdf-4072-8cfc-36aebb720ec7"),
                            Order = 4,
                            Name = "Financial KPM Scorecard",
                            Url = "FinancialKPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFinancialRibbonAccess"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("202ee198-b0cc-4869-a206-2dff4faf31e1"),
                            Order = 5,
                            Name = "Office KPM Scorecard",
                            Url = "OfficeKPMsDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("a85b10c5-0986-4961-86a5-1b00e5b21618"),
                            Order = 6,
                            Name = "Production KPM Scorecard",
                            Url = "ProductionKPMsDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("229618d9-8a8d-4607-9661-59b760fd212e"),
                            Order = 7,
                            Name = "Marketing KPM Dashboard",
                            Url = "MarketingKPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseMarketingSupportCoordinatorMSCs"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("efb2a0d0-f627-4bcc-8349-80b5041e9335"),
                            Order = 8,
                            Name = "Drive the 5 Scorecard",
                            Url = "DrivetheFiveScorecard",
                            Roles = new HashSet<string>()
                            {

                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("ea8daf13-7612-4e49-ab71-83a364f3e570"),
                            Order = 9,
                            Name = "Revenue Dashboard",
                            Url = "views/RevenueDashboard/VolumeDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("11a2ef5c-3ab7-4487-addb-4f40451dd6f5"),
                            Order = 10,
                            Name = "Additional Reports",
                            Url = "projects",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        }
                    };

            migrationBuilder.UpdateData(
                table: "MenuItem",
                keyColumn: "Id",
                keyValue: new Guid("8996855a-f826-4052-8f67-ac2a7e463f95"),
                column: "Items",
                value: Items.ToJson());
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

            var Items = new HashSet<MenuItem>()
                    {
                        new MenuItem()
                        {
                            Id = new Guid("9e46d5e2-5349-4f80-ac0a-84388daa44aa"),
                            Order = 1,
                            Name = "KPM Dashboard - Owner View",
                            Url = "views/KPMDashboard-Owner/KPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFinancialRibbonAccess"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("9a152e44-6a00-4d17-bbf4-0a4dfd509f16"),
                            Order = 2,
                            Name = "KPM Dashboard",
                            Url = "views/KPMDashboard/KPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("10196706-1a1a-46f5-ad98-7be85c16daf1"),
                            Order = 3,
                            Name = "Client KPM Scorecard",
                            Url = "views/ClientKPMDashboard/ClientKPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("e400a392-2bdf-4072-8cfc-36aebb720ec7"),
                            Order = 4,
                            Name = "Financial KPM Scorecard",
                            Url = "FinancialKPMDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFinancialRibbonAccess"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("202ee198-b0cc-4869-a206-2dff4faf31e1"),
                            Order = 5,
                            Name = "Office KPM Scorecard",
                            Url = "OfficeKPMsDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("a85b10c5-0986-4961-86a5-1b00e5b21618"),
                            Order = 6,
                            Name = "Production KPM Scorecard",
                            Url = "ProductionKPMsDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("efb2a0d0-f627-4bcc-8349-80b5041e9335"),
                            Order = 7,
                            Name = "Drive the 5 Scorecard",
                            Url = "DrivetheFiveScorecard",
                            Roles = new HashSet<string>()
                            {

                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("ea8daf13-7612-4e49-ab71-83a364f3e570"),
                            Order = 8,
                            Name = "Revenue Dashboard",
                            Url = "views/RevenueDashboard/VolumeDashboard",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        },
                        new MenuItem()
                        {
                            Id = new Guid("11a2ef5c-3ab7-4487-addb-4f40451dd6f5"),
                            Order = 9,
                            Name = "Additional Reports",
                            Url = "projects",
                            Roles = new HashSet<string>()
                            {
                                "F_AllFranchiseOwners", "F_AllFranchiseGeneralManagers", "F_AllFranchiseMarketingManagerSMMs", "F_AllFranchiseOfficeManagers", "F_AllFranchiseOperationsManagers", "F_AllFranchiseProductionManagers", "F_AllFranchiseConstructionOperationsManagers", "F_AllFranchiseConstructionManagers"
                            }.ToList(),
                        }
                    };

            migrationBuilder.UpdateData(
                table: "MenuItem",
                keyColumn: "Id",
                keyValue: new Guid("8996855a-f826-4052-8f67-ac2a7e463f95"),
                column: "Items",
                value: Items.ToJson());
        }
    }
}
