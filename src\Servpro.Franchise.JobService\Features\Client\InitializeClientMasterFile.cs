﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.CorporateService;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Client
{
    public class InitializeClientMasterFile
    {
        public class Event : IRequest {  }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<Handler> _logger;
            private readonly ICorporateServiceClient _corporateServiceClient;

            public Handler(JobDataContext context,
                ILogger<Handler> logger,
                ICorporateServiceClient corporateServiceClient)
            { 
                _context = context;
                _logger = logger;
                _corporateServiceClient = corporateServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var correlationId = Guid.NewGuid();
                using var scope = _logger.BeginScope("{correlationId}", correlationId);
                _logger.LogInformation("{Feature} invoked", nameof(InitializeClientMasterFile));

                var clientMasterFileRecords = (await _corporateServiceClient.GetClientMasterFileRecords(correlationId, cancellationToken)).ToList();
                _logger.LogInformation("Processing {clientCount} insurance clients", clientMasterFileRecords.Count);

                var clientIds = clientMasterFileRecords.Select(x => x.Id).ToList();
                var clientNumbers = clientMasterFileRecords.Select(x => x.InsuranceNumber).ToList();
                var existingClients = await _context.InsuranceClients
                    .Where(x => clientIds.Contains(x.Id) || clientNumbers.Contains(x.InsuranceNumber))
                    .ToListAsync(cancellationToken);
                foreach (var clientMasterFileRecord in clientMasterFileRecords)
                {
                    using var clientScope = _logger.BeginScope(
                        "{clientId}{insuranceNumber}",
                        clientMasterFileRecord.Id,
                        clientMasterFileRecord.InsuranceNumber);
                    _logger.LogInformation("Processing client.");
                    _logger.LogDebug("Client: {@clientMasterFileRecord}", clientMasterFileRecord);

                    var insuranceClient = existingClients
                        .FirstOrDefault(x => x.Id == clientMasterFileRecord.Id || x.InsuranceNumber == clientMasterFileRecord.InsuranceNumber);

                    if (insuranceClient == null)
                    {
                        _logger.LogInformation("New client found. Mapping to InsuranceClient");
                        insuranceClient = new InsuranceClient
                        {
                            Id = clientMasterFileRecord.Id,
                            Name = clientMasterFileRecord.Name,
                            InsuranceNumber = clientMasterFileRecord.InsuranceNumber,
                            ParentInsuranceNumber = clientMasterFileRecord.InsuranceNumber,
                            IsActive = clientMasterFileRecord.IsActive,
                            IsLocalAuditRequired = clientMasterFileRecord.AuditLocalJobs,
                            IsAuditRequired = clientMasterFileRecord.AuditJobs,
                            CreatedDate = clientMasterFileRecord.CreatedDate
                        };

                        await _context.InsuranceClients.AddAsync(insuranceClient, cancellationToken);
                    }
                    else
                    {
                        _logger.LogInformation("Client already exist. Updating client information");
                        insuranceClient.Id = insuranceClient.Id != clientMasterFileRecord.Id ? clientMasterFileRecord.Id : insuranceClient.Id; //Implementation in case ClientMasterGroup added the placeholder client with the insuranceNumber;
                        insuranceClient.Name = clientMasterFileRecord.Name;
                        insuranceClient.InsuranceNumber = clientMasterFileRecord.InsuranceNumber;
                        insuranceClient.IsActive = clientMasterFileRecord.IsActive;
                        insuranceClient.IsLocalAuditRequired = clientMasterFileRecord.AuditLocalJobs;
                        insuranceClient.IsAuditRequired = clientMasterFileRecord.AuditJobs;
                        insuranceClient.CreatedDate = clientMasterFileRecord.CreatedDate;
                        insuranceClient.ModifiedDate = clientMasterFileRecord.ModifiedDate;
                    }
                }

                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{Feature} completed - processed {count} records", nameof(InitializeClientMasterFile), clientMasterFileRecords?.Count());

                return Unit.Value;
            }
        }
    }
}
