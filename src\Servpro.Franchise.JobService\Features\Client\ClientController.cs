﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Client
{
    [Route("api/clients")]
    public class ClientController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ClientController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet("get-insurance-clients")]
        public async Task<ActionResult<IEnumerable<GetInsuranceClients.Dto>>> GetInsuranceClients()
        {
            var insuranceClients = await _mediator.Send(new GetInsuranceClients.Query());
            return Ok(insuranceClients);
        }
        [HttpGet("get-insurance-client/{id}")]
        public async Task<ActionResult<GetInsuranceClient.Dto>> GetInsuranceClient(Guid id)
        {
            var insuranceClient = await _mediator.Send(new GetInsuranceClient.Query { Id = id });
            return Ok(insuranceClient);
        }
    }
}
