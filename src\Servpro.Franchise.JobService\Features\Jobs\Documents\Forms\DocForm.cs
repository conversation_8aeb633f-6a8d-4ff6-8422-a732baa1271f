﻿using System;
using System.IO;
using System.Linq;
using Aspose.Words;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public class DocForm : FormBase<WordprocessingDocument, Text>
    {
        private readonly License _license;
        protected WordprocessingDocument Form;
        private MemoryStream _stream;

        public DocForm()
        {
            _license = new License();
            _license.SetLicense("Aspose.Words.lic");
        }

        protected override void Open(MemoryStream stream)
        {
            _stream = stream;
            Form = WordprocessingDocument.Open(_stream, true);
        }

        protected override System.Collections.Generic.IEnumerable<Text> GetFields(System.Collections.Generic.IEnumerable<Text> allFields, string token)
        {
            return allFields.Where(x => x.Text.Equals(token));
        }

        protected override Text[] GetFields()
        {
            var fields = Form.MainDocumentPart.Document.Body.Descendants<Text>();
            return fields.Where(x => !String.IsNullOrWhiteSpace(x.Text)).ToArray();
        }

        protected override void SetFieldValue(Text field, string value, Guid formFieldTypeId)
        {
            field.Text = value;
        }

        protected override Aspose.Pdf.PageCollection Save(MemoryStream stream)
        {
            //Close and Save form, this ensures the stream being used to create the new file is up to date
            Form.Close();
            stream.Seek(0, SeekOrigin.Begin);
            var tempFilePath = Path.GetTempFileName().Replace(".tmp", ".pdf");
            using (var pdfStream = File.Create(tempFilePath))
            {
                // save the word document to a stream as a pdf
                var wordDoc = new Aspose.Words.Document(stream);
                wordDoc.Save(pdfStream, SaveFormat.Pdf);
            }
            Aspose.Pdf.Document pdf = null;
            using (var pdfStream = File.OpenRead(tempFilePath))
            {
                // read the word pdf stream into a pdf document
                pdf = new Aspose.Pdf.Document(pdfStream);
            }
            File.Delete(tempFilePath);
            return pdf.Pages;
        }

        public override void Dispose()
        {
            base.Dispose();
            
            Form = null;
            _stream?.Dispose();

            GC.SuppressFinalize(this);
        }
    }
}