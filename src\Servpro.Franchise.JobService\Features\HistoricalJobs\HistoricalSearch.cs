﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.HistoricalJobs
{
    public class HistoricalSearch
    {
        public class Dto
        {
            public Guid JobId { get; set; }
            public DateTime DateOfLoss { get; set; }
            public string LossAddress { get; set; }
            public string LossType { get; set; }
            public string StructureType { get; set; }
            public string CustomerName { get; set; }
            public string JobNumber { get; set; }
            public string TierStatus { get; set; }
        }

        public enum SearchType
        {
            JobNumber,
            CustomerFirstName,
            CustomerLastName,
            LossAddress
        }

        public class Command : IRequest<IEnumerable<Dto>>
        {
            public SearchType Type { get; set; }
            public string SearchString { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(c => c.SearchString.Length).GreaterThan(2).WithMessage("Search String length must be greater than two.");
            }
        }

        public class Handler : IRequestHandler<Command, IEnumerable<Dto>>
        {
            private readonly IUserInfoAccessor _userInfo;
            private readonly string _tableName;
            private IAmazonDynamoDB _client { get; }
            private readonly ILogger<HistoricalSearch> _logger;

            public Handler(
                IAmazonDynamoDB client, 
                IUserInfoAccessor userInfo,
                IConfiguration config,
                ILogger<HistoricalSearch> logger)
            {
                _client = client;
                _userInfo = userInfo;
                _tableName = config["DynamoDB:HistoricTableName"];
                _logger = logger;
            }

            public async Task<IEnumerable<Dto>> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation($"Searching for historic jobs with search string: {request.SearchString} for field: {nameof(request.Type)}");

                var userInfo = _userInfo.GetUserInfo();
                return await GetHistoricJobsAsync(request, userInfo.FranchiseSetId.Value, cancellationToken);
            }

            private Dto Map(Dictionary<string, AttributeValue> item)
            {
                item.TryGetValue(HistoricServiceTable.JobFields.JobId, out AttributeValue jobId);
                item.TryGetValue(HistoricServiceTable.JobFields.LossDate, out AttributeValue dateOfLoss);
                item.TryGetValue(HistoricServiceTable.JobFields.LossAddress1, out AttributeValue lossAddress1);
                item.TryGetValue(HistoricServiceTable.JobFields.LossStateAbbreviation, out AttributeValue lossStateAbbreviation);
                item.TryGetValue(HistoricServiceTable.JobFields.LossCity, out AttributeValue lossCity);
                item.TryGetValue(HistoricServiceTable.JobFields.LossPostalCode, out AttributeValue lossPostalCode);
                item.TryGetValue(HistoricServiceTable.JobFields.LossType, out AttributeValue lossType);
                item.TryGetValue(HistoricServiceTable.JobFields.StructureType, out AttributeValue structureType);
                item.TryGetValue(HistoricServiceTable.JobFields.CustomerFirstName, out AttributeValue customerFirstName);
                item.TryGetValue(HistoricServiceTable.JobFields.CustomerLastName, out AttributeValue customerLastName);
                item.TryGetValue(HistoricServiceTable.JobFields.JobNumber, out AttributeValue jobNumber);
                item.TryGetValue(HistoricServiceTable.JobFields.Tier1StatusTier2Status, out AttributeValue tierStatus);

                return new Dto()
                {
                    JobId = Guid.Parse(jobId?.S),
                    DateOfLoss = DateTime.Parse(dateOfLoss?.S),
                    LossAddress = $"{lossAddress1?.S} {lossCity?.S} {lossStateAbbreviation?.S} {lossPostalCode?.S}",
                    LossType = lossType?.S,
                    StructureType = structureType?.S,
                    CustomerName = $"{customerFirstName?.S} {customerLastName?.S}",
                    JobNumber = jobNumber?.S,
                    TierStatus = tierStatus?.S
                };
            }



            async Task<IEnumerable<Dto>> GetHistoricJobsAsync(
                Command request, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var result = new List<Dto>();
                var searchString = request.SearchString.ToLower();

                if (request.Type == SearchType.JobNumber)
                {
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.JobNumber, HistoricServiceTable.JobFields.JobNumber, cancellationToken))
                        result.Add(job);
                }
                else if (request.Type == SearchType.CustomerFirstName)
                {
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.SearchCustomerFirstName, HistoricServiceTable.JobFields.SearchCustomerFirstName, cancellationToken))
                        result.Add(job);
                }
                else if (request.Type == SearchType.CustomerLastName)
                {
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.SearchCustomerLastName, HistoricServiceTable.JobFields.SearchCustomerLastName, cancellationToken))
                        result.Add(job);
                }
                else
                {
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.SearchLossAddress1, HistoricServiceTable.JobFields.SearchLossAddress1, cancellationToken))
                        result.Add(job);
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.SearchLossAddress2, HistoricServiceTable.JobFields.SearchLossAddress2, cancellationToken))
                        result.Add(job);
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.SearchLossCity, HistoricServiceTable.JobFields.SearchLossCity, cancellationToken))
                        result.Add(job);
                    await foreach (var job in RunQuery(franchiseSetId, searchString, HistoricServiceTable.Indexes.LossPostalCode, HistoricServiceTable.JobFields.LossPostalCode, cancellationToken))
                        result.Add(job);

                    return result.Distinct();
                }

                return result;
            }

            async IAsyncEnumerable<Dto> RunQuery(Guid franchiseSetId, string searchString, string indexName, string rangeKeyName, [EnumeratorCancellation]CancellationToken cancellationToken)
            {
                var qRequest = new QueryRequest
                {
                    TableName = _tableName,
                    ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                            {
                              { ":hashValue",   new AttributeValue { S = franchiseSetId.ToString().ToLower() } },
                              { ":rangeValue",   new AttributeValue { S = searchString } }
                            },
                    ExpressionAttributeNames = new Dictionary<string, string>
                            {
                                { "#lsa", HistoricServiceTable.JobFields.LossStateAbbreviation },
                                { "#ld", HistoricServiceTable.JobFields.LossDate },
                                { "#lc", HistoricServiceTable.JobFields.LossCity },
                                { "#cfn", HistoricServiceTable.JobFields.CustomerFirstName },
                                { "#cln", HistoricServiceTable.JobFields.CustomerLastName },
                                { "#st", HistoricServiceTable.JobFields.StructureType },
                                { "#la", HistoricServiceTable.JobFields.LossAddress1 },
                                { "#lpc", HistoricServiceTable.JobFields.LossPostalCode },
                                { "#lt", HistoricServiceTable.JobFields.LossType },
                                { "#jn", HistoricServiceTable.JobFields.JobNumber },
                                { "#ts", HistoricServiceTable.JobFields.Tier1StatusTier2Status },
                                { "#id", HistoricServiceTable.JobFields.JobId }
                            },
                    KeyConditionExpression = $"HashKey = :hashValue and begins_with({rangeKeyName}, :rangeValue)",
                    ProjectionExpression = "#lsa,#ld,#lc,#cfn,#cln,#st,#la,#lpc,#ts,#id,#lt,#jn",
                    IndexName = indexName
                };

                Dictionary<string, AttributeValue> lastKeyEvaluated = null;
                do
                {
                    qRequest.ExclusiveStartKey = lastKeyEvaluated;

                    var response = await _client.QueryAsync(qRequest, cancellationToken);
                    foreach (var item in response.Items)
                        yield return Map(item);
                    lastKeyEvaluated = response.LastEvaluatedKey;
                } while (lastKeyEvaluated != null && lastKeyEvaluated.Count != 0);
            }
        }
    }
}
