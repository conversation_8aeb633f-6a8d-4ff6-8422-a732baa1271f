﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public partial class GetDehumidifierReadings
    {
        public class Dto
        {
            public Dto(
                Guid id,
                string name,
                string description,
                int? maxOccurences,
                int? displayOrder,
                bool shouldCollectNA,
                bool isNameRequired,
                IEnumerable<VisitValuePair<decimal?>> tempsFarenheight,
                IEnumerable<VisitValuePair<decimal?>> tempsCelcius,
                IEnumerable<VisitValuePair<decimal?>> relativeHumidities,
                IEnumerable<VisitValuePair<decimal?>> humidityRatios,
                IEnumerable<VisitValuePair<bool?>> isHvacOn,
                IEnumerable<VisitReadingValue<Guid?>> notes,
                IEnumerable<RoomSummary> rooms)
            {
                Id = id;
                Name = name;
                Description = description;
                MaxOccurences = maxOccurences;
                DisplayOrder = displayOrder;
                ShouldCollectNA = shouldCollectNA;
                IsNameRequired = isNameRequired;
                TempsFarenheight = tempsFarenheight;
                TempsCelcius = tempsCelcius;
                RelativeHumidities = relativeHumidities;
                HumidityRatios = humidityRatios;
                IsHvacOn = isHvacOn;
                Notes = notes;
                Rooms = rooms;
            }

            public Guid Id { get; }
            public string Name { get; }
            public string Description { get; }
            public int? MaxOccurences { get; }
            public int? DisplayOrder { get; }
            public bool ShouldCollectNA { get; }
            public bool IsNameRequired { get; }
            public IEnumerable<VisitValuePair<decimal?>> TempsFarenheight { get; }
            public IEnumerable<VisitValuePair<decimal?>> TempsCelcius { get; }
            public IEnumerable<VisitValuePair<decimal?>> RelativeHumidities { get; }
            public IEnumerable<VisitValuePair<decimal?>> HumidityRatios { get; }
            public IEnumerable<VisitValuePair<bool?>> IsHvacOn { get; }
            public IEnumerable<VisitReadingValue<Guid?>> Notes { get; }
            public IEnumerable<RoomSummary> Rooms { get; }

            public class VisitNote
            {
                public VisitNote(
                    Guid visitId,
                    Guid? journalNoteId,
                    string note)
                {
                    VisitId = visitId;
                    JournalNoteId = journalNoteId;
                    Note = note;
                }

                public Guid VisitId { get; }
                public Guid? JournalNoteId { get; }
                public string Note { get; }
            }

            public class VisitValuePair<T>
            {
                public VisitValuePair(
                    Guid visitId,
                    T value,
                    bool includedInVisit)
                {
                    VisitId = visitId;
                    Value = value;
                    IncludedInVisit = includedInVisit;
                }

                public Guid VisitId { get; }
                public T Value { get; }
                public bool IncludedInVisit { get; }
            }

            public class VisitReadingValue<T>
            {
                public VisitReadingValue(
                    T value,
                    Guid? readingId,
                    Guid visitId,
                    bool includedInVisit)
                {
                    Value = value;
                    ReadingId = readingId;
                    VisitId = visitId;
                    IncludedInVisit = includedInVisit;
                }

                public T Value { get; }
                public Guid? ReadingId { get; }
                public Guid VisitId { get; }
                public bool IncludedInVisit { get; }
            }

            public class RoomSummary
            {
                public RoomSummary(
                    Guid jobAreaId,
                    Guid? roomId,
                    string name,
                    IEnumerable<EquipmentReading> equipmentReadings)
                {
                    JobAreaId = jobAreaId;
                    RoomId = roomId;
                    Name = name;
                    EquipmentReadings = equipmentReadings;
                }

                public Guid JobAreaId { get; }
                public Guid? RoomId { get; }
                public string Name { get; }
                public IEnumerable<EquipmentReading> EquipmentReadings { get; }
            }
            
            public class EquipmentReading
            {
                public EquipmentReading(
                    Guid equipmentPlacementId,
                    string equipmentName,
                    IEnumerable<VisitReadingValue<decimal?>> tempsFarenheight,
                    IEnumerable<VisitReadingValue<decimal?>> tempsCelcius,
                    IEnumerable<VisitReadingValue<decimal?>> relativeHumidities,
                    IEnumerable<VisitReadingValue<int?>> hours,
                    IEnumerable<VisitReadingValue<decimal?>> humidityRatios,
                    IEnumerable<VisitReadingValue<decimal?>> gDeps,
                    IEnumerable<VisitValuePair<bool>> inconsistentHoursIndicator,
                    IEnumerable<VisitValuePair<Guid?>> previousVisitIds,
                    IEnumerable<VisitValuePair<Guid?>> followingVisitIds,
                    IEnumerable<VisitValuePair<int?>> previousVisitHours,
                    IEnumerable<VisitValuePair<decimal?>> previousVisitHumidityRatios,
                    IEnumerable<VisitValuePair<decimal?>> previousVisitGDeps,
                    IEnumerable<VisitValuePair<int?>> followingVisitHours,
                    IEnumerable<VisitValuePair<decimal?>> followingVisitHumidityRatios)
                {
                    EquipmentPlacementId = equipmentPlacementId;
                    EquipmentName = equipmentName;
                    TempsFarenheight = tempsFarenheight;
                    TempsCelcius = tempsCelcius;
                    RelativeHumidities = relativeHumidities;
                    Hours = hours;
                    InconsistentHoursIndicator = inconsistentHoursIndicator;
                    HumidityRatios = humidityRatios;
                    Gdeps = gDeps;
                    PreviousVisitIds = previousVisitIds;
                    FollowingVisitIds = followingVisitIds;
                    PreviousVisitHours = previousVisitHours;
                    PreviousVisitHumidityRatios = previousVisitHumidityRatios;
                    PreviousVisitGdeps = previousVisitGDeps;
                    FollowingVisitHours = followingVisitHours;
                    FollowingVisitHumidityRatios = followingVisitHumidityRatios;
                }

                public Guid EquipmentPlacementId { get; }
                public string EquipmentName { get; }
                public IEnumerable<VisitReadingValue<decimal?>> TempsFarenheight { get; }
                public IEnumerable<VisitReadingValue<decimal?>> TempsCelcius { get; }
                public IEnumerable<VisitReadingValue<decimal?>> RelativeHumidities { get; }
                public IEnumerable<VisitReadingValue<int?>> Hours { get; }
                public IEnumerable<VisitValuePair<bool>> InconsistentHoursIndicator { get; }
                public IEnumerable<VisitReadingValue<decimal?>> HumidityRatios { get; }
                public IEnumerable<VisitReadingValue<decimal?>> Gdeps { get; }
                public IEnumerable<VisitValuePair<Guid?>> PreviousVisitIds { get; }
                public IEnumerable<VisitValuePair<Guid?>> FollowingVisitIds { get; }
                public IEnumerable<VisitValuePair<int?>> PreviousVisitHours { get; }
                public IEnumerable<VisitValuePair<decimal?>> PreviousVisitHumidityRatios { get; }
                public IEnumerable<VisitValuePair<decimal?>> PreviousVisitGdeps { get; }
                public IEnumerable<VisitValuePair<int?>> FollowingVisitHours { get; }
                public IEnumerable<VisitValuePair<decimal?>> FollowingVisitHumidityRatios { get; }
            }
        }
    }
}
