﻿using AutoMapper;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Split
{
    public class SplitMappingProfile : Profile
    {
        public static readonly string EmployeeMappingKey = "EmployeeMapping";
        public static readonly string OwnerKey = "Owner";
        public static readonly string ServproOwnedBaseEquipmentKey = "ServproOwnedEquipmentTypeIds";
        public static readonly string XactJobsKey = "XactJobs";
        public SplitMappingProfile()
        {
            CreateMap<Contact, SplitContact>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Contact.Id)])))
                .ForMember(dest => dest.BusinessId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.BusinessId.IsNullOrEmpty() ? GuidTransformHelpers.TransformToManipulatedGuid(src.BusinessId, ctx.Items[nameof(Contact.Id)]) : src.BusinessId))
                .ForMember(dest => dest.MarketingRepId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.MarketingRepId.HasValue ?
                    GetNewEmployeeId(src.MarketingRepId.Value,
                    (ImmutableDictionary<Guid, Guid>)ctx.Items[EmployeeMappingKey],
                    (EmployeeMapping.Employee)ctx.Items[OwnerKey]) : src.MarketingRepId))
                .ForMember(dest => dest.Business, opt => opt.Ignore());
            CreateMap<Business, SplitBusiness>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Business.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => !src.FranchiseSetId.IsNullOrEmpty() ? ctx.Items[nameof(Business.FranchiseSetId)] : src.FranchiseSetId))
                .ForMember(dest => dest.Contacts, opt => opt.Ignore());
            CreateMap<EquipmentType, SplitEquipmentType>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(EquipmentType.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(EquipmentType.FranchiseSetId)]))
                // Only transform BaseEquipmentType if it it not a Servpro Owned BaseEquipmentType
                .ForMember(dest => dest.BaseEquipmentTypeId,
                    opt => opt.MapFrom((src, dest, _, ctx) => 
                        !src.BaseEquipmentTypeId.IsNullOrEmpty() && !IsServproOwnedBaseEquipment((ImmutableHashSet<Guid>)ctx.Items[ServproOwnedBaseEquipmentKey], src.BaseEquipmentTypeId.Value) 
                        ? GuidTransformHelpers.TransformToManipulatedGuid(src.BaseEquipmentTypeId, ctx.Items[nameof(EquipmentType.Id)]) 
                        : src.BaseEquipmentTypeId));
            CreateMap<EquipmentModel, SplitEquipmentModel>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(EquipmentModel.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(EquipmentModel.FranchiseSetId)]))
                .ForMember(dest => dest.EquipmentTypeId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.EquipmentType.FranchiseSetId.HasValue
                        ? GuidTransformHelpers.TransformToManipulatedGuid(src.EquipmentTypeId, ctx.Items[nameof(EquipmentModel.Id)])
                        : src.EquipmentTypeId))
                .ForMember(dest => dest.EquipmentType, opt => opt.Ignore());
            CreateMap<Equipment, SplitEquipment>()
                .ForMember(dest => dest.Id,
                    opt => opt.MapFrom((src, dest, _, ctx) => GuidTransformHelpers.TransformToManipulatedGuid(src.Id, ctx.Items[nameof(Equipment.Id)])))
                .ForMember(dest => dest.FranchiseSetId,
                    opt => opt.MapFrom((src, dest, _, ctx) => ctx.Items[nameof(Equipment.FranchiseSetId)]))
                .ForMember(dest => dest.EquipmentModelId,
                    opt => opt.MapFrom((src, dest, _, ctx) => src.EquipmentModel.FranchiseSetId.HasValue
                        ? GuidTransformHelpers.TransformToManipulatedGuid(src.EquipmentModelId, ctx.Items[nameof(Equipment.Id)])
                        : src.EquipmentModelId))
                .ForMember(dest => dest.EquipmentModel, opt => opt.Ignore());
        }

        private static List<JournalNote.BreRule> MapBreRules(List<JournalNote.BreRule> sourceBreRules, Guid resaleId)
        {
            if (sourceBreRules is null || !sourceBreRules.Any())
                return sourceBreRules;

            foreach (var breRule in sourceBreRules)
            {
                breRule.JobAreaId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.JobAreaId, resaleId);
                breRule.JobAreaMaterialId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.JobAreaMaterialId, resaleId);
                breRule.JobVisitId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.JobVisitId, resaleId);
                breRule.ZoneId = GuidTransformHelpers.TransformToManipulatedGuid(breRule.ZoneId, resaleId);
            }

            return sourceBreRules;
        }

        public static bool IsServproOwnedBaseEquipment(ImmutableHashSet<Guid> servproOwnedBaseEquipmentTypeIds, Guid baseEquipmentTypeId)
            => servproOwnedBaseEquipmentTypeIds.Contains(baseEquipmentTypeId);

        private static Guid? GetNewEmployeeId(Guid oldEmployeeId, ImmutableDictionary<Guid, Guid> employeeMapping, EmployeeMapping.Employee owner)
        {
            if (oldEmployeeId == Guid.Empty)
            {
                return null;
            }

            //check if the franchise provided a mapping, if a mapping was not provided, default to the owner
            return employeeMapping.ContainsKey(oldEmployeeId) ? employeeMapping[oldEmployeeId] : owner.Id;
        }

        private static Guid? GetNewEmployeeId(Guid? oldEmployeeId, object employeeMapping)
            => GetNewEmployeeId(oldEmployeeId, (ImmutableDictionary<Guid, Guid>)employeeMapping);
    }
}
