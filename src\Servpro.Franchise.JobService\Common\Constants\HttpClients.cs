﻿namespace Servpro.Franchise.JobService
{
    public static class HttpClients
    {
        public const string IdentityServer = "IdentityServer";
        public const string LookupService = "LookupService";
        public const string EquipmentService = "EquipmentService";
        public const string FranchiseService = "FranchiseService";
        public const string ClientRequirementsService = "ClientRequirementsService";
        public const string XactService = "XactService";
        public const string CorporateService = "CorporateService";
        public const string JobService = "JobService";
        public const string ProjectNumberService = "LambdaService";
        public const string NotificationService = "NotificationService";
        public const string GoogleService = "GoogleApi";
        public const string MicaService = "MicaService";
    }
}
