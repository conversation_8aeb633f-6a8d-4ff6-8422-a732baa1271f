﻿using Servpro.Franchise.JobService.Common;

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using Servpro.Franchise.LookupService.Features.LookUps;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public static class InitialInspectionQuestionIds
    {
        public static readonly ImmutableHashSet<Guid> AllQuestionIds = new HashSet<Guid>
        {
            JobTriStateQuestions.AnySpecialtyContentItems, // Verified 25 Jan 2021
            JobTriStateQuestions.AreAnyServicesNeededContentsPackoutTexttileDryCleaning, // Verified 25 Jan 2021
            JobTriStateQuestions.AreThereAnyQuestionsAboutCoverage, // Verified 25 Jan 2021
            JobTriStateQuestions.DidCustomerRefuseService, // Verified 25 Jan 2021
            JobTriStateQuestions.DoesMoldExceedSF, // subquestion for MoldIsPresent
            JobTriStateQuestions.HasSafetySecurityIssues, // Verified 25 Jan 2021
            JobTriStateQuestions.IsAccessToLossSiteRestricted, // Verified 25 Jan 2021
            JobTriStateQuestions.IsDemolitionNeeded, // Verified 25 Jan 2021
            JobTriStateQuestions.IsProvidingEstimateOnly, // Verified 25 Jan 2021
            JobTriStateQuestions.IsSpecialtyDryingEquipmentNeeded, // Verified 25 Jan 2021
            JobTriStateQuestions.IsSubrogationPotentialIdentified, // Verified 25 Jan 2021
            JobTriStateQuestions.MoldIsPresent, // Verified 25 Jan 2021
            JobTriStateQuestions.MoldLessThanSF, // subquestion for MoldIsPresent
            JobTriStateQuestions.WereLeadPaintTestPositive, // Verified 25 Jan 2021
            JobTriStateQuestions.WillAsbestosTestingBePerformed, // Verified 25 Jan 2021
            JobTriStateQuestions.WillDemoExceedHours, // Verified 25 Jan 2021
            JobTriStateQuestions.WillPackOutExceedHours, // Verified 25 Jan 2021
            JobTriStateQuestions.WillRepairsDelayDrying // Verified 25 Jan 2021
        }.ToImmutableHashSet();

        public static ImmutableHashSet<Guid> AskedPerVisitIds(GetLookups.Dto lookups)
        {
            var values = new HashSet<Guid>();

            AllQuestionIds.ToList().ForEach(x =>
            {
                if (lookups.JobTriStateQuestions.FirstOrDefault(q => q.Id == x).IsAskedPerVisit)
                {
                    values.Add(x);
                }
            });

            return values.ToImmutableHashSet();
        }

        public static ImmutableHashSet<Guid> NotAskedPerVisitIds(GetLookups.Dto lookups)
        {
            var values = new HashSet<Guid>();

            AllQuestionIds.ToList().ForEach(x =>
            {
                if (lookups.JobTriStateQuestions.FirstOrDefault(q => q.Id == x).IsAskedPerVisit == false)
                {
                    values.Add(x);
                }
            });

            return values.ToImmutableHashSet();
        }
    }
}