﻿using MediatR;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Erp.ErpService
{
    public interface IErpService
    {
        Task AssociateErpToJobAsync(Guid jobId, int erpId, CancellationToken cancellationToken);
        Task AssociateErpToIntakeLeadAsync(Job job, int erpId);
        Task RemoveErpFromJobAsync(Guid jobId, CancellationToken cancellationToken);
        Task<int?> GetErpFromJobAsync(Guid jobId, CancellationToken cancellationToken);
    }
}
