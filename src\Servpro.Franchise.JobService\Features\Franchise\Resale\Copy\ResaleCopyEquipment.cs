﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto.EquipmentMapping;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyEquipment
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ImmutableList<EquipmentDto> EquipmentSold { get; set; }
            public ProcessEntityResult EquipmentModelResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> EquipmentIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyEquipment>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyEquipment> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                IServiceProvider services,
                ILogger<ResaleCopyEquipment> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _services = services;
                _logger = logger;
                _mapper = mapper;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(Equipment));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.EquipmentIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var equipmentTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedEquipmentIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(equipmentTargetIds, 
                    GetEquipmentIdsAsync, 
                    cancellationToken);
                var equipmentSoldIds = request.EquipmentSold.Select(x => x.Id).ToHashSet();
                var equipmentSoldTargetIds = request.EquipmentSold
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId)).ToHashSet();
                return await ProcessEntitiesAsync<Equipment, ResaleEquipment>(
                    request.ResaleId,
                    equipment =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.EquipmentModelResult.FailedEntities.Contains(equipment.EquipmentModelId))
                            failedDependencies.Add((nameof(EquipmentModel), equipment.EquipmentModelId));

                        return failedDependencies;
                    },
                    alreadyCopiedEquipmentIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    sourceEntity =>
                    {
                        if (equipmentSoldIds.Contains(sourceEntity.Id))
                        {
                            sourceEntity.IsDeleted = true;
                        }
                    },
                    targetEntity =>
                    {
                        if (!equipmentSoldTargetIds.Contains(targetEntity.Id))
                        {
                            targetEntity.IsDeleted = true;
                        }
                    },
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Equipments.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Equipment>> GetSourceEntitiesAsync(List<Guid> equipmentIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var equipment = await _context.Equipments
                    .Where(e => equipmentIds.Contains(e.Id))
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", equipment.Count);
                return equipment;
            }

            private async Task<List<Guid>> GetEquipmentIdsAsync(List<Guid?> equipmentTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Equipments
                    .Where(x => equipmentTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
