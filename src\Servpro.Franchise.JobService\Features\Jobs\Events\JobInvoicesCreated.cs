﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using System.Collections.Generic;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobInvoicesCreated
    {
        public class Event : InvoicesCreatedEvent, IRequest
        {
            public Event(List<InvoiceCreatedDto> invoices, Guid correlationId) : base(invoices, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<JobInvoicesCreated> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<JobInvoicesCreated> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler started. Total invoices to process: {Count}", incomingEvent.Invoices.Count);

                var jobIds = incomingEvent.Invoices
                    .Select(i => i.JobId)
                    .Distinct()
                    .ToList();

                var invoiceIds = incomingEvent.Invoices
                    .Select(i => i.InvoiceId)
                    .Distinct()
                    .ToList();

                _logger.LogInformation("Distinct jobIds: {JobCount} | Distinct invoiceIds: {InvoiceCount}", jobIds.Count, invoiceIds.Count);

                Dictionary<Guid, Job> jobs;
                try
                {
                    _logger.LogDebug("Fetching jobs from DB...");

                    jobs = await _context.Jobs
                        .Where(j => jobIds.Contains(j.Id))
                        .Include(m => m.MediaMetadata)
                        .ThenInclude(j => j.JobInvoice)
                        .ToDictionaryAsync(j => j.Id, cancellationToken);

                    _logger.LogDebug("Successfully fetched {JobCount} jobs.", jobs.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error fetching jobs from database");
                    throw;
                }

                HashSet<Guid> existingInvoiceIds;
                try
                {
                    existingInvoiceIds = new HashSet<Guid>(await _context.JobInvoices
                        .Where(i => invoiceIds.Contains(i.Id))
                        .Select(i => i.Id)
                        .ToListAsync(cancellationToken));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking existing invoice IDs");
                    throw;
                }

                foreach (var incomingInvoice in incomingEvent.Invoices)
                {
                    _logger.LogDebug("Processing invoice {InvoiceId} for job {JobId}", incomingInvoice.InvoiceId, incomingInvoice.JobId);

                    if (!jobs.TryGetValue(incomingInvoice.JobId, out var job))
                    {
                        _logger.LogWarning("Job not found for invoice {InvoiceId} - JobId {JobId}", incomingInvoice.InvoiceId, incomingInvoice.JobId);
                        continue;
                    }

                    if (existingInvoiceIds.Contains(incomingInvoice.InvoiceId))
                    {
                        _logger.LogDebug("Invoice {InvoiceId} already exists. Skipping.", incomingInvoice.InvoiceId);
                        continue;
                    }

                    try
                    {
                        InvoiceUpsert(incomingInvoice, job);
                    }
                    catch (Exception upsertEx)
                    {
                        _logger.LogError(upsertEx, "Error upserting invoice {InvoiceId}", incomingInvoice.InvoiceId);
                    }
                }

                try
                {
                    _logger.LogDebug("Attempting to save changes to DB...");
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Database save successful.");
                }
                catch (DbUpdateException ex)
                {
                    _logger.LogError(ex, "Database update failed");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error while saving changes");
                    throw;
                }

                _logger.LogInformation("Handler completed. Total invoices processed: {Count}", incomingEvent.Invoices.Count);
                return Unit.Value;
            }

            private void InvoiceUpsert(InvoicesCreatedEvent.InvoiceCreatedDto incomingInvoice, Job job)
            {
                var invoiceMedia = job.MediaMetadata?
                    .FirstOrDefault(q =>
                        q.JobId == incomingInvoice.JobId &&
                        q.JobInvoice?.Id == incomingInvoice.InvoiceId);

                if (invoiceMedia != null)
                {
                    var invoiceInfo = invoiceMedia.JobInvoice;
                    invoiceInfo.Description = incomingInvoice.Description ?? string.Empty;
                    invoiceInfo.InvoiceNumber = incomingInvoice.InvoiceNumber ?? string.Empty;
                    invoiceInfo.Source = incomingInvoice.Source ?? string.Empty;
                    invoiceInfo.Date = incomingInvoice.Date;

                    if (incomingInvoice.EventType == InvoiceEventType.Invoice)
                        invoiceInfo.Amount = incomingInvoice.Amount;

                    if (incomingInvoice.EventType == InvoiceEventType.Collected)
                        invoiceInfo.AmountCollected += incomingInvoice.Amount;

                    _logger.LogDebug("Invoice {InvoiceId} updated for job {JobId}", incomingInvoice.InvoiceId, incomingInvoice.JobId);
                    return;
                }

                var media = GenerateMediaMetadata(incomingInvoice);
                media.FranchiseSetId = job.FranchiseSetId;
                job.MediaMetadata?.Add(media);

                _logger.LogDebug("Invoice {InvoiceId} created for job {JobId}", incomingInvoice.InvoiceId, incomingInvoice.JobId);
            }

            private MediaMetadata GenerateMediaMetadata(InvoicesCreatedEvent.InvoiceCreatedDto eventInvoice)
            {
                var invoice = GenerateInvoice(eventInvoice);
                return new MediaMetadata
                {
                    Id = Guid.NewGuid(),
                    JobId = eventInvoice.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    IsForUpload = false,
                    Name = invoice.Description,
                    MediaTypeId = MediaTypes.Document,
                    ArtifactTypeId = ArtifactTypes.Invoice,
                    MediaPath = string.Empty,
                    JobInvoice = invoice,
                    JobInvoiceId = invoice?.Id
                };
            }

            private JobInvoice GenerateInvoice(InvoicesCreatedEvent.InvoiceCreatedDto invoice)
            {
                return new JobInvoice()
                {
                    Id = invoice.InvoiceId,
                    InvoiceNumber = invoice.InvoiceNumber ?? string.Empty,
                    Description = invoice.Description ?? string.Empty,
                    Source = invoice.Source ?? string.Empty,
                    Date = invoice.Date,
                    Amount = invoice.Amount
                };
            }
        }
    }
}
