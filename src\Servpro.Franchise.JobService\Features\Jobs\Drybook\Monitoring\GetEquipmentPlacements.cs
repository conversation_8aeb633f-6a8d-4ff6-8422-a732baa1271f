﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetEquipmentPlacements
    {
        #region Query
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }
        #endregion

        #region Dto
        public class Dto
        {
            public Dto(
                Guid equipmentPlacementId,
                KeyNamePair zone,
                KeyNamePair room,
                bool isUsedInValidation,
                EquipmentDto equipment,
                DateTime beginDate,
                DateTime? endDate,
                double totalDays,
                double totalHours)
            {
                EquipmentPlacementId = equipmentPlacementId;
                Zone = zone;
                Room = room;
                IsUsedInValidation = isUsedInValidation;
                Equipment = equipment;
                BeginDate = beginDate;
                EndDate = endDate;
                TotalDays = totalDays;
                TotalHours = totalHours;
            }

            public Guid EquipmentPlacementId { get; }
            public KeyNamePair Zone { get; }
            public KeyNamePair Room { get; }
            public bool IsUsedInValidation { get; }
            public EquipmentDto Equipment { get; }
            public DateTime BeginDate { get; }
            public DateTime? EndDate { get; }
            public double TotalDays { get; }
            public double TotalHours { get; }

            public class KeyNamePair
            {
                public KeyNamePair(Guid? id, string name)
                {
                    Id = id;
                    Name = name;
                }
                public Guid? Id { get; }
                public string Name { get; }
            }

            public class EquipmentDto
            {
                public EquipmentDto(
                    Guid id,
                    string assetNumber,
                    string manufacturer,
                    string modelName,
                    string equipmentTypeName,
                    Guid equipmentTypeId,
                    string serialNumber,
                    int? volumeRate,
                    decimal amps)
                {
                    Id = id;
                    AssetNumber = assetNumber;
                    Manufacturer = manufacturer;
                    ModelName = modelName;
                    EquipmentTypeName = equipmentTypeName;
                    EquipmentTypeId = equipmentTypeId;
                    SerialNumber = serialNumber;
                    VolumeRate = volumeRate;
                    Amps = amps;
                }

                public Guid Id { get; }
                public string AssetNumber { get; }
                public string Manufacturer { get; }
                public string ModelName { get; }
                public string EquipmentTypeName { get; }
                public Guid EquipmentTypeId { get; }
                public string SerialNumber { get; }
                public int? VolumeRate { get; }
                public decimal Amps { get; }
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;
            public readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                        .ThenInclude(x => x.Equipment)
                        .ThenInclude(x => x.EquipmentModel)
                        .ThenInclude(x => x.EquipmentType)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.Zone)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.Room)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return job.JobAreas.SelectMany(Map);
            }

            private IEnumerable<Dto> Map(JobArea jobArea)
            {
                return jobArea.EquipmentPlacements
                    .Select(x => new Dto(
                        x.Id,
                        new Dto.KeyNamePair(jobArea.ZoneId, jobArea.Zone?.Name),
                        new Dto.KeyNamePair(jobArea.RoomId, jobArea.RoomId.HasValue ? jobArea.Name : null),
                        x.IsUsedInValidation,
                        new Dto.EquipmentDto(
                            x.EquipmentId,
                            x.Equipment.AssetNumber,
                            x.Equipment.EquipmentModel.ManufacturerName,
                            x.Equipment.EquipmentModel.Name,
                            x.Equipment.EquipmentModel.EquipmentType.Name,
                            x.Equipment.EquipmentModel.EquipmentType.Id,
                            x.Equipment.SerialNumber,
                            x.Equipment.VolumeRate,
                            x.Equipment.EquipmentModel.Amps),
                        x.BeginDate,
                        x.EndDate,
                        ((x.EndDate ?? DateTime.UtcNow) - x.BeginDate).TotalDays,
                        ((x.EndDate ?? DateTime.UtcNow) - x.BeginDate).TotalHours));
            }
        }
        #endregion
    }
}
