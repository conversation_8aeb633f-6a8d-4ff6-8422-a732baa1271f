﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale
{
    [Route("api/resales")]
    public class ResaleController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ResaleController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("queries/get-franchise-associated-business-contact")]
        public async Task<ActionResult<GetAssociatedContactsAndBusinesses.Dto>> GetAssociatedContactsAndBusinesses([FromBody] GetAssociatedContactsAndBusinesses.Command command)
        {
            var contactAndBusiness = await _mediator.Send(command);
            return Ok(contactAndBusiness);
        }

        [HttpPost("queries/get-franchise-associated-equipment")]
        public async Task<ActionResult<GetAssociatedEquipment.Dto>> GetAssociatedEquipment([FromBody] GetAssociatedEquipment.Command command)
        {
            var equipment = await _mediator.Send(command);
            return Ok(equipment);
        }
    }


}
