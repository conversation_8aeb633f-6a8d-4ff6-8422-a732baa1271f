﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJob
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult MobileDataResult { get; set; }
            public ProcessEntityResult ContactResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJob>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJob> _logger;
            private readonly IMapper _mapper;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private readonly IServiceProvider _services;
            private JobDataContext _context;
            private static readonly string _prependProjectNumberDefault = "R";

            public Handler(
                IServiceProvider services,
                ILogger<ResaleCopyJob> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _services = services;
                _logger = logger;
                _mapper = mapper;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(Job));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var jobTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedJobIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(jobTargetIds, 
                    GetJobIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<Job, ResaleJob>(
                    request.ResaleId,
                    job =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (job.CallerId.HasValue && request.ContactResult.FailedEntities.Contains(job.CallerId.Value))
                            failedDependencies.Add((nameof(Contact), job.CallerId.Value));
                        if (job.CustomerId.HasValue && request.ContactResult.FailedEntities.Contains(job.CustomerId.Value))
                            failedDependencies.Add((nameof(Contact), job.CustomerId.Value));
                        if (job.MobileDataId.HasValue && request.MobileDataResult.FailedEntities.Contains(job.MobileDataId.Value))
                            failedDependencies.Add((nameof(MobileData), job.MobileDataId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedJobIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    sourceEntity =>
                    {
                        // Prepend "R" to the begining of the selling franchise's project number
                        sourceEntity.ProjectNumber = _prependProjectNumberDefault + sourceEntity.ProjectNumber;
                    },
                    targetEntity =>
                    {
                        // If the JobProgress is not in an Active state during resale, we want to set this flag
                        // So that the new owner is not effected by a job out of their control
                        var kpmIgnoredProgresses = new List<JobProgress> { JobProgress.Closed, JobProgress.TurnedDown, JobProgress.NotSoldCancelled };
                        if (kpmIgnoredProgresses.Contains(targetEntity.JobProgress))
                            targetEntity.WasNotActiveAtResale = true;
                    },
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Jobs.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Job>> GetSourceEntitiesAsync(List<Guid> jobIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobs = await _context.Jobs
                    .Where(j => jobIds.Contains(j.Id))
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobs.Count);
                return jobs;
            }

            private async Task<List<Guid>> GetJobIdsAsync(List<Guid?> jobTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Jobs
                    .Where(x => jobTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
