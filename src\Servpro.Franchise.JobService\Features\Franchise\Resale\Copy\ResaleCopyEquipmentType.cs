﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyEquipmentType
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> EquipmentTypeIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyEquipmentType>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyEquipmentType> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                ILogger<ResaleCopyEquipmentType> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(EquipmentType));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.EquipmentTypeIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var equipmentTypeTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedEquipmentTypeIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(equipmentTypeTargetIds, 
                    GetEquipmentTypeIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<EquipmentType, ResaleEquipmentType>(
                    request.ResaleId,
                    null,
                    alreadyCopiedEquipmentTypeIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.EquipmentTypes.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<EquipmentType>> GetSourceEntitiesAsync(List<Guid> equipmentTypeIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var equipmentTypes = await _context.EquipmentTypes
                    .Where(e => equipmentTypeIds.Contains(e.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", equipmentTypes.Count);
                return equipmentTypes;
            }

            private async Task<List<Guid>> GetEquipmentTypeIdsAsync(List<Guid?> equipmentTypeTargetIds, CancellationToken cancellationToken)
            {
                return await _context.EquipmentTypes
                    .Where(x => equipmentTypeTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
