﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Infrastructure.MicaService.GetSentEntities;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetPhotosToSendToMica
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public List<Guid> PhotoIds { get; set; }
        }

        public class Dto
        {
            public List<Photo> Photos { get; set; }
            public class Photo
            {
                public Guid Id { get; set; }
                public string FileName { get; set; }
                public string Description { get; set; }
                public string BucketName { get; set; }
                public string MediaPath { get; set; }
                public string CreatedBy { get; set; }
                public string RoomName { get; set; }
            }
        }
        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetPhotosToSendToMica> _logger;
            private readonly IMicaServiceClient _micaServiceClient;
            private readonly IAmazonS3 _clientS3;

            public Handler(JobReadOnlyDataContext context,
                ILogger<GetPhotosToSendToMica> logger,
                IAmazonS3 clientS3,
                IMicaServiceClient micaServiceClient)
            {
                _context = context;
                _logger = logger;
                _micaServiceClient = micaServiceClient;
                _clientS3 = clientS3;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Photos to send to Mica");

                var returnDto = new Dto()
                {
                    Photos = new List<Dto.Photo>()
                };
                List<MediaMetadata> photos = new List<MediaMetadata>();


                if (request.PhotoIds.Any())
                {
                    photos = await _context.MediaMetadata
                            .Include(j=>j.JobArea)
                        .Where(x => request.PhotoIds.Contains(x.Id))
                        .ToListAsync(cancellationToken);

                    foreach (var photo in photos)
                    {
                        returnDto.Photos.Add(await MapPhotoAsync(photo, cancellationToken));
                    }
                }
                return returnDto;
            }

            private async Task<Dto.Photo> MapPhotoAsync(MediaMetadata media, CancellationToken cancellationToken)
            => new Dto.Photo
                {
                    Id = media.Id,
                    FileName = media.Name,
                    Description = media.Description,
                    CreatedBy = media.CreatedBy,
                    RoomName = media.JobArea?.Name,
                    BucketName = media.BucketName,
                    MediaPath = media.MediaPath
                };
        }
    }
}
