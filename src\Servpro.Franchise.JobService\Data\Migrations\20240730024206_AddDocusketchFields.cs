﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddDocusketchFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("dc859fa3-62d5-465e-b20d-4dd164e829cf"));

            migrationBuilder.AddColumn<Guid>(
                name: "ExpressDimensionImageKey",
                table: "Rooms",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci");

            migrationBuilder.AddColumn<int>(
                name: "Length3TotalInches",
                table: "Rooms",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<long>(
                name: "NetCeilingPerimeter",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "NetCeilingSquareFootage",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "NetFloorPerimeter",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "NetFloorSquareFootage",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "NetWallSquareFootage",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<bool>(
                name: "RequiresContainment",
                table: "Rooms",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "TotalInsetsSquareFootage",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TotalOffsetsSquareFootage",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TotalOpeningsSquareFootage",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<int>(
                name: "Width3TotalInches",
                table: "Rooms",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<long>(
                name: "percentageContained",
                table: "Rooms",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.CreateTable(
                name: "JobAdditionalInfo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    SketchSourceId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobAdditionalInfo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobAdditionalInfo_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("7c7de1f4-804f-4e32-88ec-17cd2aaa68c3"), null, new DateTime(2024, 7, 30, 2, 42, 3, 977, DateTimeKind.Utc).AddTicks(124), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_JobAdditionalInfo_JobId",
                table: "JobAdditionalInfo",
                column: "JobId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "JobAdditionalInfo");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("7c7de1f4-804f-4e32-88ec-17cd2aaa68c3"));

            migrationBuilder.DropColumn(
                name: "ExpressDimensionImageKey",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "Length3TotalInches",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "NetCeilingPerimeter",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "NetCeilingSquareFootage",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "NetFloorPerimeter",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "NetFloorSquareFootage",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "NetWallSquareFootage",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "RequiresContainment",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "TotalInsetsSquareFootage",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "TotalOffsetsSquareFootage",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "TotalOpeningsSquareFootage",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "Width3TotalInches",
                table: "Rooms");

            migrationBuilder.DropColumn(
                name: "percentageContained",
                table: "Rooms");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("dc859fa3-62d5-465e-b20d-4dd164e829cf"), null, new DateTime(2024, 7, 15, 16, 34, 37, 2, DateTimeKind.Utc).AddTicks(6550), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
