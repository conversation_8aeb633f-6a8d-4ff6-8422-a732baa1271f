﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion
{
    public class GetValidationSummary
    {
        #region Query
        public class Query : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public DateTime? CompletionDate { get; set; }
        }
        #endregion

        #region DTO
        public class Dto
        {
            public Dto(IEnumerable<string> validationErrors)
            {
                ValidationErrors = validationErrors.ToImmutableList();
            }

            public ImmutableList<string> ValidationErrors { get; }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobTriStateAnswers)
                    .Include(x => x.JobVisits)
                    .Include(x => x.Tasks)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var isWaterRelatedJob = job.IsWaterRelatedLossType();

                IEnumerable<string> validationErrors;

                if (isWaterRelatedJob)
                {
                    validationErrors = GetPrecheckListValidationSummary(job)
                        .Concat(GetIncompleteTasksSummary(job))
                        .Concat(GetDryingCompleteDateValidationSummary(job, request.CompletionDate))
                        .Concat(GetEquipmentPlacementValidationSummary(job))
                        .Concat(GetIsWaterRelatedLossType(job, request.CompletionDate));
                }
                else
                {
                    validationErrors = GetPrecheckListValidationSummary(job)
                        .Concat(GetIncompleteTasksSummary(job))
                        .Concat(GetEquipmentPlacementValidationSummary(job))
                        .Concat(GetIsWaterRelatedLossType(job, request.CompletionDate));
                }
                return new Dto(validationErrors);
            }

            private IEnumerable<string> GetEquipmentPlacementValidationSummary(Job job)
            {
                var equipmentPlacements = job.JobAreas.SelectMany(x => x.EquipmentPlacements);
                var lastVisit = job.JobVisits.Any()
                    ? job.JobVisits.Max(x => x.Date)
                    : DateTime.MaxValue;
                if (equipmentPlacements.Any(x => !x.EndDate.HasValue || TrimSeconds(x.EndDate) > lastVisit))
                    yield return "Remove actively placed equipment in order to complete this job.";
            }

            private DateTime? TrimSeconds(DateTime? datetime)
                => datetime == null
                    ? default(DateTime?)
                    : new DateTime(datetime.Value.Year, datetime.Value.Month, datetime.Value.Day, datetime.Value.Hour, datetime.Value.Minute, 0);

            private IEnumerable<string> GetDryingCompleteDateValidationSummary(Job job, DateTime? completionDate)
            {
                var jobCompletionDate = job.GetDate(JobDateTypes.DryingComplete);
                if(!jobCompletionDate.HasValue)
                {
                    if(job.JobVisits.Any() &&
                       completionDate.HasValue &&
                       completionDate != job.JobVisits.Max(x => x.Date))
                    {
                        yield return "Completion date must be set to the last visit for Water jobs.";
                    }
                    yield return @"Answer ""Yes"" to Is Drying finished and/or has all equipment been removed from the project?";
                }
            }

            private IEnumerable<string> GetIncompleteTasksSummary(Job job)
            {
                if (job.Tasks.Any(x => x.TaskStatusId == TaskStatuses.Active
                     && x.TaskTypeId != TaskTypes.CustomerFinalWalkThrough
                     && x.TaskTypeId != TaskTypes.CosJobCompletionNotSigned))
                    yield return "Close all outstanding alerts in order to complete this project.";
            }

            private IEnumerable<string> GetPrecheckListValidationSummary(Job job)
            {
                var isFinalWalkthroughInvalid = IsQuestionTaskInvalid(job,
                    JobTriStateQuestions.IsCustomerFinalWalkThroughComplete,
                    TaskTypes.CustomerFinalWalkThrough);
                var isCertificateOfSatisfactionSignedInvalid = IsQuestionTaskInvalid(job,
                    JobTriStateQuestions.WasCertificateOfSatisfactionSignedAndDatedByCustomer,
                    TaskTypes.CosJobCompletionNotSigned);
                if(isFinalWalkthroughInvalid || isCertificateOfSatisfactionSignedInvalid)
                    yield return @"Answer the questions and enter the appropriate diary notes for ""No"" answers.";
            }

            /// <summary>
            /// Returns false if the question is answered as True or if the question was
            /// answered as False and there are no active tasks for the given taskTypeId
            /// </summary>
            /// <param name="job"></param>
            /// <param name="questionId"></param>
            /// <param name="taskTypeId"></param>
            /// <returns></returns>
            private bool IsQuestionTaskInvalid(Job job, Guid questionId, Guid taskTypeId)
            {
                var answer = job.GetAnswer(questionId);
                if (!answer.HasValue)
                    return true;
                if (!answer.Value && job.Tasks.Any(x => x.TaskTypeId == taskTypeId && x.TaskStatusId == TaskStatuses.Active))
                    return true;
                return false;
            }

            private IEnumerable<string> GetIsWaterRelatedLossType(Job job, DateTime? requestCompletionDate)
            {
                if (job.IsWaterRelatedLossType()) yield break;
                if(requestCompletionDate != null) yield break;
                var jobCompletionDate = job.GetDate(JobDateTypes.Complete);
                if (!jobCompletionDate.HasValue)
                    yield return "Please enter a completion date";
            }
        }
        #endregion
    }
}
