﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class ZoneTypes
    {
        //Drying zone type
        public static readonly Guid Drying = new Guid("3290B417-7100-4E2F-9654-BC9576988640"); 
        public static readonly Guid HVAC = new Guid("619ACFDE-51F4-4C35-9206-153F47F2AB02"); 
        public static readonly Guid Unaffected = new Guid("212C5258-B00A-489D-97FD-7538AA2D0A9E"); 
        public static readonly Guid Outside = new Guid("D346EB9B-7995-415F-91F6-C0DA1B620F25"); 
        //Square Feet calculation type
        public static readonly Guid AirMoverCalculationTypeId = new Guid("04C62305-D5E9-4E59-AE8B-0BCA7E5F5070"); 
        //zone subject on task for zone not confirmed
        public static readonly string ZoneNotConfirmed = "Zone not confirmed"; 
        //zone subject on task for water class overriden
        public static readonly string ZoneWaterClassOverriden = "Zone Water Class Override";
    }
}
