﻿using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;
using Servpro.Standards.Drying;
using Servpro.Standards.Drying.Calculators.Abstracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones.ZoneValidation
{
    public class ZoneValidationMapper : IZoneValidationMapper
    {
        private readonly ILogger<ZoneValidationMapper> _logger;

        public ZoneValidationMapper(ILogger<ZoneValidationMapper> logger)
        {
            _logger = logger;
        }

        public async Task<ZoneForValidationDto> ExecuteAsync(Zone zone, Dictionary<Guid, WaterClassDto> waterClassLookups, DateTime? fnolDate)
        {
            zone.JobAreas = zone.JobAreas?.Where(j => j != null).ToList() ?? new List<JobArea>();
            if (waterClassLookups == null) waterClassLookups = new Dictionary<Guid, WaterClassDto>();

            var (minRequired, maxAllowed) = GetAirMoverSquareFeetRequirements(zone.JobAreas);
            _logger.LogInformation("Min and Max allowed air movers: {minRequired} - {maxAllowed}", minRequired, maxAllowed);
            var dehuInfo = GetDehumidifierInfo(zone, waterClassLookups, fnolDate);
            _logger.LogInformation("Dehu info: {dehuInfo}", dehuInfo);
            var airMoverLinearRequirements = GetAirMoverLineaInfo(zone, fnolDate, waterClassLookups);
            var rooms = zone.JobAreas
                .Select(Map)
                .ToList();

            _logger.LogInformation("Rooms obtained: {rooms}", rooms);

            var airMoverAchievementIndicator = rooms
                    .Any(x => x.AirMoverAchievementIndicator == ZoneForValidationDto.AchievementIndicator.Exceeds150
                        || x.AirMoverAchievementIndicator == ZoneForValidationDto.AchievementIndicator.Exceeds200
                        || x.AirMoverAchievementIndicator == ZoneForValidationDto.AchievementIndicator.Under)
                    ? ZoneForValidationDto.AchievementIndicator.Invalid
                    : ZoneForValidationDto.AchievementIndicator.Meets;

            _logger.LogInformation("Air mover indicator: {airMoverAchievementIndicator}", airMoverAchievementIndicator);

            return new ZoneForValidationDto
            {
                Id = zone.Id,
                Name = zone.Name,
                WaterClassId = zone.WaterClassId,
                TotalAffectedSquareFeet = zone.TotalSquareFeet,
                TotalAffectedLinearFeet = zone.TotalAffectedLinearFeet,
                TotalAffectedCubicFeet = zone.TotalAffectedCubicFeet,
                OffsetsInsetsCount = zone.OffsetsInsetsCount,
                SubRoomsCount = zone.SubRoomsCount,
                AirMoversPlaced = rooms.Sum(r => r.AirMoversPlaced),
                AirMoverAmps = (int)Math.Ceiling(zone.AirMoverAmps),
                DehumidifierAmps = (int)Math.Ceiling(zone.DehumidifierAmps),
                TotalAmps = (int)Math.Ceiling(zone.AirMoverAmps + zone.DehumidifierAmps),
                AirMoverSquareFeetRequirementsMaximumAllowed = maxAllowed,
                AirMoverSquareFeetRequirementsMinimumRequired = minRequired,
                // if any of the room is exceeds or is under then the zone is considered invalid
                AirMoverSquareFeetAchievementIndicator = airMoverAchievementIndicator,
                // Pending Servpro.Standard.Calculator
                AirMoverUserSelectedCalculationType = zone.AirMoverCalculationTypeId == AirMoverCalculationTypes.SquareFeet
                    ? ZoneForValidationDto.AirMoverCalculationType.SquareFoot
                    : ZoneForValidationDto.AirMoverCalculationType.Linear,
                AirMoverLinearFeetAchievementIndicator = airMoverAchievementIndicator,
                AirMoverLinearFeetRequirementsMaximumAllowed = airMoverLinearRequirements.AirMoverLinearFeetRequirementsMaximumAllowed,
                AirMoverLinearFeetRequirementsMinimumRequired = airMoverLinearRequirements.AirMoverLinearFeetRequirementsMinimumRequired,
                CanGetRequirementsLinear = airMoverLinearRequirements.CanGetRequirementsLinear,
                DehuCalculationType = dehuInfo.CalculationType,
                DehumidifiersAchievedPPD = dehuInfo.PintsPerDayAchieved,
                DehumidifierRequirementsPPD = dehuInfo.PintsPerDayRequired,
                DehuPpdAchievementIndicator = dehuInfo.PintsPerDayIndicator,
                DehumidifiersAchievedCFM = dehuInfo.CubicFeetPerMinuteAchieved,
                DehumidifierRequirementsCFM = dehuInfo.CubicFeetPerMinuteRequired,
                DehuCfmAchievementIndicator = dehuInfo.CubicFeetPerMinuteIndicator,
                DehuMixedLgrAndConventionalPlaced = dehuInfo.MixedLgrAndConventialPlaced,
                Tasks = zone.Tasks.Select(Map),
                Rooms = rooms
            };
        }

        private AirMoverLinearInfo GetAirMoverLineaInfo(Zone zone, DateTime? fnolDate, Dictionary<Guid, WaterClassDto> waterClassLookups)
        {
            var standard = new DryingStandardFactory().GetEffectiveStandard(fnolDate.Value);
            var canGetRequirementsLinear = standard.AirMoverRequirementsCalculator.CanGetRequirementsLinear;
            var waterClass = waterClassLookups.ContainsKey(zone.WaterClassId)
                ? waterClassLookups[zone.WaterClassId]
                : null;
            var (minimumRequired, maximumAllowed) = GetAirMoverLinerFeetRequirements(standard, zone.TotalAffectedLinearFeet, waterClass);
            return new AirMoverLinearInfo(minimumRequired, maximumAllowed, canGetRequirementsLinear);
        }

        private (int minimumRequired, int maximumAllowed) GetAirMoverLinerFeetRequirements(
            DryingStandard standard,
            decimal totalAffectedLinearFeet,
            WaterClassDto waterClass)
        {
            try
            {
                var airMoverRequirements = standard.AirMoverRequirementsCalculator.GetRequirementsLinear(totalAffectedLinearFeet);
                if (airMoverRequirements != null)
                {
                    return (airMoverRequirements.Minimum ?? 0, airMoverRequirements.Maximum ?? 0);
                }
            }
            catch (NotImplementedException)
            {
#pragma warning disable S1854 // Unused assignments should be removed
                if (waterClass != null)
                {
                    var minimumRequired = waterClass.MinimumAirFiltersPerLinearFootFactor == 0
                        ? 0
                        : (int)Math.Ceiling(totalAffectedLinearFeet / waterClass.MinimumAirFiltersPerLinearFootFactor);

                    var maximumAllowed = waterClass.MaximumAirFiltersPerLinearFootFactor == 0
                        ? 0
                        : (int)Math.Ceiling(totalAffectedLinearFeet / waterClass.MaximumAirFiltersPerLinearFootFactor);

                    return (minimumRequired, maximumAllowed);
                }
#pragma warning restore S1854 // Unused assignments should be removed
            }
            return (0, 0);
        }

        private DehuInfo GetDehumidifierInfo(Zone x, Dictionary<Guid, WaterClassDto> waterClassLookup, DateTime? fnolDate)
        {
            var dehuTypesPlaced = x.JobAreas
                .SelectMany(x => x.EquipmentPlacements)
                .Where(x => x.Equipment.EquipmentModel.EquipmentType.BaseEquipmentTypeId == BaseEquipmentTypes.Dehumidifier)
                .Select(x => MapToDehuType(x.Equipment.EquipmentModel.EquipmentTypeId))
                .ToList();
            var mixedLgrAndConventialPlaced = dehuTypesPlaced.Contains(DehuType.Conventional)
                && dehuTypesPlaced.Contains(DehuType.LGR);

            var waterClassSeverity = waterClassLookup.ContainsKey(x.WaterClassId)
                ? waterClassLookup[x.WaterClassId].Severity
                : 1;
            var (calculationType, pintsPerDayRequired, cubicFeetPerMinuteRequired) =
                GetDehuRequirements(fnolDate, x.TotalAffectedCubicFeet, waterClassSeverity, dehuTypesPlaced);
            var (pintsPerDayAchieved, cubicFeetPerMinuteAchieved) = x.DehumidifierAchieved;
            var maxAllowed = 1.25m;
            var pintsPerDayIndicator = CalculateDehumidifierAchievementIndicator(pintsPerDayAchieved, pintsPerDayRequired, maxAllowed);
            var cubicFeetPerMinuteIndicator = CalculateDehumidifierAchievementIndicator(cubicFeetPerMinuteAchieved, cubicFeetPerMinuteRequired, maxAllowed);

            return new DehuInfo(calculationType,
                pintsPerDayAchieved,
                pintsPerDayRequired,
                pintsPerDayIndicator,
                cubicFeetPerMinuteAchieved,
                cubicFeetPerMinuteRequired,
                cubicFeetPerMinuteIndicator,
                mixedLgrAndConventialPlaced);
        }

        private (ZoneForValidationDto.DehumidifierCalculationType calculationType, int pintsPerDayRequired, int cubicFeetPerMinuteRequired) GetDehuRequirements(
            DateTime? fnolDate, decimal totalCubicFeet, int waterClassSeverity, IEnumerable<DehuType> dehuTypesPlaced)
        {
            //Don't calculate the CFM and/or PDD if WaterClassSeverity is 0
            if (fnolDate.HasValue && totalCubicFeet > 0 && waterClassSeverity != 0)
            {
                var standard = new DryingStandardFactory().GetEffectiveStandard(fnolDate.Value);
#pragma warning disable S1854 // Unused assignments should be removed
                var calculationType = GetDehuCalculationType(standard, totalCubicFeet, waterClassSeverity, dehuTypesPlaced);

                // Regardless of which calculation is actually applicable, we need to return both the PPD and CFM requirements
                var cfmRequirement = standard.DehuRequirementsCalculator.GetRequirement(
                        DehuCapacityCalculationMethod.Desiccant_CFM,
                        totalCubicFeet,
                        waterClassSeverity,
                        dehuTypesPlaced);

                var ppdRequirement = standard.DehuRequirementsCalculator.GetRequirement(
                        DehuCapacityCalculationMethod.RefrigerantPint_PPD,
                        totalCubicFeet,
                        waterClassSeverity,
                        dehuTypesPlaced);
#pragma warning restore S1854 // Unused assignments should be removed

                return (calculationType, GetMinValueOrDefault(ppdRequirement), GetMinValueOrDefault(cfmRequirement));
            }

            return (ZoneForValidationDto.DehumidifierCalculationType.NA, 0, 0);

            int GetMinValueOrDefault(DehuRequirement requirement)
                => requirement != null && requirement.Minimum.HasValue
                ? (int)Math.Ceiling(requirement.Minimum.Value)
                : 0;
        }

        private ZoneForValidationDto.DehumidifierCalculationType GetDehuCalculationType(
            DryingStandard standard,
            decimal totalCubicFeet,
            int waterClassSeverity,
            IEnumerable<DehuType> dehuTypesPlaced)
        {
            var applicable = standard.DehuRequirementsCalculator.GetApplicableRequirements(totalCubicFeet,
                    waterClassSeverity,
                    dehuTypesPlaced);

            if (applicable.Any())
            {
                return applicable.First().CapacityCalculationMethod switch
                {
                    DehuCapacityCalculationMethod.RefrigerantPint_PPD => ZoneForValidationDto.DehumidifierCalculationType.PPD,
                    DehuCapacityCalculationMethod.Desiccant_CFM => ZoneForValidationDto.DehumidifierCalculationType.CFM,
                    _ => ZoneForValidationDto.DehumidifierCalculationType.NA,
                };
            }
            return ZoneForValidationDto.DehumidifierCalculationType.NA;
        }

        private DehuType MapToDehuType(Guid equipmentTypeId)
        {
            if (equipmentTypeId == EquipmentTypes.ConventionalDehumidifier)
                return DehuType.Conventional;
            if (equipmentTypeId == EquipmentTypes.DesiccantDehumidifier)
                return DehuType.Desiccant;
            if (equipmentTypeId == EquipmentTypes.LowGrainRefrigerantDehumidifier)
                return DehuType.LGR;
            return DehuType.Conventional;
        }

        private ZoneForValidationDto.RoomDto Map(JobArea x)
        {
            var (minRequired, maxAllowed) = GetAirMoverSquareFeetForRoom(x);

            var insetsOffsetsCount = x.Room?.OffsetsInsetsCount ?? 0;
            var subRoomsCount = x.Room?.SubRoomsCount ?? 0;

            int airMoversPlaced = x.EquipmentPlacements?.Count(e => e.Equipment.EquipmentModel.EquipmentType.BaseEquipmentTypeId == BaseEquipmentTypes.AirMover) ?? 0;
            var indicator = CalculateAchievementIndicator(airMoversPlaced, minRequired, maxAllowed);

            return new ZoneForValidationDto.RoomDto
            {
                JobAreaId = x.Id,
                RoomId = x.RoomId ?? Guid.Empty,
                RoomName = x.Name,
                VisitDate = x.BeginJobVisit?.Date,
                AffectedSquareFeet = x.Room?.AffectedSquareFeet,
                AffectedPercentage = x.Room?.AffectedPercentage,
                HasMissingDimensions = HasMissingDimensions(x.Room),
                InsetsOffsetsCount = insetsOffsetsCount + subRoomsCount,
                AirMoversPlaced = airMoversPlaced,
                EquipmentPlacementCount = x.EquipmentPlacements?.Count ?? 0,
                HasEquipmentPlacements = x.EquipmentPlacements?.Any() ?? false,
                TotalAffectedArea = x.Room?.AffectedSquareFeet ?? 0,
                TotalSquareFeet = x.Room?.TotalSquareFeet,
                HasMissingFlooring = !x.Room?.RoomFlooringTypesAffected.Any() ?? false,
                AirMoverAchievementIndicator = indicator,
                AirMoverRequirements = new ZoneForValidationDto.AirMoverSquareFeetRequirements(minRequired, maxAllowed),
            };
        }

        private ZoneForValidationDto.TaskDto Map(Models.Drybook.Task x)
            => new ZoneForValidationDto.TaskDto
            {
                Id = x.Id,
                StatusId = x.TaskStatusId,
                PriorityId = x.TaskPriorityId,
                TypeId = x.TaskTypeId,
                Subject = x.Subject,
                PercentComplete = x.PercentComplete,
                CompletionDate = x.CompletionDate
            };

        private (int minRequired, int maxAllowed) GetAirMoverSquareFeetRequirements(IEnumerable<JobArea> jobAreas)
        {
            return jobAreas
                .Select(GetAirMoverSquareFeetForRoom)
                .Aggregate((minRequired: 0, maxAllowed: 0),
                           (a, b) => (a.minRequired + b.minRequired, a.maxAllowed + b.maxAllowed));
        }

        private (int minRequired, int maxAllowed) GetAirMoverSquareFeetForRoom(JobArea jobArea)
        {
            if (jobArea?.Room == null) return (0, 0);

            var airMoverFloorMin = Math.Round(jobArea.Room.AffectedFloorAreaSquareFeet / 70.0m, 2);
            var airMoverFloorMax = Math.Round(jobArea.Room.AffectedFloorAreaSquareFeet / 50.0m, 2);

            var airMoverCeilingMin = Math.Round(jobArea.Room.AffectedCeilingAreaSquareFeet / 150.0m, 2);
            var airMoverCeilingMax = Math.Round(jobArea.Room.AffectedCeilingAreaSquareFeet / 100.0m, 2);

            var wallAreaBelow2FeetSquareFeet = jobArea.Room.AffectedWallAreaBelow2FeetSquareInches / UnitConversion.SquareInchesToSquareFeet;

            var wallAffectedAreaSqFt = jobArea.Room.AffectedFloorAreaSquareFeet == 0
                ? jobArea.Room.AffectedWallAreaSquareFeet
                : jobArea.Room.AffectedWallAreaSquareFeet - wallAreaBelow2FeetSquareFeet;

            var airMoverWallMin = Math.Round(wallAffectedAreaSqFt / 150.0m, 2);
            var airMoverWallMax = Math.Round(wallAffectedAreaSqFt / 100.0m, 2);

            var subRooms = jobArea.Room.SubRoomsCount;
            var offsetInsets = jobArea.Room.OffsetsInsetsCount;

            var OffsetFloorAreaSquareFeet = jobArea.Room.OffsetFloorAreaSquareInches / UnitConversion.SquareInchesToSquareFeet;
            var MissingFloorAreaSquareFeet = jobArea.Room.MissingFloorAreaSquareInches / UnitConversion.SquareInchesToSquareFeet;

            var minAirMovers = 0;
            var maxAirMovers = 0;

            if ((jobArea.Room.FloorAreaSquareFeet + OffsetFloorAreaSquareFeet - MissingFloorAreaSquareFeet) >= 25)
            {
#pragma warning disable S1854 // Unused assignments should be removed
                minAirMovers = Convert.ToInt32(1 + subRooms + offsetInsets + Math.Ceiling(airMoverFloorMin + airMoverCeilingMin + airMoverWallMin));
                maxAirMovers = Convert.ToInt32(1 + subRooms + offsetInsets + Math.Ceiling(airMoverFloorMax + airMoverCeilingMax + airMoverWallMax));
#pragma warning restore S1854 // Unused assignments should be removed
            }

            else
            {
                minAirMovers = Convert.ToInt32(1 + subRooms + offsetInsets);
                maxAirMovers = Convert.ToInt32(1 + subRooms + offsetInsets);
            }

            if (jobArea.Room.AffectedFloorAreaSquareFeet <= 0.0m &&
                jobArea.Room.AffectedCeilingAreaSquareFeet <= 0.0m &&
                wallAffectedAreaSqFt <= 0.0m)
            {
                minAirMovers = 0;
                maxAirMovers = 0;
            }

            return (minAirMovers, maxAirMovers);
        }

        private bool HasMissingDimensions(Models.Room r)
        {
            if (r == null) return false;
            if (r.RoomShapeId == RoomShapes.Box &&
                (r.Width1TotalInches == 0 || r.Length1TotalInches == 0 ||
                r.Height1TotalInches == 0))
                return true;
            if ((r.RoomShapeId == RoomShapes.VaultedCeiling || r.RoomShapeId == RoomShapes.CathedralCeiling)
                && (r.Width1TotalInches == 0 || r.Length1TotalInches == 0 || r.Height1TotalInches == 0 || r.Height2TotalInches == 0))
                return true;
            if (r.RoomShapeId == RoomShapes.LShaped
                && (r.Length1TotalInches == 0 || r.Length2TotalInches == 0 || r.Width1TotalInches == 0 || r.Width2TotalInches == 0 || r.Height1TotalInches == 0))
                return true;
            return false;
        }

        private ZoneForValidationDto.AchievementIndicator CalculateAchievementIndicator(
            int airMoversPlaced,
            int minimumRequired,
            int maximumAllowed)
        {
            if (airMoversPlaced < minimumRequired)
            {
                return ZoneForValidationDto.AchievementIndicator.Under;
            }
#pragma warning disable S2589 // Boolean expressions should not be gratuitous
            else if (airMoversPlaced >= minimumRequired &&
                     airMoversPlaced <= maximumAllowed)
            {
                return ZoneForValidationDto.AchievementIndicator.Meets;
            }
            else if (airMoversPlaced > maximumAllowed)
            {
                var maxAllowed150Percent = maximumAllowed * 1.5;
                var maxAllowed200Percent = maximumAllowed * 2;

                if (airMoversPlaced >= maxAllowed150Percent && airMoversPlaced < maxAllowed200Percent)
                {
                    return ZoneForValidationDto.AchievementIndicator.Exceeds150;
                }
                else
                {
                    return ZoneForValidationDto.AchievementIndicator.Exceeds200;
                }
            }
#pragma warning restore S2589 // Boolean expressions should not be gratuitous
            return ZoneForValidationDto.AchievementIndicator.NA;
        }

        private ZoneForValidationDto.AchievementIndicator CalculateDehumidifierAchievementIndicator(
            int achieved,
            int required,
            decimal maximumAllowed)
        {
            if (achieved < required)
            {
                return ZoneForValidationDto.AchievementIndicator.Under;
            }
            else if (achieved == required)
            {
                return ZoneForValidationDto.AchievementIndicator.Meets;
            }
            else
            {
                var maxAllowed = required * maximumAllowed;
                if (achieved <= maxAllowed)
                {
                    return ZoneForValidationDto.AchievementIndicator.Meets;
                }
                else
                {
                    return ZoneForValidationDto.AchievementIndicator.Exceeds;
                }
            }
        }

        internal class AirMoverLinearInfo
        {
            public AirMoverLinearInfo(
                int airMoverLinearFeetRequirementsMinimumRequired,
                int airMoverLinearFeetRequirementsMaximumAllowed,
                bool canGetRequirementsLinear)
            {
                AirMoverLinearFeetRequirementsMinimumRequired = airMoverLinearFeetRequirementsMinimumRequired;
                AirMoverLinearFeetRequirementsMaximumAllowed = airMoverLinearFeetRequirementsMaximumAllowed;
                CanGetRequirementsLinear = canGetRequirementsLinear;
            }

            public int AirMoverLinearFeetRequirementsMinimumRequired { get; }
            public int AirMoverLinearFeetRequirementsMaximumAllowed { get; }
            public bool CanGetRequirementsLinear { get; }
        }

        internal class DehuInfo
        {
            public DehuInfo(
                ZoneForValidationDto.DehumidifierCalculationType calculationType,
                int pintsPerDayAchieved,
                int pintsPerDayRequired,
                ZoneForValidationDto.AchievementIndicator pintsPerDayIndicator,
                int cubicFeetPerMinuteAchieved,
                int cubicFeetPerMinuteRequired,
                ZoneForValidationDto.AchievementIndicator cubicFeetPerMinuteIndicator,
                bool mixedLgrAndConventialPlaced)
            {
                CalculationType = calculationType;
                PintsPerDayAchieved = pintsPerDayAchieved;
                PintsPerDayRequired = pintsPerDayRequired;
                PintsPerDayIndicator = pintsPerDayIndicator;
                CubicFeetPerMinuteAchieved = cubicFeetPerMinuteAchieved;
                CubicFeetPerMinuteRequired = cubicFeetPerMinuteRequired;
                CubicFeetPerMinuteIndicator = cubicFeetPerMinuteIndicator;
                MixedLgrAndConventialPlaced = mixedLgrAndConventialPlaced;
            }

            public ZoneForValidationDto.DehumidifierCalculationType CalculationType { get; }
            public int PintsPerDayAchieved { get; }
            public int PintsPerDayRequired { get; }
            public ZoneForValidationDto.AchievementIndicator PintsPerDayIndicator { get; }
            public int CubicFeetPerMinuteAchieved { get; }
            public int CubicFeetPerMinuteRequired { get; }
            public ZoneForValidationDto.AchievementIndicator CubicFeetPerMinuteIndicator { get; }
            public bool MixedLgrAndConventialPlaced { get; }

        }
    }
}
