﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class DeleteZone
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
        }


        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.ZoneId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo, ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var zone = await _context.Zones
                    .Include(z => z.Job)
                    .FirstOrDefaultAsync(z => z.Id == request.ZoneId, cancellationToken);
                
                var userInfo = _userInfo.GetUserInfo();

                if (zone is null)
                    throw new ResourceNotFoundException($"Zone not found (Id: {request.ZoneId}");

                zone.Job.JobLocks = await _context.JobLock.Where(x => x.JobId == zone.Job.Id).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(zone.Job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(zone.Job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(zone.Job.CurrentJobLock));

                var tasks = await _context.Tasks
                    .Include(t=> t.JournalNotes)
                    .Where(t => t.ZoneId == zone.Id).ToListAsync(cancellationToken);
                
                foreach (var task in tasks)
                {
                    if(task.JournalNotes.Any())
                    {
                        foreach (var note in task.JournalNotes)
                        {
                            _context.Remove(note);
                        }
                        task.JournalNotes.Clear();
                    }
                    _context.Remove(task);
                }

                var jobAreas = await _context.JobAreas.Where(ja => ja.ZoneId == zone.Id).ToListAsync(cancellationToken);
                jobAreas.ForEach(ja => ja.ZoneId = null);

                zone.Tasks.Clear();
                zone.IsDeleted = true;
                zone.ModifiedDate = DateTime.UtcNow;
                zone.ModifiedBy = userInfo.Username;

                var zoneDeletedEvent = GenerateZoneDeletedEvent(zone, _sessionIdAccessor.GetCorrelationGuid(), _userInfo.GetUserInfo());
                _context.OutboxMessages.Add(zoneDeletedEvent);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private OutboxMessage GenerateZoneDeletedEvent(Zone zone, Guid correlationId, UserInfo user)
            {
                var zoneDeletedDto = new ZoneDeletedEvent.ZoneDeletedDto
                {
                    Id = zone.Id,
                    JobId = zone.JobId
                };
                var zoneDeletedEvent = new ZoneDeletedEvent(zoneDeletedDto, correlationId);
                return new OutboxMessage(zoneDeletedEvent.ToJson(), nameof(ZoneDeletedEvent), correlationId, user.Username);
            }
        }
    }
}
