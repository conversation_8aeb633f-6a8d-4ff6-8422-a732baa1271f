﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class DeleteJobVisit
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
        }
        #endregion

        #region CommandValidator
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.JobVisitId).NotEmpty();
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(z => z.JobVisits)
                        .ThenInclude(x => x.Tasks)
                        .ThenInclude(x => x.JournalNotes)
                    .FirstOrDefaultAsync(z => z.Id == request.JobId
                        && z.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);


                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobAreas = await _context.JobAreas.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var jobVisit = job.JobVisits
                    .FirstOrDefault(z => z.Id == request.JobVisitId);

                if (jobVisit is null)
                    throw new ResourceNotFoundException($"Job Visit not found (Id: {request.JobVisitId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, _userInfo.GetUserInfo().Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var roomsAddedOnVisit = job.JobAreas.Where(x => x.BeginJobVisitId == jobVisit.Id);
                foreach (var jobArea in roomsAddedOnVisit)
                {
                    jobArea.BeginJobVisitId = null;
                    jobArea.ZoneId = null;
                    jobArea.ModifiedBy = userInfo.Username;
                    jobArea.ModifiedDate = DateTime.UtcNow;
                }

                var events = GenerateEvents(jobVisit, GetCorrelationId(), userInfo);
                _context.OutboxMessages.AddRange(events);

                _context.Tasks.RemoveRange(jobVisit.Tasks);
                _context.JournalNote.RemoveRange(jobVisit.Tasks.SelectMany(x => x.JournalNotes));
                _context.Remove(jobVisit);
                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private IEnumerable<OutboxMessage> GenerateEvents(JobVisit jobVisit, Guid correlationId, UserInfo user)
            {
                var jobVisitDeletedEvents = GenerateJobVisitDeletedEvents(jobVisit.JobId, jobVisit.Id, correlationId, user);

                return jobVisitDeletedEvents.ToList();
            }

            private JobVisitDeletedDto MapJobVisitDeletedDto(Guid jobId, Guid jobVisitId, string username, Guid userId)
            {
                return new JobVisitDeletedDto
                {
                    JobId = jobId,
                    JobVisitId = jobVisitId,
                    Username = username,
                    UserId = userId
                };
            }

            private IEnumerable<OutboxMessage> GenerateJobVisitDeletedEvents(
                Guid jobId,
                Guid jobVisitId,
                Guid correlationId,
                UserInfo userInfo)
            {
                var @event = new JobVisitDeletedEvent(MapJobVisitDeletedDto(jobId, jobVisitId, userInfo.Username, userInfo.Id), correlationId);
                yield return new OutboxMessage(@event.ToJson(), nameof(JobVisitDeletedEvent), correlationId, userInfo.Username);
            }

            private Guid GetCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();
                return correlationId;
            }
        }

    }
    #endregion
}
