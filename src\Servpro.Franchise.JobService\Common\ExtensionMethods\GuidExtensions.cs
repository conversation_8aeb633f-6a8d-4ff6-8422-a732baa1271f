using System;

namespace Servpro.Franchise.JobService.Common.ExtensionMethods
{
    public static class GuidExtensions
    {
        public static bool IsNullOrEmpty(this Guid? value)
            => !value.HasValue || value.Value == Guid.Empty;

        public static bool IsNullOrEmpty(this Guid value)
            => value == Guid.Empty;

        public static Guid? ValueOrNullIfEmpty(this Guid? value)
        {
            return value.HasValue && value.Value != Guid.Empty ? value.Value : (Guid?) null;
        }
    }
}