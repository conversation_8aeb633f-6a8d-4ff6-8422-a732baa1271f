﻿using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Standards.Drying;
using Servpro.Standards.Drying.Calculators.Abstracts;
using System;
using System.Collections.Generic;
using System.Linq;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public class ZoneCalculator
    {
        private const decimal CubicInchesPerFoot = 1728m;

        public EquipmentPlacementRequirementsProximity GetDehusRequirementsProximity(
            DehuValidationType dehuValidationType,
            int? ppdsPlaced, int? minPpdsReqd, int? cfmsPlaced, int? minCfmsReqd)
        {
            var cfmOrPpd = (int?)null;
            var minimumRequirement = (int?)null;
            if (dehuValidationType == DehuValidationType.Ppd)
            {
                cfmOrPpd = ppdsPlaced;
                minimumRequirement = minPpdsReqd;
            }
            else if (dehuValidationType == DehuValidationType.Cfm)
            {
                cfmOrPpd = cfmsPlaced;
                minimumRequirement = minCfmsReqd;
            }
            else if (dehuValidationType == DehuValidationType.None
                && (ppdsPlaced ?? 0) <= 0
                && (cfmsPlaced ?? 0) <= 0)
            {
                return EquipmentPlacementRequirementsProximity.BelowMinimum;
            }

            if (cfmOrPpd == null || minimumRequirement == null) return EquipmentPlacementRequirementsProximity.None;

            EquipmentPlacementRequirementsProximity proximity;

            var threshold = 1.25m;
            if (cfmOrPpd >= minimumRequirement && cfmOrPpd <= minimumRequirement * threshold)
                proximity = EquipmentPlacementRequirementsProximity.Acceptable;
            else if (cfmOrPpd < minimumRequirement)
                proximity = EquipmentPlacementRequirementsProximity.BelowMinimum;
            else proximity = EquipmentPlacementRequirementsProximity.AboveMaximum;

            return proximity;
        }

        public EquipmentPlacementRequirementsProximity GetAirMoverRequirementsProximity(int countAirMoversPlaced,
            int? minAirMoversRequired, int? maxAirMoversRequired)
        {
            if (minAirMoversRequired == null || maxAirMoversRequired == null)
                return EquipmentPlacementRequirementsProximity.None;

            return minAirMoversRequired <= countAirMoversPlaced && countAirMoversPlaced <= maxAirMoversRequired
                ? EquipmentPlacementRequirementsProximity.Acceptable
                : countAirMoversPlaced < minAirMoversRequired
                    ? EquipmentPlacementRequirementsProximity.BelowMinimum
                    : EquipmentPlacementRequirementsProximity.AboveMaximum;
        }

        public int? GetAirMoversRequiredUsingLinearFeetMethod(int? linFtFactor, decimal? zoneVolumeCubicFeet)
        {
            if (linFtFactor == null || zoneVolumeCubicFeet == null) return null;
            var result = (int)Math.Ceiling((decimal)zoneVolumeCubicFeet / (decimal)linFtFactor);
            return result;
        }

        public int? GetAirMoversRequiredUsingSquareFeetMethod(int? sqFtFactor, decimal? zoneVolumeCubicFeet)
        {
            if (sqFtFactor == null || zoneVolumeCubicFeet == null) return null;

            return (int)Math.Ceiling((decimal)zoneVolumeCubicFeet / (decimal)sqFtFactor);
        }

        public int? GetMinDehuCfmsRequired(ZoneDto zoneDto, DryingStandard standard, Dictionary<Guid, int> waterClasses)
        {
            if (!waterClasses.ContainsKey(zoneDto.WaterClassId)) return null;

            var zoneVolumeCuFt = GetZoneVolume(zoneDto);
            var dehuTypesInPlace = zoneDto
                .RoomDtos.SelectMany(rd => rd.EquipmentPlacementDtos)
                .Where(x => x.BaseEquipmentTypeId == ZoneEquipmentTypes.DehuBaseEquipmentTypeId)
                .Select(x => MapDehuType(x.EquipmentTypeId))
                .ToArray();

            var waterClass = waterClasses[zoneDto.WaterClassId];
            if (waterClass <= 0)
                return null;

            var requirement = standard.DehuRequirementsCalculator.GetRequirement(DehuCapacityCalculationMethod.Desiccant_CFM, zoneVolumeCuFt, waterClass, dehuTypesInPlace);

            return requirement != null && requirement.Minimum.HasValue ? Convert.ToInt32(requirement.Minimum.Value) : (int?)null;

        }

        public DehuType MapDehuType(Guid dehuTypeId)
        {
            return dehuTypeId == ZoneEquipmentTypes.ConvDehuEquipmentTypeId ? DehuType.Conventional
                : dehuTypeId == ZoneEquipmentTypes.LgrDehuEquipmentTypeId ? DehuType.LGR
                : dehuTypeId == ZoneEquipmentTypes.DesiccantDehuEquipmentTypeId ? DehuType.Desiccant
                : DehuType.Conventional;
        }

        public int? GetMinDehuPpdsRequired(ZoneDto zoneDto, DryingStandard standard, Dictionary<Guid, int> waterClasses)
        {
            var zoneVolumeCuFt = GetZoneVolume(zoneDto);
            var waterClass = waterClasses.FirstOrDefault(x => x.Key == zoneDto.WaterClassId).Value;
            var dehuTypesInPlace = zoneDto
                .RoomDtos.SelectMany(rd => rd.EquipmentPlacementDtos)
                .Where(x => x.BaseEquipmentTypeId == ZoneEquipmentTypes.DehuBaseEquipmentTypeId)
                .Select(x => MapDehuType(x.EquipmentTypeId));

            if (waterClass <= 0)
                return null;

            var ppdRequirement = standard.DehuRequirementsCalculator.GetRequirement(DehuCapacityCalculationMethod.RefrigerantPint_PPD, zoneVolumeCuFt, waterClass, dehuTypesInPlace);

            var returnValue = ppdRequirement != null && ppdRequirement.Minimum.HasValue ? Convert.ToInt32(ppdRequirement.Minimum.Value) : (int?)null;

            return returnValue;

        }

        public decimal GetZoneVolume(ZoneDto zoneDto)
        {
            return
                zoneDto.RoomDtos.Sum(
                    rd =>
                        (decimal)
                            (rd.RoomVolumeCubicInches + rd.OffsetRoomVolumeCubicInches - rd.MissingRoomVolumeCubicInches) /
                        CubicInchesPerFoot);
        }

        public DehuValidationType GetDehuValidationType(ZoneDto zoneDto)
        {
            var convDehuEquipmentTypeId = ZoneEquipmentTypes.ConvDehuEquipmentTypeId;
            var lgrDehuEquipmentTypeId = ZoneEquipmentTypes.LgrDehuEquipmentTypeId;
            var desiccantDehuEquipmentTypeId = ZoneEquipmentTypes.DesiccantDehuEquipmentTypeId;

            var placementsForZone = zoneDto
                .RoomDtos
                .SelectMany(rd => rd.EquipmentPlacementDtos)
                .Where(epd => !zoneDto.IsConfirmed || epd.IsUsedInValidation)
                .ToList();
            var zoneHasRefrigerantDehus =
                placementsForZone.Any(
                    p => p.EquipmentTypeId == convDehuEquipmentTypeId || p.EquipmentTypeId == lgrDehuEquipmentTypeId);
            var zoneHasDesiccantDehus = placementsForZone.Any(p => p.EquipmentTypeId == desiccantDehuEquipmentTypeId);
            var zoneHasDehus = zoneHasRefrigerantDehus || zoneHasDesiccantDehus;
            var zoneHasBothDehuTypes = zoneHasRefrigerantDehus && zoneHasDesiccantDehus;

            var zoneDehuValidationType = !zoneHasDehus
                ? DehuValidationType.None
                : zoneHasBothDehuTypes
                    ? DehuValidationType.Mixed
                    : zoneHasRefrigerantDehus
                        ? DehuValidationType.Ppd
                        : DehuValidationType.Cfm;

            return zoneDehuValidationType;
        }



        public AirMoverRequirements GetAirMoverSquareFeetRequirments(List<RoomScope> roomScope)
        {
            var returnInfo = new AirMoverRequirements() { Minimum = 0, Maximum = 0 };

            foreach (var room in roomScope)
            {
                returnInfo = GetAirMoverForRoomSquareFeetRequirement(returnInfo, room);
            }

            return returnInfo;
        }

        public AirMoverRequirements GetAirMoverForRoomSquareFeetRequirement(AirMoverRequirements airMoverRequirements, RoomScope room)
        {
            var flooringAffectedAreaSqFt = (double)(room.FloorAreaAffected ?? 0m);
            var ceilingAffectedAreaSqFt = (double)(room.CeilingAreaAffected ?? 0m);

            var airMoverFloorMin = Math.Round(flooringAffectedAreaSqFt / 70.0, 2);
            var airMoverFloorMax = Math.Round(flooringAffectedAreaSqFt / 50.0, 2);

            var airMoverCeilingMin = Math.Round(ceilingAffectedAreaSqFt / 150.0, 2);
            var airMoverCeilingMax = Math.Round(ceilingAffectedAreaSqFt / 100.0, 2);

            // ReSharper disable once CompareOfFloatsByEqualityOperator
            var wallAffectedAreaSqFt = flooringAffectedAreaSqFt == 0
                ? (double)(room.WallAreaAffected ?? 0m)
                : (double)(room.WallAreaAffectedAbove2Feet ?? 0m);

            var airMoverWallMin = Math.Round(wallAffectedAreaSqFt / 150.0, 2);
            var airMoverWallMax = Math.Round(wallAffectedAreaSqFt / 100.0, 2);

            var minAirMoversForRoom = 0;
            var maxAirMoversForRoom = 0;

            if (room.FloorArea >= 25)
            {
#pragma warning disable S1854 // Unused assignments should be removed
                minAirMoversForRoom = Convert.ToInt32(1 + room.SubRoomsCount + room.OffsetsInsetsCount + Math.Ceiling(airMoverFloorMin + airMoverCeilingMin + airMoverWallMin));
                maxAirMoversForRoom = Convert.ToInt32(1 + room.SubRoomsCount + room.OffsetsInsetsCount + Math.Ceiling(airMoverFloorMax + airMoverCeilingMax + airMoverWallMax));
#pragma warning restore S1854 // Unused assignments should be removed
            }

            else
            {
                minAirMoversForRoom = Convert.ToInt32(1 + room.SubRoomsCount + room.OffsetsInsetsCount);
                maxAirMoversForRoom = Convert.ToInt32(1 + room.SubRoomsCount + room.OffsetsInsetsCount);
            }

            if (flooringAffectedAreaSqFt <= 0.0 && wallAffectedAreaSqFt <= 0.0 && ceilingAffectedAreaSqFt <= 0.0)
            {
                minAirMoversForRoom = 0;
                maxAirMoversForRoom = 0;
            }

            airMoverRequirements.Minimum += minAirMoversForRoom;
            airMoverRequirements.Maximum += maxAirMoversForRoom;


            return airMoverRequirements;
        }
    }
}
