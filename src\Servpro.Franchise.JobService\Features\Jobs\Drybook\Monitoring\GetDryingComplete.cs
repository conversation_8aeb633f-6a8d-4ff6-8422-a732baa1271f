﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetDryingComplete
    {
        
        #region Query
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }
        #endregion

        #region Dto
        public class Dto
        {
            public Dto(Guid jobId, DateTime? dryingComplete)
            {
                JobId = jobId;
                DryingComplete = dryingComplete;
            }

            public Guid JobId { get; }
            public DateTime? DryingComplete { get; }
            public bool IsDryingComplete => DryingComplete.HasValue;
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return new Dto(job.Id, job.GetDate(JobDateTypes.DryingComplete));
            }
        }
        #endregion
    }
}
