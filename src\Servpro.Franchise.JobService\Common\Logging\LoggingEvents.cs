﻿using Microsoft.Extensions.Logging;

namespace Servpro.Franchise.JobService.Common
{
    /// <summary>
    /// Common Logging Events
    /// </summary>
    public static class LoggingEvents
    {
        public static EventId GetItem => new EventId(1, "GetItem");
        public static EventId GetItems => new EventId(2, "GetItems");
        public static EventId UnhandledException => new EventId(3, "UnhandledException");
        public static EventId BadRequest => new EventId(400, "BadRequest");
        public static EventId Conflict => new EventId(409, "Conflict");
        public static EventId GettingUserInformation => new EventId(5, nameof(GettingUserInformation));
        public static EventId UnauthenticatedUser => new EventId(6, nameof(UnauthenticatedUser));
        public static EventId UnauthorizedUser => new EventId(401, nameof(UnauthorizedUser));
        public static EventId EnteringExceptionMiddleware => new EventId(7, nameof(EnteringExceptionMiddleware));
        public static EventId LeavingExceptionMiddleware => new EventId(8, nameof(LeavingExceptionMiddleware));
        public static EventId GetItemNotFound => new EventId(404, nameof(GetItemNotFound));
        public static EventId CacheMiss => new EventId(9, nameof(CacheMiss));
        public static EventId CreateItem => new EventId(201, nameof(CreateItem));
        public static EventId UpdateItem => new EventId(11, nameof(UpdateItem));
        public static EventId PatchItem => new EventId(12, nameof(PatchItem));
        public static EventId EnteringCommand => new EventId(13, nameof(EnteringCommand));
        public static EventId CommandComplete => new EventId(14, nameof(CommandComplete));
        public static EventId DeleteItem => new EventId(15, nameof(DeleteItem));
        public static EventId CommunicationIssue => new EventId(16, nameof(CommunicationIssue));
        public static EventId GetItemsNotFound => new EventId(404, nameof(GetItemNotFound));
        public static EventId Timeout => new EventId(17, nameof(Timeout));
        public static EventId ServiceError => new EventId(50, nameof(ServiceError));
    }
}
