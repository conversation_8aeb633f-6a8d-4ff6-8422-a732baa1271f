﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddedJobConfigExtensionTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("728f249c-58c5-466a-ae6d-fc973d0ab90b"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.CreateTable(
                name: "JobConfigExtension",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsDryBookMobileAddEquipmentOptedIn = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RequireDailyMaterialPhotos = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RequireFinalMaterialPhotos = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RequireInitialMaterialPhotos = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobConfigExtension", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobConfigExtension_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("1b2f7188-ab26-4e54-9f49-67cd06fc26f2"), null, new DateTime(2023, 1, 11, 15, 46, 18, 557, DateTimeKind.Utc).AddTicks(4323), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_JobConfigExtension_JobId",
                table: "JobConfigExtension",
                column: "JobId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "JobConfigExtension");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("1b2f7188-ab26-4e54-9f49-67cd06fc26f2"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("728f249c-58c5-466a-ae6d-fc973d0ab90b"), null, new DateTime(2023, 1, 9, 20, 41, 55, 195, DateTimeKind.Utc).AddTicks(47), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
