﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.MicaAutomation;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Xact;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class XactMfnCreated
    {
        public class Event : XactMfnCreatedEvent, IRequest
        {
            public Event(XactMfnCreatedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<XactMfnCreated> _logger;
            private readonly IMicaAutomationUtility _micaAutomationUtility;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(JobDataContext context, ILogger<XactMfnCreated> logger, 
                IMicaAutomationUtility micaAutomationUtility, 
                IFranchiseServiceClient franchiseServiceClient)
            {
                _context = context;
                _logger = logger;
                _micaAutomationUtility = micaAutomationUtility;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var logScope = _logger.BeginScope("{jobId}", request.XactMfnCreated.JobId);
                _logger.LogInformation("Started with: {@request}", request);

                var job = await _context.Jobs
                        .Include(x => x.JobCustomAttributes)
                    .FirstOrDefaultAsync(j => j.Id == request.XactMfnCreated.JobId, cancellationToken: cancellationToken);

                if (job == null)
                {
                    throw new ResourceNotFoundException($"Job Id not found: {request.XactMfnCreated.JobId}.");
                }

                job.HasXactAssignment = !string.IsNullOrWhiteSpace(request.XactMfnCreated.XactMfn);

                if(job.InsuranceCarrierId.HasValue && job.InsuranceCarrierId == InsuranceCarrierTypes.StateFarmILR 
                    && !string.IsNullOrWhiteSpace(request.XactMfnCreated.XactMfn))
                {
                    job.InsuranceCarrierId = InsuranceCarrierTypes.StateFarm;
                    await RaiseInsuranceCarrierChangedEvent(job, InsuranceCarrierTypes.StateFarm, request.CorrelationId, 
                        request.XactMfnCreated.CreatedBy, cancellationToken);
                }

                job.ModifiedBy = request.XactMfnCreated.CreatedBy;
                job.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync(cancellationToken);
                await _micaAutomationUtility.HandleShellMicaClaimAutomationConditionallyAsync(job, 
                    MicaAutomationTrigger.MfnAssociated, 
                    request.CorrelationId, 
                    cancellationToken);

                return Unit.Value;
            }

            private async Task RaiseInsuranceCarrierChangedEvent(Job job, Guid insuranceClientId, Guid correlationId, string createdBy, CancellationToken cancellationToken)
            {
                var franchiseInfo = await _franchiseServiceClient.GetFranchiseWithoutUserClaimsAsync(job.FranchiseId, job.FranchiseSetId, cancellationToken: cancellationToken);

                var jobDto = new InsuranceCarrierChangedEvent.JobDto
                {
                    JobId = job.Id,
                    CorporateJobNumber = job.CorporateJobNumber,
                    InsuranceCarrierId = insuranceClientId,
                    FranchiseNumber = (int) franchiseInfo.FranchiseNumber,
                    CreatedBy = createdBy,
                    CreatedById = Guid.Empty
                };
                var insuranceCarrierChangedEvent = new InsuranceCarrierChangedEvent(jobDto, correlationId);

                await GenerateOutboxMessage(insuranceCarrierChangedEvent.ToJson(),
                    nameof(InsuranceCarrierChangedEvent), correlationId, jobDto.CreatedBy);
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType, correlationId, createdByUsername);
                await _context.OutboxMessages.AddAsync(newEvent);
            }
        }
    }
}
