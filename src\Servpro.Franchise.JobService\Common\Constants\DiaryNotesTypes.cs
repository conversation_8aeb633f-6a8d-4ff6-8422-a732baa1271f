﻿using System;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class DiaryNotesTypes
    {
        public static readonly Guid AdditonalEmergencyResponseServices = new Guid("35f4c5c9-38b2-4078-9039-edeaf764b0ab");
        public static readonly Guid Arrival = new Guid("321e3b4e-d594-49ac-948c-71a4e0a6a983");
        public static readonly Guid CallNotes = new Guid("a87c3c03-5fcf-4076-bb3c-7a77a16bed86");
        public static readonly Guid CancelJob = new Guid("cc6afb7c-5a22-4fdf-9c4f-a697e90b878d");
        public static readonly Guid ClientNotes = new Guid("4b186709-6901-4edf-8b19-f21623ee64a3");
        public static readonly Guid ClientServiceRepresentativeNotes = new Guid("4f77922f-acea-42b5-8dd1-5fe316f53978");
        public static readonly Guid CoverageConcerns = new Guid("dba382bb-a6a0-45a8-820c-ca40783f20b7");
        public static readonly Guid CustomerInitialContact = new Guid("e51fbd5c-0143-4cd1-8e13-9859a5aaabe2");
        public static readonly Guid DailyQuestions = new Guid("edb97e53-8e32-43f6-94aa-41d38a004edc");
        public static readonly Guid Day1EquipmentValidationException = new Guid("9fa0519f-db78-4ea9-8ed8-cab1e9e6d5bc");
        public static readonly Guid DifferentMeter = new Guid("51f9a619-1b5c-4f3d-81d1-2d97b674fa91");
        public static readonly Guid DispatchNotes = new Guid("41f81703-68ce-4ae9-b672-dc64d45d107a");
        public static readonly Guid FlooringRestorabilityDiscrepancy = new Guid("7fca7f08-6652-43b6-a6a0-2fdc133aad4e");
        public static readonly Guid FlooringSavedDiscrepancy = new Guid("e0951548-822f-4810-b8b0-bf0442cc7887");
        public static readonly Guid FNOL = new Guid("c0364e8e-4e7f-4c83-a41c-1c5edd988b4f");
        public static readonly Guid GeneralNote = new Guid("9f51e081-fe31-408f-973d-f4ac5e62e2e1");
        public static readonly Guid GrainDepressionLessThanFive = new Guid("06f176fe-c402-427e-b4c5-64c735790c50");
        public static readonly Guid GrainDepressionLessThanZero = new Guid("11fae8a5-7e85-4283-b96e-bf586b41e26b");
        public static readonly Guid HomeownerMovedEquipment = new Guid("5dd698a1-b6bf-4779-ab8f-54323654050a");
        public static readonly Guid ImmediateNotification = new Guid("812c6499-8878-45fe-9641-bd842ac1105c");
        public static readonly Guid InitialCall = new Guid("9a755018-5f65-4a61-8d42-5f98f85ffa9f");
        public static readonly Guid InitialOnSiteArrival = new Guid("869e6e6a-cdb6-4210-aaf2-adfa3fcb7b4e");
        public static readonly Guid JobCompletion = new Guid("25c001f9-5d32-44f0-9df5-4ff4e2256b2e");
        public static readonly Guid LossNotes = new Guid("34d3f4d1-61a5-4d56-b10d-58ec49a3a21c");
        public static readonly Guid OtherJobDetails = new Guid("6c4079ba-ba53-4066-9b43-a3b837365d21");
        public static readonly Guid HVACNote = new Guid("1b3bb442-89c2-4d16-82b3-01757439c409");
        public static readonly Guid ReadingGreaterThanYesterday = new Guid("ef8100ca-a1c1-4639-b029-216b9a68fbfd");
        public static readonly Guid ReadingHoursInconsistent = new Guid("f0da09f8-78ab-4075-ad19-e6c844e0900a");
        public static readonly Guid RecordOfEquipmentPlacementReadingsDeletion = new Guid("0b74a90f-a4c4-4e10-a4fd-04f9fbdf6e67");
        public static readonly Guid RecordOfFixPlacementReadingsDeletion = new Guid("f519ba17-3824-4850-81c9-368d5f4ddee5");
        public static readonly Guid RecordOfJobMaterialDeletion = new Guid("dd5c9e9e-e35e-499e-9855-6e1e46eeaf14");
        public static readonly Guid RecordOfJobVisitDeletion = new Guid("8acdddcb-76b7-4d59-b1ee-ae64a6d37820");
        public static readonly Guid RecordOfZoneDeletion = new Guid("0967dca0-295f-4b07-a37f-97b438055527");
        public static readonly Guid RelativeHumidityHigh = new Guid("4b6efdd7-825d-4179-8a11-19d90174451e");
        public static readonly Guid RelativeHumidityTooHighAfterVisit2 = new Guid("4d181504-d4f2-450b-8739-f2a21d1114b1");
        public static readonly Guid RequiredDocumentation = new Guid("ebb9fb2a-ad9b-4f99-a418-fd302be43c8a");
        public static readonly Guid RoomNotes = new Guid("af685bb6-f412-43f7-ae52-2e4088b2c038");
        public static readonly Guid TaskNotes = new Guid("9aefe028-e881-44e8-8b1e-584fdbf19be3");
        public static readonly Guid UnaffectedMaterialGoalChange = new Guid("55e96258-cc70-4229-8da4-ec1fa1b23970");
        public static readonly Guid VerbalBriefing = new Guid("f0cd99cc-1420-4345-9b54-3bb4eb8ae695");
        public static readonly Guid WasEquipmentMovedOrTurnedOff = new Guid("658ed61f-5d84-4c64-9190-cdf31f6ae844");
        public static readonly Guid ZoneJobVisitNotes = new Guid("cc8d500c-f8a8-497b-b64a-1ed17b5d7dbb");
        public static readonly Guid OccupantNote = new Guid("00000011-000A-5365-7276-70726F496E63");
        public static readonly Guid DryingPlan = new Guid("3BAA1C84-068F-412A-8C40-E3EC1193449B");
    }
}
