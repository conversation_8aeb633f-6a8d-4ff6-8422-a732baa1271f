﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Setup.Caching;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Client
{
    public class GetInsuranceClient
    {
        public class Query : IRequest<Dto>
        {           
            public Guid Id { get; set; }

        }

        public class Dto
        {
            public Guid Id { get; set;  }
            public string Name { get; set; }
            public int InsuranceNumber { get; set; }
            public bool IsActive { get; set; }
            public bool IsLocalAuditRequired { get; set; }
            public bool IsAuditRequired { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IDistributedCache _cache;
            private readonly IConfiguration _config;
            private const string insuranceKey = "GetInsuranceClient";

            public Handler(JobReadOnlyDataContext context,
                IDistributedCache cache,
                IConfiguration config)
            {
                _context = context;
                _cache = cache;
                _config = config;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var cacheExpiration = _config.GetValue("cache:GetModels:InsuranceClientExpirationInMinutes", 5);
                var cacheKey = $"{insuranceKey}:{request.Id}";
                var cachedData = await _cache.GetAsync(cacheKey, cancellationToken);

                Dto cachedInsuranceClient = null;
                if (cachedData != null)
                {
                    cachedInsuranceClient = System.Text.Json.JsonSerializer.Deserialize<Dto>(cachedData);
                }
                else
                {
                    cachedInsuranceClient = await GetDbInsuranceClient(request.Id, cancellationToken);
                    var serialized = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(cachedInsuranceClient);
                    var options = new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(cacheExpiration)
                    };
                    await _cache.SetAsync(cacheKey, serialized, options, cancellationToken);
                }
                return cachedInsuranceClient;
            }

            private async Task<Dto> GetDbInsuranceClient(Guid id, CancellationToken cancellationToken)
            {
                var insuranceClient = await _context.InsuranceClients
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == id, cancellationToken: cancellationToken);
                return Map(insuranceClient);
            }

            private Dto Map(InsuranceClient insuranceClient)
                => new Dto
                {
                    Id = insuranceClient.Id,
                    Name = insuranceClient.Name,
                    InsuranceNumber = insuranceClient.InsuranceNumber,
                    IsActive = insuranceClient.IsActive,
                    IsLocalAuditRequired = insuranceClient.IsLocalAuditRequired,
                    IsAuditRequired = insuranceClient.IsAuditRequired
                };
        }
    }
}