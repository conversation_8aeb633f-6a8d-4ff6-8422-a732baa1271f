﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.Franchise.JobService.Features.Franchise.Invoca.GetInvocaAutoMatchData;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class GetMarketingCallForCsvExport
    {
        public class Query : IRequest<List<Dto>>
        {
            public Guid? FranchiseId { get; set; }
            public Guid? FranchiseSetId { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
        }

        public class Dto
        {
            public Guid CallId { get; set; }
            public string CallRecordingId { get; set; }
            public DateTime CallReceivedDateTime { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid FranchiseId { get; set; }
            public int FranchiseNumber { get; set; }
            public string FranchiseName { get; set; }
            public Guid JobId { get; set; }
            public string ProjectNumber { get; set; }
            public Guid CallSourceId { get; set; }
            public Guid? Disposition { get; set; }
            public string DispositionName { get; set; }
            public bool HasSiteAppointment { get; set; }
            public decimal? RevenueInvoiced { get; set; }
            public DateTime? LastModified { get; set; }

        }

        public class Handler : IRequestHandler<Query, List<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetMarketingCallForCsvExport> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IConfiguration _config;
            private const string InvocaSearchDays = "Invoca:SearchDays";
            private const string InvocaMaxExportCalls = "Invoca:MaxExportCalls";

            public Handler(IConfiguration config,
                ILogger<GetMarketingCallForCsvExport> logger,
                IFranchiseServiceClient franchiseServiceClient,
                JobReadOnlyDataContext context,
                ILookupServiceClient lookupServiceClient)
            {
                _logger = logger;
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _lookupServiceClient = lookupServiceClient;
                _config = config;
            }

            public async Task<List<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{franchiseId}", request.FranchiseId);
                _logger.LogInformation("Getting Invoca Calls for Franchise: {franchise}", request.FranchiseId);
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var searchDays = _config.GetValue(InvocaSearchDays, 30);
                var maxExportCalls = _config.GetValue(InvocaMaxExportCalls, 10000);
                var returnData = new List<Dto>();
                DateTime endDate = request.EndDate ?? DateTime.UtcNow;
                if (request.StartDate > request.EndDate)
                    return returnData;
                var noFranchise = request.FranchiseId == null ? true : false;
                var noFranchiseSet = request.FranchiseSetId == null ? true : false;
                
                List<ExternalMarketingCall> franchiseCalls;

                if (request.StartDate.HasValue == false && request.EndDate.HasValue == false)
                {
                    var franchiseCallsQuery = _context.ExternalMarketingCalls
                        .Where(j =>
                            (
                                //Include calls with Disposition that have never been sent
                                j.Disposition != Guid.Empty && !j.LastExported.HasValue
                            ) ||
                            (
                                //Include calls if there are any changes since the last export
                                j.Disposition != Guid.Empty && j.LastExported.HasValue && (
                                j.ModifiedDate > j.LastExported ||
                                j.AssociatedJobLastUpdatedDateTime > j.LastExported ||
                                j.DispositionSelectionDateTime > j.LastExported ||
                                j.SiteAppointmentStartDateTimeLastUpdated > j.LastExported ||
                                j.InvoicedAmountLastUpdated > j.LastExported)
                            ) ||
                            (
                                //Include calls with no disposition that previously had a disposition if the disposition changed since the last export
                                j.Disposition == Guid.Empty && j.LastExported.HasValue &&
                                j.DispositionRevokedTime.HasValue && j.DispositionRevokedTime > j.LastExported
                            ));
                    if (!noFranchise)
                        franchiseCallsQuery = franchiseCallsQuery.Where(x => x.FranchiseId == request.FranchiseId);
                    if(!noFranchiseSet)
                        franchiseCallsQuery = franchiseCallsQuery.Where(x => x.FranchiseSetId == request.FranchiseSetId);

                    // We want to cap our export calls so that the payload isnt too large
                    // for our lambda to handle. 
                    if(noFranchise && noFranchiseSet)
                    {
                        franchiseCallsQuery = franchiseCallsQuery.OrderByDescending(x=>x.CreatedDate).Take(maxExportCalls);
                    }
                    franchiseCalls = await franchiseCallsQuery.ToListAsync(cancellationToken);
                }
                else
                {
                    var franchiseCallsQuery = _context.ExternalMarketingCalls
                        .Where(j => j.CallReceivedDateTime >= request.StartDate
                                   && j.CallReceivedDateTime <= request.EndDate);

                    if (!noFranchise)
                        franchiseCallsQuery = franchiseCallsQuery.Where(x => x.FranchiseId == request.FranchiseId);
                    if (!noFranchiseSet)
                        franchiseCallsQuery = franchiseCallsQuery.Where(x => x.FranchiseSetId == request.FranchiseSetId);

                    franchiseCalls = await franchiseCallsQuery.ToListAsync(cancellationToken);
                }
                var jobListsId = franchiseCalls.Select(x => x.AssociatedJobId).ToList();
                var jobs = await _context.Jobs.Where(m => jobListsId.Contains(m.Id)).ToListAsync(cancellationToken);

                var franchises = await GetFranchises(franchiseCalls, cancellationToken);
                foreach (var call in franchiseCalls)
                {
                    var mappedData = await Map(call, franchises, jobs, lookups.CallDispositionTypes.ToList());
                    returnData.Add(mappedData);
                }

                var numberOfExternalMarketingCalls = returnData.Count();
                _logger.LogInformation("External Marketing Calls created for CSV Export: {count}", numberOfExternalMarketingCalls);
               
                return returnData;
            }

            private async Task<List<FranchiseDto>> GetFranchises(List<ExternalMarketingCall> calls, CancellationToken cancellationToken)
            {
                var franchises = new List<FranchiseDto>();
                var franchiseIds = calls.Select(x => new { x.FranchiseSetId, x.FranchiseId }).Distinct().ToList();

                foreach (var id in franchiseIds)
                {
                    try
                    {
                        var franchise = await _franchiseServiceClient.GetFranchiseAsync(id.FranchiseId, id.FranchiseSetId, false, cancellationToken);
                        franchises.Add(franchise);
                    }
                    catch
                    {
                        _logger.LogInformation("Could not locate franchise information for franchiseId: {franchise} franchiseSetId {franchiseSet}", id.FranchiseId, id.FranchiseSetId);
                    }
                }
                return franchises;
            }

            private async Task<Dto> Map(ExternalMarketingCall call, IEnumerable<FranchiseDto> franchises, List<Job> jobs, List<CallDispositionTypeDto> dispositions)
            {
                var job = call.AssociatedJobId != null ? jobs.FirstOrDefault(x => x.Id == call.AssociatedJobId) : null;

                var franchise = franchises.FirstOrDefault(x => x.Id == call.FranchiseId);

                var lastModified = new List<DateTime?>() { call.ModifiedDate, call.CreatedDate, call.InvoicedAmountLastUpdated, call.SiteAppointmentStartDateTimeLastUpdated }.Max();

                var dto = new Dto();
                return new Dto
                {
                    JobId = job != null ? job.Id : Guid.Empty,
                    ProjectNumber = job != null ? job.ProjectNumber : "",
                    FranchiseSetId = job != null ? job.FranchiseSetId : Guid.Empty,
                    FranchiseId = call.FranchiseId,
                    FranchiseName = franchise != null ? franchise.Name : "",
                    FranchiseNumber = franchise != null ? Convert.ToInt32(franchise.FranchiseNumber) : 0,
                    Disposition = call?.Disposition,
                    DispositionName = dispositions.FirstOrDefault(x => x.Id == call.Disposition)?.Name,
                    CallId = call.Id,
                    CallReceivedDateTime = call.CallReceivedDateTime,
                    CallRecordingId = call.CallRecordingId,
                    CallSourceId = call.CallSourceId,
                    LastModified = lastModified,
                    RevenueInvoiced = call.RevenueInvoiced,
                    HasSiteAppointment = call.SiteAppointmentStartDateTime != null || job.SiteAppointmentById != null ? true : false
                };
            }
        }
    }
}
