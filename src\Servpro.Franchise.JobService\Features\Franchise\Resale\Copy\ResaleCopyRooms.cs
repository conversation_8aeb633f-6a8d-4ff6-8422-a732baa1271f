﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyRooms
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobVisitResult { get; set; }
            public ProcessEntityResult JournalNotesWithoutRFTResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> RoomIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyRooms>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyRooms> _logger;
            private readonly IMapper _mapper;
            private readonly JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyRooms> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _context = context;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }


            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(Room));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.RoomIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var roomTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedRoomIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(roomTargetIds, 
                    GetRoomIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<Room, ResaleRoom>(
                    request.ResaleId,
                    room =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (room.PreExistingConditionsDiaryNoteId.HasValue && request.JournalNotesWithoutRFTResult.FailedEntities.Contains(room.PreExistingConditionsDiaryNoteId.Value))
                            failedDependencies.Add((nameof(JournalNote), room.PreExistingConditionsDiaryNoteId.Value));
                        if (room.DryOnJobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(room.DryOnJobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), room.DryOnJobVisitId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedRoomIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Rooms.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Room>> GetSourceEntitiesAsync(List<Guid> roomIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var rooms = await _context.Rooms
                    .Where(jv => roomIds.Contains(jv.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", rooms.Count);
                return rooms;
            }

            private async Task<List<Guid>> GetRoomIdsAsync(List<Guid?> roomTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Rooms
                    .Where(x => roomTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
