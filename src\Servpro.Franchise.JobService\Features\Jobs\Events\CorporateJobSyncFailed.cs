﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class CorporateJobSyncFailed
    {
        public class Event : CorporateJobSyncFailedEvent, IRequest
        {
            public Event(CorporateJobSyncFailedEvent.JobSyncFailedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly IServiceProvider _serviceProvider;
            private readonly ILogger<CorporateJobSyncFailed> _logger;

            public Handler(IServiceProvider serviceProvider, ILogger<CorporateJobSyncFailed> logger)
            {
                _serviceProvider = serviceProvider;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{incomingEvent} started", incomingEvent);
                using var serviceScope = _serviceProvider.CreateScope();

                var db = serviceScope.ServiceProvider.GetService<JobDataContext>();

                var job = await db.Jobs
                    .FirstOrDefaultAsync(j => j.Id == incomingEvent.JobUpload.JobId, cancellationToken: cancellationToken);

                if (job == null)
                {
                    _logger.LogWarning("JobId not found for {incomingEvent}", incomingEvent);
                    return Unit.Value;
                }

                Guid jobUploadTypeId;
                switch (incomingEvent.JobUpload.UploadType)
                {
                    case CorporateJobSyncFailedEvent.UploadType.Initial:
                        jobUploadTypeId = JobUploadTypes.JobInitial;
                        break;
                    case CorporateJobSyncFailedEvent.UploadType.Daily:
                        jobUploadTypeId = JobUploadTypes.JobDaily;
                        break;
                    case CorporateJobSyncFailedEvent.UploadType.Final:
                        jobUploadTypeId = JobUploadTypes.JobFinal;
                        break;
                    default:
                        _logger.LogWarning("Invalid upload type received for {jobId}", incomingEvent.JobUpload.JobId);
                        return Unit.Value;
                }

                var jobUploadLock = await db.JobUploadLocks
                    .FirstOrDefaultAsync(x => x.JobId == incomingEvent.JobUpload.JobId && x.JobUploadTypeId == jobUploadTypeId, cancellationToken: cancellationToken);

                if (jobUploadLock != null)
                {
                    db.JobUploadLocks.Remove(jobUploadLock);
                }

                await db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}