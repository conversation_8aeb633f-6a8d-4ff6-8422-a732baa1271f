#!/bin/bash
dotnet restore $2 -s $1 -s https://api.nuget.org/v3/index.json -s $3
dotnet-ef migrations list --project $2 --startup-project $2 --context JobDataContext | tee artifacts/migration-tmp
sed -n '/^[0-9]/p' artifacts/migration-tmp > artifacts/migration-list
migration_start=""
migration_end=""
current_migration=$(cat artifacts/current-migration)
echo "Current migration: $current_migration" 

while IFS= read -r line; do
    if [[ "$current_migration" == *"$line"* ]]
    then
        echo "Found a match"        
        read -r line
        migration_start="$line"
    fi
    migration_end="$line"
done < artifacts/migration-list

echo "Start: $migration_start"
echo "End: $migration_end"

export IS_CI_BUILD="true"

if [[ "$current_migration" == "" ]]
then
    echo "No current migration found."
    dotnet-ef migrations script --context JobDataContext --idempotent --verbose --project $2  --startup-project $2 -o artifacts/migrations.sql --configuration release
elif [[ "$migration_start" == "" ]] && [[ "$migration_end" == "" ]]
then
    echo "No migration to apply.  Currently up to date."
elif [[ "$migration_start" != "" ]] && [[ "$migration_end" != "" ]]
then
    dotnet-ef migrations script $current_migration --verbose --project $2 --context JobDataContext --startup-project $2 -o artifacts//migrations.sql --configuration release
    dotnet-ef migrations script $migration_end $current_migration --verbose --context JobDataContext --project $2  --startup-project $2 -o artifacts/rollback-migrations.sql --configuration release
fi