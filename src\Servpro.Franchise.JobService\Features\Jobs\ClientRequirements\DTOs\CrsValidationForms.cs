﻿using System;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationForms
    {
        public Guid FormTemplateId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string LinkedPage { get; set; }
        public Guid? RelatedForm { get; set; }
        public Guid? RelatedForm2 { get; set; }
        public Guid? RelatedForm3 { get; set; }
        public bool? IsRequiredForAllLossTypes { get; set; }
        public bool WaterFormRequired { get; set; }
        public bool MoldFormRequired { get; set; }
        public bool FireFormRequired { get; set; }
        public bool? IsRequiredForResidentialJob { get; set; }
        public bool? IsRequiredForCommercialJob { get; set; }
        public bool WaterForm { get; set; }
        public bool MoldForm { get; set; }
        public bool FireForm { get; set; }
        public string InsuranceClient { get; set; }
        public string CommercialClient { get; set; }
        public bool? IsAvailableInFirstNotice { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string FormalLanguage { get; set; }
        public bool IsActive { get; set; }
        public string Type { get; set; }
        public int ValidationStatus { get; set; }
        public Guid? JobFormId { get; set; }
        public string ValidationEffect { get; set; }
        public string FailMessage { get; set; }
        public string PassMessage { get; set; }
        public string DoNothingMessage { get; set; }
        public string Notification { get; set; }
        public bool IsClientRequirement { get; set; }
        public bool RequiredForInitialUpload { get; set; }
    }
}