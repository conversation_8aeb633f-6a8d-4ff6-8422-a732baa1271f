﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJournalNoteWithRFT
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult ZoneResult { get; set; }
            public ProcessEntityResult JobVisitResult { get; set; }
            public ProcessEntityResult TaskResult { get; set; }
            public ProcessEntityResult RoomFlooringTypeAffectedResult { get; set; }            
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JournalNoteIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJournalNoteWithRFT>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJournalNoteWithRFT> _logger;
            private readonly IMapper _mapper;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private readonly JobDataContext _context;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyJournalNoteWithRFT> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _context = context;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(ResaleEntity.JournalNotesWithRFT));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JournalNoteIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var journalNoteWithRFTTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedJournalNoteWithRFTIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(journalNoteWithRFTTargetIds, 
                    GetJournalNoteIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JournalNote, ResaleJournalNote>(
                    request.ResaleId,
                    journalNote =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (journalNote.ZoneId.HasValue && request.ZoneResult.FailedEntities.Contains(journalNote.ZoneId.Value))
                            failedDependencies.Add((nameof(Zone), journalNote.ZoneId.Value));
                        if (journalNote.JobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(journalNote.JobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), journalNote.JobVisitId.Value));
                        if (journalNote.JobId.HasValue && request.JobResult.FailedEntities.Contains(journalNote.JobId.Value))
                            failedDependencies.Add((nameof(Job), journalNote.JobId.Value));
                        if (journalNote.TaskId.HasValue && request.TaskResult.FailedEntities.Contains(journalNote.TaskId.Value))
                            failedDependencies.Add((nameof(JobService.Models.Drybook.Task), journalNote.TaskId.Value));
                        if (journalNote.RoomFlooringTypeAffectedId.HasValue && request.RoomFlooringTypeAffectedResult.FailedEntities.Contains(journalNote.RoomFlooringTypeAffectedId.Value))
                            failedDependencies.Add((nameof(RoomFlooringTypeAffected), journalNote.RoomFlooringTypeAffectedId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedJournalNoteWithRFTIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JournalNote.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JournalNote>> GetSourceEntitiesAsync(List<Guid> journalNoteIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var journalNotes = await _context.JournalNote
                    .Where(jn => jn.RoomFlooringTypeAffectedId.HasValue && journalNoteIds.Contains(jn.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", journalNotes.Count);
                return journalNotes;
            }

            private async Task<List<Guid>> GetJournalNoteIdsAsync(List<Guid?> journalNoteTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JournalNote
                    .Where(x => journalNoteTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
