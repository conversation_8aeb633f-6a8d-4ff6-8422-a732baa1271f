﻿using Nager.Date;
using System;

using TimeZoneConverter;

namespace Servpro.Franchise.JobService.Common.ExtensionMethods
{
    public static class DateTimeExtensions
    {
        public static DateTime? ConvertDateFromUtc(this DateTime dateToConvertInUtc, string franchiseTimeZone)
        {
            if (dateToConvertInUtc.Kind == DateTimeKind.Utc)
            {
                var tzi = TZConvert.GetTimeZoneInfo(franchiseTimeZone);
                return TimeZoneInfo.ConvertTimeFromUtc(dateToConvertInUtc, tzi);
            }
            return dateToConvertInUtc;
        }

        public static bool AreEqual(this DateTime source, DateTime target)
        {
            var diff = (source.Subtract(target)).TotalMilliseconds;
            return Math.Abs(diff) < 100;
        }

        public static bool AreEqual(this DateTime? source, DateTime? target)
        {
            if (!source.HasValue || !target.HasValue)
            {
                return false;
            }

            var diff = (source.Value.Subtract(target.Value)).TotalMilliseconds;
            return Math.Abs(diff) < 100;
        }

        public static bool IsEarlierThan(this DateTime source, DateTime target)
        {
            if (source.CompareTo(target) < 0)
                return true;
            return false;
        }

        public static DateTime AddBusinessDays(this DateTime current, int days)
        {
            var sign = Math.Sign(days);
            var unsignedDays = Math.Abs(days);
            for (var i = 0; i < unsignedDays; i++)
            {
                do
                {
                    current = current.AddDays(sign);
                } while (current.DayOfWeek == DayOfWeek.Saturday ||
                         current.DayOfWeek == DayOfWeek.Sunday ||
                         DateSystem.IsPublicHoliday(current, CountryCode.US));
            }
            return current;
        }

        public static DateTime RemoveSecondsFromDateTime(this DateTime newDate, DateTime date)
        {
             newDate = new DateTime(date.Year, date.Month, date.Day, date.Hour, date.Minute, 0, date.Kind);
            return newDate;
        }

        public static DateTime RemoveSecondsFromDateTime(this DateTime date)
        {
            return new DateTime(date.Year, date.Month, date.Day, date.Hour, date.Minute, 0, date.Kind);
        }
    }
}