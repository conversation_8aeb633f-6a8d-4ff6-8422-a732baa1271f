﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

namespace Servpro.Franchise.JobService.Features.Version
{
    public class SaveVersionData
    {
        public class Command : IRequest
        {
            public string VersionNumber { get; set; }
            public int VersionId { get; set; }
            public bool VersionAcknowledge { get; set; }
            public string ReleaseNotesUrl { get; set; }
        }


        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.VersionNumber).NotEmpty();
                RuleFor(x => x.VersionAcknowledge).NotNull();
                RuleFor(x => x.ReleaseNotesUrl).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            public Handler(JobDataContext context)
            {
                _context = context;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var versionData = await _context.VersionData.AnyAsync(cancellationToken);

                var newRow = new VersionData
                {
                    VersionAcknowledge = request.VersionAcknowledge,
                    VersionNumber = request.VersionNumber,
                    ReleaseNotesUrl = request.ReleaseNotesUrl
                };

                if (!versionData)
                {
                    await _context.VersionData.AddAsync(newRow, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                }

                return Unit.Value;
            }

        }
    }
}