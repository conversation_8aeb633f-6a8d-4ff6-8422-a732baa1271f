﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class EditJournalNotes
    {
        public class Command : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public IEnumerable<Dto> JournalNotes { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleForEach(x => x.JournalNotes).ChildRules(y =>
                {
                    y.RuleFor(m => m.TypeId).NotEmpty();
                    y.RuleFor(m => m.VisibilityId).NotEmpty();
                });
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid CategoryId { get; set; }
            public DateTime? ActionDate { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public bool IncludeInSummaryCoverPage { get; set; }
        }

        public class Handler : IRequestHandler<Command, IEnumerable<Dto>>
        {
            private readonly JobDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(JobDataContext context,
                ILookupServiceClient lookupServiceClient,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo)
            {
                _lookupServiceClient = lookupServiceClient;
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
                _userInfo = userInfo;
            }

            public async Task<IEnumerable<Dto>> Handle(Command request, CancellationToken cancellationToken)
            {
                var journalNoteIds = request.JournalNotes.Select(x => x.Id).ToList();

                var journalNotes = await _context.JournalNote
                    .Include(x => x.Task)
                    .Where(q => journalNoteIds.Contains(q.Id))
                    .ToListAsync(cancellationToken);

                if (!journalNotes.Any())
                    throw new ResourceNotFoundException($"JournalNotes not found: {String.Join(',', journalNoteIds)}");

                var userInfo = _userInfo.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                foreach (var journalNote in journalNotes)
                {
                    var updatedJournalNote = request.JournalNotes.FirstOrDefault(x => x.Id == journalNote.Id);

                    //Update status of Task
                    var task = journalNote.Task;
                    if (task != null)
                    {
                        var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                        var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == task.TaskTypeId);

                        if (taskType.ShouldCloseWhenDiaryEntryIsComplete && !string.IsNullOrEmpty(updatedJournalNote.Note))
                        {
                            task.PercentComplete = 100;
                            task.TaskStatusId = TaskStatuses.Completed;
                        }
                    }

                    //Update Journal Notes
                    UpdateJournalNote(journalNote, updatedJournalNote, userInfo.Name);
                }

                var journalNoteEvents = journalNotes.Select(x => GenerateJournalNoteUpdatedEvent(x, correlationId, userInfo.Username, userInfo.Id));
                _context.OutboxMessages.AddRange(journalNoteEvents);
                await _context.SaveChangesAsync(cancellationToken);
                
                return journalNotes.Select(x => Map(x)).ToList();
            }

            private void UpdateJournalNote(JournalNote journalNote, Dto updatedJournalNote, string modifiedBy)
            {
                journalNote.ActionDate = updatedJournalNote.ActionDate;
                journalNote.CategoryId = updatedJournalNote.CategoryId;
                journalNote.Note = updatedJournalNote.Note;
                journalNote.Subject = updatedJournalNote.Subject;
                journalNote.TypeId = updatedJournalNote.TypeId;
                journalNote.VisibilityId = updatedJournalNote.VisibilityId;
                journalNote.ModifiedDate = DateTime.UtcNow;
                journalNote.ModifiedBy = modifiedBy;
                journalNote.IncludeInSummaryCoverPage = updatedJournalNote.IncludeInSummaryCoverPage;
            }

            private Dto Map(JournalNote journalNote)
                => new Dto
                {
                    Id = journalNote.Id,
                    Author = journalNote.Author,
                    Note = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage
                };

            private JournalNoteUpdatedEvent.JournalNoteDto MapUpdateEventDto(JournalNote journalNote, Guid modifiedById)
                => new JournalNoteUpdatedEvent.JournalNoteDto()
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId ?? Guid.Empty,
                    Author = journalNote.Author,
                    Subject = journalNote.Subject,
                    Message = journalNote.Note,
                    CategoryId = journalNote.CategoryId,
                    ActionDate = journalNote.ActionDate,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    UpdatedById = modifiedById,
                    UpdatedDate = DateTime.UtcNow,
                    RuleIds = journalNote.RuleIds?.ToList()
                };

            private OutboxMessage GenerateJournalNoteUpdatedEvent(JournalNote journalNote, Guid correlationId, string userName, Guid modifiedById)
            {
                var updateDto = MapUpdateEventDto(journalNote, modifiedById);
                var updateEvent = new JournalNoteUpdatedEvent(updateDto, correlationId);
                return new OutboxMessage(updateEvent.ToJson(), nameof(JournalNoteUpdatedEvent), correlationId, userName);
            }
        }
    }
}
