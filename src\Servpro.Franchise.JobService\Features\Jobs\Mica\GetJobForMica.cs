﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetJobForMica
    {
        public class Query : IRequest<JobDto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }
        public class JobDto
        {
            public Guid Id { get; set; }
            public Guid FranchiseId { get; set; }
            public int JobProgress { get; set; }
            public string ClaimNumber { get; set; }
            public string PolicyNumber { get; set; }
            public string LossType { get; set; }
            public string ProjectNumber { get; set; }
            public string PropertyType { get; set; }
            public string CauseOfLoss { get; set; }
            public string JobNote { get; set; }
            public decimal? InvoiceAmount { get; set; }
            public bool IsMoldPresent { get; set; }
            public bool IsServiceRefused { get; set; }
            public bool IsDryingConfirmed { get; set; }
            public List<JobContactMapDto> JobContacts { get; set; }
            public List<JobDateDto> JobDates { get; set; }
        }

        public class JobContactMapDto
        {
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public Guid TypeId { get; set; }
            public string Email { get; set; }
            public List<PhoneDto> Phones { get; set; }
            public AddressDto Address { get; set; }
        }

        public class PhoneDto
        {
            public int Type { get; set; }
            public string Number { get; set; }
            public string Extension { get; set; }
        }

        public class AddressDto
        {
            public string Address1 { get; set; }
            public string Address2 { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string PostalCode { get; set; }
            public Guid CountryId { get; set; }
        }

        public class JobDateDto
        {
            public Guid JobDateTypeId { get; set; }
            public DateTime Date { get; set; }
        }


        public class Handler : IRequestHandler<Query, JobDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupClient;
            private readonly ILogger<Handler> _logger;
            private readonly HashSet<Guid> _jobDatesToSend = new HashSet<Guid>()
            {
                JobDateTypes.LossOccurred,
                JobDateTypes.LossReceived,
                JobDateTypes.CustomerCalled,
                JobDateTypes.InitialOnSiteArrival,
                JobDateTypes.WorkAuthorizationSigned,
                JobDateTypes.DryingComplete,
                JobDateTypes.Complete
            };

            public Handler(
                  JobReadOnlyDataContext context,
                  ILookupServiceClient lookupClient,
                  ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
                _lookupClient = lookupClient;
            }

            public async Task<JobDto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);

                _logger.LogInformation("Getting Job data for Mica.");

                var job = await _context.Jobs
                    .Include(x => x.JobContacts)
                        .ThenInclude(x => x.Contact)
                    .Include(x => x.JobTriStateAnswers)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var lookups = await _lookupClient.GetLookupsAsync(cancellationToken);

                return Mapper.Map(job, lookups, _jobDatesToSend);
            }
        }

        public class Mapper
        {
            public static JobDto Map(Models.Job job, GetLookups.Dto lookups, HashSet<Guid> jobDatesToSend)
            {
                return new JobDto
                {
                    Id = job.Id,
                    ProjectNumber = job.ProjectNumber,
                    FranchiseId = job.FranchiseId,
                    JobProgress = (int)job.JobProgress,
                    ClaimNumber = job.InsuranceClaimNumber,
                    PolicyNumber = job.InsurancePolicyNumber,
                    LossType = lookups.LossTypes?.FirstOrDefault(x => x.Id == job.LossTypeId)?.Name,
                    PropertyType = lookups.PropertyTypes?.FirstOrDefault(x => x.Id == job.PropertyTypeId)?.Name,
                    CauseOfLoss = lookups.CausesOfLoss?.FirstOrDefault(x => x.CauseOfLossId == job.CauseOfLossId)?.Name,
                    JobNote = job.LossNote,
                    InvoiceAmount = job.TotalRevenue,
                    IsMoldPresent = IsQuestionAnsweredWithTrue(job.JobTriStateAnswers, Common.JobTriStateQuestions.MoldIsPresent),
                    IsServiceRefused = IsQuestionAnsweredWithTrue(job.JobTriStateAnswers, Common.JobTriStateQuestions.DidCustomerRefuseService),
                    IsDryingConfirmed = IsQuestionAnsweredWithTrue(job.JobTriStateAnswers, Common.JobTriStateQuestions.IsDryingWorkbookCompleted),
                    JobContacts = job.JobContacts?.Select(x => Map(x)).ToList() ?? new List<JobContactMapDto>(),
                    JobDates = job.JobDates?.Where(x => jobDatesToSend.Contains(x.JobDateTypeId))
                                    ?.Select(x => Map(x)).ToList() ?? new List<JobDateDto>(),
                };
            }

            private static JobContactMapDto Map(Models.JobContactMap contactMap)
            {
                return new JobContactMapDto
                {
                    FirstName = contactMap.Contact.FirstName,
                    LastName = contactMap.Contact.LastName,
                    Email = contactMap.Contact.EmailAddress,
                    TypeId = contactMap.JobContactTypeId,
                    Address = Map(contactMap.Contact.Address),
                    Phones = contactMap.Contact?.PhoneNumbers?.Select(x => Map(x)).ToList() ?? new List<PhoneDto>(),
                };
            }

            private static PhoneDto Map(Models.Phone phone)
            {
                return new PhoneDto
                {
                    Number = phone.PhoneNumber,
                    Extension = phone.PhoneExtension,
                    Type = (int)phone.PhoneType
                };
            }

            private static AddressDto Map(Models.Address address)
            {
                if (address == null)
                    return null;

                var addressDto = new AddressDto
                {
                    Address1 = address.Address1,
                    Address2 = address.Address2,
                    City = address.City,
                    PostalCode = address.PostalCode,
                };

                if (address.State != null)
                {
                    addressDto.State = address.State.StateName;
                    addressDto.CountryId = address.State.CountryId;
                }

                return addressDto;
            }

            private static JobDateDto Map(JobDate jobDate)
            {
                return new JobDateDto
                {
                    JobDateTypeId = jobDate.JobDateTypeId,
                    Date = jobDate.Date
                };
            }

            private static bool IsQuestionAnsweredWithTrue(ICollection<Models.Drybook.JobTriStateAnswer> jobTriStateAnswers, Guid jobTriStateQuestion)
            {
                if (jobTriStateAnswers == null || jobTriStateAnswers.Count == 0)
                    return false;

                return jobTriStateAnswers
                        .Any(x => x.JobTriStateQuestionId == jobTriStateQuestion
                                && x.Answer.HasValue && x.Answer.Value);
            }
        }
    }
}
