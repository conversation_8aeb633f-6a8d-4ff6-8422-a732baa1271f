﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Combo;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Models.RawSqlModels;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Options;
using Servpro.Franchise.JobService.Infrastructure.Options;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;

namespace Servpro.Franchise.JobService.Features.Franchise.Combo.Events
{
    public class FranchiseComboStarted
    {
        public class Event : FranchiseComboStartedEvent, IRequest
        {
            public Event(Guid id, 
                int comboAttempt, 
                Guid sourceFranchiseSetId, 
                Guid targetFranchiseSetId, 
                bool copyVendors, 
                DateTime comboAttemptExpiresOn, 
                Guid correlationId)
                : base(id, comboAttempt, sourceFranchiseSetId, targetFranchiseSetId, 
                    copyVendors, comboAttemptExpiresOn, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<Handler> _logger;
            private readonly JobDataContext _context;
            private readonly IOptionsMonitor<ComboOptions> _comboOptions;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private readonly int _maxErrorsPerEvent;

            public Handler(ILogger<Handler> logger, 
                JobDataContext context, 
                IOptionsMonitor<ComboOptions> comboOptions,
                IResaleSplitComboUtility resaleSplitComboUtility,
                IConfiguration config)
            {
                _logger = logger;
                _context = context;
                _comboOptions = comboOptions;
                _resaleSplitComboUtility = resaleSplitComboUtility;
                _maxErrorsPerEvent = Convert.ToInt32(config["Resales:MaxErrorPerEvent"]);
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{@combo}",
                    new
                    {
                        id = request.Id,
                        attempt = request.ComboAttempt,
                        sourceFranchiseSetId = request.SourceFranchiseSetId,
                        targetFranchiseSetId = request.TargetFranchiseSetId
                    });
                _logger.LogInformation("Precessing Combo: {@payload}", request);

                var errors = await UpdateEntitiesAsync(request.SourceFranchiseSetId, 
                    request.TargetFranchiseSetId, cancellationToken);

                await _resaleSplitComboUtility.TriggerRescindByRscAsync(request.SourceFranchiseSetId, null, request.CorrelationId, cancellationToken);

                var comboCompletedEvent = GenerateComboCompletedEvent(
                    request.Id,
                    request.ComboAttempt,
                    errors.Any() ? Status.Failed : Status.Succeeded,
                    errors.Any() ? errors.Count : (int?)null,
                    request.CorrelationId);

                await RaiseCompletionEventsAsync(errors, comboCompletedEvent, request, cancellationToken);

                return Unit.Value;
            }

            private async System.Threading.Tasks.Task RaiseCompletionEventsAsync(List<ErrorInfo> errors, 
                FranchiseComboCompletedEvent comboCompletedEvent, 
                Event request, 
                CancellationToken cancellationToken)
            {
                if (errors.Any())
                {
                    var outboxMessages = new List<OutboxMessage>() { GenerateOutboxMessage(comboCompletedEvent, request.CorrelationId) };
                    foreach (var errorBatch in errors.Batch(_maxErrorsPerEvent))
                    {
                        outboxMessages.Add(GenerateOutboxMessage(
                            GenerateComboErrorOccurredEvent(request.Id, request.ComboAttempt, errorBatch, request.CorrelationId),
                            request.CorrelationId));
                    }
                    await _context.OutboxMessages.AddRangeAsync(outboxMessages, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                    return;
                }

                var outboxMessage = GenerateOutboxMessage(comboCompletedEvent, request.CorrelationId);
                await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateEntitiesAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                var jobErrors = await UpdateJobsAsync(sourceFSetId, targetFSetId, cancellationToken);
                var businessErrors = await UpdateBusinessesAsync(sourceFSetId, targetFSetId, cancellationToken);
                var equipmentErrors = await UpdateEquipmentAsync(sourceFSetId, targetFSetId, cancellationToken);
                var equipmentModelErrors = await UpdateEquipmentModelsAsync(sourceFSetId, targetFSetId, cancellationToken);
                var equipmentTypeErrors = await UpdateEquipmentTypesAsync(sourceFSetId, targetFSetId, cancellationToken);
                var mediaErrors = await UpdateMediaMetadataAsync(sourceFSetId, targetFSetId, cancellationToken);
                var taskErrors = await UpdateTasksAsync(sourceFSetId, targetFSetId, cancellationToken);
                var wipRecordErrors = await UpdateWipRecordsAsync(sourceFSetId, targetFSetId, cancellationToken);
                var stormEventErrors = await UpdateStormEventsAsync(sourceFSetId, targetFSetId, cancellationToken);
                var mergeCandidateErrors = await UpdateMergeCandidatesAsync(sourceFSetId, targetFSetId, cancellationToken);

                var errors = jobErrors
                    .Concat(businessErrors)
                    .Concat(equipmentErrors)
                    .Concat(equipmentModelErrors)
                    .Concat(equipmentTypeErrors)
                    .Concat(mediaErrors)
                    .Concat(taskErrors)
                    .Concat(wipRecordErrors)
                    .Concat(stormEventErrors)
                    .Concat(mergeCandidateErrors)
                    .ToList();

                _logger.LogWarning("{errorsCount} errors Found: {@errors}", errors.Count, errors);

                return errors;
            }

            private async Task<List<ErrorInfo>> UpdateJobsAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<Job>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateBusinessesAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<Business>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateEquipmentAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<Equipment>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }
            
            private async Task<List<ErrorInfo>> UpdateEquipmentModelsAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<EquipmentModel>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateEquipmentTypesAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<EquipmentType>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }
            
            private async Task<List<ErrorInfo>> UpdateMediaMetadataAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<MediaMetadata>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateTasksAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<Models.Drybook.Task>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateMergeCandidatesAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(MergeCandidate));

                var errors = new List<ErrorInfo>();
                var mergeCandidates = await _context.MergeCandidates
                    .Where(j => j.FranchiseSetId == sourceFSetId)
                    .ToListAsync(cancellationToken: cancellationToken);

                var counter = 0;
                var total = mergeCandidates.Count;
                foreach (var mergeCandidate in mergeCandidates)
                {
                    counter++;
                    using var updateScope = _logger.BeginScope("{entityId}{progress}", mergeCandidate.Id, $"{counter}/{total}");
                    mergeCandidate.FranchiseSetId = targetFSetId;

                    try
                    {
                        await _context.SaveChangesAsync(cancellationToken);
                    }
                    catch (Exception e)
                    {
                        errors.Add(new ErrorInfo(mergeCandidate.Id.ToString(), nameof(MergeCandidate), e.Message, e.StackTrace));
                    }
                    mergeCandidates.Remove(mergeCandidate);
                }
                return errors;
            }

            private async Task<List<ErrorInfo>> UpdateStormEventsAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<StormEvent>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            private async Task<List<ErrorInfo>> UpdateWipRecordsAsync(Guid sourceFSetId,
                Guid targetFSetId, CancellationToken cancellationToken)
            {
                return await UpdateRecordsInBatchAsync<WipRecord>((j => j.FranchiseSetId == sourceFSetId), 
                    (x => x.SetProperty(pe => pe.FranchiseSetId, targetFSetId)), 
                    cancellationToken);
            }

            /// <summary>
            /// Updates all the records found using the provided recordFilter by setting properties definied and passed in 
            /// through setPropertyCalls. <br />
            /// NOTE: Be very sure you're wanting to use this, this is an ALL or NOTHING operation, the safest way to utilize this
            /// is by ensuring that any properties defined to be set should not be possible to throw an error (No FK's should be referenced) <br />
            /// If you're setting properties that can possibly fail, it is best to use UpdateRecordsAsync, where it goes through
            /// each record and granually tracks any exceptions that occur, getting through as many as possible
            /// </summary>
            private async Task<List<ErrorInfo>> UpdateRecordsInBatchAsync<T>(Expression<Func<T, bool>> recordFilter, 
                Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> setPropertyCalls, 
                CancellationToken cancellationToken)
                where T : Entity<Guid>
            {
                var errors = new List<ErrorInfo>();
                using var scope = _logger.BeginScope("{type}", typeof(T).Name);
                _context.Database.SetCommandTimeout(TimeSpan.FromMilliseconds(_comboOptions.CurrentValue.DatabaseTimeoutInMs));
                var (records, recordCount) = await GetRecordsAndCountAsync(recordFilter, cancellationToken);

                try
                {
                    _logger.LogInformation("Attempting to move {recordCount} records.", recordCount);
                    var effectedRecordCount = await records.ExecuteUpdateAsync(setPropertyCalls, cancellationToken);
                    if (effectedRecordCount != recordCount)
                    {
                        string errorMessage = $"Not all records were moved, expected: {recordCount}, effected: {effectedRecordCount}";
                        var partialCompletionErrors = HandlePartialCompletion(errorMessage, recordCount, effectedRecordCount, typeof(T).Name);
                        errors.AddRange(partialCompletionErrors);
                    }
                    _logger.LogInformation("Completed moving items.");
                }
                catch (Exception e) when (e.GetType() != typeof(OperationCanceledException))
                {
                    _logger.LogError(e, "Error occurred while moving records.");
                    errors.Add(new ErrorInfo(Guid.Empty.ToString(), typeof(T).Name, e.Message, e.StackTrace));
                }

                return errors;
            }

            private async Task<(IQueryable<T> records, int recordCount)> GetRecordsAndCountAsync<T>(Expression<Func<T, bool>> recordFilter, 
                CancellationToken cancellationToken) 
                where T : Entity<Guid>
            {
                _logger.LogInformation("Getting records and count for {type}.", typeof(T).Name);
                var records = _context.Set<T>().AsNoTracking().Where(recordFilter);
                var recordCount = await records.AsNoTracking().CountAsync(cancellationToken);

                return (records, recordCount);
            }

            private List<ErrorInfo> HandlePartialCompletion(string errorMessage, int recordCount, int effectedRecordCount, string entityType)
            {
                //Right now we just log this as an error and continue because I don't think this ever gets hit 
                //due to the ExecuteUpdate being an all or nothing action. In the future if we see that there
                //are actually cases where this is hit, I think we should do a fallback and utilize the UpdateRecordsAsync
                //to go through and get the rest of the records and go through them one by one in order to get a detailed failure
                _logger.LogError(errorMessage, recordCount, effectedRecordCount);
                return new List<ErrorInfo> { new ErrorInfo(Guid.Empty.ToString(), entityType, errorMessage, string.Empty) };
            }

            private async Task<List<ErrorInfo>> UpdateRecordsAsync<T>(List<T> records, Action<T> updateRecord, CancellationToken cancellationToken)
                where T : Entity<Guid>
            {
                var counter = 0;
                var total = records.Count;
                var errors = new List<ErrorInfo>();

                foreach (var record in records)
                {
                    counter++;
                    using var updateScope = _logger.BeginScope("{entityId}{progress}", record.Id, $"{counter}/{total}");
                    updateRecord(record);

                    try
                    {
                        await _context.SaveChangesIndependentlyAsync(cancellationToken);
                        _logger.LogInformation("Item copied successfully.");
                    }
                    catch (Exception e) when (e.GetType() != typeof(OperationCanceledException))
                    {
                        errors.Add(new ErrorInfo(record.Id.ToString(), typeof(T).Name, e.Message, e.StackTrace));
                    }
                }

                return errors;
            }

            private FranchiseComboCompletedEvent GenerateComboCompletedEvent(Guid comboId, 
                int comboAttempt, Status status, int? errorCount, Guid correlationId)
                => new FranchiseComboCompletedEvent(comboId, comboAttempt, Service.JobService, 
                    status, errorCount, correlationId);

            private FranchiseComboErrorOccurredEvent GenerateComboErrorOccurredEvent(
                Guid comboId, int comboAttempt, IEnumerable<ErrorInfo> errors, Guid correlationId)
                => new FranchiseComboErrorOccurredEvent(comboId, comboAttempt, 
                    Service.JobService, errors.ToList(), correlationId);

            private OutboxMessage GenerateOutboxMessage<T>(T message, 
                Guid correlationId, string createdBy = null)
            {
                createdBy ??= typeof(T).Name;
                return new OutboxMessage(message.ToJson(), 
                    typeof(T).Name, correlationId, createdBy);
            }
        }
    }
}
