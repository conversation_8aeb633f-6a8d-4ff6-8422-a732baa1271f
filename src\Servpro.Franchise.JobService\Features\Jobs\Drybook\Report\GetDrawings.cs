﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.Runtime.Internal.Util;
using Microsoft.Extensions.Logging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetSketches
    {
        public class Query : IRequest<DrawingsModelDto>
        {
            public Query(Guid id)
            {
                JobId = id;
            }
            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Query, DrawingsModelDto>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly ILogger<GenerateDryingReport> _logger;
            public Handler(JobReadOnlyDataContext db, ILogger<GenerateDryingReport> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<DrawingsModelDto> Handle(Query request,
                CancellationToken cancellationToken)
            {
                _logger.LogDebug("DryingReportLog - starting GetSketch for jobId: {JobId}", request.JobId);
                var jobSketches = await _db.JobSketch
                                           .Include(c => c.MediaMetadata)
                                           .AsNoTracking()
                                           .Where(x => x.JobId == request.JobId &&
                                                       x.CanvasJson != null &&
                                                       x.MediaMetadata.MediaTypeId == MediaTypes.Sketch &&
                                                       !x.MediaMetadata.IsDeleted)
                                           .OrderBy(x=>x.CreatedDate)
                                           .ToListAsync(cancellationToken);

                return new DrawingsModelDto
                {
                    Drawings = jobSketches.Select(Map).ToList()
                };

            }

            private static Drawing Map(JobSketch sketch)
                => new Drawing
                {
                    Id = sketch.Id,
                    JobId = sketch.JobId,
                    CanvasJson = sketch.CanvasJson,
                    MediaId = sketch.MediaMetadataId,
                    Name = sketch.Name,
                    JobArtifactId = sketch.MediaMetadata.ArtifactTypeId
                };
        }
    }
}
