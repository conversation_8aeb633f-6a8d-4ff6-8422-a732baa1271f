﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddStormProductionFranchise : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("6ddd74c1-4974-4d25-b91e-4ed7b885c90a"));

            migrationBuilder.AddColumn<bool>(
                name: "IsClosedFiveArounds",
                table: "Storm",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.CreateTable(
                name: "StormProductionFranchises",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    StormEventId = table.Column<int>(type: "int", nullable: false),
                    StormEventName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    StormId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StormProductionFranchises", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("a9da5a25-c44c-421e-a9a9-03ff0c978e93"), null, new DateTime(2024, 5, 31, 21, 9, 14, 470, DateTimeKind.Utc).AddTicks(4925), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StormProductionFranchises");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("a9da5a25-c44c-421e-a9a9-03ff0c978e93"));

            migrationBuilder.DropColumn(
                name: "IsClosedFiveArounds",
                table: "Storm");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("6ddd74c1-4974-4d25-b91e-4ed7b885c90a"), null, new DateTime(2024, 5, 20, 18, 4, 10, 269, DateTimeKind.Utc).AddTicks(42), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }
    }
}
