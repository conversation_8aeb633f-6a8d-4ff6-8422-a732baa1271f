﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.WipBoards.ScheduleService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Customization.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.WipBoards.Audits
{
    public class GetWipBoardAuditsOverdueReturned
    {
        public class Query : IRequest<List<WipRecord>>
        {
            public Guid FranchiseSetId { get; set; }
            public DateTime? Date { get; set; }
            public ICollection<GridColumnDto> CustomColumns { get; internal set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.FranchiseSetId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Query, List<WipRecord>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IScheduleService _scheduleService;

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                IScheduleService scheduleService)
            {
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _scheduleService = scheduleService;
            }

            public async Task<List<WipRecord>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                DateTime requestDate = request.Date == null ? DateTime.UtcNow : request.Date.Value;
                TimeZoneInfo timeZoneInfo = await _franchiseServiceClient.GetFranchiseSetPrimaryTimeZoneAsync(request.FranchiseSetId, cancellationToken: cancellationToken);
                DateTime lastBusinessDay = await _scheduleService.LastCloseOfNormalDay(timeZoneInfo, requestDate,skipHoliday:false);

                var recordsOverdue = await GetRecordsOverdueReturned(lastBusinessDay, _context, 
                    request.FranchiseSetId, timeZoneInfo, cancellationToken);

                return recordsOverdue;
            }

            public static async Task<List<WipRecord>> GetRecordsOverdueReturned(DateTime lastBusinessDay, JobDataContext context, 
                Guid franchiseSetId, TimeZoneInfo timeZoneInfo, CancellationToken cancellationToken)
            {
                var records = await context.WipRecords
                    .Where(r =>
                             r.SelfAuditReturnedDate < lastBusinessDay ||
                             r.CorporateAuditReturnedDate < lastBusinessDay)
                    .Where(r => r.FranchiseSetId == franchiseSetId 
                        && r.AuditStage != "Deleted" 
                        && r.JobProgress != JobProgress.NotSoldCancelled)
                    .ToListAsync(cancellationToken);

                records = GetWipBoard.PerformWipRecordCalculations(records, timeZoneInfo);

                return records;
            }

        }

    }
}
