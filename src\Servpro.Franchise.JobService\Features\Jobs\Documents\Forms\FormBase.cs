﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Aspose.Pdf;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public interface IForm : IDisposable
    {
        void Merge(Document outputDocument, int copies, FormTemplate rawForm, List<FormFieldDto> formFields, FieldMapper fieldMapper, string timeZoneId, ILogger logger);
    }

    public abstract class FormBase<TDoc, TField> : IForm where TDoc : IDisposable
    {
        private ILogger Logger;
        private readonly MemoryStream _stream = null;

        private void LogInfo(string data)
        {
            if (Logger != null)
                Logger.LogInformation(data);
        }


        public void Merge(Document outputDocument, int copies, FormTemplate rawForm, List<FormFieldDto> formFields, FieldMapper fieldMapper, string timeZoneId, ILogger _logger)
        {
            Logger = _logger;
            var stream = new MemoryStream();

            if (rawForm.Form == null)
                return;
            
            stream.Write(rawForm.Form.ToArray(), 0, rawForm.Form.Length);

            // The inheriting type will open this into the appropriate document type (PDF or DOC)
            Open(stream);

            // gets the fields from the document using a token
            var documentFields = GetFields();
            Logger.LogInformation("Filling document fields: " + documentFields.ToJson());

            SetFormFieldValues(documentFields, formFields, fieldMapper, timeZoneId);

            // once the fields are all set, re-save the OpenXml document back to it's own stream
            PageCollection pages = Save(stream);

            // append the pages from the pdf to the master document for as many copies as requested
            for (var i = 0; i < copies; i++)
            {
                outputDocument.Pages.Add(pages);
            }
        }

        protected abstract void Open(MemoryStream stream);

        protected abstract IEnumerable<TField> GetFields(IEnumerable<TField> allFields, string token);

        protected abstract TField[] GetFields();

        protected abstract void SetFieldValue(TField field, string value, Guid formFieldTypeId);

        protected abstract PageCollection Save(MemoryStream stream);

        private void SetFormFieldValues(TField[] documentFields, IEnumerable<FormFieldDto> templateFields, FieldMapper fieldMapper, string timeZoneId)
        {
            Logger.LogInformation("Starting SetFormFieldValues");

            // TODO: This would be much better if we just went through the not whitespace fields of the DOC, instead of every form field 
            foreach (var formField in templateFields)
            {
                // get the matching merge field from the mapping tables for meta data mapping
                var fields = GetFields(documentFields, formField.Name);

                if (fields == null)
                    continue;

                foreach (var field in fields)
                {
                    var stringBuilder = new StringBuilder();
                    var format = formField.FormatString ?? "{0}";

                    // uses reflection to get the property from the job object
                    var propertyValue = fieldMapper.GetPropertyValue(formField.FormFieldMapping.PropertyName);

                    if (propertyValue != null)
                    {
                        switch (formField.FormFieldMapping.PropertyType)
                        {
                            case "System.DateTime":
                                // convert UTC date to zone specific
                                DateTime date = (DateTime)propertyValue;
                                DateTime? result = null;
                                if (date.Kind == DateTimeKind.Utc)
                                {
                                    result = date.ConvertDateFromUtc(timeZoneId);
                                }
                                stringBuilder.AppendFormat(format, result);
                                break;

                            case "System.Int32":
                            case "System.Int64":
                            case "System.Decimal":
                                stringBuilder.AppendFormat(format, propertyValue);
                                break;

                            default:
                                // apply special formatting if necessary
                                if (!string.IsNullOrEmpty(formField.FormatString))
                                {
                                    stringBuilder.AppendFormat(formField.FormatString, propertyValue);
                                }
                                else
                                {
                                    stringBuilder.AppendFormat(format, propertyValue);
                                }

                                if (formField.FormFieldMapping.PropertyLength != null)
                                {
                                    var fieldValue = stringBuilder.ToString();
                                    stringBuilder.Clear();
                                    stringBuilder.Append(fieldValue.Take(formField.FormFieldMapping.PropertyLength.Value));
                                }

                                break;
                        }
                    }

                    // set the merge field in the word template to the field retrieved from the job objects
                    try
                    {
                        SetFieldValue(field, stringBuilder.ToString(), formField.FormFieldTypeId);
                    }
                    catch (NullReferenceException exc)
                    {
                        // Unable to set field
                        Logger.LogError("Set Field Value exception: {Field}, {FieldValue}, {FormFieldTypeId}, {Exception}", 
                            field, stringBuilder.ToString(), formField.FormFieldTypeId, @exc);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "An unexpected error occurred in SetFormFieldValues. Details: {Message}", ex.Message);
                    }
                }
            }
        }
        
        public virtual void Dispose()
        {
            Dispose(true);
        }
        
        protected void Dispose(bool disposing)
        {
            if (disposing)
            {
                _stream?.Dispose();
            }
        }
    }
}
