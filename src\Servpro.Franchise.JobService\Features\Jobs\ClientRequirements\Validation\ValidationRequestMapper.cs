﻿using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Xact.DTOs;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using System;
using System.Collections.Generic;
using System.Linq;
using State = Servpro.Franchise.JobService.Models.State;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Validation
{
    public static class ValidationRequestMapper
    {
        public static string Map(IEnumerable<FacilityTypeDto> facilityTypes, int? facilityType)
        {
            if (facilityTypes == null)
                return null;
            if (!facilityType.HasValue)
                return null;

            return facilityTypes
                .Where(x => x.Id == facilityType)
                .Select(x => x.Name)
                .FirstOrDefault();
        }

        public static string Map(IEnumerable<StructureTypeDto> structureTypes, Guid? structureType)
        {
            if (structureTypes == null)
                return null;
            if (!structureType.HasValue)
                return null;

            return structureTypes
                .Where(x => x.Id == structureType)
                .Select(x => x.Name)
                .FirstOrDefault();
        }

        public static GetJobRequirementsRequestDto.JobValidationJobArea Map(JobArea a)
            => a != null ? new GetJobRequirementsRequestDto.JobValidationJobArea()
            {
                Id = a.Id,
                JobId = a.JobId,
                JobAreaTypeId = a.JobAreaTypeId,
                ZoneId = a.ZoneId,
                IsDeleted = a.IsDeleted,
                RoomId = a.RoomId,
                Name = a.Name,
                Room = Map(a.Room),
                BeginJobVisitId = a.BeginJobVisitId,
                EndJobVisitId = a.EndJobVisitId,
                EquipmentPlacements = a.EquipmentPlacements?.Select(Map).ToList(),
                FloorTypeId = a.Room != null && a.Room.RoomFlooringTypesAffected.Any() ? a.Room.RoomFlooringTypesAffected.First().FlooringTypeId : Guid.Empty,
                HasFlooringType = a.Room != null && a.Room.RoomFlooringTypesAffected.Any() && a.Room.RoomFlooringTypesAffected.First().FlooringTypeId != Guid.Empty,
                IsUsedInValidation = true,
                JobAreaMaterials = a.JobAreaMaterials?.Select(Map).ToList()
            } : null;


        public static GetJobRequirementsRequestDto.JobValidationEquipmentPlacement Map(EquipmentPlacement equipmentPlacement)
        {
            if (equipmentPlacement == null)
                return null;

            return new GetJobRequirementsRequestDto.JobValidationEquipmentPlacement()
            {
                Id = equipmentPlacement.Id,
                EquipmentId = equipmentPlacement.EquipmentId,
                JobAreaId = equipmentPlacement.JobAreaId,
                BeginDate = equipmentPlacement.BeginDate,
                EndDate = equipmentPlacement.EndDate,
                IsUsedInValidation = equipmentPlacement.IsUsedInValidation,
                Equipment = Map(equipmentPlacement.Equipment)
            };
        }

        private static GetJobRequirementsRequestDto.JobValidationEquipment Map(Equipment equipment)
        {
            if (equipment == null)
                return null;
            return new GetJobRequirementsRequestDto.JobValidationEquipment()
            {
                Id = equipment.Id,
                AssetNumber = equipment.AssetNumber,
                SerialNumber = equipment.SerialNumber,
                FranchiseSetId = equipment.FranchiseSetId,
                Notes = equipment.Notes,
                IsDeleted = equipment.IsDeleted,
                EquipmentModelId = equipment.EquipmentModelId,
                EquipmentModel = Map(equipment.EquipmentModel)
            };
        }

        private static GetJobRequirementsRequestDto.JobValidationEquipmentModel Map(EquipmentModel equipmentModel)
        {
            if (equipmentModel == null)
                return null;
            return new GetJobRequirementsRequestDto.JobValidationEquipmentModel()
            {
                Id = equipmentModel.Id,
                Name = equipmentModel.Name,
                ManufacturerName = equipmentModel.ManufacturerName,
                IsSymbol = equipmentModel.IsSymbol,
                FranchiseSetId = equipmentModel.FranchiseSetId,
                IsValidModel = equipmentModel.IsValidModel,
                Description = equipmentModel.Description,
                ManufacturerModelNumber = equipmentModel.ManufacturerModelNumber,
                Notes = equipmentModel.Notes,
                EquipmentTypeId = equipmentModel.EquipmentTypeId,
                EquipmentType = Map(equipmentModel.EquipmentType),
                CubicFeetPerMinute = equipmentModel.CubicFeetPerMinute,
                PintsPerDay = equipmentModel.PintsPerDay,
                Amps = equipmentModel.Amps,
                IsPenetratingMeter = equipmentModel.IsPenetratingMeter,
                IsNotPenetratingMeter = equipmentModel.IsNotPenetratingMeter,
                IsCurrent = equipmentModel.IsCurrent,
                IsDeleted = equipmentModel.IsDeleted
            };
        }


        private static GetJobRequirementsRequestDto.JobValidationEquipmentType Map(EquipmentType equipmentType)
        {
            if (equipmentType == null)
                return null;
            return new GetJobRequirementsRequestDto.JobValidationEquipmentType()
            {
                Id = equipmentType.Id,
                BaseEquipmentTypeId = equipmentType.BaseEquipmentTypeId,
                Name = equipmentType.Name,
                IsDeleted = equipmentType.IsDeleted,
                FranchiseSetId = equipmentType.FranchiseSetId,
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationJobAreaMaterial Map(JobAreaMaterial jam)
        {
            if (jam == null)
                return null;

            return new GetJobRequirementsRequestDto.JobValidationJobAreaMaterial()
            {
                Id = jam.Id,
                MaterialType = jam.JobMaterial?.Name,
                ObjectId = jam.JobMaterial?.ObjectId ?? Guid.Empty,
                RemovedOnJobVisitId = jam.RemovedOnJobVisitId
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationJobVisit Map(JobVisit jv)
            => jv != null ? new GetJobRequirementsRequestDto.JobValidationJobVisit()
            {
                JobId = jv.JobId,
                Date = jv.Date,
                Id = jv.Id,
                Rooms = jv.Rooms?.Select(Map).ToList(),
                JobVisitTriStateAnswers = jv.JobVisitTriStateAnswers?.Select(Map).ToList(),
                BeginVisitJobAreas = jv.BeginVisitJobAreas?.Select(Map).ToList(),
                EndVisitJobAreas = jv.EndVisitJobAreas?.Select(Map).ToList()
            } : null;

        public static GetJobRequirementsRequestDto.JobValidationRequestJobContactMap Map(JobContactMap jcm)
            => new GetJobRequirementsRequestDto.JobValidationRequestJobContactMap()
            {
                JobContactTypeId = jcm.JobContactTypeId,
                ContactId = jcm.ContactId,
                Id = jcm.Id,
                JobId = jcm.JobId,
                Contact = Map(jcm.Contact)
            };

        private static GetJobRequirementsRequestDto.JobValidationRequestJobContact Map(Contact c)
            => new GetJobRequirementsRequestDto.JobValidationRequestJobContact()
            {
                Id = c.Id,
                Address = c.Address != null ? Map(c.Address) : new GetJobRequirementsRequestDto.JobValidationRequestAddress(),
                EmailAddress = c.EmailAddress,
                FirstName = c.FirstName,
                LastName = c.LastName,
                PhoneNumbers = c.PhoneNumbers?.Select(Map).ToList()
            };

        private static GetJobRequirementsRequestDto.JobValidationRequestPhone Map(Phone p)
            => p != null ? new GetJobRequirementsRequestDto.JobValidationRequestPhone()
            {
                PhoneType = (GetJobRequirementsRequestDto.JobValidationRequestPhoneType)p.PhoneType,
                Id = p.Id,
                PhoneExtension = p.PhoneExtension,
                PhoneNumber = p.PhoneNumber
            } : null;

        private static GetJobRequirementsRequestDto.JobValidationRequestAddress Map(Address a)
            => new GetJobRequirementsRequestDto.JobValidationRequestAddress()
            {
                Address1 = a.Address1,
                Address2 = a.Address2,
                State = Map(a.State),
                Latitude = a.Latitude,
                Longitude = a.Logitude,
                AddressType = a.AddressType,
                City = a.City,
                PostalCode = a.PostalCode
            };

        public static GetJobRequirementsRequestDto.JobValidationRequestRoom Map(Room r)
        {
            if (r == null)
                return null;

            return new GetJobRequirementsRequestDto.JobValidationRequestRoom()
            {
                FloorTypeId = r.FloorTypeId,
                Id = r.Id,
                PreExistingConditionsDiaryNoteId = r.PreExistingConditionsDiaryNoteId,
                RoomFlooringTypesAffected = r.RoomFlooringTypesAffected?.Select(Map).ToList()
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationRequestRoomFlooringTypeAffected Map(RoomFlooringTypeAffected flooringTypeAffected)
        {
            if (flooringTypeAffected == null)
                return null;

            return new GetJobRequirementsRequestDto.JobValidationRequestRoomFlooringTypeAffected()
            {
                Id = flooringTypeAffected.Id,
                AffectedPercentage = flooringTypeAffected.AffectedPercentage,
                AffectedSquareFeet = flooringTypeAffected.AffectedSquareFeet,
                FlooringTypeId = flooringTypeAffected.FlooringTypeId,
                IsDeleted = flooringTypeAffected.IsDeleted,
                IsPadRestorable = flooringTypeAffected.IsPadRestorable,
                IsSalvageable = flooringTypeAffected.IsSalvageable,
                OtherText = flooringTypeAffected.OtherText,
                RoomId = flooringTypeAffected.RoomId,
                SavedPercentage = flooringTypeAffected.SavedPercentage,
                SavedSquareFeet = flooringTypeAffected.SavedSquareFeet,
                TotalSquareFeet = flooringTypeAffected.TotalSquareFeet
            };
        }

        private static GetJobRequirementsRequestDto.JobValidationRequestState Map(State s)
            => s != null
                ? new GetJobRequirementsRequestDto.JobValidationRequestState()
                {
                    CountryName = s.CountryName,
                    CountryId = s.CountryId,
                    StateId = s.StateId,
                    StateName = s.StateName
                }
                : null;

        public static List<GetJobRequirementsRequestDto.JobValidationJobDate> MapJobDates(IEnumerable<JobDate> jobDates, int? corpJobNo)
        {
            return jobDates?
                .Select(d => new GetJobRequirementsRequestDto.JobValidationJobDate()
                {
                    Id = Guid.NewGuid(),
                    Value = d.Date,
                    InsertionDate = DateTime.UtcNow,
                    JobDateTypeId = d.JobDateTypeId
                })
                .ToList();
        }

        public static List<GetJobRequirementsRequestDto.JobValidationRequestJournalNote> MapJournalNotes(IEnumerable<JournalNote> journalNotes)
        {
            var mappedNotes = new List<GetJobRequirementsRequestDto.JobValidationRequestJournalNote>();

            foreach (var note in journalNotes.Where(h => !h.IsDeleted).ToList())
            {
                if (note.Rules?.Any() ?? false)
                {
                    mappedNotes.AddRange(note.Rules.Select(rule => MapJournalNote(note, rule)));
                }
                else if (note.RuleIds?.Any() ?? false)
                {
                    mappedNotes.AddRange(note.RuleIds.Select(ruleId => MapJournalNote(note, ruleId)));
                }
                else
                {
                    mappedNotes.Add(MapJournalNote(note, (int?)null));
                }
            }
            return mappedNotes;
        }

        private static GetJobRequirementsRequestDto.JobValidationRequestJournalNote MapJournalNote(JournalNote note, int? ruleId)
        {
            return new GetJobRequirementsRequestDto.JobValidationRequestJournalNote()
            {
                JobAreaId = note.JobAreaId,
                JobVisitId = note.JobVisitId,
                JobAreaMaterialId = note.JobAreaMaterialId,
                ZoneId = note.ZoneId,
                JobId = note.JobId,
                RuleId = ruleId,
                Subject = note.Subject,
                TypeId = note.TypeId,
                VisibilityId = note.VisibilityId,
                IsDeleted = false,
                Author = note.Author,
                ActionDate = note.ActionDate,
                CategoryId = note.CategoryId,
                CreatedDate = note.CreatedDate,
                Id = note.Id,
                TaskId = note.TaskId,
                Note = note.Note
            };
        }

        private static GetJobRequirementsRequestDto.JobValidationRequestJournalNote MapJournalNote(JournalNote note, JournalNote.BreRule rule)
        {
            return new GetJobRequirementsRequestDto.JobValidationRequestJournalNote()
            {
                Id = note.Id,
                JobId = note.JobId,
                Subject = note.Subject,
                TypeId = note.TypeId,
                VisibilityId = note.VisibilityId,
                IsDeleted = false,
                Author = note.Author,
                ActionDate = note.ActionDate,
                CategoryId = note.CategoryId,
                CreatedDate = note.CreatedDate,
                TaskId = note.TaskId,
                Note = note.Note,
                JobAreaId = rule.JobAreaId,
                JobVisitId = rule.JobVisitId,
                JobAreaMaterialId = rule.JobAreaMaterialId,
                ZoneId = rule.ZoneId,
                RuleId = rule.RuleId,
                MaterialType = rule.MaterialType
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationJobTriStateAnswer Map(JobTriStateAnswer jobTriStateAnswer)
        {
            if (jobTriStateAnswer == null)
                return null;

            return new GetJobRequirementsRequestDto.JobValidationJobTriStateAnswer()
            {
                Answer = jobTriStateAnswer.Answer,
                Id = jobTriStateAnswer.Id,
                JobId = jobTriStateAnswer.JobId,
                JobTriStateQuestionId = jobTriStateAnswer.JobTriStateQuestionId
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationJobVisitTriStateAnswer Map(JobVisitTriStateAnswer jobVisitTriStateAnswer)
        {
            if (jobVisitTriStateAnswer == null)
                return null;

            return new GetJobRequirementsRequestDto.JobValidationJobVisitTriStateAnswer()
            {
                JobTriStateQuestionId = jobVisitTriStateAnswer.JobTriStateQuestionId,
                JobVisitId = jobVisitTriStateAnswer.JobVisitId,
                Answer = jobVisitTriStateAnswer.Answer,
                Id = jobVisitTriStateAnswer.Id
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationZone Map(Zone zone, GetLookups.Dto lookups)
        {
            return new GetJobRequirementsRequestDto.JobValidationZone()
            {
                AchievedDehuCapacity = zone.AchievedDehuCapacity,
                AirMoverCalculationTypeId = zone.AirMoverCalculationTypeId,
                CanValidateDehuCapacity = zone.CanValidateDehuCapacity,
                Description = zone.Description,
                Id = zone.Id,
                JobId = zone.JobId,
                Name = zone.Name,
                RequiredDehuCapacity = zone.RequiredDehuCapacity,
                SketchMediaContentId = zone.SketchMediaContentId,
                WaterCategory = Map(zone.WaterCategoryId, lookups),
                WaterCategoryId = zone.WaterCategoryId,
                WaterClass = MapWaterClass(zone.WaterClassId, lookups),
                WaterClassId = zone.WaterClassId,
                WaterClassOverridden = zone.WaterClassOverridden,
                ZoneTypeId = zone.ZoneTypeId
            };
        }

        public static GetJobRequirementsRequestDto.JobValidationRequestWaterCategory Map(Guid waterCategoryId, GetLookups.Dto lookups)
        {
            var waterCat = lookups.WaterCategories.FirstOrDefault(c => c.Id == waterCategoryId);
            if (waterCat != null)
            {
                return new GetJobRequirementsRequestDto.JobValidationRequestWaterCategory()
                {
                    Id = waterCategoryId,
                    Ordinal = waterCat.Ordinal
                };
            }
            return new GetJobRequirementsRequestDto.JobValidationRequestWaterCategory();
        }

        public static GetJobRequirementsRequestDto.JobValidationRequestWaterClass MapWaterClass(Guid waterClassId,
            GetLookups.Dto lookups)
        {
            var waterClass = lookups.WaterClasses.FirstOrDefault(c => c.Id == waterClassId);
            if (waterClass != null)
            {
                return new GetJobRequirementsRequestDto.JobValidationRequestWaterClass()
                {
                    Id = waterClassId,
                    Severity = waterClass.Severity
                };
            }
            return new GetJobRequirementsRequestDto.JobValidationRequestWaterClass();
        }

        public static GetJobRequirementsRequestDto.XactimateGroupCode Map(GetEstimaticsResponseDto.XactimateGroupCode groupCode)
        {
            if (groupCode == null)
                return null;

            return new GetJobRequirementsRequestDto.XactimateGroupCode()
            {
                CategoryId = groupCode.Category,
                SelectorId = groupCode.Code,
                GroupCodeId = groupCode.GroupCodeId,
                GroupId = groupCode.GroupId
            };
        }

        public enum ValidationItemStatus
        {
            Active = 0,
            Complete = 1,
            Exception = 2
        }

        public enum ValidationItemValidationStatus
        {
            Active = 0,
            Complete = 1
        }

        public enum ValidationPhotosGroupType
        {
            None = 1,
            PreMitigation = 2,
            PostMitigation = 3,
            Visit = 4
        }

        public enum ValidationRuleType
        {
            PhotosCabinetsCounterTops = 1,
            PhotosDumpstersDebris = 4,
            PhotosPostDemolition = 7,
        }

        public enum MiscellaneousRuleIds
        {
            AdjusterEmail = 56,
            AdjusterName = 57,
            FacilityStructureType = 121,
            CustomerCalledTime = 59,
            CustomerCalledExceptionReason = 67,
            SiteInspectedTime = 58,
            DryingReport = 61,
            SiteInspectedTimeExceptionReason = 66,
            ProjectRevenue = 64,
            Invoice = 90,
            ClaimNumber = 123,
            FlooringSample = 69
        }
    }
}