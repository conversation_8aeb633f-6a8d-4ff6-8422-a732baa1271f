﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.AtmosphericReadingsDto;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class AtmosphericReadingsModelGeneratorHelper
    {
        private const decimal GppLimit = 65M;
        private const decimal RhLimit = 40M;
        private const decimal ZoneTempOptimalLowerLimit = 68M;
        private const decimal ZoneTempOptimalUpperLimit = 90M;
        private const decimal GDepLimit = 5M;

        public static void UpdatePreviousReadingsAndThresholdFlags(this AtmosphericReadingsDto model)
        {
            var visitDates = model.ZoneReadingSets.SelectMany(x => x.VisitReadings.Select(y => y.VisitTimestamp)).ToList();
            if (!visitDates.Any()) return;

            var day2 = visitDates.Min().Date.AddDays(1);

            model.ZoneReadingSets
                .Where(zrs => zrs.ZoneType == "Drying").ToList()
                .ForEach(zrs => zrs.VisitReadings
                    .ForEach(vrs =>
                    {
                        vrs.GppTooHigh = vrs.VisitTimestamp.Date == day2 && vrs.GPP >= GppLimit;
                        vrs.RhTooHigh = vrs.VisitTimestamp.Date == day2 && vrs.RH >= RhLimit;
                        vrs.TempOutsideOptimalRangeForEquip = vrs.Temp < ZoneTempOptimalLowerLimit ||
                                                                  vrs.Temp > ZoneTempOptimalUpperLimit;
                        vrs.DehuReadings.ForEach(dr =>
                        {
                            dr.GDepNegative = dr.GDep < 0;
                            dr.GDepTooLow = dr.GDep < GDepLimit;
                        });
                        var prevReadingSet = vrs.GetPreviousVisitReadingSet(zrs);
                        if (prevReadingSet != null)
                        {
                            vrs.PreviousTemp = prevReadingSet.Temp;
                            vrs.PreviousRH = prevReadingSet.RH;
                            vrs.PreviousGPP = prevReadingSet.GPP;
                            vrs.GppNotDecreasing = vrs.GPP >= vrs.PreviousGPP;
                            vrs.DehuReadings.ForEach(dr =>
                            {
                                var prevDehuReading = dr.GetPreviousDehuReading(prevReadingSet);
                                if (prevDehuReading != null)
                                {
                                    dr.PreviousTemp = prevDehuReading.Temp;
                                    dr.PreviousRH = prevDehuReading.RH;
                                    dr.PreviousGPP = prevDehuReading.GPP;
                                }
                            });
                        }
                    }));
        }

        public static AtmosphericReadingsDto.VisitReadingSet GetPreviousVisitReadingSet(this AtmosphericReadingsDto.VisitReadingSet vrs, AtmosphericReadingsDto.ZoneReadingSet zrs)
        {
            return zrs.VisitReadings
                .Where(vr => vr.VisitTimestamp < vrs.VisitTimestamp
                    && vr.GPP != null)
                .OrderBy(x => x.VisitTimestamp)
                .LastOrDefault();
        }

        public static AtmosphericReadingsDto.DehuReading GetPreviousDehuReading(this AtmosphericReadingsDto.DehuReading r, AtmosphericReadingsDto.VisitReadingSet previousVisitReadingSet)
        {
            // HACK: This should really be SingleOrDefault... but QA somehow caused a UHE by placing the 
            // same Asset in the same room 3 times in one second
            return previousVisitReadingSet.DehuReadings.FirstOrDefault(prev => prev.EquipmentModel == r.EquipmentModel && prev.AssetNo == r.AssetNo);
        }

        public static Decimal? CalculateGpp(decimal? temperature, decimal? relativeHumidity)
        {
            /*  This is a code snippet condensed from the Access version of the function:
                This code copied directly from the drying workbook formulas.
                It seems to be using some regression formula to emulate the steam tables.  (water vapor pressures at given temperatures)
                It also seems to be presuming a given ambient air pressure.  The industry standard seems to not factor in local air pressure.
            */

            if (temperature == null || relativeHumidity == null)
            {
                return null;
            }

            var t = (double)temperature.Value;
            var rh = (double)relativeHumidity.Value;
            var g = t + 459.67;
            var h = -10443.97 / g;
            const double i = -11.29465;
            var j = -0.027022355 * g;
            var k = 0.00001289036 * Math.Pow(g, 2);
            var l = -0.000000002478068 * Math.Pow(g, 3);
            var m = 6.5459673 * Math.Log(g);
            var n = Math.Exp(h + i + j + k + l + m); //  ‘Exp(x) function is the natural logarithm e raised to the power of x.
            var o = n * rh * 0.01;
            var p = 0.62198 * (o / (14.696 - o));
            var answer = Math.Round(7000 * p);
            return (decimal)answer;
        }

        public class TKFriendlyAtmosReadingsModel
        {
            public string ZoneName { get; set; }
            public List<ReadingDetail> TopRow { get; set; }
            public List<List<ReadingDetail>> LoloExtraControlGroups { get; set; }
            public List<List<ReadingDetail>> LoloDehuReadings { get; set; }
        }

        public class ReadingDetail
        {
            public DateTime VisitTimestamp { get; set; }
            public string Technician { get; set; }
            public bool IsMissingVisit { get; set; }
            public string ZoneType { get; set; }
            public string ZoneNameOrEquipModel { get; set; }
            public string ZoneDescOrEquipAssetNo { get; set; }
            public decimal? Temp { get; set; }
            public decimal? RH { get; set; }
            public decimal? GPP { get; set; }
            public decimal? GDep { get; set; }
            public int? Hours { get; set; }

            public Color GDepCellColor { get; set; }
            public Color GppCellColor { get; set; }
            public Color RhCellColor { get; set; }
            public Color TempCellColor { get; set; }
        }
        public static void PadWithMissingVisits(AtmosphericReadingsDto model)
        {
            var realVisits = model
                .ZoneReadingSets
                .SelectMany(x => x.VisitReadings.Select(y => new { y.VisitTimestamp, y.Technician }))
                .Distinct()
                .OrderBy(x => x.VisitTimestamp)
                .ToList();

            if (!realVisits.Any()) return;

            var firstVisitDay = realVisits.Select(x => x.VisitTimestamp.Date).OrderBy(x => x).First().Date;
            var lastVisitDay = realVisits.Select(x => x.VisitTimestamp.Date).OrderBy(x => x).Last().Date;

            var countOfDays = (int)(lastVisitDay - firstVisitDay).TotalDays + 1;
            var allDays = Enumerable.Range(0, countOfDays).ToList().Select(x => firstVisitDay.AddDays(x)).ToList();
            var missingDays = allDays.Except(realVisits.Select(v => v.VisitTimestamp.Date));

            var allVisits = realVisits
                .Union(missingDays
                    .Select(x => new { VisitTimestamp = x, Technician = String.Empty }))
                    .OrderBy(v => v.VisitTimestamp)
                    .ToList();

            model.ZoneReadingSets.ForEach(x =>
            {
                var visitsInCurrentSet = x.VisitReadings.Select(vr => new { vr.VisitTimestamp, vr.Technician }).ToList();
                var missingVisits = allVisits.Except(visitsInCurrentSet);
                var visitReadingSetsForMissingVisits =
                    missingVisits
                        .Select(mv => new AtmosphericReadingsDto.VisitReadingSet
                        {
                            VisitTimestamp = mv.VisitTimestamp,
                            Technician = mv.Technician,
                            IsMissingVisit = realVisits.All(rv => rv.VisitTimestamp != mv.VisitTimestamp),
                            DehuReadings = x.VisitReadings
                                    .SelectMany(vr => vr.DehuReadings
                                        .Select(dr => new
                                        {
                                            dr.EquipmentModel,
                                            dr.AssetNo
                                        })
                                        .Distinct()
                                        .Select(dr => new AtmosphericReadingsDto.DehuReading
                                        {
                                            EquipmentModel = dr.EquipmentModel,
                                            AssetNo = dr.AssetNo
                                        }))
                                        .ToList()
                        });

                x.VisitReadings.AddRange(visitReadingSetsForMissingVisits);
            });
        }

        public static List<TKFriendlyAtmosReadingsModel> Process(AtmosphericReadingsDto originalModel)
        {
            var zoneTypeSortOrder = new Dictionary<string, int> { { "Outside", 1 }, { "Unaffected", 2 }, { "HVAC", 3 }, { "Drying", 4 } };
            const int atmosReadingsPerPageWidth = 4;
            const int dehuReadingsPerPageWidth = 3;

            PadWithMissingVisits(originalModel);

            var dryingZones = originalModel
                .ZoneReadingSets
                .Where(x => x.ZoneType == "Drying")
                .OrderBy(x => x.ZoneName)
                .ToList();

            var ctrlZones = originalModel
                .ZoneReadingSets
                .Except(dryingZones)
                .ToList();

            var topCtrlZones = ctrlZones
                .OrderBy(x => zoneTypeSortOrder[x.ZoneType])
                .ThenBy(x => x.ZoneName)
                .Take(atmosReadingsPerPageWidth - 1)
                .ToList();

            var xtraCtrlZones = ctrlZones
                .Except(topCtrlZones)
                .OrderBy(z => z.ZoneType)
                .ThenBy(z => z.ZoneName);

            var newModel =
                dryingZones
                    .Select(x => new
                    {
                        DryingZone = x,
                        topRow = topCtrlZones.Concat(new List<AtmosphericReadingsDto.ZoneReadingSet> { x }).ToList(),
                        extraControlGroups = xtraCtrlZones
                                .Select((xcz, i) => new { Index = i, ZRC = xcz })
                                .GroupBy(y => y.Index / atmosReadingsPerPageWidth)
                                .Select(y => y.Select(v => v.ZRC).ToList())
                                .ToList(),
                    })
                    .Select(x => new TKFriendlyAtmosReadingsModel
                    {
                        ZoneName = x.DryingZone.ZoneName,
                        TopRow = x
                            .topRow
                            .SelectMany(zrs => zrs.VisitReadings
                                .Select(vr => new ReadingDetail
                                {
                                    ZoneType = zrs.ZoneType,
                                    ZoneNameOrEquipModel = zrs.ZoneName,
                                    ZoneDescOrEquipAssetNo = zrs.ZoneDesc,
                                    VisitTimestamp = vr.VisitTimestamp,
                                    Technician = vr.Technician,
                                    IsMissingVisit = vr.IsMissingVisit,
                                    Temp = vr.Temp,
                                    RH = vr.RH,
                                    GPP = vr.GPP,
                                    TempCellColor = vr.TempOutsideOptimalRangeForEquip ? Color.Orchid : Color.Empty,
                                    RhCellColor = vr.RhTooHigh ? Color.Aqua : Color.Empty,
                                    GppCellColor = vr.GppTooHigh ? Color.Aqua
                                        : vr.GppNotDecreasing ? Color.Gray
                                        : Color.Empty,
                                })).ToList(),
                        LoloExtraControlGroups = x.extraControlGroups
                            .Select(x1 => x1
                                .SelectMany(zrs => zrs.VisitReadings
                                    .Select(vr => new ReadingDetail
                                    {
                                        ZoneType = zrs.ZoneType,
                                        ZoneNameOrEquipModel = zrs.ZoneName,
                                        ZoneDescOrEquipAssetNo = zrs.ZoneDesc,
                                        VisitTimestamp = vr.VisitTimestamp,
                                        Technician = vr.Technician,
                                        IsMissingVisit = vr.IsMissingVisit,
                                        Temp = vr.Temp,
                                        RH = vr.RH,
                                        GPP = vr.GPP,
                                    }).ToList()
                                )
                                .OrderBy(s => s.ZoneType)
                                .ThenBy(s => s.ZoneNameOrEquipModel)
                                .ThenBy(s => s.ZoneDescOrEquipAssetNo)
                                .ThenBy(s => s.VisitTimestamp)
                                .ToList()
                            ).ToList()
                        ,
                        LoloDehuReadings = x.DryingZone.VisitReadings
                            .SelectMany(vr => vr.DehuReadings
                                .Select(dr => new ReadingDetail
                                {
                                    VisitTimestamp = vr.VisitTimestamp,
                                    Technician = vr.Technician,
                                    IsMissingVisit = vr.IsMissingVisit,
                                    ZoneType = String.Empty,
                                    ZoneNameOrEquipModel = dr.EquipmentModel,
                                    ZoneDescOrEquipAssetNo = dr.AssetNo,
                                    Temp = dr.Temp,
                                    RH = dr.RH,
                                    GPP = dr.GPP,
                                    GDep = dr.GDep,
                                    Hours = dr.Hours,
                                    GDepCellColor = dr.GDepNegative
                                        ? Color.Red
                                        : dr.GDepTooLow ? Color.Yellow
                                        : Color.Empty,
                                })
                            )
                            .GroupBy(vr => new { EquipModel = vr.ZoneNameOrEquipModel, EquipAssetNo = vr.ZoneDescOrEquipAssetNo })
                            .OrderBy(vr => vr.Key.EquipModel)
                            .ThenBy(vr => vr.Key.EquipAssetNo)
                            .Select((w, i) => new { Index = i, Dehu = w.Key, DehuReadings = w.OrderBy(w1 => w1.VisitTimestamp).Select(x1 => x1) })
                            .GroupBy(d => d.Index / dehuReadingsPerPageWidth)
                            .Select(d => d.SelectMany(e => e.DehuReadings).ToList())
                            .ToList()
                    })
                    .ToList()
                    ;

            return newModel.ToList();
        }
    }
}
