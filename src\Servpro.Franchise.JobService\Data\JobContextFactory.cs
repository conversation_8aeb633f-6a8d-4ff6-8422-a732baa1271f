﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;
using System;
using System.IO;

namespace Servpro.Franchise.JobService.Data
{
    public class JobDataDesignTimeContextFactory : IDesignTimeDbContextFactory<JobDataContext>
    {
        public JobDataContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<JobDataContext>();
            var mySqlDefaultVerison = "5.7.32";
            var configBuilder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .AddEnvironmentVariables();
            var config = configBuilder.Build();
            var dbVersion = config["AuroraDatabaseVersion"];
            var auroraDbVersion = string.IsNullOrEmpty(dbVersion) ? mySqlDefaultVerison : dbVersion;
            optionsBuilder.UseMySql("Server=localhost;Port=3307;Database=JobService;Uid=root;Pwd=Password1;SSLMode=None",
                ServerVersion.Parse(auroraDbVersion, ServerType.MySql),
                options =>
            {
                options.UseNewtonsoftJson(MySqlCommonJsonChangeTrackingOptions.FullHierarchyOptimizedFast);
            });
            return new JobDataContext(optionsBuilder.Options);
        }
    }
}
