﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class UpdateZone
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
            public string ZoneName { get; set; }
            public Guid WaterClassId { get; set; }
            public Guid WaterCategoryId { get; set; }
            public bool WaterClassOverridden { get; set; }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? WaterClassOverrideJournalNoteId { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.ZoneId).NotNull();
                RuleFor(x => x.ZoneName).NotNull();
                RuleFor(x => x.WaterClassId).NotEmpty();
                RuleFor(x => x.WaterCategoryId).NotEmpty();
                RuleFor(x => x.WaterClassOverridden).NotNull();
            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo, ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                var user = _userInfo.GetUserInfo();
                var zone = await _context.Zones
                    .Include(z => z.Job)
                    .Include(q => q.Tasks)
                        .ThenInclude(x => x.JournalNotes)
                    .FirstOrDefaultAsync(x => x.Job.FranchiseSetId == user.FranchiseSetId && x.Id == request.ZoneId, cancellationToken);

                if (zone is null)
                    throw new ResourceNotFoundException($"Zone not found (Id: {request.ZoneId}");

                if (zone.Job != null)
                    zone.Job.JobLocks = await _context.JobLock.Where(x => x.JobId == zone.Job.Id).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(zone.Job.CurrentJobLock, user.Id))
                    throw new JobConflictException(zone.Job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(zone.Job.CurrentJobLock));

                zone.ModifiedDate = DateTime.UtcNow;
                zone.ModifiedBy = user.Username;
                zone.Name = request.ZoneName;
                zone.WaterClassId = request.WaterClassId;
                zone.WaterCategoryId = request.WaterCategoryId;
                zone.AirMoverCalculationTypeId = ZoneTypes.AirMoverCalculationTypeId;
                zone.ZoneTypeId = ZoneTypes.Drying;
                zone.WaterClassOverridden = request.WaterClassOverridden;

                //Unconfirms the zone
                var zoneNotConfirrmedAlert = zone.Tasks
                     .First(t => t.TaskTypeId == TaskTypes.ZoneNotConfirmed);

                if (zoneNotConfirrmedAlert.TaskStatusId == TaskStatuses.Completed)
                {
                    zoneNotConfirrmedAlert.ModifiedDate = DateTime.UtcNow;
                    zoneNotConfirrmedAlert.ModifiedBy = user.Username;
                    zoneNotConfirrmedAlert.PercentComplete = 0;
                    zoneNotConfirrmedAlert.TaskStatusId = TaskStatuses.Active;
                }

                var waterClassOverridenAlert = zone.Tasks.FirstOrDefault(x => x.TaskTypeId == TaskTypes.WaterClassOverriden);
                if (zone.WaterClassOverridden && waterClassOverridenAlert is null)
                {
                    var overridenTask = GenerateWaterClassOverridenAlert(request, user);
                    zone.Tasks.Add(overridenTask);
                }
                var events = GenerateEvents();
                await _context.OutboxMessages.AddRangeAsync(events, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);
                return Map(zone);
            }

            private Dto Map(Zone zone)
            {
                Guid? journalId = zone.WaterClassOverridden
                    ? (Guid?)zone.Tasks
                        .FirstOrDefault(q => q.TaskTypeId == TaskTypes.WaterClassOverriden)
                        ?.JournalNotes?.FirstOrDefault()?.Id
                    : null;

                return new Dto
                {
                    Id = zone.Id,
                    WaterClassOverrideJournalNoteId = journalId
                };
            }

            private static Models.Drybook.Task GenerateWaterClassOverridenAlert(Command request, UserInfo user)
            {
                return new Models.Drybook.Task
                {
                    JobId = request.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = user.Username,
                    ModifiedBy = user.Username,
                    IsSystem = false,
                    IsTemplate = false,
                    TaskPriorityId = TaskPriorities.Medium,
                    TaskStatusId = TaskStatuses.Active,
                    TaskTypeId = TaskTypes.WaterClassOverriden,
                    Subject = $"{request.ZoneName} - {ZoneTypes.ZoneWaterClassOverriden}",
                    Body = ZoneTypes.ZoneWaterClassOverriden,
                    StartDate = DateTime.UtcNow,
                    PercentComplete = 0,
                    JournalNotes = new List<JournalNote>
                        {
                            new JournalNote {
                                JobId = request.JobId,
                                Author = user.Username,
                                Subject = $"{request.ZoneName} - {ZoneTypes.ZoneWaterClassOverriden}",
                                Note = string.Empty,
                                TypeId = JournalNoteTypes.ZoneWaterClassOverride,
                                CategoryId = JournalNoteCategories.Notes,
                                VisibilityId = JournalNoteVisibilities.Franchise,
                                ActionDate = DateTime.UtcNow,
                                IsDeleted = false,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                CreatedBy = user.Username,
                                ModifiedBy = user.Username
                            }
                        }
                };
            }

            private IEnumerable<OutboxMessage> GenerateEvents()
            {
                var zoneUpdatedEvents = GenerateZoneUpdatedEvents();
                var taskUpdatedEvents = GenerateTaskUpdatedEvents();
                var taskCreatedEvents = GenerateTaskCreatedEvents();
                var journalNoteCreatedEvents = GenerateJournalNoteCreatedEvents();

                var events = zoneUpdatedEvents
                    .Concat(taskUpdatedEvents)
                    .Concat(taskCreatedEvents)
                    .Concat(journalNoteCreatedEvents)
                    .ToList();

                return events;
            }

            private IEnumerable<OutboxMessage> GenerateZoneUpdatedEvents()
            {
                var zoneUpdatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Zone && x.State == EntityState.Modified)
                    .Select(x => x.Entity as Zone)
                    .Select(MapZoneUpdatedDto)
                    .Select(x => new ZoneUpdatedEvent(x, _sessionIdAccessor.GetCorrelationGuid()))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(ZoneUpdatedEvent), _sessionIdAccessor.GetCorrelationGuid(), _userInfo.GetUserInfo().Username));

                return zoneUpdatedEvents;
            }

            private IEnumerable<OutboxMessage> GenerateTaskUpdatedEvents()
            {
                var taskUpdatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Models.Drybook.Task && x.State == EntityState.Modified)
                    .Select(x => x.Entity as Models.Drybook.Task)
                    .Select(MapTaskUpdatedDto)
                    .Select(x => new TaskUpdatedEvent(x, _sessionIdAccessor.GetCorrelationGuid()))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(TaskUpdatedEvent), _sessionIdAccessor.GetCorrelationGuid(), _userInfo.GetUserInfo().Username));

                return taskUpdatedEvents;
            }

            private IEnumerable<OutboxMessage> GenerateTaskCreatedEvents()
            {
                var taskCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Models.Drybook.Task && x.State == EntityState.Added)
                    .Select(x => x.Entity as Models.Drybook.Task)
                    .Select(MapTaskCreatedDto)
                    .Select(x => new TaskCreatedEvent(x, _sessionIdAccessor.GetCorrelationGuid()))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(TaskCreatedEvent), _sessionIdAccessor.GetCorrelationGuid(), _userInfo.GetUserInfo().Username));

                return taskCreatedEvents;
            }

            private IEnumerable<OutboxMessage> GenerateJournalNoteCreatedEvents()
            {
                var journalNoteCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JournalNote && x.State == EntityState.Added)
                    .Select(x => x.Entity as JournalNote)
                    .Select(MapJournalNoteDto)
                    .Select(x => new JournalNoteCreatedEvent(x, _sessionIdAccessor.GetCorrelationGuid()))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), _sessionIdAccessor.GetCorrelationGuid(), _userInfo.GetUserInfo().Username));

                return journalNoteCreatedEvents;
            }

            private ZoneUpdatedEvent.ZoneUpdatedDto MapZoneUpdatedDto(Zone zone) =>
                new ZoneUpdatedEvent.ZoneUpdatedDto
                {
                    Id = zone.Id,
                    JobId = zone.JobId,
                    ZoneName = zone.Name,
                    WaterCategoryId = zone.WaterCategoryId,
                    WaterClassId = zone.WaterClassId,
                    WaterClassOverridden = zone.WaterClassOverridden
                };

            private TaskUpdatedEvent.TaskUpdatedEventDto MapTaskUpdatedDto(Models.Drybook.Task task) =>
                new TaskUpdatedEvent.TaskUpdatedEventDto
                {
                    Id = task.Id,
                    Body = task.Body,
                    CancellationDate = task.CancellationDate,
                    CompletionDate = task.CompletionDate,
                    DueDate = task.DueDate,
                    EstimateId = task.EstimateId,
                    FranchiseSetId = task.FranchiseSetId,
                    IsSystem = task.IsSystem,
                    IsTemplate = task.IsTemplate,
                    JobId = task.JobId,
                    ModifiedDate = task.ModifiedDate,
                    ModifiedBy = _userInfo.GetUserInfo().Id,
                    Subject = task.Subject,
                    ZoneReadingId = task.ZoneReadingId,
                    JobVisitId = task.JobVisitId,
                    ZoneId = task.ZoneId,
                    StartDate = task.StartDate,
                    WorkOrderId = task.WorkOrderId,
                    TaskTypeId = task.TaskTypeId,
                    TaskStatusId = task.TaskStatusId,
                    TaskPriorityId = task.TaskPriorityId,
                    ReminderDate = task.ReminderDate,
                    PercentComplete = task.PercentComplete,
                    JobTriStateQuestionId = task.JobTriStateQuestionId
                };

            private TaskCreatedEvent.TaskCreatedDto MapTaskCreatedDto(Models.Drybook.Task task) =>
                new TaskCreatedEvent.TaskCreatedDto
                {
                    Id = task.Id,
                    Body = task.Body,
                    CancellationDate = task.CancellationDate,
                    CompletionDate = task.CompletionDate,
                    DueDate = task.DueDate,
                    EstimateId = task.EstimateId,
                    FranchiseSetId = task.FranchiseSetId,
                    IsSystem = task.IsSystem,
                    IsTemplate = task.IsTemplate,
                    JobId = task.JobId,
                    Subject = task.Subject,
                    ZoneReadingId = task.ZoneReadingId,
                    JobVisitId = task.JobVisitId,
                    ZoneId = task.ZoneId,
                    StartDate = task.StartDate,
                    WorkOrderId = task.WorkOrderId,
                    TaskTypeId = task.TaskTypeId,
                    TaskStatusId = task.TaskStatusId,
                    TaskPriorityId = task.TaskPriorityId,
                    ReminderDate = task.ReminderDate,
                    PercentComplete = task.PercentComplete,
                    JobTriStateQuestionId = task.JobTriStateQuestionId,
                };

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteDto(JournalNote note) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = note.Id,
                    ActionDate = note.ActionDate,
                    Author = note.Author,
                    JobId = note.JobId.Value,
                    CategoryId = note.CategoryId,
                    CreatedById = _userInfo.GetUserInfo().Id,
                    CreatedBy = _userInfo.GetUserInfo().Username,
                    CreatedDate = note.CreatedDate,
                    Message = note.Note,
                    Subject = note.Subject,
                    TypeId = note.TypeId,
                    VisibilityId = note.VisibilityId
                };

            private TaskDeletedEvent.TaskDeletedDto MapTaskDeletedDto(Models.Drybook.Task task) =>
                new TaskDeletedEvent.TaskDeletedDto
                {
                    Id = task.Id,
                    JobId = task.JobId.Value,
                    DeletedBy = _userInfo.GetUserInfo().Username,
                    DeletedDate = DateTime.UtcNow
                };
        }
    }
}
