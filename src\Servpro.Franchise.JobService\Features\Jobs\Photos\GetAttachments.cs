﻿using Amazon.S3;
using Amazon.S3.Model;

using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.LookupService.Constants;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class GetAttachments
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Query(Guid id, Guid franchiseSetId)
            {
                JobId = id;
                FranchiseSetId = franchiseSetId;
            }
            public Guid JobId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public string Comments { get; set; }
            public string Name { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public string MediaPath { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobVisitId { get; set; }
            public string RoomName { get; set; }
            public string ZoneName { get; set; }
            public DateTime? VisitDate { get; set; }
            public string Description { get; set; }
            public bool IsForUpload { get; set; }
            public Guid FranchiseSetId { get; set; }
            public string ImageGroup { get; set; }
            public string ImageType { get; set; }
            public bool IsRequired { get; set; }
            public DateTime? ArtifactDate { get; set; }
            public bool IsXactUploaded { get; set; }
            public bool UploadedSuccessfully { get; set; }
            public Guid? JobAreaMaterialId { get; set; }

        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private readonly IClientRequirementsService _clientRequirementsService;
            private readonly ILogger<GetAttachments> _logger;
            private readonly ILookupServiceClient _lookupServiceClient;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(
                JobReadOnlyDataContext db, 
                IConfiguration config, 
                IAmazonS3 clientS3, 
                ILogger<GetAttachments> logger,
                ILookupServiceClient lookupServiceClient,
                IXactServiceClient xactServiceClient,
                IClientRequirementsService clientRequirementsService
                )
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
                _logger = logger;
                _lookupServiceClient = lookupServiceClient;
                _clientRequirementsService = clientRequirementsService;
            }

            public async Task<ICollection<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var jobId = request.JobId;
                var franchiseSetId = request.FranchiseSetId;
                using var scope = _logger.BeginScope("{jobId}{franchiseSetId}", jobId, franchiseSetId);
                var job = await GetJobAsync(jobId, franchiseSetId, cancellationToken);

                var requirements = await _clientRequirementsService.GetJobRequirementsAsync(job, cancellationToken);

                var mediaMetaData = await GetMediaAsync(jobId, franchiseSetId, Servpro.Franchise.JobService.Common.MediaTypes.Photo, cancellationToken);
                var sketchesData = await GetMediaAsync(jobId, franchiseSetId, Servpro.Franchise.JobService.Common.MediaTypes.Sketch, cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var result = mediaMetaData.Select(x => Map(x, lookups)).ToList();
                result.AddRange(sketchesData.Select(x => Map(x, lookups)));

                var artifacts = requirements.Result?.PhotoRequirements;
                if (artifacts != null)
                {
                    foreach (var item in artifacts)
                    {
                        if (result.Any(q => q != null && q.ArtifactTypeId == item.ArtifactTypeId)) continue;
                        var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == item.ArtifactTypeId);
                        if (artifactType != null && artifactType.Required)
                            result.Add(MapRequiredItem(item, request));
                    }
                }

                return result;
            }

            private async Task<List<MediaMetadata>> GetMediaAsync(Guid jobId, Guid franchiseSetId, Guid mediaTypeId, CancellationToken cancellationToken)
            {
                var media = await _db.MediaMetadata
                    .AsNoTracking()
                    .Include(x => x.JobArea)
                        .ThenInclude(x => x.Zone)
                    .Include(x => x.JobVisit)
                    .Where(x => x.JobId == jobId &&
                        x.FranchiseSetId == franchiseSetId &&
                        x.MediaTypeId == mediaTypeId &&
                        !x.IsDeleted)
                    .ToListAsync(cancellationToken);

                if (mediaTypeId == Servpro.Franchise.JobService.Common.MediaTypes.Photo && media.Any())
                    media = media.Where(x => x.ArtifactTypeId != Guid.Empty).ToList();

                return media;
            }

            private Dto MapRequiredItem(ValidationPhotoRequirements item, Query request)
            {
                return new Dto
                {
                    Id = Guid.Empty,
                    Name = string.Empty,
                    JobId = request.JobId,
                    ArtifactTypeId = new Guid(item.ArtifactTypeId.ToString()),
                    ImageType = string.IsNullOrEmpty(item.Name)? "" : item.Name,
                    ImageGroup = "Required: Images",
                    IsRequired = true,
                    FranchiseSetId = request.FranchiseSetId
                };
            }

            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs
                    .Include(j => j.JournalNotes)
                    .FirstOrDefaultAsync(j => j.Id == jobId && 
                        j.FranchiseSetId == franchiseSetId,
                        cancellationToken);

                if (job is null)
                {
                    var errorMessage = "Job was not found when attempting to get attachments.";
                    throw new ResourceNotFoundException(errorMessage);
                }

                job.JobTriStateAnswers = await _db.JobTriStateAnswers
                    .Where(jsa => jsa.JobId == jobId)
                    .ToListAsync(cancellationToken);

                job.MediaMetadata = await _db.MediaMetadata
                    .Include(mm => mm.JobInvoice)
                    .Where(mm => mm.JobId == jobId)
                    .ToListAsync(cancellationToken);

                job.JobContacts = await _db.JobContactMap
                    .Include(jc => jc.Contact)
                    .Where(jc => jc.JobId == jobId)
                    .ToListAsync(cancellationToken);

                job.JobAreas = await _db.JobAreas
                    .Include(x => x.Room)
                        .ThenInclude(x => x.RoomFlooringTypesAffected)
                    .Where(jc => jc.JobId == jobId)
                    .ToListAsync(cancellationToken);

                var jobAreasWithMaterialsAndVisits = await _db.JobAreas
                    .Include(x => x.JobAreaMaterials)
                    .Include(x => x.BeginJobVisit)
                    .Where(jc => jc.JobId == jobId)
                    .ToDictionaryAsync(x => x.Id, cancellationToken);

                foreach (var jobArea in job.JobAreas)
                {
                    jobArea.JobAreaMaterials = jobAreasWithMaterialsAndVisits[jobArea.Id].JobAreaMaterials;
                    jobArea.BeginJobVisit = jobAreasWithMaterialsAndVisits[jobArea.Id].BeginJobVisit;
                }

                return job;
            }

            private Dto Map(MediaMetadata media, GetLookups.Dto lookups)
            {
                var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == media.ArtifactTypeId);
                if (artifactType == null)
                    return null;
                return new Dto
                {
                    Id = media.Id,
                    JobId = media.JobId,
                    Comments = media.Comment,
                    Name = media.Name,
                    ArtifactTypeId = media.ArtifactTypeId,
                    MediaPath = GetPreSignedUrl(media),
                    JobAreaId = media.JobAreaId,
                    JobVisitId = media.JobVisitId,
                    RoomName = media.JobArea?.Name,
                    ZoneName = media.JobArea?.Zone?.Name,
                    VisitDate = media.JobVisit?.Date,
                    Description = media.Description,
                    IsForUpload = media.IsForUpload,
                    ImageType = artifactType.Name,
                    ImageGroup = artifactType.Required ? "Required: Images" : "Other Images",
                    IsRequired = artifactType.Required,
                    FranchiseSetId = media.FranchiseSetId,
                    ArtifactDate = media.ArtifactDate,
                    IsXactUploaded = media.IsUploadedToXact,
                    UploadedSuccessfully = media.UploadedSuccessfully
                        || (!string.IsNullOrEmpty(media.BucketName) && !string.IsNullOrEmpty(media.MediaPath)),
                    JobAreaMaterialId = media.JobAreaMaterialId
                };
            }

            
            private string GetPreSignedUrl(MediaMetadata metadata)
            {
                if (metadata.ArtifactTypeId == ArtifactTypes.DocuSketch360Photo)
                    return metadata.MediaPath;

                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = metadata.BucketName.IsNullOrWhiteSpace() ? _config[S3MediaBucketNameKey] : metadata.BucketName,
                    Key = metadata.MediaPath,
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };
                return _clientS3.GetPreSignedURL(preSignedUrlRequest);
            }
        }
    }
}