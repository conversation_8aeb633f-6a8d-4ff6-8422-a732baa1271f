﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobAreaMaterial
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult MaterialResult { get; set; }
            public ProcessEntityResult AreaResult { get; set; }
            public ProcessEntityResult JobVisitResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobAreaMaterialIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobAreaMaterial>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobAreaMaterial> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobAreaMaterial> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobAreaMaterial));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobAreaMaterialIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var areaMaterialTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedAreaMaterialIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(areaMaterialTargetIds, 
                    GetJobAreaMaterialIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobAreaMaterial, ResaleJobAreaMaterial>(
                    request.ResaleId,
                    areaMaterial =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.MaterialResult.FailedEntities.Contains(areaMaterial.JobMaterialId))
                            failedDependencies.Add((nameof(JobMaterial), areaMaterial.JobMaterialId));
                        if (request.AreaResult.FailedEntities.Contains(areaMaterial.JobAreaId))
                            failedDependencies.Add((nameof(JobArea), areaMaterial.JobAreaId));
                        if (areaMaterial.RemovedOnJobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(areaMaterial.RemovedOnJobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), areaMaterial.RemovedOnJobVisitId.Value));
                        if (areaMaterial.GoalMetOnJobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(areaMaterial.GoalMetOnJobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), areaMaterial.GoalMetOnJobVisitId.Value));
                        if (areaMaterial.BeginJobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(areaMaterial.BeginJobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), areaMaterial.BeginJobVisitId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedAreaMaterialIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobAreaMaterials.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobAreaMaterial>> GetSourceEntitiesAsync(List<Guid> jobAreaMaterialIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobAreaMaterials = await _context.JobAreaMaterials
                    .Where(j => jobAreaMaterialIds.Contains(j.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobAreaMaterials.Count);
                return jobAreaMaterials;
            }

            private async Task<List<Guid>> GetJobAreaMaterialIdsAsync(List<Guid?> areaMaterialTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobAreaMaterials
                    .Where(x => areaMaterialTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
