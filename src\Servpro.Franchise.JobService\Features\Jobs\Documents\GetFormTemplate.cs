﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GetFormTemplate
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId, Guid templateId, Guid franchiseSetId)
            {
                JobId = jobId;
                FormTemplateId = templateId;
                FranchiseSetId = franchiseSetId;
            }
            public Guid JobId { get; }
            public Guid FormTemplateId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Dto
        {
            public bool ShouldShowInFirstNotice { get; set; }
            public string Name { get; set; }
            public string Version { get; set; }
            public string Description { get; set; }
            public bool IsActive { get; set; }
            public bool IsApproved { get; set; }
            public string InsuranceClient { get; set; }
            public string FileType { get; set; }
            public byte[] Form { get; set; }
            public Guid FormTemplateId { get; set; }
            public Guid? JobFormId { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IFormsService _formService;
            private readonly ILogger<Handler> _logger;

            public Handler(JobReadOnlyDataContext db, IFranchiseServiceClient franchiseServiceClient, IFormsService formService, ILogger<Handler> logger)
            {
                _db = db;
                _franchiseServiceClient = franchiseServiceClient;
                _formService = formService;
                _logger = logger;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting Job with JobId: {JobId}", request.JobId);
                var job = await GetJobAsync(request.JobId, request.FranchiseSetId, cancellationToken);

                _logger.LogInformation("Getting formTemplate with FormTemplateId: {FormTemplateId}", request.FormTemplateId);
                var formTemplate = await GetFormTemplateAsync(request.FormTemplateId, cancellationToken);

                if (job == null || formTemplate == null)
                {
                    return null;
                }

                var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, cancellationToken);
                var franchise = franchiseSet.Franchises.FirstOrDefault(x => x.Id == job.FranchiseId);
                var stream = await GetFormDataAsync(job, franchise, franchiseSet, formTemplate, cancellationToken);
                byte[] bytesinMemory = stream.ToArray();

                return MapDtos(job, formTemplate, bytesinMemory);
            }

            private Dto MapDtos(Job job, FormTemplate formTemplate, byte[] stream)
            {
                return new Dto
                {
                    FormTemplateId = formTemplate.Id,
                    Description = formTemplate.Description,
                    FileType = formTemplate.FileType,
                    InsuranceClient = formTemplate.InsuranceClient,
                    IsActive = formTemplate.IsActive,
                    IsApproved = formTemplate.Approved,
                    JobFormId = job.Id,
                    Name = formTemplate.Name,
                    ShouldShowInFirstNotice = formTemplate.IsAvailableInFirstNotice.Value,
                    Version = formTemplate.FormVersion,
                    Form = stream,
                };
            }

            private async Task<MemoryStream> GetFormDataAsync(Job job, FranchiseDto franchise, GetFranchiseSetDto franchiseSet, FormTemplate formTemplate, CancellationToken cancellationToken)
            {
                var mergedFormRequest = new MergeFormsRequest(job, franchise, franchiseSet);
                mergedFormRequest.FormTemplates.Add(formTemplate);
                var formData = await _formService.GetMergedFormsAsync(mergedFormRequest, cancellationToken);

                return formData;
            }

            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs
                    .FirstOrDefaultAsync(j => j.Id == jobId && j.FranchiseSetId == franchiseSetId, cancellationToken);
                return job;
            }

            private async Task<FranchiseDto> GetFranchiseAsync(Guid franchiseId)
            {
                return await _franchiseServiceClient.GetFranchiseAsync(franchiseId);
            }

            private async Task<FormTemplate> GetFormTemplateAsync(Guid formTemplateId, CancellationToken cancellationToken)
            {
                var formTemplates = await _db.FormTemplates.FirstOrDefaultAsync(x => x.Id == formTemplateId, cancellationToken);
                return formTemplates;
            }

            private static readonly Dictionary<string, string> MimeTypesDictionary =
                new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    {"jpeg", "image/jpeg"},
                    {"jpe", "image/jpeg"},
                    {"jpg", "image/jpg"},
                    {"png", "image/png"},
                    {"gif", "image/gif"},
                    {"pdf", "application/pdf"},
                    {"ppt", "application/vnd.ms-powerpoint"},
                    {"pptm", "application/vnd.ms-powerpoint.presentation.macroenabled.12"},
                    {"pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
                    {"xls", "application/vnd.ms-excel"},
                    {"xlsb", "application/vnd.ms-excel.sheet.binary.macroenabled.12"},
                    {"xlsm", "application/vnd.ms-excel.sheet.macroenabled.12"},
                    {"xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
                    {"doc", "application/msword"},
                    {"docm", "application/vnd.ms-word.document.macroenabled.12"},
                    {"docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
                };
        }
    }
}
