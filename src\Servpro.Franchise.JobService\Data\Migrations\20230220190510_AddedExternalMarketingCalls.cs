﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class AddedExternalMarketingCalls : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("bc0e37b9-9f04-4af3-b2b3-d389d5d4784f"));

            migrationBuilder.AddColumn<Guid>(
                name: "CallDisposition",
                table: "WipRecord",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci");

            migrationBuilder.AddColumn<string>(
                name: "CallRecordingId",
                table: "WipRecord",
                type: "varchar(240)",
                maxLength: 240,
                nullable: true,
                collation: "utf8mb4_general_ci");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AddColumn<Guid>(
                name: "CallDisposition",
                table: "Job",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci");

            migrationBuilder.AddColumn<string>(
                name: "CallRecordingId",
                table: "Job",
                type: "varchar(240)",
                maxLength: 240,
                nullable: true,
                collation: "utf8mb4_general_ci");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.CreateTable(
                name: "ExternalMarketingCalls",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CallRecordingId = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    FranchiseId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CallSourceId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CallReceivedDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CallerPhoneNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CallStartTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CallCompletionTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CallDuration = table.Column<decimal>(type: "decimal(19,4)", precision: 19, scale: 4, nullable: false),
                    RecordingLink = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Disposition = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    DispositionSelectionDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DispositionSelectionUserName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    AssociatedJobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    AssociatedJobLastUpdatedDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    SiteAppointmentStartDateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    RevenueInvoiced = table.Column<decimal>(type: "decimal(19,4)", precision: 19, scale: 4, nullable: true),
                    LastExported = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExternalMarketingCalls", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("0ad4440b-7b4c-41f8-a719-d8b40e6aa688"), null, new DateTime(2023, 2, 20, 19, 5, 9, 181, DateTimeKind.Utc).AddTicks(1764), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });

            migrationBuilder.CreateIndex(
                name: "IX_ExternalMarketingCalls_FranchiseId_CallReceivedDateTime",
                table: "ExternalMarketingCalls",
                columns: new[] { "FranchiseId", "CallReceivedDateTime" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ExternalMarketingCalls");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("0ad4440b-7b4c-41f8-a719-d8b40e6aa688"));

            migrationBuilder.DropColumn(
                name: "CallDisposition",
                table: "WipRecord");

            migrationBuilder.DropColumn(
                name: "CallRecordingId",
                table: "WipRecord");

            migrationBuilder.DropColumn(
                name: "CallDisposition",
                table: "Job");

            migrationBuilder.DropColumn(
                name: "CallRecordingId",
                table: "Job");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("bc0e37b9-9f04-4af3-b2b3-d389d5d4784f"), null, new DateTime(2023, 2, 14, 17, 11, 20, 133, DateTimeKind.Utc).AddTicks(3224), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }
    }
}
