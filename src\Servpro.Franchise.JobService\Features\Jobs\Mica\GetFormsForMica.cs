﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.Franchise.JobService.Infrastructure.MicaService.GetSentEntities;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetFormsForMica
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
        }
        public class Dto
        {
            public Guid Id { get; set; }
            public DateTime? CreatedDate { get; set; }
            public Guid? FormType { get; set; }
            public string Name { get; set; }
            public bool HasBeenSentToMica { get; set; }
            public string FormTemplateName { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IMicaServiceClient _micaServiceClient;
            private readonly ILogger<Handler> _logger;
            private List<FormTemplate> _templates { get; set; }
            private List<string> _allowedExtensions = new List<string>{ "pdf" };
            public Handler(
                  JobReadOnlyDataContext context,
                  IMicaServiceClient micaServiceClient,
                  IConfiguration config,
                  ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
                _micaServiceClient = micaServiceClient;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Forms to Submit to Mica");

                var forms = await GetFormsAsync(request.JobId, cancellationToken);
                forms = forms.Where(x => _allowedExtensions.Contains(x.GetFileExtension())).ToList();

                var formTemplateIds = forms.Select(x => x.FormTemplateId).Distinct();

                _templates = await _context.FormTemplates
                                    .Where(k => formTemplateIds.Contains(k.Id))
                                    .ToListAsync(cancellationToken);

                var sentEntities = await _micaServiceClient.GetSentEntitiesAsync(
                    new GetSentEntities.Command(request.JobId, GetSentEntities.EntityType.Form), cancellationToken);

                return forms.Select(x => Map(x, sentEntities)).ToList();
            }

            private async Task<List<MediaMetadata>> GetFormsAsync(Guid jobId, CancellationToken cancellationToken)
            => await _context.MediaMetadata
                .Where(x => x.JobId == jobId && 
                    x.FormTemplateId.HasValue &&
                    x.UploadedSuccessfully &&
                    !x.IsDeleted)
                .ToListAsync(cancellationToken);

            private Dto Map(Models.MediaMetadata form, GetSentEntitiesDto entities)
            {               
                return new Dto
                {
                    Id = form.Id,
                    CreatedDate = form.CreatedDate,
                    Name = form.Name,
                    FormType = form.FormTemplateId,
                    HasBeenSentToMica = entities.SentEntities?.Any(x => x.Id == form.Id) ?? false,
                    FormTemplateName = _templates?.Where(k => k.Id == form.FormTemplateId).FirstOrDefault()?.Name
                };
            }
        }
    }
}
