﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using Guid = System.Guid;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents.FormTemplates
{
    public class FormTemplatesUpdated : IRequest
    {
        public class Event : FormTemplatesUpdatedEvent, IRequest
        {
            public Event(FormTemplatesUpdatedEventDto dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IConfiguration _configuration;
            private readonly IS3ClaimCheckValet _valet;

            public Handler(JobDataContext dbContext, ILogger<Handler> logger, IConfiguration configuration,
                IS3ClaimCheckValet valet)
            {
                _db = dbContext;
                _logger = logger;
                _configuration = configuration;
                _valet = valet;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing " + nameof(FormTemplatesUpdatedEvent) + " message with correlationsId: " +
                   request.CorrelationId);
                //bucketName is a configuration value
                var bucketName = _configuration["AWS:S3FormTemplatesBucketName"];
                if (string.IsNullOrWhiteSpace(bucketName))
                {
                    _logger.LogWarning(
                        "Unable to import form templates. The AWS:S3FormTemplatesBucketName is not set.");
                    return Unit.Value;
                }

                //Check that FormTemplate is not empty
                //if it's empty, we will call one method, and if it's not, we will call another one
                if (request?.FormTemplatesUpdatedEventDto?.FormTemplates.Count == 0)
                {
                    //empty
                    await ImportFormTemplatesFromS3(request, bucketName, cancellationToken);
                }
                else
                {
                    //not empty
                    await ImportFormTemplateFromEvent(request, cancellationToken);
                }
                return Unit.Value;
            }

            //Called when FormTemplate is not empty
            private async Task ImportFormTemplateFromEvent(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing FormTemplates included in " + nameof(FormTemplatesUpdatedEvent));

                var forms = (await _db.FormTemplates.ToListAsync(cancellationToken)).ToHashSet();

                var addedCount = 0;
                var updatedCount = 0;
                foreach (var form in request?.FormTemplatesUpdatedEventDto?.FormTemplates)
                {
                    if (form == null)
                        continue;

                    var mappedForm = MapForm(form);
                    var currentForm = forms.FirstOrDefault(x => x.Id == form.FormTemplateId);
                    if (currentForm == null)
                    {
                        _db.Add(mappedForm);
                        addedCount++;
                    }
                    else
                    {
                        var hasChanged = !CompareForms(currentForm, mappedForm);
                        if (hasChanged)
                        {
                            var updatedForm = UpdateForm(currentForm, mappedForm);
                            _db.Update(updatedForm);
                            updatedCount++;
                        }
                    }
                }

                await _db.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation(
                 "Imported {request.FormTemplates.Count} - Added: {AddedCount}, Updated: {UpdatedCount}",
                 request?.FormTemplatesUpdatedEventDto?.StorageKey, addedCount, updatedCount);
            }

            //Called when FormTemplate is empty
            private async Task ImportFormTemplatesFromS3(Event request, string bucketName, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing FormTemplates stored in S3");
                var storageKey = request?.FormTemplatesUpdatedEventDto?.StorageKey;
                if (string.IsNullOrWhiteSpace(storageKey))
                {
                    _logger.LogInformation(
                        $"{nameof(FormTemplatesUpdatedEvent)} dto was null or its FormTemplates property was null");
                    return;
                }

                _logger.LogInformation(nameof(FormTemplatesUpdatedEvent) + " received {request.CorrelationId}",
                    request.CorrelationId);

                var formTemplates = await _valet.PresentClaimCheck<List<FormTemplateDto>>(bucketName, storageKey);
                if (formTemplates == null)
                {
                    _logger.LogInformation(
                        "No form templates were found in the S3 bucket {bucketName} with key {storageKey}", bucketName,
                        storageKey);
                    return;
                }
                
                formTemplates = formTemplates.Where(x => x.FormTemplateId != Guid.Empty).ToList();
                
                var forms = (await _db.FormTemplates.ToListAsync(cancellationToken)).ToHashSet();
                var inactivatedCount = 0;
                var addedForms = new List<FormTemplate>();
                var updatedForms = new List<FormTemplate>();

                foreach (var form in formTemplates)
                {
                    if (form == null)
                        continue;

                    var mappedForm = MapForm(form);
                    var currentForm = forms.FirstOrDefault(x => x.Id == form.FormTemplateId);
                    if (currentForm == null)
                    {
                        addedForms.Add(mappedForm);
                    }
                    else
                    {
                        var hasChanged = !CompareForms(currentForm, mappedForm);
                        if (hasChanged)
                        {
                            var updatedForm = UpdateForm(currentForm, mappedForm);
                            updatedForms.Add(updatedForm);
                        }
                    }
                }


                var sharePointHash = formTemplates
                    .Select(x => forms.FirstOrDefault(f => f.Id == x.FormTemplateId))
                    .ToHashSet();
                forms.ExceptWith(sharePointHash);
                forms.Where(x => !x.IsActive)
                    .ToList().ForEach(x =>
                {
                    x.IsActive = false;
                    inactivatedCount++;
                });

                _db.UpdateRange(forms);
                _db.AddRange(addedForms);
                _db.UpdateRange(updatedForms);

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation(
               "Imported {TotalCount} - Added: {AddedCount}, Updated: {UpdatedCount}, Inactivated: {InactivatedCount}",
               formTemplates.Count, addedForms.Count, updatedForms.Count, inactivatedCount);
            }

            private static bool CompareForms(FormTemplate currentForm, FormTemplate form)
            {
                return
                    currentForm.Id == form.Id &&
                    currentForm.Name == form.Name &&
                    currentForm.Description == form.Description &&
                    currentForm.LinkedPage == form.LinkedPage &&
                    currentForm.RelatedForm == form.RelatedForm &&
                    currentForm.RelatedForm2 == form.RelatedForm2 &&
                    currentForm.RelatedForm3 == form.RelatedForm3 &&
                    currentForm.IsRequiredForAllLossTypes == form.IsRequiredForAllLossTypes &&
                    currentForm.WaterFormRequired == form.WaterFormRequired &&
                    currentForm.MoldFormRequired == form.MoldFormRequired &&
                    currentForm.FireFormRequired == form.FireFormRequired &&
                    currentForm.IsRequiredForResidentialJob == form.IsRequiredForResidentialJob &&
                    currentForm.IsRequiredForCommercialJob == form.IsRequiredForCommercialJob &&
                    currentForm.WaterForm == form.WaterForm &&
                    currentForm.MoldForm == form.MoldForm &&
                    currentForm.FireForm == form.FireForm &&
                    currentForm.InsuranceClient == form.InsuranceClient &&
                    currentForm.CommercialClient == form.CommercialClient &&
                    currentForm.IsAvailableInFirstNotice == form.IsAvailableInFirstNotice &&
                    currentForm.State == form.State &&
                    currentForm.Country == form.Country &&
                    currentForm.FormalLanguage == form.FormalLanguage &&
                    currentForm.Approved == form.Approved &&
                    currentForm.FileType == form.FileType &&
                    currentForm.MediaPath == form.MediaPath &&
                    currentForm.FormVersion == form.FormVersion &&
                    currentForm.SyncDate.AreEqual(form.SyncDate) &&
                    currentForm.IsAuthorizationForm == form.IsAuthorizationForm &&
                    currentForm.IsActive == form.IsActive;
            }

            private static FormTemplate UpdateForm(FormTemplate currentForm, FormTemplate form)
            {
                currentForm.Id = form.Id;
                currentForm.Name = form.Name;
                currentForm.Description = form.Description;
                currentForm.LinkedPage = form.LinkedPage;
                currentForm.RelatedForm = form.RelatedForm;
                currentForm.RelatedForm2 = form.RelatedForm2;
                currentForm.RelatedForm3 = form.RelatedForm3;
                currentForm.IsRequiredForAllLossTypes = form.IsRequiredForAllLossTypes;
                currentForm.WaterFormRequired = form.WaterFormRequired;
                currentForm.MoldFormRequired = form.MoldFormRequired;
                currentForm.FireFormRequired = form.FireFormRequired;
                currentForm.IsRequiredForResidentialJob = form.IsRequiredForResidentialJob;
                currentForm.IsRequiredForCommercialJob = form.IsRequiredForCommercialJob;
                currentForm.WaterForm = form.WaterForm;
                currentForm.MoldForm = form.MoldForm;
                currentForm.FireForm = form.FireForm;
                currentForm.InsuranceClient = form.InsuranceClient;
                currentForm.CommercialClient = form.CommercialClient;
                currentForm.IsAvailableInFirstNotice = form.IsAvailableInFirstNotice;
                currentForm.State = form.State;
                currentForm.Country = form.Country;
                currentForm.FormalLanguage = form.FormalLanguage;
                currentForm.IsActive = form.IsActive;
                currentForm.Approved = form.Approved;
                currentForm.FileType = form.FileType;
                currentForm.MediaPath = form.MediaPath;
                currentForm.FormVersion = form.FormVersion;
                currentForm.IsAuthorizationForm = form.IsAuthorizationForm;
                return currentForm;
            }

            private static FormTemplate MapForm(FormTemplateDto formDto)
            {
                return new FormTemplate()
                {
                    Id = formDto.FormTemplateId,
                    Name = formDto.Name,
                    Description = formDto.Description,
                    LinkedPage = formDto.LinkedPage,
                    RelatedForm = formDto.RelatedForm,
                    RelatedForm2 = formDto.RelatedForm2,
                    RelatedForm3 = formDto.RelatedForm3,
                    IsRequiredForAllLossTypes = formDto.IsRequiredForAllLossTypes,
                    WaterFormRequired = formDto.WaterFormRequired,
                    MoldFormRequired = formDto.MoldFormRequired,
                    FireFormRequired = formDto.FireFormRequired,
                    IsRequiredForResidentialJob = formDto.IsRequiredForResidentialJob,
                    IsRequiredForCommercialJob = formDto.IsRequiredForCommercialJob,
                    WaterForm = formDto.WaterForm,
                    MoldForm = formDto.MoldForm,
                    FireForm = formDto.FireForm,
                    InsuranceClient = formDto.InsuranceClient,
                    CommercialClient = formDto.CommercialClient,
                    IsAvailableInFirstNotice = formDto.IsAvailableInFirstNotice,
                    State = formDto.State,
                    Country = formDto.Country,
                    FormalLanguage = formDto.FormalLanguage,
                    IsActive = formDto.Activated,
                    FormVersion = formDto.FormVersion,
                    SyncDate = formDto.SyncDate,
                    MediaPath = formDto.MediaPath,
                    Approved = formDto.Approved,
                    FileType = formDto.FileType,
                    IsAuthorizationForm = formDto.IsAuthorizationForm,
                    ModifiedBy = "Form Template Import Process",
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = "Form Template Import Process",
                    CreatedDate = DateTime.UtcNow
                };
            }
        }
    }
}