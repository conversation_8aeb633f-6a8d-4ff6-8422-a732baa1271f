@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.DailyEquipmentCountDto
@using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers

@*--------- Create the report model using the Helper class ---------*@
@{ var processedModel = EquipmentUsageHelper.ProcessModel(Model);
    var insertEquipmentReport = processedModel.EquipmentPlacementCountSummary.TotalsByDay.Count > 0 || processedModel.EquipmentPlacementSummary.EquipmentPlacementDetails.Count > 0;
    var insertEquipmentCountReport = processedModel.EquipmentPlacementCountSummary.TotalsByDay.Count > 0;
    var insertEquipmentSummaryReport = processedModel.EquipmentPlacementSummary.EquipmentPlacementDetails.Count > 0;
    var equipmentTypes = insertEquipmentCountReport ? processedModel.EquipmentPlacementCountSummary.TotalsByEquipmentType.Select(equipment => equipment.Key).ToList() : new List<string>();
    var dailyCountTotal = 0;}
@if (insertEquipmentReport || insertEquipmentCountReport || insertEquipmentSummaryReport)
{
    <div class="floatLeft">
        @if (insertEquipmentReport)
        {
            <p class="title">{equipment}Equipment{/equipment}</p>
        }
        @if (insertEquipmentCountReport)
        {
            <div class="subtitle">{placementCountByDateType}Placement Count by Day and Type{/placementCountByDateType}</div>
            @foreach (var (dateRangeEquipmentSummary, index) in processedModel.EquipmentPlacementCountSummary.TotalsByDay.Select((value, i) => (value, i)))
            {
                dailyCountTotal += dateRangeEquipmentSummary.Sum(detail => detail.Value);
                var days = dateRangeEquipmentSummary.Select(day => day.Key).ToList();
                <table class="equipmentCountTable" summary="Equipment count table">
                    <thead>
                        <tr>
                            <th scope="col">Day</th>
                            @foreach (var day in days)
                            {
                                <th scope="col">@day</th>
                            }
                            @if (index == processedModel.EquipmentPlacementCountSummary.TotalsByDay.Count - 1)
                            {
                                <th scope="col" class="totalEquipmentCount">Total</th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var equipmentType in equipmentTypes)
                        {
                            <tr>
                                <td class="equipmentType">@equipmentType</td>
                                @foreach (var day in days)
                                {
                                    <td>@processedModel.EquipmentPlacementCountSummary.EquipmentCountByDay.GetValueOrDefault((day, equipmentType), 0)</td>
                                }
                                @if (index == processedModel.EquipmentPlacementCountSummary.TotalsByDay.Count - 1)
                                {
                                    <td class="totalEquipmentCount">@processedModel.EquipmentPlacementCountSummary.TotalsByEquipmentType[equipmentType]</td>
                                }
                            </tr>
                        }
                        <tr class="totalDayCount">
                            <td>Total</td>
                            @foreach (var day in days)
                            {
                                <td>@dateRangeEquipmentSummary[day]</td>
                            }
                            @if (index == processedModel.EquipmentPlacementCountSummary.TotalsByDay.Count - 1)
                            {
                                <td class="totalEquipmentCount">@dailyCountTotal</td>
                            }
                        </tr>
                    </tbody>
                </table>
            }
        }
        @if (insertEquipmentSummaryReport)
        {
            @* TODO : Need a solution for bookmarks, this causes text to wrap and be hidden
                <div class="subtitle">{usageSummary}Usage Summary{/usageSummary}</div>*@
            <div class="subtitle">Usage Summary</div>
            <table class="equipmentPlacementTable" summary="eqipment place">
                <thead>
                    <tr>
                        <th scope="col">Equipment Type</th>
                        <th scope="col">Room</th>
                        <th scope="col">Equipment Model</th>
                        <th scope="col">Asset Number</th>
                        <th scope="col">Placed</th>
                        <th scope="col">Removed</th>
                        <th scope="col">Total Days*</th>
                        <th scope="col">Total Hours*</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var (equipmentType, typeIndex) in processedModel.EquipmentPlacementSummary.EquipmentTypes.Select((value, i) => (value, i)))
                    {
                        @foreach (var (room, roomIndex) in processedModel.EquipmentPlacementSummary.Rooms[equipmentType].Select((value, i) => (value, i)))
                        {
                            @foreach (var (detail, detailIndex) in processedModel.EquipmentPlacementSummary.EquipmentPlacementDetails[(equipmentType, room)].Select((value, i) => (value, i)))
                            {
                                <tr>
                                    @if (roomIndex == 0 && detailIndex == 0)
                                    {
                                        <td class="equipmentType cellAlignTop wrapTextEquipmentType" rowspan="@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, "rowspan", "rowspan")]">
                                            @equipmentType
                                        </td>
                                    }
                                    @if (detailIndex == 0)
                                    {
                                        <td class="room cellAlignTop wrapTextRoom" rowspan="@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, room, "rowspan")]">
                                            @room
                                        </td>
                                    }
                                    <td class="wrapTextEquipmentModel">@detail.EquipmentModel</td>
                                    <td>@detail.AssetNumber</td>
                                    <td>
                                        @if (detail.Placed != null)
                                        {
                                            @detail.Placed.Value.ToString("MM/dd/yy h:mm tt")
                                        }
                                    </td>
                                    <td>
                                        @if (detail.Removed.HasValue && detail.Removed.Value != DateTime.MinValue)
                                        {
                                            @detail.Removed.Value.ToString("MM/dd/yy h:mm tt");
                                        }
                                    </td>
                                    <td>@detail.Days</td>
                                    <td>@detail.Hours</td>
                                </tr>
                            }
                            <tr class="roomTotal">
                                <td></td>
                                <td>@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, room, "assets")]</td>
                                <td></td>
                                <td></td>
                                <td>@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, room, "days")]</td>
                                <td>@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, room, "hours")]</td>
                            </tr>
                        }
                        <tr class="equipmentTypeTotal">
                            <td colspan="5">Total</td>
                            <td>@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, "total", "days")]</td>
                            <td>@processedModel.EquipmentPlacementSummary.Totals[(equipmentType, "total", "hours")]</td>
                        </tr>
                    }
                    <tr class="equipmentTotal">
                        <td colspan="6">Total</td>
                        <td>@processedModel.EquipmentPlacementSummary.Totals[("total", "total", "days")]</td>
                        <td>@processedModel.EquipmentPlacementSummary.Totals[("total", "total", "hours")]</td>
                    </tr>
                </tbody>
            </table>
            <div style='page-break-before: always;'></div>
        }

        @if (insertEquipmentReport)
        {
            <div class="subtitle">{usageChart}Usage Chart{/usageChart}</div>
            @await Html.PartialAsync("~/Features/Jobs/Drybook/Report/ReportViews/_zoneEquipmentUsageGraph.cshtml", Model.EquipmentPlacementCountByDay)
        }
    </div>
    <div style='page-break-before: always;'></div>
}