﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements
{
    public class FinalUploadDueDateChanged
    {
        public class Event : FinalUploadDueDateChangedEvent, IRequest
        {
            public Event(FinalDueDateDto finalDueDate, Guid correlationId)
                : base(finalDueDate, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<FinalUploadDueDateChanged> _logger;

            public Handler(JobDataContext jobDataContext, ILogger<FinalUploadDueDateChanged> logger)
            {
                _db = jobDataContext;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with: {@request}", request);

                if (request.FinalDueDate == null)
                {
                    _logger.LogWarning("FinalDueDateDto was null for {request}", request);
                    return Unit.Value;
                }

                var job = await _db.Jobs
                    .FirstOrDefaultAsync(j => j.Id == request.FinalDueDate.JobId, cancellationToken);

                if (job is null)
                {
                    _logger.LogWarning("Job not found Id: {id}", request.FinalDueDate.JobId);
                    throw new ResourceNotFoundException($"Job not found: {request.FinalDueDate.JobId}");
                }

                if (job.FinalUploadDueDateModifiedDate > request.FinalDueDate.CreatedUtc)
                {
                    _logger.LogWarning("Current data is more recent, disregarding update");
                    return Unit.Value;
                }

                _logger.LogDebug("Updating final upload due date.");

                job.FinalUploadDueDate = request.FinalDueDate.FinalUploadDueUtc;
                job.FinalUploadDueDateModifiedDate = request.FinalDueDate.CreatedUtc;
                job.OrigFinalUploadDueDate = request.FinalDueDate.OrigFinalUploadDueUtc;

                if (HasHadAtLeastOneExtension(job.FinalExtensionCount, job.FinalUploadDueDate, job.OrigFinalUploadDueDate))
                {
                    job.FinalExtensionCount = 1;
                }

                //If a job has an extension, but the original and current due dates are identical,
                //it indicates that the due dates were reset.
                //Therefore, no extension should be visible for those jobs.
                if (CheckIfTheDatesMatch(job.FinalUploadDueDate, job.OrigFinalUploadDueDate) && job.FinalExtensionCount > 0)
                {
                    job.FinalExtensionCount = 0;
                }

                await _db.SaveChangesAsync(cancellationToken);

                _logger.LogDebug("Processing complete. Exiting.");

                return Unit.Value;
            }

            private bool HasHadAtLeastOneExtension(int currentCount, DateTime? currentDueDate, DateTime? originalDuDate)
            {
                if (currentDueDate.HasValue && originalDuDate.HasValue)
                {
                    return currentCount <= 0 && currentDueDate > originalDuDate;
                }
                return false;
            }

            private bool CheckIfTheDatesMatch(DateTime? currentDueDate, DateTime? originalDuDate)
            {
                if (currentDueDate.HasValue && originalDuDate.HasValue)
                {
                    return currentDueDate == originalDuDate;
                }

                return false;
            }
        }
    }
}