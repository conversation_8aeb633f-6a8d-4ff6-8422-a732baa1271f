﻿using System.Reflection;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public static class ObjectExtensions
    {
        public static object GetPropertyValue(this object source, string property, string expression = "")
        {
            if (source == null)
                return (object)null;
            object obj = source;
            string str = property;
            char[] chArray = new char[1] { '.' };
            foreach (string name in str.Split(chArray))
            {
                if (obj == null)
                    return (object)null;
                PropertyInfo property1 = obj.GetType().GetProperty(name);
                if (property1 == (PropertyInfo)null)
                    return (object)null;
                obj = property1.GetValue(obj, (object[])null);
            }
            return obj;
        }
    }
}
