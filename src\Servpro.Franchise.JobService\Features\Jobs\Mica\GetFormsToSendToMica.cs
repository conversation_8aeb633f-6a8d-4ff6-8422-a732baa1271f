﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Infrastructure.MicaService.GetSentEntities;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetFormsToSendToMica
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public List<Guid> FormIds { get; set; }
        }

        public class Dto
        {
            public List<Form> Forms { get; set; }
            public class Form
            {
                public Guid Id { get; set; }
                public string FileName { get; set; }
                public string Description { get; set; }
                public string BucketName { get; set; }
                public string MediaPath { get; set; }
                public string CreatedBy { get; set; }
            }
        }
        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetFormsToSendToMica> _logger;
            private readonly IMicaServiceClient _micaServiceClient;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(JobReadOnlyDataContext context,
                ILogger<GetFormsToSendToMica> logger,
                IAmazonS3 clientS3,
                IMicaServiceClient micaServiceClient)
            {
                _context = context;
                _logger = logger;
                _micaServiceClient = micaServiceClient;
                _clientS3 = clientS3;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Forms to send to Mica");

                var returnDto = new Dto()
                {
                    Forms = new List<Dto.Form>()
                };
                List<MediaMetadata> forms = new List<MediaMetadata>();

                if (request.FormIds.Any())
                {
                    forms = await _context.MediaMetadata
                        .Where(x => request.FormIds.Contains(x.Id))
                        .ToListAsync(cancellationToken);

                    foreach (var form in forms)
                    {
                        returnDto.Forms.Add(await MapFormAsync(form, cancellationToken));
                    }
                }
                return returnDto;
            }

            private async Task<Dto.Form> MapFormAsync(MediaMetadata media, CancellationToken cancellationToken)
            => new Dto.Form
                {
                    Id = media.Id,
                    FileName = media.Name,
                    Description = media.Description,
                    CreatedBy = media.CreatedBy,
                    BucketName = media.BucketName,
                    MediaPath = media.MediaPath
                };
        }
    }
}
