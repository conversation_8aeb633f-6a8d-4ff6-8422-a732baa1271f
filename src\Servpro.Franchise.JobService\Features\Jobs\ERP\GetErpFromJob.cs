﻿using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Features.Jobs.Erp.ErpService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Erp
{
    public class GetErpFromJob
    {
        public class Command : IRequest<int?>
        {
            public Command(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, int?>
        {
            private readonly ILogger<Handler> _logger;
            private readonly IErpService _erpService;

            public Handler(ILogger<Handler> logger,
                           IErpService erpService)
            {
                _logger = logger;
                _erpService = erpService;
            }

            public async Task<int?> Handle(Command request, CancellationToken cancellationToken)
            {
                return await _erpService.GetErpFromJobAsync(request.JobId, cancellationToken);
            }
        }

    }
}
