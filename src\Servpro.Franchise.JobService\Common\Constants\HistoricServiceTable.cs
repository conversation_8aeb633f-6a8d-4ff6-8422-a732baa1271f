﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class HistoricServiceTable
    {
        public static class Indexes
        {
            public const string CustomerFirstName = "CustomerFirstName";
            public const string CustomerLastName = "CustomerLastName";
            public const string LossAddress1 = "LossAddress1";
            public const string LossAddress2 = "LossAddress2";
            public const string LossCity = "LossCity";
            public const string LossPostalCode = "LossPostalCode";
            public const string JobNumber = "JobNumber";
            public const string LastArchivedDate = "LastArchivedDate";
            public const string SearchLossAddress1 = "SearchLossAddress1";
            public const string SearchLossAddress2 = "SearchLossAddress2";
            public const string SearchLossCity = "SearchLossCity";
            public const string SearchCustomerFirstName = "SearchCustomerFirstName";
            public const string SearchCustomerLastName = "SearchCustomerLastName";
        }

        public static class MediaFields
        {
            public const string ArtifactTypeId = "ArtifactTypeId";
            public const string BucketName = "BucketName";
            public const string FileName = "FileName";
            public const string MediaId = "MediaId";
            public const string MediaTypeId = "MediaTypeId";
            public const string MediaPath = "MediaPath";
        }

        public static class JobFields
        {
            public const string HashKey = "HashKey";
            public const string RangeKey = "RangeKey";

            public const string LossStateAbbreviation = "LossStateAbbreviation";
            public const string JobNumber = "JobNumber";
            public const string LossDate = "LossDate";
            public const string LossCity = "LossCity";
            public const string CustomerFirstName = "CustomerFirstName";
            public const string CustomerLastName = "CustomerLastName";
            public const string StructureType = "StructureType";
            public const string LossAddress1 = "LossAddress1";
            public const string LossAddress2 = "LossAddress2";
            public const string LossPostalCode = "LossPostalCode";
            public const string Tier1StatusTier2Status = "Tier1StatusTier2Status";
            public const string LossType = "LossType";
            public const string JobId = "JobId";
            public const string SearchLossAddress1 = "SearchLossAddress1";
            public const string SearchLossAddress2 = "SearchLossAddress2";
            public const string SearchLossCity = "SearchLossCity";
            public const string SearchCustomerFirstName = "SearchCustomerFirstName";
            public const string SearchCustomerLastName = "SearchCustomerLastName";
        }
         
        public const string JobPrefix = "job_";
        public const string MediaPrefix = "media_";

        public static Guid GetJobId(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("The key parmater was null or empty", nameof(key));

            var value = key.Replace(JobPrefix, string.Empty);

            if (Guid.TryParse(value, out Guid jobId))
            {
                return jobId;
            }

            return Guid.Empty;
        }
    }
}