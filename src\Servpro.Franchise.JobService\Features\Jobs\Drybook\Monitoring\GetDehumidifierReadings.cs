﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Common.ExtensionMethods;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public partial class GetDehumidifierReadings
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GetDehumidifierReadings> _logger;

            public Handler(
                JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient,
                ILogger<GetDehumidifierReadings> logger)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Begin handler with: {@request}", request);

                var userInfo = _userInfoAccessor.GetUserInfo();

                var job = await _context.Jobs
                    .Include(x => x.JobVisits)
                        .ThenInclude(x => x.Tasks)
                        .ThenInclude(x => x.JournalNotes)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == request.JobId
                    && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.Zones = await _context.Zones
                        .Include(x => x.JobAreas)
                            .ThenInclude(x => x.BeginJobVisit)
                        .Include(x => x.ZoneReadings)
                        .Include(x => x.JobAreas)
                            .ThenInclude(x => x.EquipmentPlacements)
                            .ThenInclude(x => x.EquipmentPlacementReadings)
                        .Include(x => x.JobAreas)
                             .ThenInclude(x => x.EquipmentPlacements)
                                .ThenInclude(x => x.Equipment)
                                    .ThenInclude(x => x.EquipmentModel)
                                        .ThenInclude(x => x.EquipmentType)
                        .AsNoTracking()
                        .Where(x => x.JobId == request.JobId)
                        .ToListAsync(cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var zoneType = lookups.ZoneTypes.First(x => x.Id == ZoneTypes.Drying);
                var visits = job.JobVisits
                    .OrderBy(x => x.Date)
                    .ToList();

                _logger.LogInformation("JobVisits for Job {jobId}: {@visits}", request.JobId, visits);

                var zones = job.Zones
                    .Where(x => x.ZoneTypeId == ZoneTypes.Drying)
                    .Select(x => new
                    {
                        Zone = MapZone(x, visits, zoneType),
                        x.CreatedDate
                    })
                    .ToList();

                _logger.LogInformation("Dehumidifier Readings for Job {jobId}: {@dto}", request.JobId, zones);

                return zones
                    .OrderBy(x => x.Zone.DisplayOrder)
                    .ThenBy(x => x.CreatedDate)
                    .Select(x => x.Zone);
            }

            Dto MapZone(
                Zone zone,
                IEnumerable<JobVisit> visits,
                ZoneTypeDto zoneType)
            {
                var isIncludedInVisit = visits.ToDictionary(x => x.Id, x => IsIncludedInVisit(zone, x));
                var readingsLookup = zone.ZoneReadings.ToDictionary(x => x.JobVisitId);
                var tempsFerenheight = visits.Select(x => MapTemp(x, readingsLookup, isIncludedInVisit)).ToList();
                var tempsCelcius = tempsFerenheight.Select(ConvertToCelcius);
                var rhs = visits.Select(x => MapRHS(x, readingsLookup, isIncludedInVisit));
                var humidityRatios = visits.Select(x => MapHumidityRatio(x, readingsLookup, isIncludedInVisit));
                var isHvacOn = visits.Select(x => MapHVAC(x, readingsLookup, isIncludedInVisit));
                var notes = visits.Select(x => MapVisitReadingValue(x, readingsLookup, isIncludedInVisit));
                var zoneRatios = humidityRatios.ToDictionary(x => x.VisitId, x => x.Value);
                var rooms = zone.JobAreas.Select(x => MapRoom(x, visits, zoneRatios));
                return new Dto(
                    zone.Id,
                    zone.Name,
                    zone.Description,
                    zoneType.MaxOccurrences,
                    zoneType.DisplayOrder,
                    zoneType.ShouldCollectNA,
                    zoneType.IsNameRequired,
                    tempsFerenheight,
                    tempsCelcius,
                    rhs,
                    humidityRatios,
                    isHvacOn,
                    notes,
                    rooms);
            }

            private Dto.RoomSummary MapRoom(
                JobArea jobArea,
                IEnumerable<JobVisit> visits,
                IDictionary<Guid, decimal?> zoneRatios)
            {
                var equipmentReadings = jobArea.EquipmentPlacements
                    .Where(x => x.Equipment.EquipmentModel.EquipmentType.BaseEquipmentTypeId == BaseEquipmentTypes.Dehumidifier)
                    .Select(x => MapEquipmentReading(x, visits, zoneRatios));

                return new Dto.RoomSummary(
                    jobArea.Id,
                    jobArea.RoomId,
                    jobArea.Name,
                    equipmentReadings);
            }

            private Dto.EquipmentReading MapEquipmentReading(
                EquipmentPlacement ep,
                IEnumerable<JobVisit> visits,
                IDictionary<Guid, decimal?> zoneRatios)
            {
                var latestReadings = ep.EquipmentPlacementReadings.GroupBy(x => x.JobVisitId)
                                                                  .Select(group => group.OrderByDescending(v => v.CreatedDate).FirstOrDefault()).ToList();

                var readings = latestReadings.ToDictionary(x => x.JobVisitId);
                var isIncluded = visits.ToDictionary(x => x.Id, x => IsIncludedInVisit(ep, x));
                var prevVisits = visits.ToDictionary(
                    x => x.Id,
                    x => visits
                        .Where(v => v.Date < x.Date)
                        .OrderByDescending(v => v.Date)
                        .FirstOrDefault());
                var followingVisits = visits.ToDictionary(
                    x => x.Id,
                    x => visits
                        .Where(v => v.Date > x.Date)
                        .OrderBy(v => v.Date)
                        .FirstOrDefault());

                var tempsFarenheight = visits.Select(x => MapTemp(x, readings, isIncluded));
                var tempsCelcius = tempsFarenheight.Select(ConvertToCelcius);
                var relativeHumidities = visits.Select(x => MapRelativeHumidity(x, readings, isIncluded));
                var hours = visits.Select(x => MapHour(x, readings, isIncluded));
                var humidityRatios = visits.Select(x => MapHumidityRatio(x, readings, isIncluded));
                var gdeps = visits.Select(x => MapGdep(x, readings, isIncluded, zoneRatios));
                var inconsistentHours = visits.Select(x => MapInconsistentHour(x, readings, isIncluded, prevVisits));
                var previousVisitIds = visits.Select(x => MapAroundVisit(x, isIncluded, prevVisits));
                var followingVisitIds = visits.Select(x => MapAroundVisit(x, isIncluded, followingVisits));
                var previousVisitHours = visits.Select(x => MapAroundHour(x, readings, isIncluded, prevVisits));
                var followingVisitHours = visits.Select(x => MapAroundHour(x, readings, isIncluded, followingVisits));
                var humidityRatiosLookup = humidityRatios.ToDictionary(x => x.VisitId, x => x.Value);
                var followingVisitHumidityRatios = visits.Select(x => MapAroundValue(x, humidityRatiosLookup, isIncluded, followingVisits));
                var previousVisitHumidityRatios = visits.Select(x => MapAroundValue(x, humidityRatiosLookup, isIncluded, prevVisits));
                var gdepsLookup = gdeps.ToDictionary(x => x.VisitId, x => x.Value);
                var previousVisitGdeps = visits.Select(x => MapAroundValue(x, gdepsLookup, isIncluded, prevVisits));
                return new Dto.EquipmentReading(
                    ep.Id,
                    ep.Equipment.AssetNumber,
                    tempsFarenheight,
                    tempsCelcius,
                    relativeHumidities,
                    hours,
                    humidityRatios,
                    gdeps,
                    inconsistentHours,
                    previousVisitIds,
                    followingVisitIds,
                    previousVisitHours,
                    previousVisitHumidityRatios,
                    previousVisitGdeps,
                    followingVisitHours,
                    followingVisitHumidityRatios);
            }

            private Dto.VisitValuePair<decimal?> MapAroundValue(
                JobVisit visit,
                IDictionary<Guid, decimal?> humidityRatiosLookup,
                IDictionary<Guid, bool> isIncluded,
                IDictionary<Guid, JobVisit> previousVisits)
            {
                var previousVisit = previousVisits.GetOrDefault(visit.Id);
                var value = previousVisit != null
                    ? humidityRatiosLookup[previousVisit.Id]
                    : null;
                return new Dto.VisitValuePair<decimal?>(visit.Id, value, isIncluded[visit.Id]);
            }

            private Dto.VisitValuePair<int?> MapAroundHour(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readings,
                IDictionary<Guid, bool> isIncluded,
                IDictionary<Guid, JobVisit> prevVisits)
            {
                var previousVisit = prevVisits.GetOrDefault(visit.Id);
                var value = previousVisit != null &&
                    readings.ContainsKey(previousVisit.Id)
                    ? (int?)readings[previousVisit.Id].HourCount
                    : null;
                return new Dto.VisitValuePair<int?>(visit.Id, value, isIncluded[visit.Id]);
            }

            private Dto.VisitValuePair<Guid?> MapAroundVisit(
                JobVisit visit,
                IDictionary<Guid, bool> isIncluded,
                IDictionary<Guid, JobVisit> prevVisits)
                => new Dto.VisitValuePair<Guid?>(
                    visit.Id,
                    prevVisits.GetOrDefault(visit.Id)?.Id,
                    isIncluded[visit.Id]);

            private Dto.VisitValuePair<bool> MapInconsistentHour(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readings,
                IDictionary<Guid, bool> isIncluded,
                IDictionary<Guid, JobVisit> previousVisits)
            {
                var previousVisit = previousVisits.GetOrDefault(visit.Id);
                var previousReading = previousVisit != null
                    && readings.ContainsKey(previousVisit.Id)
                    ? (int?)readings[previousVisit.Id].HourCount
                    : null;
                var isHourCountConsistent = !(previousReading.HasValue && readings.ContainsKey(visit.Id))
                    || IsHourCountConsistent(visit, previousVisit, readings[visit.Id].HourCount, previousReading.Value)
;
                return new Dto.VisitValuePair<bool>(visit.Id, !isHourCountConsistent, isIncluded[visit.Id]);
            }

            private bool IsHourCountConsistent(
                JobVisit visit,
                JobVisit previousVisit,
                int currentHours,
                int previousHours)
            {
                var counterDiff = currentHours - previousHours;
                var visitDiff = (visit.Date - previousVisit.Date).TotalHours;
                return Math.Abs(counterDiff - visitDiff) <= 3;
            }

            private Dto.VisitReadingValue<decimal?> MapGdep(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readings,
                IDictionary<Guid, bool> isIncluded,
                IDictionary<Guid, decimal?> zoneRatios)
            {
                var equipRatio = CalculateGrainsPerPound(
                        readings.GetOrDefault(visit.Id)?.Temperature,
                        readings.GetOrDefault(visit.Id)?.RelativeHumidity);
                var value = zoneRatios.ContainsKey(visit.Id)
                    && zoneRatios[visit.Id].HasValue && equipRatio.HasValue
                    ? zoneRatios[visit.Id] - equipRatio
                    : null;
                return new Dto.VisitReadingValue<decimal?>(
                    value,
                    readings.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);
            }

            private Dto.VisitReadingValue<int?> MapHour(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue<int?>(
                    readingsLookup.GetOrDefault(visit.Id)?.HourCount,
                    readingsLookup.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);

            private Dto.VisitReadingValue<decimal?> MapRelativeHumidity(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue<decimal?>(
                    readingsLookup.GetOrDefault(visit.Id)?.RelativeHumidity,
                    readingsLookup.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);

            private bool IsIncludedInVisit(EquipmentPlacement ep, JobVisit visit)
            {
                var epBeginDate = ep.BeginDate.RemoveSecondsFromDateTime(ep.BeginDate);
                var epEndDate = ep.EndDate.HasValue ? ep.EndDate.Value.RemoveSecondsFromDateTime(ep.EndDate.Value) : ep.EndDate;
                var visitDate = visit.Date.RemoveSecondsFromDateTime(visit.Date);

                return epBeginDate <= visitDate
                    && (!ep.EndDate.HasValue || visitDate <= epEndDate);
            }

            private bool IsIncludedInVisit(Zone zone, JobVisit visit)
            {
                var zoneDate = zone.JobAreas
                    .Where(x => !x.EndJobVisitId.HasValue)
                    .Select(x => x.BeginJobVisit?.Date)
                    .Min(x => x);

                zoneDate = zoneDate.HasValue 
                    ? zoneDate.Value.RemoveSecondsFromDateTime(zoneDate.Value)
                    : zoneDate;
                return zoneDate.HasValue && zoneDate <= visit.Date;
            }

            private Dto.VisitValuePair<decimal?> ConvertToCelcius(Dto.VisitValuePair<decimal?> value)
                => new Dto.VisitValuePair<decimal?>(value.VisitId, ToCelcius(value.Value), value.IncludedInVisit);

            private Dto.VisitReadingValue<decimal?> ConvertToCelcius(Dto.VisitReadingValue<decimal?> value)
                => new Dto.VisitReadingValue<decimal?>(ToCelcius(value.Value), value.ReadingId, value.VisitId, value.IncludedInVisit);

            private decimal? ToCelcius(decimal? temp)
                => temp.HasValue
                ? (decimal?)((temp.Value - 32m) * (5m / 9m))
                : null;

            Dto.VisitValuePair<decimal?> MapTemp(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<decimal?>(
                    visit.Id,
                    readingsLookup.GetOrDefault(visit.Id)?.Temperature,
                    isIncluded[visit.Id]);

            private Dto.VisitReadingValue<decimal?> MapTemp(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue<decimal?>(
                    readingsLookup.GetOrDefault(visit.Id)?.Temperature,
                    readingsLookup.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);

            Dto.VisitValuePair<decimal?> MapRHS(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<decimal?>(
                    visit.Id,
                    readingsLookup.GetOrDefault(visit.Id)?.RelativeHumidity,
                    isIncluded[visit.Id]);

            Dto.VisitValuePair<decimal?> MapHumidityRatio(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<decimal?>(
                    visit.Id,
                    CalculateGrainsPerPound(
                        readingsLookup.GetOrDefault(visit.Id)?.Temperature,
                        readingsLookup.GetOrDefault(visit.Id)?.RelativeHumidity),
                    isIncluded[visit.Id]);

            private Dto.VisitReadingValue<decimal?> MapHumidityRatio(
                JobVisit visit,
                IDictionary<Guid, EquipmentPlacementReading> readings,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue<decimal?>(
                    CalculateGrainsPerPound(
                        readings.GetOrDefault(visit.Id)?.Temperature,
                        readings.GetOrDefault(visit.Id)?.RelativeHumidity),
                    readings.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);


            Dto.VisitValuePair<bool?> MapHVAC(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<bool?>(
                    visit.Id,
                    readingsLookup.ContainsKey(visit.Id),
                    isIncluded[visit.Id]);

            Dto.VisitReadingValue<Guid?> MapVisitReadingValue(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue<Guid?>(
                    readingsLookup.GetOrDefault(visit.Id)?.JournalNoteId,
                    readingsLookup.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);

            /// <summary>
            /// Calculates the GPP.
            /// </summary>
            /// <param name="temperature">The temperature.</param>
            /// <param name="relativeHumidity">The relative humidity.</param>
            /// <returns>Grains Per Pound (Humidity Ratio)</returns>
            decimal? CalculateGrainsPerPound(decimal? temperature, decimal? relativeHumidity)
            {
                /*  This is a code snippet condensed from the Access version of the function:
                    This code copied directly from the drying workbook formulas.
                    It seems to be using some regression formula to emulate the steam tables.  (water vapor pressures at given temperatures)
                    It also seems to be presuming a given ambient air pressure.  The industry standard seems to not factor in local air pressure.
                */
                if (temperature == null || relativeHumidity == null)
                {
                    return null;
                }

                var t = (double)temperature.Value;
                var rh = (double)relativeHumidity.Value;
                var g = t + 459.67;
                var h = -10443.97 / g;
                var i = -11.29465;
                var j = -0.027022355 * g;
                var k = 0.00001289036 * Math.Pow(g, 2);
                var l = -0.000000002478068 * Math.Pow(g, 3);
                var m = 6.5459673 * Math.Log(g);
                var n = Math.Exp(h + i + j + k + l + m); //  ‘Exp(x) function is the natural logarithm e raised to the power of x.
                var o = n * rh * 0.01;
                var p = 0.62198 * (o / (14.696 - o));
                var answer = Math.Round(7000 * p);
                return (decimal)answer;
            }
        }

    }
}
