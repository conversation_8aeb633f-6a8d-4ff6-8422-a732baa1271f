﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationClientNotificationRequirement
    {
        public int RuleId { get; set; }
        public int ValidationStatus { get; set; }
        public string Name { get; set; }
        public string ValidationEffect { get; set; }
        public string ValidationMessage { get; set; }
        public string Description { get; set; }
        public string Notification { get; set; }
        public Guid? JobTriStateQuestionId { get; set; }
        public bool? TriggerAnswer { get; set; }
        public bool BypassAllowed { get; set; }
        public bool AskedPerVisit { get; set; }
        public bool? RequiredForInitialUpload { get; set; }
        public List<CrsValidationClientNotification> ClientNotifications { get; set; }
        public List<KeyValuePair<string, string>> RuleParameters { get; set; }

        public CrsValidationClientNotificationRequirement()
        {
            this.ClientNotifications = new List<CrsValidationClientNotification>();
            this.RuleParameters = new List<KeyValuePair<string, string>>();
        }
    }
}