﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class GetContactJournalNotes
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid ContactId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public DateTime? StartDate { get; set; } = DateTime.MinValue;
            public DateTime? EndDate { get; set; } = DateTime.UtcNow;
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? JobId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid CategoryId { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public DateTime? ActionDate { get; set; }
            public DateTime CreatedDate { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var contact = await _context.Contacts
                        .Include(c => c.JobContactMaps)
                            .ThenInclude(jc => jc.Job)
                                .ThenInclude(j => j.JournalNotes)
                        .Where(c => c.Id == request.ContactId)
                        .FirstOrDefaultAsync(cancellationToken);

                if (contact is null)
                    throw new ResourceNotFoundException("Contact not found");

                var journalNotes = contact.JobContactMaps.GroupBy(x => x.JobId).Select(g => g.First()).SelectMany(x => x.Job.JournalNotes.Select(Map));
                var filter = journalNotes.Where(x => x.ActionDate >= request.StartDate
                        && x.ActionDate <= request.EndDate);
                    
                return filter;
            }


            private Dto Map(JournalNote journalNote)
                => new Dto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Note = journalNote.Note,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    Subject = journalNote.Subject,
                    ActionDate = journalNote.ActionDate,
                    VisibilityId = journalNote.VisibilityId
                };
        }

    }
}
