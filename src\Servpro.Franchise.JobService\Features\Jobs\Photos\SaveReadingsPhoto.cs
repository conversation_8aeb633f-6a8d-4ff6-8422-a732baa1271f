﻿using FluentValidation;

using MediatR;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Microsoft.Extensions.Configuration;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class SaveReadingsPhoto
    {
        public class Command : IRequest<List<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid JobAreaMaterialId { get; set; }
            public Guid VisitId { get; set; }
            public ICollection<MediaDto> Media { get; set; }
        }

        public class MediaDto
        {
            public Guid ArtifactTypeId { get; set; }
            public bool IsForUpload { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public Guid Id { get; set; }
            public Guid? JobVisitId { get; set; }
            public Guid? ZoneId { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Command, List<Dto>>
        {
            private readonly JobDataContext _db;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private readonly IConfiguration _config;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";

            public Handler(
                JobDataContext db,
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor,
                IMediaEventGenerator mediaEventGenerator,
                IConfiguration config)
            {
                _db = db;
                _lookupServiceClient = lookupServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _mediaEventGenerator = mediaEventGenerator;
                _config = config;
            }

            public async Task<List<Dto>> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var mediaMetadata = request.Media.Select(x => Map(request, x, lookups)).ToList();

                await _db.MediaMetadata.AddRangeAsync(mediaMetadata, cancellationToken);                

                if (request.VisitId != Guid.Empty && request.JobAreaMaterialId != Guid.Empty)
                {
                    var jobAreaMatReading = await _db.JobAreaMaterialReadings
                                                     .Where(x => x.JobVisitId == request.VisitId && x.JobAreaMaterialId == request.JobAreaMaterialId)
                                                     .FirstOrDefaultAsync(cancellationToken);
                    if (jobAreaMatReading != null)
                    {
                        jobAreaMatReading.MediaMetadataId = mediaMetadata.FirstOrDefault().Id;
                        _db.JobAreaMaterialReadings.Update(jobAreaMatReading);
                    }
                }
                await _db.SaveChangesAsync(cancellationToken);

                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                //Generate MediaAddedEvent
                var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(mediaMetadata, correlationId, userInfo);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);

                //Generate Save photo event
                var photoSavedOutboxMessage =
                    await _mediaEventGenerator.GeneratePhotoSavedEvent(mediaMetadata, correlationId, userInfo);
                await _db.OutboxMessages.AddAsync(photoSavedOutboxMessage, cancellationToken);

                await _db.SaveChangesAsync(cancellationToken);

                return mediaMetadata.Select(x => new Dto() { Id = x.Id, Name = x.Name }).ToList();
            }

            private MediaMetadata Map(Command command, MediaDto media, GetLookups.Dto lookups)
            {
                var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == media.ArtifactTypeId);
                if (artifactType == null)
                    return null;

                return new MediaMetadata
                {
                    Id = media.Id,
                    JobId = command.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    FranchiseSetId = command.FranchiseSetId,
                    IsDeleted = false,
                    IsForUpload = artifactType.Required || media.IsForUpload,
                    Name = media.Name,
                    MediaTypeId = MediaTypes.Photo,
                    ArtifactTypeId = media.ArtifactTypeId,
                    MediaPath = media.MediaPath,
                    ZoneId = media.ZoneId.ValueOrNullIfEmpty(),
                    JobAreaId = media.JobAreaId.ValueOrNullIfEmpty(),
                    JobVisitId = command.VisitId,
                    JobAreaMaterialId = command.JobAreaMaterialId,
                    ArtifactDate = DateTime.UtcNow,
                    BucketName = _config.GetValue<string>(S3MediaBucketNameKey),
                    UploadedSuccessfully = true
                };
            }

        }
    }
}