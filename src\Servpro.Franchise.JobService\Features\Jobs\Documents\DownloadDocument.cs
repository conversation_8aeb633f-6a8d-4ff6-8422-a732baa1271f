﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class DownloadDocument
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }

            public ICollection<Guid> Ids { get; set; } = new List<Guid>();
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.Ids).NotEmpty();
                RuleForEach(m => m.Ids).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public string BucketName { get; set; }
        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";
            private readonly ILogger<DownloadDocument> _logger;

            public Handler(JobReadOnlyDataContext db, IConfiguration config, IAmazonS3 clientS3, ILogger<DownloadDocument> logger)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
                _logger = logger;
            }

            public async Task<ICollection<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                try
                {
                    _logger.LogInformation("{downloadDocument}: Begin handler with: {@request}", nameof(DownloadDocument), request);
                    var mediaMetadata = await _db.MediaMetadata.Where(x => x.JobId == request.JobId &&
                                                                     x.FranchiseSetId == request.FranchiseSetId &&
                                                                     x.MediaTypeId == MediaTypes.Document &&
                                                                     request.Ids.Contains(x.Id) &&
                                                                     !x.IsDeleted).ToListAsync(cancellationToken);

                    return (mediaMetadata.AsEnumerable().Select(metadata => GetPreSignedUrl(metadata))).ToList();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "{downloadDocument}: Error downloading a document from S3", nameof(DownloadDocument));
                    throw;
                }
            }

            private Dto GetPreSignedUrl(MediaMetadata metadata)
            {
                var key = metadata.MediaPath;

                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = metadata.BucketName.IsNullOrWhiteSpace() ? _config[S3MediaBucketNameKey] : metadata.BucketName,
                    Key = key,
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };
                preSignedUrlRequest.ResponseHeaderOverrides.ContentDisposition = $"attachment; filename={metadata.Name}";
                var preSignedUrlDto = new Dto
                {
                    Id = metadata.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = metadata.Name,
                    MediaPath = metadata.MediaPath,
                    BucketName = metadata.BucketName
                };
                _logger.LogInformation("{downloadDocument}: Presigned DTO: {@preSignedUrlDto}", nameof(DownloadDocument), preSignedUrlDto);
                return preSignedUrlDto;
            }
        }
    }
}