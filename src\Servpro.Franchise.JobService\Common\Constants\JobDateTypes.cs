﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class JobDateTypes
    {
        public static readonly Guid AllServicesComplete = new Guid("7226CCE3-6116-49CB-9F02-FDB24FE76E01");
        public static readonly Guid Billing = new Guid("1561C489-7A25-472C-8D5F-B6F049B2DD89");
        public static readonly Guid Cancellation = new Guid("F349A874-4903-45AD-8674-C43B7D254AF6");
        public static readonly Guid Collected = new Guid("1C2C7E27-A054-4B69-B099-44C3A4F3B410");
        public static readonly Guid Complete = new Guid("81FDD5BB-A5CD-4F19-A52F-F3B4140B1109");
        public static readonly Guid CustomerCalled = new Guid("CB9384B5-8770-4256-B852-EC4B952AC6CE");
        public static readonly Guid DateClosed = new Guid("0C409378-841C-4495-9710-4848046FC5FE");
        public static readonly Guid Dispatch = new Guid("1D5D2A49-936C-4128-9FE0-05D0530365FF");
        public static readonly Guid DispatchExpiresOn = new Guid("BAA917E7-**************-7DFFD3258C62");
        public static readonly Guid DryingComplete = new Guid("74479F48-E1EA-41FD-9003-FDF3258CD289");
        public static readonly Guid DryingStarted = new Guid("9CD920D7-350B-4942-9099-EE5403364602");
        public static readonly Guid EmergencyServicesScheduled = new Guid("A0EC4C3E-ECE2-4508-AE88-1A3BFB06EE81"); // Site Appointment Start (same guid, this name may be a relic from 1.0) 
        public static readonly Guid EstimateApproved = new Guid("DDB32FEB-6267-464B-9E92-1126E5A840BF");
        public static readonly Guid EstimateDelivered = new Guid("F1517329-2527-4745-934D-3431337B6B5A");
        public static readonly Guid EstimateReturnedfromXact = new Guid("1F114EDC-A7B8-40BF-B60A-8D6D6E40F97D");
        public static readonly Guid FinalInspection = new Guid("418B24C8-5FD9-4BEA-B35F-DBE084C15D70");
        public static readonly Guid InitialOnSiteArrival = new Guid("357D6E3D-28A6-41B5-A50A-A650A9F369E2");
        public static readonly Guid JobNotSold = new Guid("FE0F62B8-9DCE-416E-BDB7-B70AB6B9D23E");
        public static readonly Guid LeadQualified = new Guid("CA092A8B-2FE9-49DD-B4D0-52EF2592BBF7");
        public static readonly Guid LossOccurred = new Guid("E80BA082-FB93-445D-8823-BE661EE3EE23");
        public static readonly Guid MitigationComplete = new Guid("D0A58C7C-020A-49D1-9D4A-9279D936E869");
        public static readonly Guid MobileJobRecordDate = new Guid("276F1339-58F9-4479-9D6F-4FFC7805219B");
        public static readonly Guid NextDayServicesScheduled = new Guid("11B95066-A9C0-4B83-ADD5-DF65E526DC9C");
        public static readonly Guid PreConstConsultDate = new Guid("3146CC5C-B66F-45B6-9396-82A5DE61D914");
        public static readonly Guid ReceivedDate = new Guid("F588005B-FDBB-46A9-A41A-9DBC65469E7F");
        public static readonly Guid RestorationComplete = new Guid("D0733FB5-69F5-4CD3-A9B0-76175A612472");
        public static readonly Guid SiteAppointmentCreated = new Guid("50408019-48B2-4D1F-81F8-EBD0502A7A61");
        public static readonly Guid SiteAppointmentEnd = new Guid("E5BE1964-2E2B-42DF-905D-F799EFCAAFA8");
        public static readonly Guid SiteAppointmentStart = new Guid("A0EC4C3E-ECE2-4508-AE88-1A3BFB06EE81");
        public static readonly Guid TargetCompletion = new Guid("42855933-9B6A-4A90-B16A-BB5F388AA9B8");
        public static readonly Guid TargetStart = new Guid("3E1A7086-4377-4086-970E-29DCFC511E00");
        public static readonly Guid UploadAsCompleteDeadline = new Guid("E8AB5C19-AA0D-4613-BD97-A56F598A6523");
        public static readonly Guid WorkAuthorizationSigned = new Guid("ABE3BA02-CEFC-42B0-A66D-D11A31B0F4F3");
        public static readonly Guid WorkEnd = new Guid("10017BD9-611F-4B73-9DBE-EE6A65D3BADF");
        public static readonly Guid WorkStart = new Guid("6BFB5D0A-6D61-4D3C-AE48-9F687BBEC305");
        public static readonly Guid WorkCenterComplete = new Guid("D7E58953-EA77-4422-AFD5-006C2D44A623");
    }
}
