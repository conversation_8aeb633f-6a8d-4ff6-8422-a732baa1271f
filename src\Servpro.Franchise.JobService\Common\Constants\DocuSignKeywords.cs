using System;
using System.Collections.Generic;
using System.Collections.Immutable;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class DocuSignKeywords
    {
        public const string InitialHereField = "initial";
        public readonly static IImmutableList<string> CustomerFieldIdentifiers = ImmutableList.Create("customer", "client", "insured");
        public readonly static IImmutableList<string> FranchiseFieldIdentifiers = ImmutableList.Create("franchisee", "provider");
        public readonly static IImmutableList<string> ClaimsProfessionalFieldIdentifiers = ImmutableList.Create("claimsprofessional", "claims professional");
    }
}
