﻿using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeAttachments : IJobMergeSection
    {
        public void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            var sourceJobId = sourceJob.Id;
            var sourceJobMedia = sourceJob.MediaMetadata
                .Where(x => (x.MediaTypeId == MediaTypes.Photo || x.MediaTypeId == MediaTypes.Sketch)
                            && !x.IsDeleted);

            sourceJobMedia
                .Where(x => !targetJob.MediaMetadata.Any(y => y.Id == x.Id) && !targetJob.MediaMetadata.Any(y => y.MediaPath == x.MediaPath))
                .ToList()
                .ForEach(x =>
                {
                    x.JobId = targetJob.Id;
                    x.Name = $"Merged from {sourceJob.ProjectNumber} - {x.Name}";
                    x.JobSketch = MapJobSketch(x.JobSketch, targetJob.Id);
                    x.Job = null;
                    targetJob.MediaMetadata.Add(x);
                    sourceJob.MediaMetadata.Remove(x);
                });
        }

        private JobSketch MapJobSketch(JobSketch sourceJobSketch, Guid jobId)
        {
            if (sourceJobSketch is null)
                return null;

            return new JobSketch
            {
                JobId = jobId,
                Name = sourceJobSketch.Name,
                CanvasJson = sourceJobSketch.CanvasJson,
            };
        }
    }
}
