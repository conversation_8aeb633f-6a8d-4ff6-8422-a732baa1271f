﻿using System;

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public class MigrationHelper
    {
        public static string ChangeDelimiterSql = "\r\nDELIMITER //\r\n";
        public static string DelimiterSql = "\r\n//\r\n";
        public static string ResetDelimiterSql = "\r\n//\r\nDELIMITER ;\r\n";

        public static bool IsPipeline()
        {
            return Environment.GetEnvironmentVariable("IS_CI_BUILD") == "true" ? true : false;
        }
    }
}
