﻿using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace Servpro.Franchise.JobService.Common.ExtensionMethods
{
    public static class StringExtensions
    {
        public static string ToCamelCase(this string value)
        {
            var lowerCaseLetter = value.First().ToString().ToLower();
            var result = value.Remove(0, 1);
            result = result.Insert(0, lowerCaseLetter);
            return result;
        }

        public static bool FileOrDirectoryExists(string name)
        {
            return (Directory.Exists(name) || File.Exists(name));
        }
        public static bool IsNullOrWhiteSpace(this string value)
        {
            return string.IsNullOrWhiteSpace(value);
        }

        public static bool NullableContains(this string @string, string value, System.StringComparison comparisonType = System.StringComparison.InvariantCultureIgnoreCase)
            => @string != null && @string.Contains(value, comparisonType);

        public static string StripDomain(this string username)
            => username is null ? username : Regex.Replace(username, ".*\\\\(.*)", "$1", RegexOptions.None);
    }
}
