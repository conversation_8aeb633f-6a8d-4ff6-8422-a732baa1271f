import boto3
import json
import time

client = boto3.client('ecs')
tasks = client.list_tasks(cluster="wc-dev")
job_migration_arn = ''

described_tasks = client.describe_tasks(cluster="wc-dev", tasks = tasks['taskArns'])

for task in described_tasks['tasks']:
    for container in task['containers']:
        if container['name'] == 'job-service-migration':
            job_migration_arn = container['taskArn']


tasks_list  = [job_migration_arn]
job_migration = client.describe_tasks(cluster="wc-dev", tasks = tasks_list)
current_status = job_migration['tasks'][0]['lastStatus']

while (current_status != 'STOPPED'):
    job_migration = client.describe_tasks(cluster="wc-dev", tasks = tasks_list)
    current_status = job_migration['tasks'][0]['lastStatus']
    print(current_status)
    time.sleep(5)