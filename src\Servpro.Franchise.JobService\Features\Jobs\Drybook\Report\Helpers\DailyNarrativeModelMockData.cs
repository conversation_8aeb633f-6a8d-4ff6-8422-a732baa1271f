﻿using System;
using System.Collections.Generic;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class DailyNarrativeModelMockData
    {
        public static List<DailyNarrative> GetMockForPreview()
        {
            return new List<DailyNarrative>
            {
                new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = "Narrative 1", CreatedBy = "tyrion", CreatedDate = DateTime.UtcNow, Timezone = "PDT"},
                new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = "Narrative 2", CreatedBy = "tyrion", CreatedDate = DateTime.UtcNow, Timezone = "PDT"},
                new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = "Narrative 3", CreatedBy = "tyrion", CreatedDate = DateTime.UtcNow, Timezone = "PDT"},
                new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = "Narrative 4", CreatedBy = "tyrion", CreatedDate = DateTime.UtcNow, Timezone = "PDT"},
                new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = "Narrative 5", CreatedBy = "tyrion", CreatedDate = DateTime.UtcNow, Timezone = "PDT"},
                new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = "Narrative 6", CreatedBy = "tyrion", CreatedDate = DateTime.UtcNow, Timezone = "PDT"}
            };
        }
    }
}
