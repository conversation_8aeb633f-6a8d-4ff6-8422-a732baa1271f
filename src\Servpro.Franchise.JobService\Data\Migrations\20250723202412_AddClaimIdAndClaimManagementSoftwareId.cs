﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddClaimIdAndClaimManagementSoftwareId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("f04ddc82-e34e-4fb8-81c3-16525ff012c4"));

            migrationBuilder.AddColumn<string>(
                name: "ClaimId",
                table: "WipRecord",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                collation: "utf8mb4_general_ci");

            migrationBuilder.AddColumn<int>(
                name: "ClaimManagementSoftwareId",
                table: "WipRecord",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClaimId",
                table: "Job",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                collation: "utf8mb4_general_ci");

            migrationBuilder.AddColumn<int>(
                name: "ClaimManagementSoftwareId",
                table: "Job",
                type: "int",
                nullable: true);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("1f11dff8-d825-4c3c-ad60-25f7be2e1bc7"), null, new DateTime(2025, 7, 23, 20, 24, 11, 282, DateTimeKind.Utc).AddTicks(5396), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("1f11dff8-d825-4c3c-ad60-25f7be2e1bc7"));

            migrationBuilder.DropColumn(
                name: "ClaimId",
                table: "WipRecord");

            migrationBuilder.DropColumn(
                name: "ClaimManagementSoftwareId",
                table: "WipRecord");

            migrationBuilder.DropColumn(
                name: "ClaimId",
                table: "Job");

            migrationBuilder.DropColumn(
                name: "ClaimManagementSoftwareId",
                table: "Job");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("f04ddc82-e34e-4fb8-81c3-16525ff012c4"), null, new DateTime(2025, 5, 20, 16, 59, 21, 228, DateTimeKind.Utc).AddTicks(3442), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
