﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements
{
    public class InitialUploadDueDateChanged
    {
        public class Event : InitialUploadDueDateChangedEvent, IRequest
        {
            public Event(InitialDueDateDto initialDueDate, Guid correlationId)
                : base(initialDueDate, correlationId) { }

            public class Handler : IRequestHandler<Event>
            {
                private readonly JobDataContext _db;
                private readonly ILogger<InitialUploadDueDateChanged> _logger;

                public Handler(JobDataContext jobDataContext, ILogger<InitialUploadDueDateChanged> logger)
                {
                    _db = jobDataContext;
                    _logger = logger;
                }

                public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
                {

                    _logger.LogInformation("Begin handler with: {@request}", request);

                    if (request.InitialDueDate == null)
                    {
                        _logger.LogWarning("InitialDueDateDto was null for {request}", request);
                        return Unit.Value;
                    }

                    var job = await _db.Jobs
                        .FirstOrDefaultAsync(j => j.Id == request.InitialDueDate.JobId, cancellationToken);

                    if (job is null)
                    {
                        _logger.LogWarning("Job not found Id: {id}", request.InitialDueDate.JobId);
                        throw new ResourceNotFoundException($"Job not found: {request.InitialDueDate.JobId}");
                    }

                    if (job.InitialUploadDueDateModifiedDate > request.InitialDueDate.CreatedUtc)
                    {
                        _logger.LogWarning("Current data is more recent, disregarding update");
                        return Unit.Value;
                    }

                    _logger.LogDebug("Updating initial upload due date.");

                    job.InitialUploadDueDate = request.InitialDueDate.InitialUploadDueUtc;
                    job.InitialUploadDueDateModifiedDate = request.InitialDueDate.CreatedUtc;
                    job.OrigInitialUploadDueDate = request.InitialDueDate.OrigInitialUploadDueUtc;

                    if (HasHadAtLeastOneExtension(job.InitialExtensionCount, job.InitialUploadDueDate, job.OrigInitialUploadDueDate))
                    {
                        job.InitialExtensionCount = 1;
                    }

                    //If a job has an extension, but the original and current due dates are identical,
                    //it indicates that the due dates were reset.
                    //Therefore, no extension should be visible for those jobs.
                    if (CheckIfTheDatesMatch(job.InitialUploadDueDate, job.OrigInitialUploadDueDate) && job.InitialExtensionCount > 0)
                    {
                        job.InitialExtensionCount = 0;
                    }

                    await _db.SaveChangesAsync(cancellationToken);

                    _logger.LogDebug("Processing complete. Exiting.");

                    return Unit.Value;
                }
                private bool HasHadAtLeastOneExtension(int currentCount, DateTime? currentDueDate, DateTime? originalDuDate)
                {
                    if (currentDueDate.HasValue && originalDuDate.HasValue)
                    {
                        return currentCount <= 0 && currentDueDate > originalDuDate;
                    }
                    return false;
                }

                private bool CheckIfTheDatesMatch(DateTime? currentDueDate, DateTime? originalDuDate)
                {
                    if (currentDueDate.HasValue && originalDuDate.HasValue)
                    {
                        return currentDueDate == originalDuDate;
                    }

                    return false;
                }
            }
        }
    }
}