﻿using System;
using System.Collections.Generic;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class GetJobRequirementsRequestDto
    {
        public JobValidationRequestJobDto Job { get; set; }

        public UserInfo UserInfo { get; set; }

        public Guid FranchiseSetId { get; set; }

        public GetJobRequirementsRequestDto()
        {
            this.Job = new JobValidationRequestJobDto();
        }

        public class JobValidationRequestJobDto
        {
            public Guid Id { get; set; }
            public int? MasterId { get; set; }
            public Guid LossTypeId { get; set; }
            public double DeductibleAmount { get; set; }
            public bool? CollectDeductible { get; set; }
            public bool ActiveInAudit { get; set; }
            public ICollection<JobValidationRequestJobContactMap> JobContacts { get; set; } = new List<JobValidationRequestJobContactMap>();
            public ICollection<JobValidationRequestJournalNote> JournalNotes { get; set; } = new List<JobValidationRequestJournalNote>();
            public ICollection<JobValidationRequestMediaMetadata> MediaMetadata { get; set; } = new List<JobValidationRequestMediaMetadata>();
            public ICollection<JobSketch> JobSketches { get; set; }
            public ICollection<JobValidationJobTriStateAnswer> JobTriStateAnswers { get; set; }
            public ICollection<JobValidationJobVisit> JobVisits { get; set; } = new List<JobValidationJobVisit>();
            public ICollection<JobValidationJobDate> JobDates { get; set; } = new List<JobValidationJobDate>();
            public ICollection<JobValidationJobArea> JobAreas { get; set; } = new List<JobValidationJobArea>();
            public ICollection<JobValidationZone> Zones { get; set; } = new List<JobValidationZone>();
            public ICollection<JobValidationRequestJobForm> JobForms { get; set; } = new List<JobValidationRequestJobForm>();
            public ICollection<JobValidationRequestJobSummary> JobSummaries { get; set; } = new List<JobValidationRequestJobSummary>();
            public ICollection<JobValidationRequestMilestone> JobMilestones { get; set; } = new List<JobValidationRequestMilestone>();
            public ICollection<ScopeLineItem> ScopeLineItems { get; set; } = new List<ScopeLineItem>();
            public long? WorkCenterJobNumber { get; set; }
            public int? CorporateJobNumber { get; set; }
            public FranchiseSet FranchiseSet { get; set; }
            public Guid FranchiseSetId { get; set; }
            public string MasterFileNumber { get; set; }
            public int? InsuranceClientId { get; set; }
            public string InsuranceClient { get; set; }
            public int? InsuranceClientParentId { get; set; }
            public string LossType { get; set; }
            public string PropertyType { get; set; }
            public string StructureType { get; set; }
            public string FacilityType { get; set; }
            public string State { get; set; }
            public string Country { get; set; }
            public string FranchiseType { get; set; }
            public string JobSource { get; set; }
            public string EstimateType { get; set; }
            public bool IsStormJob { get; set; }
            public bool IsManagedStormProject { get; set; }
            public bool IsStreamlined { get; set; }
            public string InsuranceClaimNumber { get; set; }
            public JobProgress JobProgress { get; set; }
            //public List<JobUploadDate> JobUploadDates { get; set; }
            //public List<DryingPlanEntry> DryingPlanEntries { get; set; }
            //public List<JobForm> JobForms { get; set; }
            //public List<XactwareEstimate> Estimates { get; set; }
        }

        public class ScopeLineItem
        {
            public Guid? LineItemId { get; set; }
            public Guid? JobVisitId { get; set; }
            public Guid? RoomId { get; set; }
            public decimal Quantity { get; set; }
            public DateTime? DeletedDate { get; set; }
            public JobValidationLineItem LineItem { get; set; }
        }

        public class JobValidationLineItem
        {
            public string ActivityCode { get; set; }
            public string Code { get; set; }
            public string Category { get; set; }
            public string Description { get; set; }
            public string UnitOfMeasure { get; set; }
            public ICollection<XactimateGroupCode> GroupCodes { get; set; } = new HashSet<XactimateGroupCode>();
            public XactEstimateItemType EstimateItemType { get; set; }
        }

        public class XactimateGroupCode
        {
            public int GroupCodeId { get; set; }
            public int GroupId { get; set; }
            public string CategoryId { get; set; }
            public string SelectorId { get; set; }
        }

        public class XactEstimateItemType
        {
            public int? PackContents { get; set; }
            public int? CleanContents { get; set; }
            public bool? AfterHours { get; set; }
            public bool? Supervisory { get; set; }
        }

        public class JobValidationRequestMilestone
        {
            public int? CustomerCalledExceptionId { get; set; }
            public Guid? ArrivalExceptionCommentId { get; set; }
            public Guid? VerbalBriefingExceptionCommentId { get; set; }
        }

        public class JobValidationRequestMediaMetadata
        {
            public Guid Id { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid JobId { get; set; }
            public Guid MediaTypeId { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public Guid? FormTemplateId { get; set; } /* If this has a value, it IS a Form; otherwise an Artifact */
            public string Name { get; set; }
            public string Description { get; set; }
            public string MediaPath { get; set; }
            public bool IsDeleted { get; set; }
            public bool IsForUpload { get; set; }
            public bool UploadedSuccessfully { get; set; }
            public Guid? JobSketchId { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobVisitId { get; set; }
            public Guid? ZoneId { get; set; }
            public JobSketch JobSketch { get; set; }
        }

        public class JobValidationRequestJobForm
        {
            public Guid Id { get; set; }
            public Guid FormTemplateId { get; set; }
        }

        public class JobValidationRequestJournalNote
        {
            public Guid Id { get; set; }
            public Guid? JobId { get; set; }
            public Guid? TaskId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid CategoryId { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public DateTime? ActionDate { get; set; }
            public bool IsDeleted { get; set; }
            public DateTime CreatedDate { get; set; }
            public int? RuleId { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobVisitId { get; set; }
            public string MaterialType { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
            public Guid? ZoneId { get; set; }
        }

        public class JobValidationJobTriStateAnswer
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public Job Job { get; set; }
            public Guid JobTriStateQuestionId { get; set; }
            public bool? Answer { get; set; }
        }

        public class JobValidationJobVisitTriStateAnswer
        {
            public Guid Id { get; set; }
            public Guid JobVisitId { get; set; }
            public Guid JobTriStateQuestionId { get; set; }
            public bool? Answer { get; set; }
        }

        public class JobValidationJobDate
        {
            public Guid Id { get; set; }
            public Guid JobDateTypeId { get; set; }
            public DateTime? Value { get; set; }
            public DateTime InsertionDate { get; set; }
        }

        public class JobValidationRequestJobContactMap
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public Guid ContactId { get; set; }
            public JobValidationRequestJobContact Contact { get; set; }
            public Guid JobContactTypeId { get; set; }
        }

        public class JobValidationRequestJobContact
        {
            public Guid Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string EmailAddress { get; set; }
            public ICollection<JobValidationRequestPhone> PhoneNumbers { get; set; }
            public JobValidationRequestAddress Address { get; set; }
        }

        public class JobValidationRequestPhone
        {
            public Guid Id { get; set; }
            public string PhoneNumber { get; set; }
            public string PhoneExtension { get; set; }
            public JobValidationRequestPhoneType PhoneType { get; set; }
        }

        public enum JobValidationRequestPhoneType
        {
            Home = 1,
            Mobile,
            Office,
            WorkMobile,
            OfficeFax,
            HomeFax,
            Pager,
            Other
        }

        public class JobValidationRequestAddress
        {
            public string Address1 { get; set; }
            public string Address2 { get; set; }
            public string City { get; set; }
            public string PostalCode { get; set; }
            public JobValidationRequestState State { get; set; }
            public decimal? Latitude { get; set; }
            public decimal? Longitude { get; set; }
            public AddressType AddressType { get; set; }
        }

        public class JobValidationRequestState
        {
            public Guid StateId { get; set; }
            public string StateName { get; set; }
            public Guid CountryId { get; set; }
            public string CountryName { get; set; }
        }

        public class JobValidationJobAreaMaterial
        {
            public Guid Id { get; set; }
            public Guid? RemovedOnJobVisitId { get; set; }
            public Guid ObjectId { get; set; }
            public string MaterialType { get; set; }
        }

        public class JobValidationJobVisit
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public DateTime Date { get; set; }
            public ICollection<JobValidationRequestRoom> Rooms { get; set; } = new List<JobValidationRequestRoom>();
            public ICollection<JobValidationJobVisitTriStateAnswer> JobVisitTriStateAnswers { get; set; } = new List<JobValidationJobVisitTriStateAnswer>();
            public ICollection<JobValidationJobArea> BeginVisitJobAreas { get; set; } = new List<JobValidationJobArea>();
            public ICollection<JobValidationJobArea> EndVisitJobAreas { get; set; } = new List<JobValidationJobArea>();
        }

        public class JobValidationJobArea
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public Guid JobAreaTypeId { get; set; }
            public Guid? RoomId { get; set; }
            public string Name { get; set; }
            public int SortOrder { get; set; }
            public bool IsUsedInValidation { get; set; }
            public bool IsDeleted { get; set; }
            public JobValidationRequestRoom Room { get; set; }
            public Guid? ZoneId { get; set; }
            /// <summary>
            /// This must be set if the ZoneId is being set per current requirements in WC
            /// </summary>
            public Guid? BeginJobVisitId { get; set; }
            public Guid? EndJobVisitId { get; set; }
            public Guid? PreExistingConditionsDiaryNoteId { get; set; }
            public Guid? FloorTypeId { get; set; } /* I changed this to nullable - CRS is not */
            public List<JobValidationEquipmentPlacement> EquipmentPlacements { get; set; } = new List<JobValidationEquipmentPlacement>();
            public List<JobValidationJobAreaMaterial> JobAreaMaterials { get; set; } = new List<JobValidationJobAreaMaterial>();
            public bool? HasFlooringType { get; set; }
        }

        public class JobValidationEquipmentPlacement {
            public Guid Id { get; set; }
            public Guid EquipmentId { get; set; }
            public Guid JobAreaId { get; set; }
            public DateTime BeginDate { get; set; }
            public DateTime? EndDate { get; set; }
            public bool IsUsedInValidation { get; set; }
            public JobValidationEquipment Equipment { get; set; }
        }

        public class JobValidationEquipment
        {
            public Guid Id { get; set; }
            public string AssetNumber { get; set; }
            public string SerialNumber { get; set; }
            public Guid FranchiseSetId { get; set; }
            public string Notes { get; set; }
            public decimal? Amps { get; set; }
            public bool IsDeleted { get; set; }
            public Guid EquipmentModelId { get; set; }
            public JobValidationEquipmentModel EquipmentModel { get; set; }

        }

        public class JobValidationEquipmentModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public string ManufacturerName { get; set; }
            public bool IsSymbol { get; set; }
            public Guid? FranchiseSetId { get; set; }
            public bool IsValidModel { get; set; }
            public string Description { get; set; }
            public String ManufacturerModelNumber { get; set; }
            public string Notes { get; set; }
            public Guid EquipmentTypeId { get; set; }
            public JobValidationEquipmentType EquipmentType { get; set; }
            public int CubicFeetPerMinute { get; set; }
            public int PintsPerDay { get; set; }
            public decimal Amps { get; set; }
            public bool IsPenetratingMeter { get; set; }
            public bool IsNotPenetratingMeter { get; set; }
            public bool IsCurrent { get; set; }
            public bool IsDeleted { get; set; }
        }

        public class JobValidationEquipmentType
        {
            public Guid Id { get; set; }
            public Guid? BaseEquipmentTypeId { get; set; }
            public string Name { get; set; }
            public bool IsDeleted { get; set; }
            public Guid? FranchiseSetId { get; set; }
        }


        public class JobValidationRequestJobSummary
        {
            public double? JobRevenue { get; set; }
        }

        public class JobValidationRequestRoom
        {
            public Guid Id { get; set; }
            public Guid FloorTypeId { get; set; }
            public Guid? PreExistingConditionsDiaryNoteId { get; set; }
            public ICollection<JobValidationRequestRoomFlooringTypeAffected> RoomFlooringTypesAffected { get; set; } = new List<JobValidationRequestRoomFlooringTypeAffected>();
        }

        public class JobValidationRequestRoomFlooringTypeAffected
        {
            public Guid Id { get; set; }
            public Guid RoomId { get; set; }
            public Guid FlooringTypeId { get; set; }
            public string OtherText { get; set; }
            public bool? IsSalvageable { get; set; }
            public bool? IsPadRestorable { get; set; }
            public decimal? TotalSquareFeet { get; set; }
            public decimal? AffectedSquareFeet { get; set; }
            public decimal? AffectedPercentage { get; set; }
            public decimal? SavedSquareFeet { get; set; }
            public decimal? SavedPercentage { get; set; }
            public bool IsDeleted { get; set; }
        }

        public class JobValidationZone
        {
            public Guid Id { get; set; }
            public Guid ZoneTypeId { get; set; }
            public string Name { get; set; }
            public Guid JobId { get; set; }
            public string Description { get; set; }
            public Guid AirMoverCalculationTypeId { get; set; }
            public Guid WaterClassId { get; set; }
            public Guid WaterCategoryId { get; set; }
            public Guid? SketchMediaContentId { get; set; }
            public bool WaterClassOverridden { get; set; }
            public bool? CanValidateDehuCapacity { get; set; }
            public int? RequiredDehuCapacity { get; set; }
            public int? AchievedDehuCapacity { get; set; }
            public JobValidationRequestWaterCategory WaterCategory { get; set; }
            public JobValidationRequestWaterClass WaterClass { get; set; }
        }

        public class JobValidationRequestWaterCategory
        {
            public Guid Id { get; set; }
            public int? Ordinal { get; set; }
        }

        public class JobValidationRequestWaterClass
        {
            public Guid Id { get; set; }
            public int Severity { get; set; }
        }

        public class FranchiseSet
        {
            public TimeZone TimeZone { get; set; }
        }

        public class TimeZone
        {
            public string WindowsTimeZoneIdentifier { get; set; }
        }

    }
}