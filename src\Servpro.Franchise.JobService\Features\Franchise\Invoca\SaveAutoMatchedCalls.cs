﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NuGet.Protocol;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class SaveAutoMatchedCalls
    {
        public class Command : IRequest<OperationResult>
        {
            public List<MatchedCall> Calls { get; set; } = new List<MatchedCall>();

        }

        public class MatchedCall
        {
            public Guid JobId { get; set; }
            public Guid CallId { get; set; }
        }

        public class OperationResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
        }

        public class Handler : IRequestHandler<Command, OperationResult>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<SaveAutoMatchedCalls> _logger;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;


            public Handler(ILogger<SaveAutoMatchedCalls> logger,
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfoAccessor,
                JobDataContext db)
            {
                _logger = logger;
                _lookupServiceClient = lookupServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _db = db;
            }

            public async Task<OperationResult> Handle(Command request, CancellationToken cancellationToken)
            {
                var opResult = CheckCommandData(request);

                if (!opResult.Success)
                    return opResult;

                _logger.LogInformation("Saving Invoca Auto Matched Calls");
                var user = _userInfoAccessor.GetUserInfo();

                try
                {
                    foreach (var updCall in request.Calls)
                        await UpdateCallsAndJobs(updCall, user, cancellationToken);

                    await _db.SaveChangesAsync(cancellationToken);
                }
                catch (Exception ex)
                {
                    opResult.Success = false;
                    opResult.ErrorMessage = ex.Message;
                }             

                return opResult;
            }

            public OperationResult CheckCommandData(Command incomingData)
            {
                bool proceed = true;
                var errorList = new List<MatchedCall>();

                if (!incomingData.Calls.Any())
                {
                    _logger.LogWarning("No Matched calls received to save");
                    proceed = false;
                }
                foreach (var updCall in incomingData.Calls)
                {
                    if (updCall.CallId == Guid.Empty || updCall.JobId == Guid.Empty)
                    {
                        _logger.LogWarning("CallId or JobId is empty for Match: {id}", incomingData.Calls.IndexOf(updCall));
                        proceed = false;
                        errorList.Add(updCall);
                        continue;
                    }
                }
                if (!proceed)
                {
                    return new OperationResult()
                    {
                        Success = false,
                        ErrorMessage = JsonSerializer.Serialize(errorList)
                    };
                }
                else
                    return new OperationResult() {Success= true };
            }

            public async Task UpdateCallsAndJobs(MatchedCall request,
                UserInfo user,
                CancellationToken cancellationToken)
            {
                var call = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => x.Id == request.CallId, cancellationToken);
                if (call == null)
                {
                    _logger.LogInformation("Call : {call}, not found", request.CallId);
                    throw new ResourceNotFoundException();
                }

                call.Disposition = CallDispositionTypes.LinkWCLead;
                call.DispositionSelectionDateTime = DateTime.UtcNow;
                call.DispositionSelectionUserName = user.Username ?? "System";

                if (request.JobId != Guid.Empty)
                {
                    var job = await _db.Jobs.FirstOrDefaultAsync(k => k.Id == request.JobId, cancellationToken);
                    if (job == null)
                    {
                        _logger.LogInformation("Job : {job}, not found", request.JobId);
                        throw new ResourceNotFoundException();
                    }
                    job.CallDisposition = CallDispositionTypes.LinkWCLead;
                    job.CallRecordingId = call.CallRecordingId;
                    call.AssociatedJobId = job.Id;
                    call.AssociatedJobLastUpdatedDateTime = DateTime.UtcNow;
                }

            }
        }
    }
}
