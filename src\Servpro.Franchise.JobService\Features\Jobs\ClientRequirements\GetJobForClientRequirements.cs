﻿using MediatR;

using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Validation;
using Servpro.Franchise.JobService.Features.Jobs.JobSourceProvider;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements
{
    public class GetJobForClientRequirements
    {
        public class Query : IRequest<GetJobRequirementsRequestDto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Handler : IRequestHandler<Query, GetJobRequirementsRequestDto>
        {
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IXactServiceClient _xactServiceClient;
            private readonly IClientRequirementsService _clientRequirementsService;
            private readonly ILogger<GetJobForClientRequirements> _logger;
            private readonly IJobSourcerProvider _jobSourceProvider;

            public Handler(
                IFranchiseServiceClient franchiseServiceClient,
                IXactServiceClient xactServiceClient,
                IClientRequirementsService clientRequirementsService,
                ILogger<GetJobForClientRequirements> logger,
                IJobSourcerProvider jobSourceProvider)
            {
                _franchiseServiceClient = franchiseServiceClient;
                _xactServiceClient = xactServiceClient;
                _clientRequirementsService = clientRequirementsService;
                _logger = logger;
                _jobSourceProvider = jobSourceProvider;
            }

            public async Task<GetJobRequirementsRequestDto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _clientRequirementsService.GetJobAsync(request.JobId, cancellationToken);

                if (job is null)
                {
                    _logger.LogWarning("job {jobId} not found.", request?.JobId);
                    return null;
                }

                return await _clientRequirementsService.CreateJobRequirementsRequestDto(job, cancellationToken);
            }
        }

    }
}