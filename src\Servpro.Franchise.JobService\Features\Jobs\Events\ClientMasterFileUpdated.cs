﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.CorporateService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Client;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ClientMasterFileUpdated
    {
        #region Event
        public class Event : ClientMasterFileUpdatedEvent, IRequest
        {
            public Event(ClientMasterFileUpdatedDto client, Guid correlationId) : base(client, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly ICorporateServiceClient _corporateServiceClient;

            public Handler(JobDataContext db,
                ILogger<Handler> logger,
                ICorporateServiceClient corporateServiceClient)
            {
                _db = db;
                _logger = logger;
                _corporateServiceClient = corporateServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{Event} receieved", nameof(ClientMasterFileUpdatedEvent));

                var clientMasterFileRecords = (await _corporateServiceClient
                    .GetClientMasterFileRecords(request.CorrelationId, cancellationToken)).ToList();
                _logger.LogInformation("Processing {clientCount} insurance clients", clientMasterFileRecords.Count);

                var clientIds = clientMasterFileRecords.Select(x => x.Id).ToList();
                var clientNumbers = clientMasterFileRecords.Select(x => x.InsuranceNumber).ToList();
                var existingClients = await _db.InsuranceClients
                    .Where(x => clientIds.Contains(x.Id) || clientNumbers.Contains(x.InsuranceNumber))
                    .ToListAsync(cancellationToken);

                foreach (var clientMasterFileRecord in clientMasterFileRecords)
                {
                    using var clientScope = _logger.BeginScope(
                        "{clientId}{insuranceNumber}",
                        clientMasterFileRecord.Id,
                        clientMasterFileRecord.InsuranceNumber);
                    _logger.LogInformation("Processing client.");
                    _logger.LogDebug("Client: {@clientMasterFileRecord}", clientMasterFileRecord);
                    var insuranceClient = existingClients
                        .FirstOrDefault(x => x.Id == clientMasterFileRecord.Id || x.InsuranceNumber == clientMasterFileRecord.InsuranceNumber);

                    if (insuranceClient == null)
                    {
                        insuranceClient = GetInsuranceFromMemoryDB(clientMasterFileRecord.Id, clientMasterFileRecord.InsuranceNumber);
                    }

                    if (insuranceClient == null)
                    {
                        _logger.LogInformation("New client found. Mapping to InsuranceClient");
                        var newClient = MapClient(clientMasterFileRecord);
                        _logger.LogDebug("New client: {@client}", newClient);
                        await _db.InsuranceClients.AddAsync(newClient, cancellationToken);
                    }
                    else if (insuranceClient.Id != clientMasterFileRecord.Id)// This is to handle when a placeholder insurance client was added by ClientGroupMaster.
                    {
                        var parentInsuranceNumber = insuranceClient.ParentInsuranceNumber;
                        _db.InsuranceClients.Remove(insuranceClient);
                        _db.InsuranceClients.Add(MapClient(clientMasterFileRecord, parentInsuranceNumber));
                    }
                    else
                    {
                        _logger.LogInformation("Client already exist. Updating client information");
                        insuranceClient.Name = clientMasterFileRecord.Name;
                        insuranceClient.InsuranceNumber = clientMasterFileRecord.InsuranceNumber;
                        insuranceClient.IsActive = clientMasterFileRecord.IsActive;
                        insuranceClient.IsLocalAuditRequired = clientMasterFileRecord.AuditLocalJobs;
                        insuranceClient.IsAuditRequired = clientMasterFileRecord.AuditJobs;
                        insuranceClient.ModifiedDate = clientMasterFileRecord.ModifiedDate;
                    }
                }

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{Event} completed", nameof(ClientMasterFileUpdatedEvent));

                return Unit.Value;
            }

            private InsuranceClient MapClient(GetClientMasterFileRecordsDto clientMasterFileRecord, int? parentInsuranceNumber = null)
                => new InsuranceClient
                {
                    Id = clientMasterFileRecord.Id,
                    Name = clientMasterFileRecord.Name,
                    InsuranceNumber = clientMasterFileRecord.InsuranceNumber,
                    ParentInsuranceNumber = parentInsuranceNumber ?? clientMasterFileRecord.InsuranceNumber,
                    IsActive = clientMasterFileRecord.IsActive,
                    IsLocalAuditRequired = clientMasterFileRecord.AuditLocalJobs,
                    IsAuditRequired = clientMasterFileRecord.AuditJobs,
                    CreatedDate = clientMasterFileRecord.CreatedDate
                };

            private InsuranceClient GetInsuranceFromMemoryDB(Guid? insuranceId, int? insuranceNumber) //Check in memory changes in case a client exists more than one time before saving changes to DB.
            {
                if (!insuranceId.HasValue && !insuranceNumber.HasValue)
                {
                    return null;
                }

                return _db.ChangeTracker.Entries()
                                   .Where(x => x.State == EntityState.Added && x.Entity is InsuranceClient)
                                   .Select(x => x.Entity as InsuranceClient)
                                   .FirstOrDefault(ic => ic.Id == (insuranceId ?? Guid.Empty) || ic.InsuranceNumber == (insuranceNumber ?? -1));
            }
        }
        #endregion Handler
    }
}
