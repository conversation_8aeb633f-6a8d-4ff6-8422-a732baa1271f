﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetEquipmentUsage
    {
        public class Query : IRequest<EquipmentUsageDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }

        public class Handler : IRequestHandler<Query, EquipmentUsageDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GenerateDryingReport> _logger;
            private readonly IEquipmentServiceClient _equipmentServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                IEquipmentServiceClient equipmentServiceClient,
                ILogger<GenerateDryingReport> logger)
            {
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _equipmentServiceClient = equipmentServiceClient;
                _logger = logger;
            }

            public async Task<EquipmentUsageDto> Handle(Query request, CancellationToken cancellationToken)
            {
                using (new PerformanceTimer($"DryingReport - GetEquipmentUsage: jobId: {request.Job.Id}", _logger))
                {
                    var job = await GetJobAsync(request.Job.Id, cancellationToken);

                    var equipmentPlacementIds = job.JobAreas.SelectMany(a => a.EquipmentPlacements).Select(e => e.Id);
                    var equipmentPlacementsReadings = await _context.EquipmentPlacementReadings
                        .Where(e => equipmentPlacementIds.Contains(e.EquipmentPlacementId))
                        .Select(x => new
                        {
                            x.EquipmentPlacementId,
                            x.JobVisitId,
                            x.HourCount
                        }).ToListAsync(cancellationToken);

                    var visitDates = job.JobVisits.Select(s => s.Date);
                    if (!visitDates.Any())
                    {
                        _logger.LogDebug("DryingReportLog - GetEquipmentUsage - no visits for jobId: {Id}", job.Id);
                        return new EquipmentUsageDto();
                    }

                    var firstDay = visitDates.Min();
                    var lastDay = visitDates.Max();

                    var dayCount = (int)Math.Round((lastDay - firstDay).TotalDays, 0) + 1;
                    var days = Enumerable.Range(0, dayCount).Select(x => firstDay.AddDays(x));

                    var daysWithEod = days.Select(x => new { DayBegin = x.Date, DayEnd = x.AddDays(1).AddMilliseconds(-1) });
                    var model = new EquipmentUsageDto();

                    _logger.LogDebug("DryingReportLog - GetEquipmentUsage - getting placements for jobId: {Id}", job.Id);

                    var baseEquipmentTypes = await _equipmentServiceClient.GetBaseEquipmentTypeAsync(job.FranchiseSetId, cancellationToken);

                    var placements = (from ep in job.JobAreas.SelectMany(ja => ja.EquipmentPlacements)
                                      let equipment = ep.Equipment
                                      let equipmentModel = equipment.EquipmentModel
                                      let equipmentType = equipmentModel.EquipmentType
                                      let isDehu = equipmentType.BaseEquipmentTypeId == BaseEquipmentTypes.Dehumidifier
                                      let jobArea = ep.JobArea
                                      let zone = jobArea?.Zone
                                      let dehuCalcType = !isDehu || zone == null ? null : equipment.DehuCalculationTypeAbbreviation
                                      select new
                                      {
                                          BadEquipmentPlacement = zone == null,
                                          ZoneName = zone == null ? String.Empty : zone.Name,
                                          RoomName = ep.JobArea.Name,
                                          EquipmentType = baseEquipmentTypes.FirstOrDefault(bet => bet.Id == equipmentType.BaseEquipmentTypeId)?.Name,
                                          EquipmentModelName = equipment.EquipmentModel.Name,
                                          DehuInfo = !isDehu ? null : new
                                          {
                                              Rating = equipment.IsCfmVolumeRate ? equipment.CubicFeetPerMinute : equipment.PintsPerDay,
                                              RatingType = dehuCalcType,
                                              equipment.AssetNumber,
                                              HourCounters = equipmentPlacementsReadings.Where(eqr => eqr.EquipmentPlacementId == ep.Id)
                                          },
                                          ep.BeginDate,
                                          ep.EndDate
                                      })
                                 .Where(x => !x.BadEquipmentPlacement)
                                 .ToList();

                    var placementCounts = from placement in placements
                                          join d in daysWithEod on 1 equals 1
                                          select new
                                          {
                                              placement.ZoneName,
                                              placement.EquipmentType,
                                              Day = d.DayBegin,
                                              CountIt = placement.BeginDate <= d.DayEnd && (!placement.EndDate.HasValue || placement.EndDate.Value.Date >= d.DayEnd.Date)
                                          };

                    var placementCountsSummarized = from placementCount in placementCounts
                                                    group placementCount by new { placementCount.ZoneName, placementCount.EquipmentType, placementCount.Day } into placementCountGroup
                                                    select new
                                                    {
                                                        placementCountGroup.Key.ZoneName,
                                                        placementCountGroup.Key.EquipmentType,
                                                        placementCountGroup.Key.Day,
                                                        Count = placementCountGroup.Count(x => x.CountIt)
                                                    };

                    var byZone = from placementCount in placementCountsSummarized
                                 group placementCount by placementCount.ZoneName into placementCountGroup
                                 select new ZoneEquipmentUsage
                                 {
                                     ZoneName = placementCountGroup.Key,
                                     EquipmentPlacementCountByDay =
                                         placementCountGroup.Select(x => new EquipmentCountDetail
                                         {
                                             EquipmentType = x.EquipmentType,
                                             Day = x.Day.GetLocalFranchiseDateTime(request.FranchiseTimeZone),
                                             Count = x.Count
                                         }).ToList()
                                 };

                    model.ZoneEquipmentUsages = byZone.OrderBy(z => z.ZoneName).ToList();
                    _logger.LogDebug("DryingReportLog - GetEquipmentUsage - getting zoneEquipmentUsages for jobId: {Id}, ZoneEquipmentUsages: {ZoneEquipmentUsages}", job.Id, model.ZoneEquipmentUsages);
                    foreach (var zoneEquipmentUsage in model.ZoneEquipmentUsages)
                    {
                        _logger.LogDebug("DryingReportLog - GetEquipmentUsage - getting equipmentUsageFor zoneName: {ZoneName} for jobId: {Id}", zoneEquipmentUsage.ZoneName, job.Id);
                        var equipmentUsageVisitsForZone = job.JobVisits.Select(jobVisit => new EquipmentUsageVisit
                        {
                            JobVisitDateTime = jobVisit.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone),
                            Technician = jobVisit.EmployeeInitials,
                            RoomEquipmentUsageItems =
                                (from placement in placements
                                 let jobVisitDate = jobVisit.Date
                                 let visitsBeforePlacement = visitDates.Where(jvd => jvd.Date <= placement.BeginDate).ToList()
                                 where visitsBeforePlacement.Any() && jobVisitDate >= visitsBeforePlacement.Max(jvd => jvd.Date)
                                 where !placement.EndDate.HasValue || placement.EndDate.Value.Date > jobVisitDate.Date
                                 select placement)
                                .Where(p => p.ZoneName == zoneEquipmentUsage.ZoneName)
                                .GroupBy(p => p.RoomName)
                                .Select(g => new RoomEquipmentUsageItem
                                {
                                    RoomName = g.Key,
                                    AirMoverCount = g.Count(x => x.EquipmentType == "Air Mover"),
                                    DehuUsageItems = g.Where(x => x.EquipmentType == nameof(BaseEquipmentTypes.Dehumidifier))
                                                        .Select(d => new DehuUsageItem
                                                        {
                                                            DehuModel = d.EquipmentModelName,
                                                            DehuRating = d.DehuInfo.Rating,
                                                            AssetNumber = d.DehuInfo.AssetNumber,
                                                            DehuHourReading = d.DehuInfo
                                                                .HourCounters.Any(dhr => dhr.JobVisitId == jobVisit.Id)
                                                                ? d.DehuInfo.HourCounters.First(dhr => dhr.JobVisitId == jobVisit.Id).HourCount
                                                                : (int?)null,
                                                            DehuRatingType = d.DehuInfo.RatingType == "PPD" ? EquipmentUsageDehuRatingType.Ppd : EquipmentUsageDehuRatingType.Cfm
                                                        }).ToList()
                                }).ToList()
                        }).ToList();

                        zoneEquipmentUsage.EquipmentUsageVisits = equipmentUsageVisitsForZone;
                    }

                    var firstVisit = job.JobVisits.OrderBy(x => x.Date).FirstOrDefault();
                    var lastVisit = job.JobVisits.OrderByDescending(x => x.Date).FirstOrDefault();
                    model.FirstJobVisitDate = firstVisit?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);
                    model.LastJobVisitDate = lastVisit?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);

                    return model;
                }
            }

            public async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {jobId}");

                job.JobAreas = await _context.JobAreas.Where(x => x.JobId == job.Id)
                    .ToListAsync(cancellationToken);

                var jobAreaIds = job.JobAreas.Select(x => x.Id).ToList();

                var equipmentPlacements = await _context.EquipmentPlacements
                    .Where(ep => jobAreaIds.Contains(ep.JobAreaId))
                    .ToListAsync(cancellationToken: cancellationToken);

                var equipmentIds = equipmentPlacements.Select(x => x.EquipmentId).ToList();

                var equipments = await _context.Equipments
                    .TagWith(RemoveLastOrderByInterceptor.QueryTag)
                    .Include(x => x.EquipmentModel)
                    .ThenInclude(e => e.EquipmentType)
                    .Where(e => equipmentIds.Contains(e.Id))
                    .ToListAsync(cancellationToken);

                foreach (var ja in job.JobAreas)
                {
                    ja.EquipmentPlacements = equipmentPlacements.Where(ep => ep.JobAreaId == ja.Id).ToList();

                    foreach (var ep in ja.EquipmentPlacements)
                    {
                        ep.Equipment = equipments.FirstOrDefault(e => ep.EquipmentId == e.Id);
                    }
                }

                var zoneIds = job.JobAreas.Select(x => x.ZoneId).ToList();
                var zones = await _context.Zones.Where(z => zoneIds.Contains(z.Id))
                    .ToListAsync(cancellationToken);

                foreach (var ja in job.JobAreas)
                {
                    ja.Zone = zones.FirstOrDefault(z => ja.ZoneId == z.Id);
                }

                return job;
            }
        }
    }
}
