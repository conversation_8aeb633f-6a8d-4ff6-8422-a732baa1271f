﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Tasks
{

    [Route("api/jobs/{jobId}/tasks")]
    public class TasksController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<TasksController> _logger;

        public TasksController(
            IMediator mediator,
            ILogger<TasksController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<GetTasks.Dto>>> GetAsync(Guid jobId, [FromQuery]GetTasks.Query query)
        {
            query.JobId = jobId;
            var response = await _mediator.Send(query);
            return Ok(response);
        }

        [HttpGet("taskId/{taskId}")]
        public async Task<ActionResult<GetTasks.Dto>> GetTaskAsync(Guid jobId, Guid taskId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (taskId == Guid.Empty)
                return BadRequest(nameof(taskId));

            try
            {
                var response = await _mediator.Send(new GetTask.Query { JobId = jobId, TaskId = taskId });
                return Ok(response);
            }
            catch (ResourceNotFoundException)
            {
                _logger.LogWarning("Task not found: {taskId}", taskId);
                return NotFound();
            }
          
        }
    }
}
