﻿using System;
using System.Collections.Generic;

using Amazon.DynamoDBv2.Model;

namespace Servpro.Franchise.JobService.Common
{
    public static class DynamoDbExtensions
    {
        public static TDynamoType GetDynamoValue<TDynamoType>(this Dictionary<string, AttributeValue> item, string attrName)
        {
            TDynamoType dynamoValue = default;
            if (item?.ContainsKey(attrName) == true && !item[attrName].NULL)
            {
                //todo: add more to this, create an enum with dynamo types.
                if (typeof(TDynamoType) == typeof(string))
                {
                    dynamoValue = (TDynamoType)(object)item[attrName]?.S;
                }
                else if (typeof(TDynamoType) == typeof(int))
                {
                    if (int.TryParse(item[attrName]?.N, out var value))
                    {
                        dynamoValue = (TDynamoType)(object)value;
                    }
                }
                else if (typeof(TDynamoType) == typeof(bool))
                {
                    bool value = item[attrName]?.BOOL ?? false;
                    dynamoValue = (TDynamoType)(object)value;
                }
                else if (typeof(TDynamoType) == typeof(long))
                {
                    if (long.TryParse(item[attrName]?.N, out var value))
                    {
                        dynamoValue = (TDynamoType)(object)value;
                    }
                }
                else if (typeof(TDynamoType) == typeof(Guid))
                {
                    if (Guid.TryParse(item[attrName]?.S, out Guid value))
                    {
                        dynamoValue = (TDynamoType)(object)value;
                    }
                }
                else if (typeof(TDynamoType) == typeof(DateTime))
                {
                    if (DateTime.TryParse(item[attrName]?.S, out DateTime value))
                    {
                        dynamoValue = (TDynamoType)(object)value;
                    }
                }
                else if (typeof(TDynamoType) == typeof(AttributeValue))
                {
                    dynamoValue = (TDynamoType)(object)item[attrName];
                }

                else if (typeof(TDynamoType) == typeof(Dictionary<string, AttributeValue>))
                {
                    dynamoValue = (TDynamoType)(object)item[attrName].M;
                }

            }
            return dynamoValue;
        }

        public static Dictionary<string, AttributeValue> GetDynamoMap(this Dictionary<string, AttributeValue> item, string attrName)
        {
            return item.GetDynamoValue<Dictionary<string, AttributeValue>>(attrName);
        }

        public static bool AttributeValueIsNotNull(this Dictionary<string, AttributeValue> item, string index)
        {
            return item.ContainsKey(index) && !item[index].NULL;
        }
    }
}