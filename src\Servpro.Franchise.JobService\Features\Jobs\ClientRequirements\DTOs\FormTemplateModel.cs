﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class FormTemplateModel
    {
        public Guid FormTemplateId { get; set; }
        public List<FormFieldModel> FormFields { get; set; }
        public List<JobFormModel> JobForms { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsApproved { get; set; }
        public string InsuranceClient { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string FormalLanguage { get; set; }
        public int? Copies { get; set; }
        public bool IsWaterForm { get; set; }
        public bool IsFireForm { get; set; }
        public bool IsMoldForm { get; set; }
        public bool IsWaterFormRequired { get; set; }
        public bool IsFireFormRequired { get; set; }
        public bool IsMoldFormRequired { get; set; }
        public string FileType { get; set; }
        public int? DisplayOrder { get; set; }
        public string Version { get; set; }
        public Guid? RelatedForm { get; set; }
        public string LinkedPage { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public Guid? RelatedForm2 { get; set; }
        public Guid? RelatedForm3 { get; set; }
        public bool? IsRequiredForCommercialJob { get; set; }
        public bool? IsRequiredForResidentialJob { get; set; }
        public bool? IsRequiredForAllLossTypes { get; set; }
        public bool? IsAvailableInFirstNotice { get; set; }
        public string CommercialClient { get; set; }
        // Custom Properties.
        public bool IsDocumentUploaded { get; set; }
        public string FormCategoryDesc { get; set; }
        public int GroupOrder { get; set; }
        public bool IsForUpload { get; set; }
        public bool IsRequired { get; set; }
        public bool IsXactUploaded { get; set; }
        public Guid MediaId { get; set; }
        public DateTime? SignedDate { get; set; }
        public bool UploadedSuccessfully { get; set; }

        public class FormFieldModel
        {

        }

        public class JobFormModel
        {

        }
    }
}