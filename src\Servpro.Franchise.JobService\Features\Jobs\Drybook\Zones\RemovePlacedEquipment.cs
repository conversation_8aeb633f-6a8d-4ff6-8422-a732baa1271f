﻿using FluentValidation;
using MediatR;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones
{
    public class RemovePlacedEquipment
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
            public IEnumerable<Guid> EquipmentPlacementIds { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.RoomId).NotEmpty();
                RuleFor(x => x.EquipmentPlacementIds).NotEmpty();
                RuleForEach(x => x.EquipmentPlacementIds)
                    .Must(q => q != Guid.Empty);
            }
        }
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            public readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo)
            {
                _userInfo = userInfo;
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();

                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                    .ThenInclude(x => x.EquipmentPlacements)
                    .FirstOrDefaultAsync(x => x.FranchiseSetId == userInfo.FranchiseSetId.Value
                        && x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == request.RoomId);

                if (jobArea is null)
                    throw new ResourceNotFoundException($"RoomId not found: {request.RoomId}");

                var equipmentPlacementToRemove = jobArea.EquipmentPlacements
                    .Where(x => request.EquipmentPlacementIds.Contains(x.Id))
                    .ToList();
                _context.EquipmentPlacements.RemoveRange(equipmentPlacementToRemove);

                var removedEvent = GenerateEvent(equipmentPlacementToRemove, jobArea, userInfo);
                _context.OutboxMessages.Add(removedEvent);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            OutboxMessage GenerateEvent(
                IEnumerable<Models.Drybook.EquipmentPlacement> equipmentPlacements,
                Models.Drybook.JobArea jobArea,
                UserInfo userInfo)
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();
                var equipmentPlacedEvent = MapToEvent(equipmentPlacements, jobArea, userInfo, correlationId);
                return new OutboxMessage(equipmentPlacedEvent.ToJson(),
                                   nameof(EquipmentRemovedFromRoomEvent), correlationId, userInfo.Username);
            }

            EquipmentRemovedFromRoomEvent MapToEvent(
                IEnumerable<Models.Drybook.EquipmentPlacement> equipmentPlacements,
                Models.Drybook.JobArea jobArea,
                UserInfo userInfo,
                Guid correlationId)
            {
                var equipmentPlacedDto = new EquipmentRemovedFromRoomDto
                {
                    JobId = jobArea.JobId,
                    FranchiseSetId = userInfo.FranchiseSetId.Value,
                    JobAreaId = jobArea.Id,
                    RoomId = jobArea.RoomId.Value,
                    UserId = userInfo.Id,
                    Username = userInfo.Username,
                    Removals = equipmentPlacements.Select(x => new EquipmentRemovedFromRoomDto.RemovalDto
                    {
                        Id = x.Id,
                        EquipmentId = x.EquipmentId
                    })
                };
                var equipRemovedFromRoomEvent = new EquipmentRemovedFromRoomEvent(equipmentPlacedDto, correlationId);
                return equipRemovedFromRoomEvent;
            }
        }
    }
}
