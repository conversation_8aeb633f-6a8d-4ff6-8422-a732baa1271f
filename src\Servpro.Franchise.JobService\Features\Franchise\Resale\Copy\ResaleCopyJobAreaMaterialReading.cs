﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobAreaMaterialReading
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobAreaMaterialResult { get; set; }
            public ProcessEntityResult JobVisitResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobAreaMaterialIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobAreaMaterialReading>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobAreaMaterialReading> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobAreaMaterialReading> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobAreaMaterialReading));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobAreaMaterialIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var areaMaterialReadingTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedAreaMaterialReadingIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(areaMaterialReadingTargetIds, 
                    GetJobAreaMaterialReadingIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobAreaMaterialReading, ResaleJobAreaMaterialReading>(
                    request.ResaleId,
                    areaMaterialReading =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobAreaMaterialResult.FailedEntities.Contains(areaMaterialReading.JobAreaMaterialId))
                            failedDependencies.Add((nameof(JobAreaMaterial), areaMaterialReading.JobAreaMaterialId));
                        if (request.JobVisitResult.FailedEntities.Contains(areaMaterialReading.JobVisitId))
                            failedDependencies.Add((nameof(JobVisit), areaMaterialReading.JobVisitId));

                        return failedDependencies;
                    },
                    alreadyCopiedAreaMaterialReadingIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobAreaMaterialReadings.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobAreaMaterialReading>> GetSourceEntitiesAsync(List<Guid> jobAreaMaterialIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobAreaMaterialReadings = await _context.JobAreaMaterialReadings
                    .Where(jamr => jobAreaMaterialIds.Contains(jamr.JobAreaMaterialId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobAreaMaterialReadings.Count);
                return jobAreaMaterialReadings;
            }

            private async Task<List<Guid>> GetJobAreaMaterialReadingIdsAsync(List<Guid?> areaMaterialReadingTargetIds, 
                CancellationToken cancellationToken)
            {
                return await _context.JobAreaMaterialReadings
                    .Where(x => areaMaterialReadingTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
