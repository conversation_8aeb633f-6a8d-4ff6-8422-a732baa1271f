﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.LossAddress
{
    public class GetJobLossAddress
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Dto(string address1, string address2, string city, string postalCode, string stateAbbreviation, string countryCode)
            {
                Address1 = address1;
                Address2 = address2;
                City = city;
                PostalCode = postalCode;
                StateAbbreviation = stateAbbreviation;
                CountryCode = countryCode;
            }
            public string Address1 { get; set; }
            public string Address2 { get; set; }
            public string City { get; set; }
            public string PostalCode { get; set; }
            public string StateAbbreviation { get; set; }
            public string CountryCode { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _jobDataContext;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobReadOnlyDataContext jobDataContext, ILookupServiceClient lookupServiceClient)
            {
                _jobDataContext = jobDataContext;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var job = await _jobDataContext.Jobs.FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return new Dto(
                    job.LossAddress?.Address1,
                    job.LossAddress?.Address2,
                    job.LossAddress?.City,
                    job.LossAddress?.PostalCode,
                    job.LossAddress?.State?.StateAbbreviation,
                    lookups.Countries.FirstOrDefault(c => c.Id == job.LossAddress.State.CountryId)?.Alpha2Code
                    );
            }
        }
    }
}
