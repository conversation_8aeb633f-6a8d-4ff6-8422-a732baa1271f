﻿using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeContactInformation : IJobMergeSection
    {
        public void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            targetJob.LossAddress = sourceJob.LossAddress;
            targetJob.AssociatedErpId = sourceJob.AssociatedErpId;
            targetJob.ReferredById = sourceJob.ReferredById;
            targetJob.ReportedById = sourceJob.ReportedById;
            targetJob.SiteAppointmentById = sourceJob.SiteAppointmentById;
            MergeJobContacts(sourceJob, targetJob);
            MergeJobBusinesses(sourceJob, targetJob);
        }

        private void MergeJobContacts(<PERSON> sourceJob, <PERSON> targetJob)
        {
            var sourceContacts = sourceJob.JobContacts;
            var targetContacts = targetJob.JobContacts;
            var clientContactTypes = new List<Guid>
                { JobContactTypes.Agent, JobContactTypes.Adjuster, JobContactTypes.ReferralSource };

            // what was the purpose of this?
            var jobContactsToMerge = sourceContacts
                            // ignore referral source as this is a special case
                            .Where(x => !clientContactTypes.Contains(x.JobContactTypeId))
                            .Select(x => MapJobContact(x, targetJob.Id));

            foreach (var jobContact in jobContactsToMerge)
            {
                targetJob.JobContacts.Add(jobContact);
            }
            foreach(var jobContactTypeId in clientContactTypes)
            {
                ProcessSpecialContacts(sourceJob, targetJob, jobContactTypeId);
            }
        }

        /// <summary>
        /// a. If the source project has the contact but the target doesn’t then copy over the source contact to the target project.
        /// b.If both the source project and target project have the referral contact, then remove the target referral contact and copy over the referral contact from the source.
        /// c.If the target has the referral contact and the source does not then keep the target contact. 
        /// </summary>
        /// <param name="sourceJob"></param>
        /// <param name="targetJob"></param>
        /// <param name="jobContactTypeId"></param>
        private void ProcessSpecialContacts(Job sourceJob, Job targetJob, Guid jobContactTypeId)
        {
            var sourceContacts = sourceJob.JobContacts
                .Where(x => x.JobContactTypeId == jobContactTypeId)
                .ToList();
            var targetContacts = targetJob.JobContacts
                .Where(x => x.JobContactTypeId == jobContactTypeId)
                .ToList();
            // WC3-4823
            // a. If the source project has the contact but the target doesn’t then copy over the source contact to the target project. 
            if (sourceContacts.Any()
                && !targetContacts.Any())
            {
                foreach (var sourceContact in sourceContacts)
                {
                    targetJob.JobContacts.Add(MapJobContact(sourceContact, targetJob.Id));
                }
            }
            // b. If both the source project and target project have the referral contact,
            // then remove the target referral contact and copy over the referral contact from the source. 
            else if (sourceContacts.Any()
                && targetContacts.Any())
            {
                // remove existing referrals in the target
                foreach(var targetContact in targetContacts)
                {
                    targetJob.JobContacts.Remove(targetContact);
                }
                // add source referrals contacts
                foreach(var sourceContact in sourceContacts)
                {
                    targetJob.JobContacts.Add(MapJobContact(sourceContact, targetJob.Id));
                }
            }
            // c. If the target has the referral contact and the source does not then keep the target contact.
            else if (!sourceContacts.Any() && targetContacts.Any())
            {
                // do nothing in this case... we want to keep the target referral contacts
            }
        }

        private JobContactMap MapJobContact(JobContactMap x, Guid targetJobId)
        => new JobContactMap()
        {
            Contact = x.Contact,
            ContactId = x.ContactId,
            JobContactTypeId = x.JobContactTypeId,
            JobId = targetJobId,
            CreatedDate = DateTime.UtcNow,
            IsBusinessContact = x.IsBusinessContact,
            TitleId = x.TitleId
        };

        private void MergeJobBusinesses(Job sourceJob, Job targetJob)
        {
            sourceJob.JobBusinesses
                .Where(x => !targetJob.JobBusinesses.Any(y => y.BusinessId == x.BusinessId
                    && y.JobBusinessTypeId == x.JobBusinessTypeId))
                .Select(x => MapJobBusiness(x, targetJob.Id))
                .ToList().ForEach(targetJob.JobBusinesses.Add);
        }

        private JobBusinessMap MapJobBusiness(JobBusinessMap x, Guid targetJobId)
        => new JobBusinessMap
        {
            BusinessId = x.BusinessId,
            JobId = targetJobId,
            IsPrimary = x.IsPrimary,
            JobBusinessTypeId = x.JobBusinessTypeId
        };
    }
}
