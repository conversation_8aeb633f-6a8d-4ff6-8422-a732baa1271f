﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.TaskCreatedEvent;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.TaskDeletedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using JournalNoteDto = Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes.JournalNoteUpdatedEvent.JournalNoteDto;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class SaveJobVisit
    {

        public class Command : IRequest
        {
            public Guid? Id { get; set; }
            public Guid JobId { get; set; }
            public string Initials { get; set; }
            public DateTime Date { get; set; }
            public DateTime? DepartureDate { get; set; }
            public IEnumerable<QuestionAnswerDto> QuestionAnswers { get; set; } = new List<QuestionAnswerDto>();
            public class QuestionAnswerDto
            {
                public Guid JobTriStateQuestionId { get; set; }
                public bool? Answer { get; set; }
                public Guid? JournalNoteId { get; set; }
            }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.Initials).NotEmpty();
                RuleFor(x => x.Initials).MaximumLength(3);
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly ILogger<SaveJobVisit> _logger;
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(ILogger<SaveJobVisit> logger,
                JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor,
                ILookupServiceClient lookupServiceClient)
            {
                _logger = logger;
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler SaveJobVisit with request: {@request}", request);

                var job = await GetJobAsync(request, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                var visit = job.JobVisits
                    .FirstOrDefault(x => x.Id == request.Id);

                if (visit is null && request.Id.HasValue)
                    throw new ResourceNotFoundException($"Visit not found (Id: {request.Id}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, _userInfo.GetUserInfo().Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var userInfo = _userInfo.GetUserInfo();
                if (visit is null)
                {
                    visit = new JobVisit
                    {
                        Id = Guid.NewGuid(),
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow
                    };
                    job.JobVisits.Add(visit);
                    AddZoneReadings(job.Zones, visit, userInfo);
                }
                else
                {
                    visit.ModifiedBy = _userInfo.GetUserInfo().Username;
                    visit.ModifiedDate = DateTime.UtcNow;
                }
                visit.EmployeeInitials = request.Initials;
                visit.Date = request.Date;
                visit.DepartureDate = request.DepartureDate;

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var noteIds = request.QuestionAnswers
                    .Where(x => x.JournalNoteId.HasValue)
                    .Select(x => x.JournalNoteId.Value)
                    .ToHashSet();
                var journalNotes = await _context.JournalNote
                    .Where(x => noteIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);
                UpdateQuestionAnswers(job, visit, request.QuestionAnswers, lookups, journalNotes);

                var eventMessage = GenerateEditJobVisitEvent(request, visit, userInfo);
                _context.OutboxMessages.Add(eventMessage);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private async Task<Job> GetJobAsync(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);
                job.Zones = await _context.Zones
                    .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobTriStateAnswers = await _context.JobTriStateAnswers
                    .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit
                    .Include(x => x.JobVisitTriStateAnswers)
                    .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobLocks = await _context.JobLock
                    .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.Tasks = await _context.Tasks
                    .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                return job;
            }

            private void AddZoneReadings(
                IEnumerable<Zone> zones,
                JobVisit visit,
                UserInfo userInfo)
            {
                foreach(var zone in zones)
                {
                    zone.ZoneReadings.Add(new ZoneReading()
                    {
                        JobVisit = visit,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow
                    });
                }
            }

            private void UpdateQuestionAnswers(
                Job job,
                JobVisit jobVisit,
                IEnumerable<Command.QuestionAnswerDto> requestQuestionAnswers,
                GetLookups.Dto lookups,
                List<JournalNote> journalNotes)
            {
                foreach (var questionAnswer in requestQuestionAnswers)
                {
                    var IsAskedPerVisit = lookups.JobTriStateQuestions
                        .First(x => x.Id == questionAnswer.JobTriStateQuestionId).IsAskedPerVisit;

                    if (IsAskedPerVisit)
                        UpdateJobVisitTriStateAnswer(job, jobVisit, questionAnswer, lookups, journalNotes);
                    else
                        UpdateJobTriStateAnswer(job, jobVisit, questionAnswer, lookups, journalNotes);
                }
            }

            private void UpdateJobVisitTriStateAnswer(
                Job job,
                JobVisit jobVisit,
                Command.QuestionAnswerDto questionAnswer,
                GetLookups.Dto lookups,
                List<JournalNote> journalNotes)
            {
                var userInfo = _userInfo.GetUserInfo();
                var journalNote = journalNotes.FirstOrDefault(x => x.Id == questionAnswer.JournalNoteId);
                var existingAnswer = jobVisit.JobVisitTriStateAnswers
                        .FirstOrDefault(x => x.JobTriStateQuestionId == questionAnswer.JobTriStateQuestionId);

                if (existingAnswer != null)
                {
                    var currentAnswer = existingAnswer.Answer;
                    existingAnswer.Answer = questionAnswer.Answer;
                    existingAnswer.ModifiedBy = userInfo.Username;
                    existingAnswer.ModifiedDate = DateTime.UtcNow;
                    if (journalNote != null)
                        AddTaskAndAssociateToNote(questionAnswer, currentAnswer, job, jobVisit, lookups, journalNote, userInfo);
                }
                else if (existingAnswer is null
                    && (questionAnswer != null || questionAnswer.JournalNoteId.HasValue))
                {
                    jobVisit.JobVisitTriStateAnswers.Add(new JobVisitTriStateAnswer
                    {
                        Id = Guid.NewGuid(),
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        JobVisitId = jobVisit.Id,
                        JobTriStateQuestionId = questionAnswer.JobTriStateQuestionId,
                        Answer = questionAnswer.Answer
                    });
                    if (journalNote != null)
                        AddTaskAndAssociateToNote(questionAnswer, null, job, jobVisit, lookups, journalNote, userInfo);
                }
            }

            private void UpdateJobTriStateAnswer(
                Job job,
                JobVisit jobVisit,
                Command.QuestionAnswerDto questionAnswer,
                GetLookups.Dto lookups,
                List<JournalNote> journalNotes)
            {
                var userInfo = _userInfo.GetUserInfo();
                var journalNote = journalNotes.FirstOrDefault(x => x.Id == questionAnswer.JournalNoteId);
                var existingAnswer = job.JobTriStateAnswers
                        .FirstOrDefault(x => x.JobTriStateQuestionId == questionAnswer.JobTriStateQuestionId);

                if (existingAnswer != null)
                {
                    var currentAnswer = existingAnswer.Answer;
                    existingAnswer.Answer = questionAnswer.Answer;
                    existingAnswer.ModifiedBy = userInfo.Username;
                    existingAnswer.ModifiedDate = DateTime.UtcNow;
                    if (journalNote != null)
                        AddTaskAndAssociateToNote(questionAnswer, currentAnswer, job, jobVisit, lookups, journalNote, userInfo);
                }
                else if (existingAnswer is null
                    && (questionAnswer != null || questionAnswer.JournalNoteId.HasValue))
                {
                    job.JobTriStateAnswers.Add(new JobTriStateAnswer
                    {
                        Id = Guid.NewGuid(),
                        JobId = job.Id,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        JobTriStateQuestionId = questionAnswer.JobTriStateQuestionId,
                        Answer = questionAnswer.Answer
                    });
                    if (journalNote != null)
                        AddTaskAndAssociateToNote(questionAnswer, null, job, jobVisit, lookups, journalNote, userInfo);
                }
            }

            private void AddTaskAndAssociateToNote(
                Command.QuestionAnswerDto questionAnswer,
                bool? currentAnswer,
                Job job,
                JobVisit jobVisit,
                GetLookups.Dto lookups,
                JournalNote note,
                UserInfo userInfo)
            {
                var question = lookups.JobTriStateQuestions
                    .First(x => x.Id == questionAnswer.JobTriStateQuestionId);
                var transition = question.Transitions
                    .FirstOrDefault(x => x.FromValue == currentAnswer && x.ToValue == questionAnswer.Answer);
                if (transition != null && transition.TaskTypeId.HasValue)
                {
                    // find active tasks associated with this question and delete
                    var existingTask = job.Tasks
                        .FirstOrDefault(x => x.JobTriStateQuestionId == questionAnswer.JobTriStateQuestionId
                            && x.TaskStatusId == TaskStatuses.Active);
                    if (existingTask != null)
                    {
                        job.Tasks.Remove(existingTask);

                        var taskDeletedEvent = GetTaskDeletedEvent(existingTask, userInfo);
                        _context.OutboxMessages.Add(taskDeletedEvent);
                    }
                        
                    var fromValue = GetAnswerDisplayValue(currentAnswer);
                    var toValue = GetAnswerDisplayValue(questionAnswer.Answer);
                    var taskType = lookups.TaskTypes.First(x => x.Id == transition.TaskTypeId);
                    var body = taskType.DefaultBody != null
                        ? string.Format(taskType.DefaultBody, fromValue, toValue)
                        : null;
                    var task = new Models.Drybook.Task
                    {
                        Id = Guid.NewGuid(),
                        JobId = job.Id,
                        JobVisit = question.IsAskedPerVisit ? jobVisit : null,
                        Body = body,
                        FranchiseSetId = job.FranchiseSetId,
                        TaskTypeId = taskType.Id,
                        TaskPriorityId = TaskPriorities.Medium,
                        TaskStatusId = taskType.ShouldCloseWhenDiaryEntryIsComplete
                            && !string.IsNullOrWhiteSpace(note.Note)
                            ? TaskStatuses.Completed
                            : TaskStatuses.Active,
                        PercentComplete = taskType.ShouldCloseWhenDiaryEntryIsComplete
                            && !string.IsNullOrWhiteSpace(note.Note)
                            ? 100
                            : 0,
                        JobTriStateQuestionId = questionAnswer.JobTriStateQuestionId,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow
                    };
                    note.TaskId = task.Id;
                    var journalUpdtedEvent = GetJournalNoteUpdatedEvent(note, userInfo);
                    _context.OutboxMessages.Add(journalUpdtedEvent);

                    job.Tasks.Add(task);
                    var taskCreatedEvent = GetTaskCreatedEvent(task, userInfo);
                    _context.OutboxMessages.Add(taskCreatedEvent);
                }
            }

            private string GetAnswerDisplayValue(bool? answer)
            {
                if (answer == null)
                    return "Unknown";
                else if (answer == true)
                    return "Yes";
                else
                    return "No";
            }

            private OutboxMessage GenerateEditJobVisitEvent(
                Command request,
                JobVisit visit,
                UserInfo userInfo)
            {
                var correlationId = GetCorrelationId();
                if(request.Id.HasValue)
                {
                    var jobVisitUpdatedEvent = new JobVisitUpdatedEvent(
                        GetVisitUpdatedEventDto(request, visit, userInfo),
                        correlationId);
                    return new OutboxMessage(jobVisitUpdatedEvent.ToJson(),
                        nameof(JobVisitUpdatedEvent),
                        correlationId,
                        userInfo.Username);
                }
                else
                {
                    var jobVisitCreatedEvent = new JobVisitCreatedEvent(
                        GetVisitCreatedEventDto(request, visit, userInfo),
                        correlationId);
                    return new OutboxMessage(jobVisitCreatedEvent.ToJson(),
                        nameof(JobVisitCreatedEvent),
                        correlationId,
                        userInfo.Username);
                }
            }

            private JobVisitCreatedDto GetVisitCreatedEventDto(Command request, JobVisit visit, UserInfo userInfo)
                => new JobVisitCreatedDto
                {
                    JobId = request.JobId,
                    Id = visit.Id,
                    EmployeeInitials = request.Initials,
                    Date = request.Date,
                    DepartureDate = request.DepartureDate,
                    //Username = userInfo.Username,
                    //UserId = userInfo.Id,
                    Answers = visit.JobVisitTriStateAnswers
                        .Select(x => new JobVisitCreatedDto.TriStateAnswer
                        {
                            Answer = x.Answer,
                            JobTriStateQuestionId = x.JobTriStateQuestionId
                        })
                };

            private JobVisitUpdatedDto GetVisitUpdatedEventDto(Command request, JobVisit visit, UserInfo userInfo)
                => new JobVisitUpdatedDto
                {
                    JobId = request.JobId,
                    Id = visit.Id,
                    EmployeeInitials = request.Initials,
                    Date = request.Date,
                    DepartureDate = request.DepartureDate,
                    //Username = userInfo.Username,
                    //UserId = userInfo.Id,
                    Answers = visit.JobVisitTriStateAnswers
                        .Select(x => new JobVisitUpdatedDto.TriStateAnswer
                        {
                            Answer = x.Answer,
                            JobTriStateQuestionId = x.JobTriStateQuestionId
                        })
                };

            private OutboxMessage GetTaskCreatedEvent(Models.Drybook.Task task, UserInfo userInfo)
            {
                var taskCreatedDto = new TaskCreatedDto
                {
                    Id = task.Id,
                    JobId = task.JobId,
                    JobVisitId = task.JobVisitId,
                    JobTriStateQuestionId = task.JobTriStateQuestionId,
                    Body = task.Body,
                    FranchiseSetId = task.FranchiseSetId,
                    TaskTypeId = task.TaskTypeId,
                    TaskPriorityId = task.TaskPriorityId,
                    TaskStatusId = task.TaskStatusId,
                    PercentComplete = task.PercentComplete
                };

                var taskCreatedEvent = new TaskCreatedEvent(taskCreatedDto, GetCorrelationId());

                return new OutboxMessage(taskCreatedEvent.ToJson(), nameof(TaskCreatedEvent), GetCorrelationId(), userInfo.Username);
            }

            private OutboxMessage GetTaskDeletedEvent(Models.Drybook.Task task, UserInfo userInfo)
            {
                var taskDeletedDto = new TaskDeletedDto
                {
                    Id = task.Id,
                    JobId = task.JobId ?? Guid.Empty,
                    DeletedBy = userInfo.Name,
                    DeletedDate = DateTime.UtcNow                
                };

                var taskDeleteEvent = new TaskDeletedEvent(taskDeletedDto, GetCorrelationId()); 

                return new OutboxMessage(taskDeleteEvent.ToJson(), nameof(TaskDeletedEvent), GetCorrelationId(), userInfo.Username);

            }

            private OutboxMessage GetJournalNoteUpdatedEvent(JournalNote journalNote, UserInfo userInfo)
            {

                var journalNoteDto = new JournalNoteDto
                {
                     Author = journalNote.Author,
                     JobId = journalNote.JobId ?? Guid.Empty,
                     ActionDate = journalNote.ActionDate,
                     Id = journalNote.Id,
                     CategoryId = journalNote.CategoryId,
                     TypeId = journalNote.TypeId,
                     UpdatedDate = DateTime.UtcNow,
                     Subject = journalNote.Subject,
                     VisibilityId = journalNote.VisibilityId,
                     UpdatedById = userInfo.Id,
                     Message = journalNote.Note
                };

                var journalNoteUpdateEvent = new JournalNoteUpdatedEvent(journalNoteDto, GetCorrelationId());

                return new OutboxMessage(journalNoteUpdateEvent.ToJson(), nameof(JournalNoteUpdatedEvent), GetCorrelationId(), userInfo.Username);
            }

            private Guid GetCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();

                return correlationId;
            }
        }
    }
}