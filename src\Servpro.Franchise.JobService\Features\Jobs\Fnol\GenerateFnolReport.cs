﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Fnol.Mapping;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.FnolReport;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Models;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.Fnol
{
    public class GenerateFnolReport
    {
        public class Query : IRequest<FnolReportDto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }

        public class QueryValidator : AbstractValidator<Query>
        {
            public QueryValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Query, FnolReportDto>
        {
            private readonly ILogger<GenerateFnolReport> _logger;
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupService;

            public Handler(ILogger<GenerateFnolReport> logger, JobReadOnlyDataContext context, ILookupServiceClient lookupService)
            {
                _logger = logger;
                _context = context;
                _lookupService = lookupService;
            }

            public async Task<FnolReportDto> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobId = request.JobId;
                using var scope = _logger.BeginScope("{jobId}", jobId);
                _logger.LogDebug("GenerateFnolReport - Generating FNOL report");
                var job = await GetJobAsync(jobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException("Job not found.");

                var insurances = await _context.InsuranceClients.ToListAsync(cancellationToken);
                var lookups = await _lookupService.GetLookupsAsync(cancellationToken);
                var jobWithCustomerAndCaller = await GetJobWithCustomerAndCallerAsync(jobId, cancellationToken);

                job.JobContacts = await GetJobContactsAsync(jobId, cancellationToken);
                job.Customer = jobWithCustomerAndCaller?.Customer;
                job.Caller = jobWithCustomerAndCaller?.Caller;

                var fnolReport = FnolReportMapper.Map(job, lookups, insurances);
                _logger.LogInformation("GenerateFnolReport - Completed, FNOL report: {@fnolReport}", fnolReport);

                return fnolReport;
            }

            private async Task<List<JobContactMap>> GetJobContactsAsync(Guid jobId, CancellationToken cancellationToken)
            {
                return await _context.JobContactMap
                    .Include(c => c.Contact)
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
            }

            private async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
            {
                return await _context.Jobs
                    .Include(j => j.MediaMetadata)
                    .Include(j => j.JournalNotes)
                    .Include(j => j.JobTriStateAnswers)
                    .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
            }

            private async Task<Job> GetJobWithCustomerAndCallerAsync(Guid jobId, CancellationToken cancellationToken)
            {
                return await _context.Jobs
                    .Include(j => j.Customer)
                        .ThenInclude(c => c.Business)
                    .Include(j => j.Caller)
                    .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
            }
        }
    }
}
