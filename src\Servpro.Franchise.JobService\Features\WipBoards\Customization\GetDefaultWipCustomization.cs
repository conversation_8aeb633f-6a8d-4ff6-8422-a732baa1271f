﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models.Customization.Dtos;
using Servpro.FranchiseSystems.Framework.Setup.FeatureFlags;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.WipBoards
{
    public class GetDefaultWipCustomization
    {
        public class Query : IRequest<ICollection<GridColumnDto>>
        {
        
        }

        public class Handler : IRequestHandler<Query, ICollection<GridColumnDto>>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IFeatureFlagUtility _featureFlags;

            public Handler(JobReadOnlyDataContext db,
                IFeatureFlagUtility featureFlags)
            {
                _db = db;
                _featureFlags = featureFlags;
            }

            public async Task<ICollection<GridColumnDto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                 var customization = await _db.ServproWipColumnCustomization.FirstOrDefaultAsync(cancellationToken);
                if (!_featureFlags.IsFeatureEnabled("StormPreliminaryEstimate"))
                {
                    var stormEstimateColumn = customization.Data.FirstOrDefault(x => x.ColumnName == "StormPreliminaryEstimate");
                    customization.Data.Remove(stormEstimateColumn);
                }
                    return customization.Data;
            }
        }
    }
}
