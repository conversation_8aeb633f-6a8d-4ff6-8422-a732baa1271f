﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.WipBoards.ScheduleService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Audit;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class SelfAuditDataChanged
    {
        public class Event : SelfAuditDataChangedEvent, IRequest
        {
            public Event(SelfAuditDataDto selfAuditData, Guid correlationId) : base(selfAuditData, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<SelfAuditDataChanged> _logger;
            private readonly IScheduleService _scheduleService;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(JobDataContext db, 
                ILogger<SelfAuditDataChanged> logger,
                IScheduleService scheduleService,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _db = db;
                _logger = logger;
                _scheduleService = scheduleService;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with: {@request}", request);

                var selfAuditDataDto = request.SelfAuditData;

                var job = await _db.Jobs.Include(p => p.Customer)
                                        .FirstOrDefaultAsync(x => x.Id == selfAuditDataDto.JobId, cancellationToken);
                if (job != null)
                {
                    job.SelfAuditDate = selfAuditDataDto.SelfAuditDueUtc;
                    job.AuditRejectionDate = selfAuditDataDto.AuditRejectionDateUtc;
                    job.AuditRejectRole = selfAuditDataDto.AuditRejectRoleId;
                    job.RejectedFinalAuditsCount = selfAuditDataDto.RejectedFinalAuditsCount;
                    job.ModifiedDate = selfAuditDataDto.CreatedUtc;
                    job.ModifiedBy = selfAuditDataDto.CreatedBy;
                    job.IsAuditComplete = selfAuditDataDto.IsComplete;

                    
                    TimeZoneInfo timeZoneInfo = await _franchiseServiceClient.GetFranchiseSetPrimaryTimeZoneAsync(job.FranchiseSetId, cancellationToken: cancellationToken);
                    DateTime? correctionDueDate = selfAuditDataDto.AuditRejectionDateUtc;
                    if (correctionDueDate != null)
                    {
                        _logger.LogDebug("Starting the AuditReturnedEvent");

                        correctionDueDate = await _scheduleService.NextCloseBusinessDayInDays(timeZoneInfo, 0, selfAuditDataDto.AuditRejectionDateUtc.Value);
                        await GenerateAuditReturnedEventAsync(job, request.CorrelationId);
                    }

                    job.CorrectionDueDate = correctionDueDate;
                    await _db.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    _logger.LogWarning("The Job With Id({ID}) Does Not Exist", selfAuditDataDto.JobId);
                }

                return Unit.Value;
            }

            private async Task GenerateAuditReturnedEventAsync(Job job, Guid correlationId)
            {
                _logger.LogInformation("Generating {eventName} for : {jobId}", nameof(AuditReturnedEvent), job.Id.ToString());

                var eventDto = new AuditReturnedEvent.EventDto
                {
                    JobId = job.Id,
                    ProjectNumber = job.ProjectNumber,
                    LossTypeId = job.LossTypeId,
                    PropertyTypeId = job.PropertyTypeId,
                    BusinessName = job.BusinessName,
                    FranchiseId = job.FranchiseId,
                    FranchiseSetId = job.FranchiseSetId,
                    Customer = MapContactToCustomer(job),
                    LossAddress = MapLossAddress(job),
                };

                var generatedEvent = new AuditReturnedEvent(eventDto, correlationId);

                await _db.OutboxMessages.AddAsync(
                    new OutboxMessage(generatedEvent.ToJson(), nameof(AuditReturnedEvent), correlationId, nameof(SelfAuditDataChangedEvent)));
            }

            private static AuditReturnedEvent.AddressDto MapLossAddress(Job job)
            {
                return new AuditReturnedEvent.AddressDto(job.LossAddress?.Address1,
                    job.LossAddress?.City,
                    job.LossAddress?.PostalCode,
                    job.LossAddress?.State != null ?
                    new AuditReturnedEvent.StateDto { 
                        StateAbbreviation = job.LossAddress.State.StateAbbreviation, 
                        StateId = job.LossAddress.State.StateId
                    } : null) 
                { 
                    Address2 = job.LossAddress?.Address2 
                };
            }

            private static AuditReturnedEvent.ContactDto MapContactToCustomer(Job job)
            {
                return job.Customer != null ? new AuditReturnedEvent.ContactDto
                {
                    Id = job.Customer.Id,
                    FirstName = job.Customer.FirstName,
                    LastName = job.Customer.LastName,
                } : null;
            }
        }
    }
}