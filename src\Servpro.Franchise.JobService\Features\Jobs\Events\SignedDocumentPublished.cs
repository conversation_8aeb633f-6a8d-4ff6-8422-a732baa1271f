﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Microsoft.Extensions.Configuration;
using Servpro.FranchiseSystems.Framework.Messaging.Events;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using System.Configuration;
using Servpro.Franchise.JobService.Features.Jobs.Documents;
using Servpro.FranchiseSystems.Framework.Messaging;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class SignedDocumentPublished
    {
        public class MediaMetadataParameters
        {
            public string BucketName { get; set; }
            public FormTemplate FormTemplate { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class Event : SignedDocumentPublishedEvent, IRequest
        {
            public Event(SignedDocumentPublishedDto dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly IMediator _mediator;
            private readonly IConfiguration _config;
            private readonly ILogger<Handler> _logger;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string ModificationUserName = "Signing process";

            public Handler(JobDataContext dataContext, IConfiguration config, 
                ILogger<Handler> logger, IMediator mediator,
                IMediaEventGenerator mediaEventGenerator)
            {
                _mediator = mediator;
                _db = dataContext;
                _logger = logger;
                _config = config;
                _mediaEventGenerator = mediaEventGenerator;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} Handler: activated", nameof(SignedDocumentPublishedEvent));
                var documentPublishedDto = request?.DocumentPublished;
                if (IsDtoInvalid(documentPublishedDto))
                {
                    _logger.LogInformation("{event} Handler: completed early due to invalid request or DTO", nameof(SignedDocumentPublishedEvent));
                    return Unit.Value;
                }

                using var scope = _logger.BeginScope("{@event}",
                    new
                    {
                        DocumentId = documentPublishedDto.DocumentId,
                        JobId = documentPublishedDto.JobId,
                        FormTemplateId = documentPublishedDto.FormTemplateId,
                        S3BucketLocation = documentPublishedDto.S3BucketLocation
                    });

                bool result = await HandleEvent(documentPublishedDto, request.CorrelationId, cancellationToken);
                if (result)
                {
                    _logger.LogInformation("{event} Handler: completed successfully", nameof(SignedDocumentPublishedEvent));
                }

                return Unit.Value;
            }

            private async Task<bool> HandleEvent(SignedDocumentPublishedDto documentPublishedDto, Guid correlationId, CancellationToken cancellationToken)
            {
                MediaMetadata media = await GetMediaMetadataByDocumentIdAsync(documentPublishedDto, cancellationToken);
                if (media != null)
                {
                    _logger.LogWarning("Document with Id: {id} already exists.", documentPublishedDto.DocumentId);
                    return false;
                }

                MediaMetadata existingMedia = await GetMediaMetadataByFormTemplateIdAsync(documentPublishedDto, cancellationToken);
                if (existingMedia != null)
                {
                    existingMedia.IsDeleted = true;
                    existingMedia.ModifiedBy = nameof(SignedDocumentPublishedEvent);
                    existingMedia.ModifiedDate = DateTime.UtcNow;
                    _logger.LogInformation("Document with Id: {oldId}, JobId: {jobId}, FormatTemplateId: {formTemplateId} is being replaced by new document with Id: {newId}.", existingMedia.Id, documentPublishedDto.JobId, documentPublishedDto.FormTemplateId, documentPublishedDto.DocumentId);
                }

                FormTemplate formTemplate = await GetFormTemplateAsync(documentPublishedDto.FormTemplateId, cancellationToken);
                if (formTemplate == null)
                {
                    _logger.LogInformation("Document with Id: {id}, JobId: {jobId} has an associated form template, but the form template does not exist; FormatTemplateId: {formTemplateId}.", documentPublishedDto.DocumentId, documentPublishedDto.JobId, documentPublishedDto.FormTemplateId);

                    return false;
                }

                Guid? franchiseSetId = await GetFranchiseSetIdAsync(documentPublishedDto.JobId, cancellationToken);
                if (!franchiseSetId.HasValue)
                {
                    _logger.LogInformation("Document with Id: {id}, JobId: {jobId}. Job does not exist.", documentPublishedDto.DocumentId, documentPublishedDto.JobId);

                    return false;
                }
                string bucketName = _config.GetValue<string>(S3MediaBucketNameKey);

                var mediaMetadataParams = new MediaMetadataParameters
                {
                    BucketName = bucketName,
                    FormTemplate = formTemplate,
                    FranchiseSetId = franchiseSetId.Value,
                };

                MediaMetadata mediaMetadata = await AddMediaMetadataAsync(documentPublishedDto, mediaMetadataParams, cancellationToken);
                await AddEventsToPublishAsync(correlationId, documentPublishedDto, mediaMetadata, cancellationToken);

                await _db.SaveChangesAsync(cancellationToken);

                await SatisfyAtpRequirementValidationAsync(documentPublishedDto, cancellationToken);

                return true;
            }

            private async Task SatisfyAtpRequirementValidationAsync(SignedDocumentPublishedDto documentPublishedDto, CancellationToken cancellationToken)
            {
                _logger.LogDebug("SatisfyAtpRequirementValidation: User {@user}", ModificationUserName);

                var atpRequirement = new SatisfyAtpRequirement.Command()
                {
                    JobId = documentPublishedDto.JobId,
                    Date = DateTime.UtcNow,
                    FormTemplateIds = new List<Guid?> { documentPublishedDto.FormTemplateId },
                    ModifiedBy = ModificationUserName
                };
                _logger.LogInformation("SatisfyAtpRequirementValidation: Sending atpRequirement request {@request}", atpRequirement);
                await _mediator.Send(atpRequirement, cancellationToken);
            }

            private async Task AddEventsToPublishAsync(Guid correlationId, SignedDocumentPublishedDto documentPublishedDto, MediaMetadata mediaMetadata, CancellationToken cancellationToken)
            {
                var mediaMetadataList = new List<MediaMetadata>
                {
                    mediaMetadata
                };
                const string createdBy = nameof(SignedDocumentPublishedEvent);
                await AddMediaAddedEventAsync(correlationId, mediaMetadataList, createdBy, cancellationToken);
            }

            private async Task AddMediaAddedEventAsync(Guid correlationId, List<MediaMetadata> mediaMetadataList, string createdBy, CancellationToken cancellationToken)
            {
                var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(mediaMetadataList, correlationId, createdBy);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task<MediaMetadata> AddMediaMetadataAsync(SignedDocumentPublishedDto documentPublishedDto, MediaMetadataParameters mediaMetadataParams, CancellationToken cancellationToken)
            {
                var mediaMetadata = Map(documentPublishedDto, mediaMetadataParams);
                _logger.LogInformation("Processing MediaMetadata with Id: {id}", mediaMetadata.Id);
                await _db.MediaMetadata.AddAsync(mediaMetadata, cancellationToken);
                return mediaMetadata;
            }

            private async Task<MediaMetadata> GetMediaMetadataByFormTemplateIdAsync(SignedDocumentPublishedDto documentDto, CancellationToken cancellationToken)
            {
                return await _db.MediaMetadata
                    .FirstOrDefaultAsync(x => x.JobId == documentDto.JobId
                                            && x.FormTemplateId == documentDto.FormTemplateId
                                            && !x.IsDeleted, cancellationToken);
            }

            private async Task<MediaMetadata> GetMediaMetadataByDocumentIdAsync(SignedDocumentPublishedDto documentDto, CancellationToken cancellationToken)
            {
                return await _db.MediaMetadata.FirstOrDefaultAsync(x => x.Id == documentDto.DocumentId, cancellationToken);
            }

            private async Task<FormTemplate> GetFormTemplateAsync(Guid formTemplateId, CancellationToken cancellationToken)
            {
                var formTemplate = await _db.FormTemplates.Where(x => x.Id == formTemplateId).FirstOrDefaultAsync(cancellationToken);

                return formTemplate;
            }

            private async Task<Guid?> GetFranchiseSetIdAsync(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs
                    .Select(job => new { job.Id, job.FranchiseSetId })
                    .FirstOrDefaultAsync(job => job.Id == jobId, cancellationToken);

                return job?.FranchiseSetId;
            }

            private bool IsDtoInvalid(SignedDocumentPublishedDto documentDto)
            {
                if (documentDto == null)
                {
                    _logger.LogWarning("{event} Handler: DocumentPublished should not be Null", nameof(SignedDocumentPublishedEvent));
                    return true;
                }
                if (documentDto.DocumentId == Guid.Empty)
                {
                    _logger.LogWarning("{event} Handler: DocumentId should not be Empty", nameof(SignedDocumentPublishedEvent));
                    return true;
                }
                if (documentDto.JobId == Guid.Empty)
                {
                    _logger.LogWarning("{event} Handler: JobId should not be Empty", nameof(SignedDocumentPublishedEvent));
                    return true;
                }
                if (documentDto.FormTemplateId == Guid.Empty)
                {
                    _logger.LogWarning("{event} Handler: FormTemplateId should not be Empty", nameof(SignedDocumentPublishedEvent));
                    return true;
                }
                if (documentDto.S3BucketLocation.IsNullOrWhiteSpace())
                {
                    _logger.LogWarning("{event} Handler: S3BucketLocation should not be Null or Empty", nameof(SignedDocumentPublishedEvent));
                    return true;
                }
                if (documentDto.SignedDate == DateTime.MinValue)
                {
                    _logger.LogWarning("{event} Handler: SignedDate should not be Empty", nameof(SignedDocumentPublishedEvent));
                    return true;
                }

                return false;
            }

            private MediaMetadata Map(SignedDocumentPublishedDto dto, MediaMetadataParameters mediaMetadataParams) =>
                new MediaMetadata
                {
                    Id = dto.DocumentId,
                    JobId = dto.JobId,
                    FranchiseSetId = mediaMetadataParams.FranchiseSetId,
                    FormTemplateId = dto.FormTemplateId,
                    MediaTypeId = MediaTypes.Document,
                    Name = mediaMetadataParams.FormTemplate.Description,
                    BucketName = mediaMetadataParams.BucketName,
                    ArtifactDate = dto.SignedDate,
                    MediaPath = dto.S3BucketLocation,
                    SignedDate = dto.SignedDate,
                    SyncDate = DateTime.UtcNow,
                    CreatedBy = ModificationUserName,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedBy = ModificationUserName,
                    ModifiedDate = DateTime.UtcNow,
                    IsForUpload = true,
                    UploadedSuccessfully = true,
                    IsDeleted = false
                };
        }
    }
}