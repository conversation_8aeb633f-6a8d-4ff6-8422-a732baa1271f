﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.JobDates
{
    public class SetJobDate
    {
        public class Command : IRequest<Unit>
        {
            public Guid JobId { get; set; }
            public Guid JobDateTypeId { get; set; }
            public Guid? ExceptionReasonId { get; set; }
            public DateTime Value { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.JobDateTypeId).NotEmpty();
                RuleFor(x => x.Value).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context, IUserInfoAccessor userInfoAccessor, ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs.FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException();

                var isUpdate = job.JobDates.Any(d => d.JobDateTypeId == request.JobDateTypeId);

                job.SetOrUpdateDate(request.JobDateTypeId, request.Value, request.ExceptionReasonId);

                await RaiseEvents(isUpdate, request);

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task RaiseEvents(bool isUpdate, Command request)
            {

                var user = _userInfoAccessor.GetUserInfo();

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                if (isUpdate)
                {
                    // raise JobDateUpdated
                    var eventDto = new JobDateUpdatedEvent.JobDateUpdatedDto(
                            request.JobId, request.JobDateTypeId, request.Value, user.Username);
                    var newEvent = new JobDateUpdatedEvent(eventDto, correlationId);
                    await GenerateOutboxMessage(newEvent.ToJson(), nameof(JobDateUpdatedEvent),
                        newEvent.CorrelationId, eventDto.ModifiedBy);
                }
                else
                {
                    // raise JobDateCreated
                    var eventDto = new JobDateCreatedEvent.JobDateCreatedDto(
                            request.JobId, request.JobDateTypeId, request.Value, user.Username);
                    var newEvent = new JobDateCreatedEvent(eventDto, correlationId);
                    await GenerateOutboxMessage(newEvent.ToJson(), nameof(JobDateCreatedEvent),
                        newEvent.CorrelationId, eventDto.CreatedBy);
                }
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _context.OutboxMessages.AddAsync(newEvent);
            }
        }
    }
}