﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class FixEmployeeAssociation
    {
        public class Event : IRequest
        {
            /// <summary>
            /// Source Employee Id
            /// </summary>
            public Guid SourceId { get; set; }
            /// <summary>
            /// Target Employee Id
            /// </summary>
            public Guid TargetId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid CorrelationId { get; set; }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<ReplayLeadCreatedEvents> _logger;
            private readonly JobDataContext _db;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(
                ILogger<ReplayLeadCreatedEvents> logger,
                JobDataContext db,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _logger = logger;
                _db = db;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{sourceId}{targetId}{franchiseSetId}",
                    request.SourceId, request.TargetId, request.FranchiseSetId);
                _logger.LogInformation("Processing event: {@request}", request);

                var jobsToUpdate = await _db.Jobs
                    .Where(x => x.FranchiseSetId == request.FranchiseSetId && (
                        x.JobFileCoordinatorId == request.SourceId ||
                        x.ProjectManagerId == request.SourceId ||
                        x.CrewChiefId == request.SourceId ||
                        x.RecpDispatcherId == request.SourceId ||
                        x.GeneralManagerId == request.SourceId ||
                        x.OfficeManagerId == request.SourceId ||
                        x.ReconSupportId == request.SourceId ||
                        x.ProductionManagerId == request.SourceId ||
                        x.PriorityResponderId == request.SourceId
                    ))
                    .ToListAsync(cancellationToken);
                _logger.LogInformation("Found {jobCount} matching jobs", jobsToUpdate.Count);
                foreach (var job in jobsToUpdate)
                {
                    using var jobScope = _logger.BeginScope("{jobId}{projectNumber}", job.Id, job.ProjectNumber);
                    _logger.LogInformation("Processing job.");
                    if (job.JobFileCoordinatorId == request.SourceId)
                        job.JobFileCoordinatorId = request.TargetId;
                    if (job.ProjectManagerId == request.SourceId)
                        job.ProjectManagerId = request.TargetId;
                    if (job.CrewChiefId == request.SourceId)
                        job.CrewChiefId = request.TargetId;
                    if (job.RecpDispatcherId == request.SourceId)
                        job.RecpDispatcherId = request.TargetId;
                    if (job.GeneralManagerId == request.SourceId)
                        job.GeneralManagerId = request.TargetId;
                    if (job.OfficeManagerId == request.SourceId)
                        job.OfficeManagerId = request.TargetId;
                    if (job.ReconSupportId == request.SourceId)
                        job.ReconSupportId = request.TargetId;
                    if (job.ProductionManagerId == request.SourceId)
                        job.ProductionManagerId = request.TargetId;
                    if (job.PriorityResponderId == request.SourceId)
                        job.PriorityResponderId = request.TargetId;
                    var updateEvent = new JobUpdatedViaInlineEditEvent(
                        MapUpdateEvent(job),
                        request.CorrelationId);
                    var outboxMessage = new OutboxMessage(
                        updateEvent.ToJson(),
                        nameof(JobUpdatedViaInlineEditEvent),
                        request.CorrelationId,
                        "data-fix");
                    _logger.LogTrace("Event: {@event}", outboxMessage);
                    await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                }
                await _db.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private JobUpdatedViaInlineEditDto MapUpdateEvent(Job job)
            {
                return new JobUpdatedViaInlineEditDto
                {
                    JobId = job.Id,
                    FranchiseSetId = job.FranchiseSetId,
                    JobFileCoordinatorId = job.JobFileCoordinatorId,
                    ProductionManagerId = job.ProductionManagerId,
                    ProjectManagerId = job.ProjectManagerId,
                    CrewChiefId = job.CrewChiefId,
                    ReconSupportId = job.ReconSupportId,
                    GeneralManagerId = job.GeneralManagerId,
                    OfficeManagerId = job.OfficeManagerId,
                    RecpDispatcherId = job.RecpDispatcherId,
                    StatusNotes = job.StatusNotes,
                    PriorityResponder = job.PriorityResponderId,
                    UpdatedBy = "data-fix"
                };
            }
        }
    }
}
