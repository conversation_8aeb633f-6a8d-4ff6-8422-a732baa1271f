﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobVisitTriStateAnswer
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobVisitResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobVisitIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobVisitTriStateAnswer>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobVisitTriStateAnswer> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobVisitTriStateAnswer> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobVisitTriStateAnswer));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobVisitIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var jobVisitTriStateAnswerTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedJobVisitTriStateAnswerIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(jobVisitTriStateAnswerTargetIds, 
                    GetJobVisitTriStateAnswerIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobVisitTriStateAnswer, ResaleJobVisitTriStateAnswer>(
                    request.ResaleId,
                    jobVisitTriStateAnswer =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobVisitResult.FailedEntities.Contains(jobVisitTriStateAnswer.JobVisitId))
                            failedDependencies.Add((nameof(JobVisit), jobVisitTriStateAnswer.JobVisitId));

                        return failedDependencies;
                    },
                    alreadyCopiedJobVisitTriStateAnswerIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobVisitTriStateAnswers.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobVisitTriStateAnswer>> GetSourceEntitiesAsync(List<Guid> jobVisitIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobVisitTriStateAnswers = await _context.JobVisitTriStateAnswers
                    .Where(jvtsa => jobVisitIds.Contains(jvtsa.JobVisitId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobVisitTriStateAnswers.Count);
                return jobVisitTriStateAnswers;
            }

            private async Task<List<Guid>> GetJobVisitTriStateAnswerIdsAsync(List<Guid?> jobVisitTriStateAnswerTargetIds, 
                CancellationToken cancellationToken)
            {
                return await _context.JobVisitTriStateAnswers
                    .Where(x => jobVisitTriStateAnswerTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
