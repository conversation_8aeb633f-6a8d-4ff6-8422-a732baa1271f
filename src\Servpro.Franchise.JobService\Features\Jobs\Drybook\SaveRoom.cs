﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobAreaCreatedEvent;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.RoomCreatedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class SaveRoom
    {
        public class Command : BaseRoomCommand, IRequest
        {
            public Guid JobId { get; set; }
            public ICollection<AffectedFlooringDto> AffectedFlooring { get; set; }
            public ICollection<AdjustmentDto> Adjustments { get; set; }
            public ICollection<MissingSpaceDto> MissingSpaces { get; set; }
            public PreExistingConditionsDto PreExistingConditions { get; set; }
        }

        public class BaseRoomCommand
        {
            public Guid RoomShapeId { get; set; }
            public Guid? RoomTypeId { get; set; }
            public string RoomName { get; set; }
            public decimal AffectedCeilingAreaSquareFeet { get; set; }
            public decimal AffectedWallAreaSquareFeet { get; set; }
            public int CeilingAreaSquareInches { get; set; }
            public int CeilingPerimeterInches { get; set; }
            public int FloorAreaSquareInches { get; set; }
            public int FloorPerimeterInches { get; set; }
            public int Height1TotalInches { get; set; }
            public int Height2TotalInches { get; set; }
            public int Height3TotalInches { get; set; }
            public int Length1TotalInches { get; set; }
            public int Length2TotalInches { get; set; }
            public int MissingCeilingAreaSquareInches { get; set; }
            public int MissingCeilingPerimeterInches { get; set; }
            public int MissingFloorAreaSquareInches { get; set; }
            public int MissingFloorPerimeterInches { get; set; }
            public int MissingWallAreaSquareInches { get; set; }
            public int OffsetCeilingAreaSquareInches { get; set; }
            public int OffsetCeilingPerimeterInches { get; set; }
            public int OffsetFloorAreaSquareInches { get; set; }
            public int OffsetFloorPerimeterInches { get; set; }
            public int OffsetWallAreaSquareInches { get; set; }
            public decimal WallAreaBelow2FeetSquareInches { get; set; }
            public int WallAreaSquareInches { get; set; }
            public int Width1TotalInches { get; set; }
            public int Width2TotalInches { get; set; }
            public decimal AffectedWallAreaAbove2FeetSquareInches { get; set; }
            public decimal AffectedWallAreaBelow2FeetSquareInches { get; set; }
            public long MissingRoomVolumeCubicInches { get; set; }
            public long OffsetRoomVolumeCubicInches { get; set; }
            public long RoomVolumeCubicInches { get; set; }
            public int Length3TotalInches { get; set; }
            public int Width3TotalInches { get; set; }
            public long NetVolume { get; set; }
            public long NetCeilingSquareFootage { get; set; }
            public long NetFloorSquareFootage { get; set; }
            public long NetWallSquareFootage { get; set; }
            public long NetCeilingPerimeter { get; set; }
            public long NetFloorPerimeter { get; set; }
            public long TotalOffsetsSquareFootage { get; set; }
            public long TotalInsetsSquareFootage { get; set; }
            public long TotalOpeningsSquareFootage { get; set; }
            public Guid? ExpressDimensionImageKey { get; set; }
            public bool? RequiresContainment { get; set; }
            public long PercentageContained { get; set; }
        }

        public class AffectedFlooringDto
        {
            public Guid FlooringTypeId { get; set; }
            public string OtherText { get; set; }
            public bool? IsSalvageable { get; set; }
            public bool? IsPadRestorable { get; set; }
            public decimal TotalSquareFeet { get; set; }
            public decimal AffectedSquareFeet { get; set; }
            public decimal AffectedPercentage { get; set; }
            public decimal SavedSquareFeet { get; set; }
            public decimal SavedPercentage { get; set; }
        }

        public class AdjustmentDto
        {
            public string Name { get; set; }
            public Guid RoomAdjustmentTypeId { get; set; }
            public bool IsAdjoiningWallRemoved { get; set; }
            public bool IsOppositeWallRemoved { get; set; }
            public bool IsLeftWallRemoved { get; set; }
            public bool IsRightWallRemoved { get; set; }
            public int DepthTotalInches { get; set; }
            public int HeightTotalInches { get; set; }
            public int WidthTotalInches { get; set; }
            public bool IsCeilingAffected { get; set; }
            public long Volume { get; set; }
            public long CeilingSquareFootage { get; set; }
            public long FloorSquareFootage { get; set; }
            public long WallSquareFootage { get; set; }
            public Guid? Subtype { get; set; }
        }

        public class MissingSpaceDto
        {
            public Guid MissingSpaceReasonTypeId { get; set; }
            public Guid MissingSpaceRemoveFromTypeId { get; set; }
            public int Quantity { get; set; }
            public int LengthTotalInches { get; set; }
            public int HeightTotalInches { get; set; }
            public int WidthTotalInches { get; set; }
            public string Name { get; set; }
            public string OtherReason { get; set; }
            public int DepthTotalInches { get; set; }
        }

        public class PreExistingConditionsDto
        {
            public bool IsDirty { get; set; }
            public bool NeedsRefresh { get; set; }
            public string Note { get; set; }
            public DateTime NoteDate { get; set; }
            public string Subject { get; set; }
            public Guid VisibilityId { get; set; }
        }


        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.RoomShapeId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                  .Include(x => x.JobLocks)
                  .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                var roomId = Guid.NewGuid();
                var userInfo = _userInfo.GetUserInfo();
                var username = userInfo.Username;
                var author = userInfo.Name;

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var room = new Room
                {
                    Id = roomId,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = username,
                    RoomTypeId = request.RoomTypeId,
                    RoomShapeId = request?.RoomShapeId ?? RoomShapes.Box,
                    WaterCategoryId = WaterCategories.Sanitary,
                    WaterClassId = WaterClasses.NoWaterDamage,
                    FloorTypeId = FloorTypes.AboveGround,
                    AffectedCeilingAreaSquareInches = request.AffectedCeilingAreaSquareFeet * 144,
                    AffectedWallAreaSquareInches = request.AffectedWallAreaSquareFeet * 144,
                    AffectedWallAreaAbove2FeetSquareInches = request.AffectedWallAreaAbove2FeetSquareInches,
                    AffectedWallAreaBelow2FeetSquareInches = request.AffectedWallAreaBelow2FeetSquareInches,
                    CeilingAreaSquareInches = request.CeilingAreaSquareInches,
                    CeilingPerimeterInches = request.CeilingPerimeterInches,
                    FloorAreaSquareInches = request.FloorAreaSquareInches,
                    FloorPerimeterInches = request.FloorPerimeterInches,
                    Height1TotalInches = request.Height1TotalInches,
                    Height2TotalInches = request.Height2TotalInches,
                    Height3TotalInches = request.Height3TotalInches,
                    Length1TotalInches = request.Length1TotalInches,
                    Length2TotalInches = request.Length2TotalInches,
                    MissingCeilingAreaSquareInches = request.MissingCeilingAreaSquareInches,
                    MissingCeilingPerimeterInches = request.MissingCeilingPerimeterInches,
                    MissingFloorAreaSquareInches = request.MissingFloorAreaSquareInches,
                    MissingFloorPerimeterInches = request.MissingFloorPerimeterInches,
                    MissingRoomVolumeCubicInches = request.MissingRoomVolumeCubicInches,
                    MissingWallAreaSquareInches = request.MissingWallAreaSquareInches,
                    OffsetCeilingAreaSquareInches = request.OffsetCeilingAreaSquareInches,
                    OffsetCeilingPerimeterInches = request.OffsetCeilingPerimeterInches,
                    OffsetFloorAreaSquareInches = request.OffsetFloorAreaSquareInches,
                    OffsetFloorPerimeterInches = request.OffsetFloorPerimeterInches,
                    OffsetRoomVolumeCubicInches = request.OffsetRoomVolumeCubicInches,
                    OffsetWallAreaSquareInches = request.OffsetWallAreaSquareInches,
                    WallAreaBelow2FeetSquareInches = request.WallAreaBelow2FeetSquareInches,
                    WallAreaSquareInches = request.WallAreaSquareInches,
                    Width1TotalInches = request.Width1TotalInches,
                    Width2TotalInches = request.Width2TotalInches,
                    RoomVolumeCubicInches = request.RoomVolumeCubicInches,
                };

                var journalNoteId = SaveJournalNote(request.JobId,
                    username,
                    author,
                    request.PreExistingConditions);

                if (journalNoteId != Guid.Empty)
                {
                    room.PreExistingConditions = request.PreExistingConditions.Note;
                    room.PreExistingConditionsDiaryNoteId = journalNoteId;
                }

                var adjustments = MapAdjustments(request.Adjustments, username).ToList();
                room.OffsetSpaces = adjustments;

                var missingSpaces = MapMissingSpaces(request.MissingSpaces, username).ToList();
                room.MissingSpaces = missingSpaces;

                _context.Rooms.Add(room);

                SaveFlooringTypeAffected(
                        request.AffectedFlooring,
                        roomId,
                        username);

                SaveJobArea(request.JobId,
                    roomId,
                    username,
                    request.RoomName);

                //Publish Events
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var events = GenerateEvents(correlationId, userInfo, request.JobId);
                await _context.OutboxMessages.AddRangeAsync(events, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private IEnumerable<OutboxMessage> GenerateEvents(Guid correlationId, UserInfo user, Guid jobId)
            {
                var jobAreaCreatedEvent = GenerateJobAreaCreatedEvent(correlationId, user);
                var journalNoteCreatedEvent = GenerateJournalNoteCreatedEvent(correlationId, user);
                var roomCreatedEvent = GenerateRoomCreatedEvent(correlationId, user, jobId);

                return jobAreaCreatedEvent
                        .Concat(journalNoteCreatedEvent)
                        .Concat(roomCreatedEvent)
                        .ToList();
            }

            #region Methods for generating JobAreaCreatedEvent
            private IEnumerable<OutboxMessage> GenerateJobAreaCreatedEvent(
                Guid correlationId,
                UserInfo userInfo)
            {
                var jobAreaCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobArea
                        && x.State == EntityState.Added)
                    .Select(x => x.Entity as JobArea)
                    .Select(x => new JobAreaCreatedEvent(MapJobAreaCreatedDto(x, userInfo), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JobAreaCreatedEvent), correlationId, userInfo.Username));

                return jobAreaCreatedEvents;
            }

            private JobAreaCreatedDto MapJobAreaCreatedDto(JobArea jobArea, UserInfo userInfo)
                => new JobAreaCreatedDto
                {
                    Id = jobArea.Id,
                    JobId = jobArea.JobId,
                    BeginJobVisitId = jobArea.BeginJobVisitId,
                    CreatedBy = userInfo.Name,
                    CreatedDate = DateTime.UtcNow,
                    EndJobVisitId = jobArea.EndJobVisitId,
                    IsUsedInValidation = jobArea.IsUsedInValidation,
                    JobAreaTypeId = jobArea.JobAreaTypeId,
                    Name = jobArea.Name,
                    RoomId = jobArea.RoomId,
                    SortOrder = jobArea.SortOrder,
                    ZoneId = jobArea.ZoneId
                };
            #endregion Methods for generating JobAreaCreatedEvent

            #region Methods for generating RoomCreatedEvent
            private IEnumerable<OutboxMessage> GenerateRoomCreatedEvent(
                Guid correlationId,
                UserInfo userInfo,
                Guid jobId)
            {
                var roomCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Room
                        && x.State == EntityState.Added)
                    .Select(x => x.Entity as Room)
                    .Select(x => new RoomCreatedEvent(MapRoomCreatedDto(x, userInfo, jobId), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(RoomCreatedEvent), correlationId, userInfo.Username));

                return roomCreatedEvents;
            }

            private RoomCreatedDto MapRoomCreatedDto(Room room, UserInfo userInfo, Guid jobId)
                => new RoomCreatedDto
                {
                    Id = room.Id,
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.UtcNow,
                    AffectedCeilingAreaSquareInches = room.AffectedCeilingAreaSquareInches,
                    AffectedWallAreaAbove2FeetSquareInches = room.AffectedWallAreaAbove2FeetSquareInches,
                    AffectedWallAreaBelow2FeetSquareInches = room.AffectedWallAreaBelow2FeetSquareInches,
                    AffectedWallAreaSquareInches = room.AffectedWallAreaSquareInches,
                    AreaAffectedPercentage = room.AreaAffectedPercentage,
                    CarpetTypeId = room.CarpetTypeId,
                    CeilingAreaSquareInches = room.CeilingAreaSquareInches,
                    CeilingPerimeterInches = room.CeilingPerimeterInches,
                    DryOnJobVisitId = room.DryOnJobVisitId,
                    Floor = room.Floor,
                    FloorAreaSquareInches = room.FloorAreaSquareInches,
                    FlooringPercentageAffected = room.FlooringPercentageAffected,
                    FlooringPercentageSaved = room.FlooringPercentageSaved,
                    FloorPerimeterInches = room.FloorPerimeterInches ,
                    FloorTypeId = room.FloorTypeId,
                    HasDamagedContents = room.HasDamagedContents,
                    HasSpecialSituation = room.HasSpecialSituation,
                    Height1TotalInches = room.Height1TotalInches,
                    Height2TotalInches = room.Height2TotalInches,
                    Height3TotalInches = room.Height3TotalInches,
                    IsContentScopeCompleted = room.IsContentScopeCompleted,
                    IsPadRestorable = room.IsPadRestorable,
                    IsSlopeOrientedAlongLength = room.IsSlopeOrientedAlongLength,
                    IsStructureScopeCompleted = room.IsStructureScopeCompleted,
                    Length1TotalInches = room.Length1TotalInches,
                    Length2TotalInches = room.Length2TotalInches,
                    MissingCeilingAreaSquareInches = room.MissingCeilingAreaSquareInches,
                    MissingCeilingPerimeterInches = room.MissingCeilingPerimeterInches,
                    MissingFloorAreaSquareInches = room.MissingFloorAreaSquareInches,
                    MissingFloorPerimeterInches = room.MissingFloorPerimeterInches,
                    MissingRoomVolumeCubicInches = room.MissingRoomVolumeCubicInches,
                    MissingWallAreaSquareInches = room.MissingWallAreaSquareInches,
                    OffsetCeilingAreaSquareInches = room.OffsetCeilingAreaSquareInches,
                    OffsetCeilingPerimeterInches = room.OffsetCeilingPerimeterInches,
                    OffsetFloorAreaSquareInches = room.OffsetFloorAreaSquareInches,
                    OffsetFloorPerimeterInches = room.OffsetFloorPerimeterInches,
                    OffsetRoomVolumeCubicInches = room.OffsetRoomVolumeCubicInches,
                    OffsetWallAreaSquareInches = room.OffsetWallAreaSquareInches,
                    OldId = room.OldId,
                    OtherCarpetType = room.OtherCarpetType,
                    OtherPadType = room.OtherPadType,
                    PadTypeId = room.PadTypeId,
                    PreExistingConditions = room.PreExistingConditions,
                    PreExistingConditionsDiaryNoteId = room.PreExistingConditionsDiaryNoteId,
                    Quantity = room.Quantity,
                    RoomFlooringTypesAffected = room.RoomFlooringTypesAffected?.Select(MapFlooringTypeAffected).ToList(),
                    RoomOrder = room.RoomOrder,
                    RoomShapeId = room.RoomShapeId,
                    RoomTypeId = room.RoomTypeId,
                    RoomVolumeCubicInches = room.RoomVolumeCubicInches,
                    WallAreaBelow2FeetSquareInches = room.WallAreaBelow2FeetSquareInches,
                    WallAreaSquareInches = room.WallAreaSquareInches,
                    WaterCategoryId = room.WaterCategoryId,
                    WaterClassId = room.WaterClassId,
                    Width1TotalInches = room.Width1TotalInches,
                    Width2TotalInches = room.Width2TotalInches,
                    JobId = jobId
                };

            private RoomCreatedEvent.RoomFlooringTypeAffected MapFlooringTypeAffected(Models.Drybook.RoomFlooringTypeAffected flooringTypeAffected)
                => new RoomCreatedEvent.RoomFlooringTypeAffected
                {
                    AffectedPercentage = flooringTypeAffected.AffectedPercentage,
                    AffectedSquareFeet = flooringTypeAffected.AffectedSquareFeet,
                    FlooringTypeId = flooringTypeAffected.FlooringTypeId,
                    IsPadRestorable = flooringTypeAffected.IsPadRestorable,
                    IsSalvageable = flooringTypeAffected.IsSalvageable,
                    OtherText = flooringTypeAffected.OtherText,
                    RoomId = flooringTypeAffected.RoomId,
                    SavedPercentage = flooringTypeAffected.SavedPercentage,
                    SavedSquareFeet = flooringTypeAffected.SavedSquareFeet,
                    TotalSquareFeet = flooringTypeAffected.TotalSquareFeet
                };
            #endregion Methods for generating RoomCreatedEvent

            #region Methods for generating JournalNoteCreatedEvent
            private IEnumerable<OutboxMessage> GenerateJournalNoteCreatedEvent(
                 Guid correlationId,
                UserInfo userInfo)
            {
                var journalNoteCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JournalNote
                        && x.State == EntityState.Added)
                    .Select(x => x.Entity as JournalNote)
                    .Select(x => new JournalNoteCreatedEvent(MapJournalNoteCreatedDto(x, userInfo), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username));

                return journalNoteCreatedEvents;
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteCreatedDto(JournalNote journalNote, UserInfo userInfo)
                => new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    ActionDate = journalNote.ActionDate,
                    Author = journalNote.Author,
                    CategoryId = journalNote.CategoryId,
                    CreatedById = userInfo.Id,
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.Now,
                    JobId = journalNote.JobId.Value,
                    Message = journalNote.Note,
                    Subject = journalNote.Subject,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId
                };
            #endregion Methods for generating JournalNoteCreatedEvent

            private void SaveFlooringTypeAffected(
                IEnumerable<AffectedFlooringDto> flooringTypesAffected,
                Guid roomId,
                string username)
            {
                if (flooringTypesAffected != null)
                {
                    foreach (var floorigTypeAffected in flooringTypesAffected)
                    {
                        _context.RoomFlooringTypesAffected.Add(new Models.Drybook.RoomFlooringTypeAffected
                        {
                            Id = Guid.NewGuid(),
                            CreatedDate = DateTime.UtcNow,
                            CreatedBy = username,
                            RoomId = roomId,
                            AffectedPercentage = floorigTypeAffected.AffectedPercentage,
                            AffectedSquareFeet = floorigTypeAffected.AffectedSquareFeet,
                            FlooringTypeId = floorigTypeAffected.FlooringTypeId,
                            IsPadRestorable = floorigTypeAffected.IsPadRestorable,
                            IsSalvageable = floorigTypeAffected.IsSalvageable,
                            OtherText = floorigTypeAffected.OtherText ?? "",
                            SavedPercentage = floorigTypeAffected.SavedPercentage,
                            SavedSquareFeet = floorigTypeAffected.SavedSquareFeet,
                            TotalSquareFeet = floorigTypeAffected.TotalSquareFeet
                        });
                    }
                }
            }

            private Guid SaveJournalNote(
                Guid jobId,
                string username,
                string author,
                PreExistingConditionsDto preExistingConditions)
            {
                if (preExistingConditions != null)
                {
                    var journalNoteGuid = Guid.NewGuid();
                    _context.JournalNote.Add(new JournalNote
                    {
                        Id = journalNoteGuid,
                        JobId = jobId,
                        CategoryId = JournalNoteCategories.Room,
                        TypeId = TaskTypes.PreExistingCondition,
                        VisibilityId = JournalNoteVisibilities.FranchiseClientAndCustomer,
                        Subject = preExistingConditions.Subject,
                        Note = preExistingConditions.Note,
                        Author = author,
                        ActionDate = DateTime.UtcNow,
                        CreatedBy = username,
                        CreatedDate = DateTime.UtcNow,
                    });

                    return journalNoteGuid;
                }
                else
                {
                    return Guid.Empty;
                }
            }

            private void SaveJobArea(Guid jobId,
                Guid roomId,
                string username,
                string roomName)
            {
                _context.JobAreas.Add(new JobArea
                {
                    Id = Guid.NewGuid(),
                    CreatedBy = username,
                    CreatedDate = DateTime.UtcNow,
                    JobId = jobId,
                    JobAreaTypeId = JobAreaTypes.Room,
                    RoomId = roomId,
                    Name = roomName
                });
            }

            private IEnumerable<OffsetSpace> MapAdjustments(ICollection<AdjustmentDto> adjustments,
                string username)
            {
                if (adjustments != null)
                {
                    foreach (var adjustment in adjustments)
                    {
                        yield return new OffsetSpace
                        {
                            Id = Guid.NewGuid(),
                            CreatedDate = DateTime.UtcNow,
                            CreatedBy = username,
                            Version = DateTime.UtcNow,
                            Name = adjustment.Name,
                            OffsetSpaceTypeId = adjustment.RoomAdjustmentTypeId,
                            IsAdjoiningWallRemoved = adjustment.IsAdjoiningWallRemoved,
                            IsOppositeWallRemoved = adjustment.IsOppositeWallRemoved,
                            IsLeftWallRemoved = adjustment.IsLeftWallRemoved,
                            IsRightWallRemoved = adjustment.IsRightWallRemoved,
                            DepthTotalInches = adjustment.DepthTotalInches,
                            HeightTotalInches = adjustment.HeightTotalInches,
                            WidthTotalInches = adjustment.WidthTotalInches
                        };
                    }
                }
            }

            private IEnumerable<MissingSpace> MapMissingSpaces(ICollection<MissingSpaceDto> missingSpaces,
                string username)
            {
                if (missingSpaces != null)
                {
                    foreach (var missingSpace in missingSpaces)
                    {
                        yield return new MissingSpace
                        {
                            Id = Guid.NewGuid(),
                            CreatedDate = DateTime.UtcNow,
                            CreatedBy = username,
                            Version = DateTime.UtcNow,
                            MissingSpaceReasonTypeId = missingSpace.MissingSpaceReasonTypeId,
                            Quantity = missingSpace.Quantity,
                            LengthTotalInches = missingSpace.LengthTotalInches,
                            HeightTotalInches = missingSpace.HeightTotalInches,
                            WidthTotalInches = missingSpace.WidthTotalInches,
                            Name = missingSpace.Name,
                            OtherReason = missingSpace.OtherReason,
                            DepthTotalInches = missingSpace.DepthTotalInches,
                            MissingSpaceRemoveFromTypeId = missingSpace.MissingSpaceRemoveFromTypeId
                        };
                    }
                }
            }
        }
    }
}
