﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class EditJournalNote
    {
        public class Command : GetJournalNote.Dto, IRequest { }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.TypeId).NotEmpty();
                RuleFor(m => m.VisibilityId).NotEmpty();
                RuleFor(command => command.Subject)
                    .NotNull()
                    .NotEmpty()
                    .Length(2, 64);
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _db;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(
                JobDataContext db,
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfo)
            {
                _lookupServiceClient = lookupServiceClient;
                _db = db;
                _userInfo = userInfo;
            }

            public async Task<Unit> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var journalNote = await _db.JournalNote
                    .Include(x => x.Job)
                    .Include(x => x.Task)
                    .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

                if (journalNote is null)
                    throw new ResourceNotFoundException($"JournalNote not found (Id: {request.Id}");
                
                if (journalNote.Job != null)
                    journalNote.Job.JobLocks = await _db.JobLock.Where(x => x.JobId == journalNote.Job.Id).ToListAsync(cancellationToken);
                
                var userInfo = _userInfo.GetUserInfo();
                if (JobLockUtils.HasLockConflict(journalNote.Job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(journalNote.Job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(journalNote.Job.CurrentJobLock));

                //Update status of Task
                var task = journalNote.Task;
                if (task != null)
                {
                    var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                    var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == task.TaskTypeId);

                    if (taskType.ShouldCloseWhenDiaryEntryIsComplete && !string.IsNullOrEmpty(request.Note))
                    {
                        task.PercentComplete = 100;
                        task.TaskStatusId = TaskStatuses.Completed;
                    }
                }

                Map(journalNote, request, userInfo.Username);
                await _db.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private void Map(JournalNote journalNote,
                Command request,
                string username)
            {
                journalNote.ActionDate = request.ActionDate;
                journalNote.CategoryId = request.CategoryId;
                journalNote.Note = request.Note;
                journalNote.Subject = request.Subject;
                journalNote.TypeId = request.TypeId;
                journalNote.VisibilityId = request.VisibilityId;
                journalNote.ModifiedDate = DateTime.UtcNow;
                journalNote.ModifiedBy = username;
            }
        }
    }
}
