@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.EquipmentUsageDto

@if (Model != null && Model.ZoneEquipmentUsages != null && Model.ZoneEquipmentUsages.Any())
{
    <div class="title">{zoneEquipmentUsage}Zone Equipment Usage{/zoneEquipmentUsage}</div>

    @foreach (var zone in Model.ZoneEquipmentUsages.OrderBy(u => u.ZoneName))
    {
        <div class="subtitle">{zoneEquipUsageName}@zone.ZoneName Equipment Usage{/zoneEquipUsageName}</div>

        @await Html.PartialAsync("~/Features/Jobs/Drybook/Report/ReportViews/_zoneEquipmentUsageGraph.cshtml", zone.EquipmentPlacementCountByDay)

        if (!zone.EquipmentUsageVisits.Any())
        {
            continue;
        }

        <table class="zoneCompositionTable" summary="Zone Composition Table">
            <thead>
                <tr class="darkTableCell">
                    <th scope="col" class="grayBorder" style="max-width:100px">Date</th>
                    <th scope="col" class="grayBorder">Room</th>
                    <th scope="col" class="grayBorder" style="max-width:45px">Air Mover Count</th>
                    <th scope="col" class="grayBorder">Dehu Model</th>
                    <th scope="col" class="grayBorder" style="max-width:55px">Dehu Rating</th>
                    <th scope="col" class="grayBorder">Asset No</th>
                    <th scope="col" class="grayBorder" style="max-width:50px">Dehu Hour Counter</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var equipment in zone.EquipmentUsageVisits.OrderBy(x => x.JobVisitDateTime))
                {
                    int dehuCountModel = 0;
                    int dehuRaiting = 0;
                    int dehuHour = 0;
                    int counter = 0;
                    int rowSpanNumber = 0;
                    string dehuType = string.Empty;

                    @foreach (var room in equipment.RoomEquipmentUsageItems)
                    {
                        @if (room.DehuUsageItems.Any())
                        {
                            int dehuItemCounter = 0;
                            int dehuItemRowSpan = 0;
                            
                            @foreach (var item in room.DehuUsageItems)
                            {
                                dehuType = item.DehuRatingType.ToString().ToUpper();
                                dehuCountModel++;
                                dehuRaiting += item.DehuRating.HasValue ? item.DehuRating.Value : 0;
                                dehuHour += item.DehuHourReading.HasValue ? item.DehuHourReading.Value : 0;

                                <tr>
                                    @if (++counter == 1)
                                    {
                                        rowSpanNumber = equipment.RoomEquipmentUsageItems.Count() + 1;
                                        foreach (var roomUsageItem in equipment.RoomEquipmentUsageItems.Where(x => x.DehuUsageItems.Count > 0))
                                            rowSpanNumber += roomUsageItem.DehuUsageItems.Count - 1;

                                        <td rowspan="@rowSpanNumber" class="grayBorder cellCenterText cellAlignTop" style="max-width:100px">@equipment.JobVisitDateTime.ToString("M/d/yyyy h:mm tt") Tech: @equipment.Technician</td>
                                    }
                                    @if (++dehuItemCounter == 1)
                                    {
                                        dehuItemRowSpan = room.DehuUsageItems.Count();
                                        <td rowspan="@dehuItemRowSpan" class="grayBorder cellCenterText cellAlignTop">@room.RoomName</td>
                                        <td rowspan="@dehuItemRowSpan" class="grayBorder cellCenterText cellAlignTop">@room.AirMoverCount</td>
                                    }

                                    <td class="grayBorder cellCenterText">@item.DehuModel</td>
                                    <td class="grayBorder cellCenterText">@item.DehuRating @dehuType</td>
                                    <td class="grayBorder cellCenterText">@item.AssetNumber</td>
                                    <td class="grayBorder cellCenterText">@item.DehuHourReading</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                @if (++counter == 1)
                                {
                                    rowSpanNumber = equipment.RoomEquipmentUsageItems.Count() + 1;
                                    foreach (var roomUsageItem in equipment.RoomEquipmentUsageItems.Where(x => x.DehuUsageItems.Count > 0))
                                        rowSpanNumber += roomUsageItem.DehuUsageItems.Count - 1;

                                    <td rowspan="@rowSpanNumber" class="grayBorder cellCenterText cellAlignTop" style="max-width:100px">@equipment.JobVisitDateTime.ToString("M/d/yyyy h:mm tt") Tech: @equipment.Technician</td>
                                }
                                <td class="grayBorder cellCenterText">@room.RoomName</td>
                                <td class="grayBorder cellCenterText">@room.AirMoverCount</td>
                                <td class="grayBorder"></td>
                                <td class="grayBorder"></td>
                                <td class="grayBorder"></td>
                                <td class="grayBorder"></td>
                            </tr>
                        }
                    }
                <tr>
                    <td class="grayBorder darkTableCell"></td>
                    <td class="grayBorder darkTableCell"></td>
                    <td class="grayBorder darkTableCell cellSummary">@dehuCountModel</td>
                    <td class="grayBorder darkTableCell cellSummary" colspan="2">@dehuRaiting @dehuType</td>
                    <td class="grayBorder darkTableCell cellSummary">@dehuHour</td>
                </tr>
                }
            </tbody>
        </table>

        <div style='page-break-before: always;'></div>
    }

    <div style='page-break-before: always;'></div>
}