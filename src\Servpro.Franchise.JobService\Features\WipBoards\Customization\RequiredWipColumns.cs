﻿using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.WipBoards.Customization
{
    public class RequiredWipColumns
    {
        public static List<string> RequiredColumns => new List<string>()
            {
                "id",
                "jobRowId",
                "dispatchedDate",
                "projectNumber",
                "franchiseSetId",
                "franchiseId",
                "lossBusinessName",
                "jobProgress",
                "journalNoteCount",
                "documentCount",
                "photoCount",
                "islocked",
                "lockedByUser",
                "lockedTime",
                "lockedByDevice",
                "lockedByApplication",
                "jobFileCoordinatorId",
                "productionManagerId",
                "projectManagerId",
                "crewChiefId",
                "recpDispatcherId",
                "generalManagerId",
                "officeManagerId",
                "reconSupportId",
                "statusNotes",
                "isChaseLead",
                "redFlagNotes",
                "mergeCandidates",
                "mergeSource",
                "mergeTarget",
                "mentorId",
                "finalUploadDueDate",
                "mostRecentFinalUploadCompleted",
                "finalExtensionCount",
                "finalUploadStatus",
                "numberOfFinalReturns",
                "targetCompletionDate",
                "selfAuditStatus",
                "selfAuditDate",
                "selfAuditReturnedDate",
                "corporateAuditStatus",
                "corporateAuditDueDate",
                "corporateAuditDate",
                "corporateAuditReturnedDate",
                "finalUploadPastDueDays",
                "selfAuditPastDueHours",
                "corporateAuditPastDueDays",
                "isAuditRequired",
                "propertyTypeName",
                "dateOfLoss",
                "isNonStandardJob"
            };
    }
}
