﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class DrawingHelper
    {
        public static string GetSvgPath(string[][] path)
        {
            var pathResult = "";
            foreach (var pathItem in path)
            {
                foreach (var pathObjectItem in pathItem)
                {
                    pathResult += pathObjectItem + " ";
                }
            };
            return pathResult.Trim();
        }

        public static string GetImageStyle(CanvasObject canvas)
        {
             var style = "position: absolute; transform: translate(" + canvas.Left + "px, " + canvas.Top +"px) " +
                "rotate("+ canvas.Angle + "deg); transform-origin: top left;";

            return style;
        }
        public static string GetTextStyle(CanvasObject canvas)
        {
            var style = "left: " + canvas.Left.ToString() + "px; top: " + (canvas.Top + 20).ToString() + "px; position: absolute; font-family: Arial, Helvetica, sans-serif; font-size: 40px;";
            return style;
        }
        public static string GetPathStyle(CanvasObject canvas)
        {
            var style = "position: absolute;";
            return style;
        }

        public static List<CanvasObject> GetCanvasItems(string canvasJson)
        {
            var canvasObjects = JsonConvert.DeserializeObject<Canvas>(canvasJson);
            return canvasObjects.Objects;
        }
    }

    public class Canvas
    {
        public List<CanvasObject> Objects { get; set; }
    }

    public class CanvasObject
    {
        public string Text { get; set; }
        public string Type { get; set; }
        public string Src { get; set; }
        public string[][] Path { get; set; }
        public float Width { get; set; }
        public float Height { get; set; }
        public float? Left { get; set; }
        public float? Top { get; set; }
        public float ScaleX { get; set; }
        public float ScaleY { get; set; }
        public float StrokeWidth { get; set; }
        public float Angle { get; set; }
        public PathOffset PathOffset { get; set; }
    }

    public class PathOffset
    {
        public float X { get; set; }
        public float Y { get; set; }
    }
}
