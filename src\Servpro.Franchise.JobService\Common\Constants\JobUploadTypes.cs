﻿
using System;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class JobUploadTypes
    {
        public static readonly Guid JobInitial = new Guid("00000001-0011-5365-4444-70726f490000");
        public static readonly Guid JobDaily = new Guid("00000001-0011-5365-4444-70726f490001");
        public static readonly Guid JobFinal = new Guid("00000001-0011-5365-4444-70726f490002");
        public static readonly Guid XactJobInitial = new Guid("00000001-0011-5365-4444-70726f490003");
        public static readonly Guid XactJobDaily = new Guid("00000001-0011-5365-4444-70726f490004");
        public static readonly Guid XactJobFinal = new Guid("00000001-0011-5365-4444-70726f490005");
        public static readonly Guid XactLineItem = new Guid("00000001-0011-5365-4444-70726f490006");
        public static readonly Guid XactJobDiaryNotes = new Guid("00000001-0011-5365-4444-70726f490007");
        public static readonly Guid XactDailyNarrative = new Guid("00000001-0011-5365-4444-70726f490008");
        public static readonly Guid InitialExtension = new Guid("00000001-0011-5365-4444-70726f490009");
        public static readonly Guid FinalExtension = new Guid("00000001-0011-5365-4444-70726f49000a");
        public static readonly Guid DailyExtension = new Guid("00000001-0011-5365-4444-70726f49000b");
    }
}