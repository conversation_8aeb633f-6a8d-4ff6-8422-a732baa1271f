﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Common.Utils
{
    public class PerformanceTimer : IDisposable
    {
        private readonly string _message;
        private readonly Stopwatch _timer;
        private readonly ILogger _logger;

        public PerformanceTimer(string message, ILogger logger)
        {
            _message = message;
            _timer = new Stopwatch();
            _logger = logger;
            _timer.Start();
        }

        public void Dispose()
        {
            _timer.Stop();
            _logger.LogInformation($"{_message} Total time taken: {_timer.ElapsedMilliseconds} milliseconds");
        }
    }
}
