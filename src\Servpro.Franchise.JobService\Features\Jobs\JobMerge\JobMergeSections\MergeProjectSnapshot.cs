﻿using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.JobService.Data;

using System;
using System.Collections.Generic;
using System.Linq;

using JobDateTypes = Servpro.Franchise.JobService.Common.JobDateTypes;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeProjectSnapshot : IJobMergeSection
    {
        private readonly HashSet<Guid> JobDateTypesToMerge = new HashSet<Guid>() { JobDateTypes.DryingComplete };

        public void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            if (!targetJob.CorporateJobNumber.HasValue)
            {
                targetJob.JobProgress = sourceJob.JobProgress;
            }

            MergeJobDates(sourceJob, targetJob, hasCallDispositionSourceJob, hasCallDispositionTargetJob);

            MergeDispositionCall(sourceJob, targetJob, hasCallDispositionSourceJob, hasCallDispositionTargetJob);

            targetJob.IsThereStandingWater = sourceJob.IsThereStandingWater;
            targetJob.IsWaterAvailable = sourceJob.IsWaterAvailable;
            targetJob.IsElectricAvailable = sourceJob.IsElectricAvailable;
            targetJob.HasSourceBeenTurnedOff = sourceJob.HasSourceBeenTurnedOff;

            targetJob.SourceOfOpportunity = sourceJob.SourceOfOpportunity;
            targetJob.StructureTypeId = sourceJob.StructureTypeId;
            targetJob.FacilityTypeId = sourceJob.FacilityTypeId;
            targetJob.YearStructureBuilt = sourceJob.YearStructureBuilt;
            targetJob.IsCeilingAffected = sourceJob.IsCeilingAffected;
            targetJob.IsWallAffected = sourceJob.IsWallAffected;
            targetJob.IsContentAffected = sourceJob.IsContentAffected;
            targetJob.LevelsAffected = sourceJob.LevelsAffected;
            targetJob.RoomsAffected = sourceJob.RoomsAffected;
            targetJob.LossOccurredOnLevel = sourceJob.LossOccurredOnLevel;
            targetJob.SquareFeetAffected = sourceJob.SquareFeetAffected;
            targetJob.SquareFeet = sourceJob.SquareFeet;
            targetJob.IsMultiUnitStructure = sourceJob.IsMultiUnitStructure;
            targetJob.LossNote = sourceJob.LossNote;
            targetJob.StatusNotes = sourceJob.StatusNotes;

            targetJob.PriorityResponderId = sourceJob.PriorityResponderId;
            targetJob.JobFileCoordinatorId = sourceJob.JobFileCoordinatorId;
            targetJob.ProjectManagerId = sourceJob.ProjectManagerId;
            targetJob.ProductionManagerId = sourceJob.ProductionManagerId;
            targetJob.CrewChiefId = sourceJob.CrewChiefId;
            targetJob.RecpDispatcherId = sourceJob.RecpDispatcherId;
            targetJob.GeneralManagerId = sourceJob.GeneralManagerId;
            targetJob.OfficeManagerId = sourceJob.OfficeManagerId;
            targetJob.ReconSupportId = sourceJob.ReconSupportId;
            targetJob.BusinessName = sourceJob.BusinessName;
            targetJob.IsWarmLead = sourceJob.IsWarmLead;
            targetJob.LeadEstimate = sourceJob.LeadEstimate;
            targetJob.MarketingRepId = sourceJob.MarketingRepId;
            targetJob.DeductibleAmountDue = sourceJob.DeductibleAmountDue;
            targetJob.JobCancelReasonId = sourceJob.JobCancelReasonId;
            targetJob.OnHoldReasonId = sourceJob.OnHoldReasonId;
            targetJob.HasInvoice = sourceJob.HasInvoice;
            targetJob.JobEstimator = sourceJob.JobEstimator;
            targetJob.IsRedFlagged = sourceJob.IsRedFlagged;
            targetJob.RedFlagNotes = sourceJob.RedFlagNotes;

            targetJob.LossSeverityId = sourceJob.LossSeverityId;
            targetJob.ProjectRangeId = sourceJob.ProjectRangeId;
            targetJob.MentorId = sourceJob.MentorId;
            targetJob.DefaultMentorId = sourceJob.DefaultMentorId;

            var sourceFlooringTypes = sourceJob.FlooringTypesAffected
                .Where(x => !targetJob.FlooringTypesAffected.Contains(x)).ToList();
            targetJob.FlooringTypesAffected.ToList().AddRange(sourceFlooringTypes);
        }

        public void MergeDispositionCall(Job sourceJob, Job targetJob, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            var sourceCall = sourceJob.ExternalMarketingCall;
            var targetCall = targetJob.ExternalMarketingCall;

            if (hasCallDispositionSourceJob && !hasCallDispositionTargetJob)
            {
                if (sourceCall != null)
                {
                    sourceCall.ModifiedDate = DateTime.UtcNow;
                    sourceCall.AssociatedJobLastUpdatedDateTime = DateTime.UtcNow;
                    sourceCall.AssociatedJobId = targetJob.Id;
                }
                targetJob.CallDisposition = sourceJob.CallDisposition;
                targetJob.CallRecordingId = sourceJob.CallRecordingId;
                sourceJob.CallDisposition = null;
                sourceJob.CallRecordingId = null;
            }

            if (!hasCallDispositionSourceJob && hasCallDispositionTargetJob)
            {
                targetJob.CallDisposition = targetJob.CallDisposition;
            }
            if (hasCallDispositionSourceJob && hasCallDispositionTargetJob)
            {
                if (targetJob.CreatedDate.IsEarlierThan(sourceJob.CreatedDate))
                {
                    if (sourceCall != null)
                    {
                        sourceCall.Disposition = GetMergedStatus();
                    }
                }
                else
                {
                    if (sourceCall != null)
                    {
                        sourceCall.ModifiedDate = DateTime.UtcNow;
                        sourceCall.AssociatedJobLastUpdatedDateTime = DateTime.UtcNow;
                        sourceCall.AssociatedJobId = targetJob.Id;
                    }
                    if (targetCall != null)
                    {
                        SetCallToMergedDisposition(targetCall);
                    }
                    sourceJob.CallRecordingId = null;
                }
            }
        }

        private void SetCallToMergedDisposition(ExternalMarketingCall call)
        {
            call.ModifiedDate = DateTime.UtcNow;
            call.AssociatedJobLastUpdatedDateTime = DateTime.UtcNow;
            call.AssociatedJobId = null;
            call.Disposition = GetMergedStatus();
        }

        private Guid GetMergedStatus()
        {
            return Guid.Parse("06781c69-1a50-4e8b-93c2-7ff068a9faa2");
        }

        private void MergeJobDates(Job sourceJob, Job targetJob, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            foreach (var sourceJobDate in sourceJob.JobDates)
            {
                var jobDateType = sourceJobDate.JobDateTypeId;
                var targetJobDate = targetJob.JobDates.FirstOrDefault(x => x.JobDateTypeId == jobDateType);
                var sourceDate = sourceJobDate.Date;
                if (hasCallDispositionSourceJob && !hasCallDispositionTargetJob)
                {
                    targetJob.SetOrUpdateDate(jobDateType, sourceDate);
                }
                else
                {
                    if (jobDateType == JobDateTypes.CustomerCalled || jobDateType == JobDateTypes.InitialOnSiteArrival)
                    {
                        var newDate = sourceDate;

                        if (targetJobDate != null)
                        {
                            var targetDate = targetJobDate.Date;
                            newDate = sourceDate.IsEarlierThan(targetDate) ? sourceDate : targetDate;
                        }

                        targetJob.SetOrUpdateDate(jobDateType, newDate);
                    }

                    if (jobDateType == JobDateTypes.EmergencyServicesScheduled && targetJobDate is null)
                        targetJob.SetOrUpdateDate(jobDateType, sourceDate);

                    if (JobDateTypesToMerge.Contains(jobDateType))
                        targetJob.SetOrUpdateDate(jobDateType, sourceDate);
                }

            }

            GenerateMergedDatesCorrespondenceNote(targetJob);
        }

        private void GenerateMergedDatesCorrespondenceNote(Job targetJob)
        {
            var customerCalledOn = targetJob.JobDates.FirstOrDefault(x => x.JobDateTypeId == JobDateTypes.CustomerCalled)?.Date;
            var arrivedAt = targetJob.JobDates.FirstOrDefault(x => x.JobDateTypeId == JobDateTypes.InitialOnSiteArrival)?.Date;

            targetJob.JournalNotes.Add(new JournalNote
            {
                JobId = targetJob.Id,
                Subject = "Merged Local Project Information",
                Note = $"(merged) Customer Called on: {customerCalledOn} (merged) Arrived at: {arrivedAt}",
                CategoryId = JournalNotesCategoryTypes.Notes,
                TypeId = JournalNoteTypes.Note,
                VisibilityId = JournalNoteVisibilityTypes.FranchiseClientandCustomer,
                ActionDate = DateTime.Now
            });
        }
    }
}
