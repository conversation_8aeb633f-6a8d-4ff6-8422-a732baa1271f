﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Locks
{
    public class StealJob
    {
        public class Command : IRequest<GetJobLock.Dto>
        {
            public Guid JobId { get; set; }
            public string DeviceId { get; set; }
            public Guid ApplicationId { get; set; }
        }

        public class Handler : IRequestHandler<Command, GetJobLock.Dto>
        {
            private readonly JobDataContext _context;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<StealJob> _logger;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor,
                ILookupServiceClient lookupServiceClient,
                ILogger<StealJob> logger)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            public async Task<GetJobLock.Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}{eventName}", request.JobId, nameof(StealJob));
                _logger.LogInformation("Begin command with request: {@request}", request);

                ValidateRequest(request);

                var job = await GetJobFromDb(request, cancellationToken);
                var clientApplication = await GetClientApplication(request, cancellationToken);

                var jobLock = GetLatestJobLock(job);
                var userInfo = _userInfo.GetUserInfo();
                ValidateUserFranchiseSet(job, userInfo);
                Guid correlationId = GetOrCreateCorrelationId();

                JobLock newJobLock = jobLock;

                if (jobLock is null || !jobLock.IsLocked)
                {
                    //Lock a job either by creating a new one or just locking it 
                    if (jobLock is null)
                    {
                        newJobLock = GenerateNewJobLock(request, userInfo, clientApplication.Name);

                        _logger.LogWarning("No job lock found to steal, creating new lock: {@newLock}", newJobLock);

                        job.JobLocks.Add(newJobLock);
                    }
                    else
                    {
                        _logger.LogInformation("Job lock was found to steal, but is not locked, locking job.");

                        jobLock.IsLocked = true;
                        jobLock.LockedByUserId = userInfo.Id;
                        jobLock.LockedByUserFullName = userInfo.Name;
                        jobLock.LockedByDeviceId = request.DeviceId;
                        jobLock.LockedTime = DateTime.UtcNow;
                        jobLock.ModifiedBy = userInfo.Username;
                        jobLock.ModifiedDate = DateTime.UtcNow;
                        jobLock.LockedByApplicationName = clientApplication.Name;
                        jobLock.LockedByApplicationId = request.ApplicationId;

                        newJobLock = jobLock;
                    }

                    var jobLockEvent = new OutboxMessage(new JobLockEvent(MapUnlockEventDto(newJobLock), correlationId).ToJson(),
                        nameof(JobLockEvent),
                        correlationId,
                        userInfo.Username);

                    await _context.OutboxMessages.AddAsync(jobLockEvent, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                }
                else if (jobLock.LockedByUserId != userInfo.Id
                    || jobLock.LockedByDeviceId != request.DeviceId)
                {
                    _logger.LogInformation("Attempting to unlock previous lock with id: {jobLockId}.", jobLock.Id);

                    //Unlock job
                    jobLock.IsLocked = false;
                    jobLock.UnlockedByUserId = userInfo.Id;
                    jobLock.UnlockedByDeviceId = request.DeviceId;
                    jobLock.UnlockedTime = DateTime.UtcNow;
                    jobLock.ModifiedBy = userInfo.Username;
                    jobLock.ModifiedDate = DateTime.UtcNow;

                    //Lock job
                    newJobLock = GenerateNewJobLock(request, userInfo, clientApplication.Name);
                    job.JobLocks.Add(newJobLock);

                    _logger.LogInformation("Created new job lock: {@newJobLock}.", newJobLock);

                    await AddJobStealEventToOutbox(jobLock, correlationId, cancellationToken);
                }

                return GetJobLock.Handler.Map(jobLock);
            }

            private void ValidateRequest(Command request)
            {
                if (string.IsNullOrEmpty(request.DeviceId))
                    throw new ValidationException($"DeviceId must not be null");
            }

            private void ValidateUserFranchiseSet(Job job, UserInfo user)
            {
                if (job.FranchiseSetId != user.FranchiseSetId)
                {
                    _logger.LogWarning($"User's franchiseSetId ({user.FranchiseSetId}) does not match the job's franchiseSetId ({job.FranchiseSetId})");
                    throw new ValidationException($"User's franchiseSetId ({user.FranchiseSetId}) does not match the job's franchiseSetId ({job.FranchiseSetId})");
                }
            }

            private async Task<Job> GetJobFromDb(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                                        .Include(x => x.JobLocks)
                                        .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return job;
            }

            private async Task<ClientApplicationDto> GetClientApplication(Command request, CancellationToken cancellationToken)
            {
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                if (lookups is null)
                {
                    _logger.LogWarning("Failed to get lookups, exiting process");
                    throw new Exception("Failed to get lookups.");
                }

                var clientApplication = lookups.ClientApplications.FirstOrDefault(c => c.Id == request.ApplicationId);
                if (clientApplication == null)
                    throw new ValidationException($"Client Application Id not found");

                return clientApplication;
            }

            private JobLock GetLatestJobLock(Job job)
            {
                return job.JobLocks.OrderByDescending(x => x.LockedTime).FirstOrDefault();
            }

            private Guid GetOrCreateCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();

                return correlationId;
            }

            private async Task AddJobStealEventToOutbox(JobLock jobLock, Guid correlationId, CancellationToken cancellationToken)
            {
                var stealEvent = CreateJobStealEvent(jobLock, correlationId, _userInfo.GetUserInfo());
                await _context.OutboxMessages.AddAsync(stealEvent, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);
            }

            public JobLockDto MapUnlockEventDto(JobLock jobLock)
            {
                return new JobLockDto
                {
                    Id = jobLock.Id,
                    JobId = jobLock.JobId,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.LockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId,
                    IsAleradyLocked = jobLock.IsLocked
                };
            }

            private OutboxMessage CreateJobStealEvent(JobLock newJobLock, Guid correlationId, UserInfo userInfo)
            {
                var jobStealEvent = new OutboxMessage(new JobStealEvent(MapUnlockEventDto(newJobLock), correlationId).ToJson(),
                    nameof(JobStealEvent),
                    correlationId,
                    userInfo.Username);

                return jobStealEvent;
            }

            public JobLock GenerateNewJobLock(Command request,
                                            UserInfo userInfo,
                                            string applicationName)
            {
                return new JobLock
                {
                    IsLocked = true,
                    JobId = request.JobId,
                    LockedByUserId = userInfo.Id,
                    LockedByUserFullName = userInfo.Name,
                    LockedByDeviceId = request.DeviceId,
                    LockedTime = DateTime.UtcNow,
                    LockedByApplicationId = request.ApplicationId,
                    LockedByApplicationName = applicationName,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = userInfo.Username,
                    ModifiedBy = userInfo.Username
                };
            }
        }
    }
}
