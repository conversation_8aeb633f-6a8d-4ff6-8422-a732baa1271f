﻿using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class DeleteJobMaterial
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
            public IEnumerable<Guid> JobMaterials { get; set; }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleForEach(m => m.JobMaterials).NotEmpty();
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            public readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobMaterials)
                        .ThenInclude(y => y.JobAreaMaterials)
                            .ThenInclude(z => z.JobAreaMaterialReadings)
                                .ThenInclude(w => w.JobVisit)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var validationErrors = ValidateRequest(request, job);
                if (validationErrors.Any())
                    throw new ValidationException(validationErrors);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var readingTypeLookups = GetReadingTypeLookups(lookups);
                var visitNumberLookups = GetVisitLookups(job);

                foreach (var jobMaterialId in request.JobMaterials)
                {
                    var jobMaterial = job.JobMaterials.FirstOrDefault(x => x.Id == jobMaterialId);

                    var allReadings = jobMaterial.JobAreaMaterials
                        .SelectMany(x => x.JobAreaMaterialReadings)
                        .ToList();

                    var allVisits = allReadings
                        .Select(x => x.JobVisit)
                        .OrderBy(x => x.Date)
                        .ToList();

                    var journalNoteType = lookups.JournalNoteTypes
                        .FirstOrDefault(x => x.Id == JournalNoteTypes.RecordOfJobMaterialDeletion);

                    var journalNote = new JournalNote
                    {
                        Id = Guid.NewGuid(),
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        ActionDate = DateTime.UtcNow,
                        Subject = $"{jobMaterial.Name} Deleted",
                        TypeId = journalNoteType.Id,
                        CategoryId = journalNoteType.JournalNoteCategoryId,
                        VisibilityId = journalNoteType.DefaultDiaryEntryVisibilityId,
                        Note = GetDiaryNoteBody(jobMaterial, readingTypeLookups, visitNumberLookups, allReadings)
                    };

                    foreach (var jobAreaMaterial in jobMaterial.JobAreaMaterials)
                    {
                        jobAreaMaterial.JobAreaMaterialReadings.Clear();
                    }

                    jobMaterial.JobAreaMaterials.Clear();
                    job.JobMaterials.Remove(jobMaterial);
                    job.JournalNotes.Add(journalNote);
                }

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private Dictionary<Guid, string> GetReadingTypeLookups(GetLookups.Dto lookups) =>
                lookups.MaterialReadingTypes.ToDictionary(x => x.Id, x => x.Name);

            private Dictionary<Guid, int> GetVisitLookups(Job job) =>
                job.JobVisits
                    .OrderBy(x => x.Date)
                    .Select((x, index) => new { x.Id, index = index + 1 })
                    .ToDictionary(x => x.Id, x => x.index);

            private string GetDiaryNoteBody(
                JobMaterial jobMaterial,
                Dictionary<Guid, string> readingTypeLookups,
                Dictionary<Guid, int> visitNumberLookups,
                List<JobAreaMaterialReading> materialReadings)
            {
                var readingsStr = materialReadings
                    .Select(x => new
                    {
                        MaterialName = jobMaterial.Name,
                        Reading = x.Value,
                        ReadingType = readingTypeLookups[x.MaterialReadingTypeId],
                        VisitNumber = visitNumberLookups[x.JobVisitId]
                    })
                    .OrderBy(x => x.VisitNumber)
                    .Select(x => $"\tVisit Number {x.VisitNumber} - {x.MaterialName}, {x.ReadingType} reading: {x.Reading}");
                var body = materialReadings.Any()
                    ? $"Removed Readings{string.Concat(readingsStr)}"
                    : $"Removed Readings - {jobMaterial.Name}";

                return body;
            }

            IEnumerable<ValidationFailure> ValidateRequest(Command request, Job job)
            {
                if (!job.JobVisits.Any(x => x.Id == request.JobVisitId))
                    yield return new ValidationFailure(
                        nameof(Command.JobVisitId),
                        "JobVisit not found", request.JobVisitId);

                var jobMaterialsLookups = job.JobMaterials.Select(x => x.Id).ToHashSet();
                foreach (var jobMaterialId in request.JobMaterials)
                {
                    if (!jobMaterialsLookups.Contains(jobMaterialId))
                        yield return new ValidationFailure(
                            nameof(Command.JobMaterials),
                            "JobMaterial not found", jobMaterialId);
                }
            }
        }
        #endregion
    }
}
