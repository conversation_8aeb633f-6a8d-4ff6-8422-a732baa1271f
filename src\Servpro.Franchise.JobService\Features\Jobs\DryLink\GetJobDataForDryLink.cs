﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Jobs.DryLink
{
    public class GetJobDataForDryLink
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public string ProjectNumber { get; set; } = string.Empty;
            public JobProgress JobProgress { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public Contact Customer { get; set; }
        }

        public class Contact
        {
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string FullName => $"{FirstName} {LastName}";
            public Address Address { get; set; }
        }

        public class Address
        {
            public string Address1 { get; set; }
            public string Address2 { get; set; }
            public string City { get; set; }
            public string PostalCode { get; set; }
            public State State { get; set; }
        }

        public class State
        {
            public Guid StateId { get; set; }
            public string StateName { get; set; }
            public string StateAbbreviation { get; set; }
            public Guid CountryId { get; set; }
            public string CountryName { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetJobDataForDryLink> _logger;

            public Handler(
                ILogger<GetJobDataForDryLink> logger,
                JobReadOnlyDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Documents and email addresses for DocuSign");

                var job = await _context.Jobs
                    .Where(x => x.Id == request.JobId)
                    .Include(x => x.Customer)
                    .FirstOrDefaultAsync(cancellationToken);

                if(job == null)
                {
                    _logger.LogWarning("Get job for DryLink. Job doesn't exist. {handler}", nameof(GetJobDataForDryLink));
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");
                }

                return Map(job);
            }

            private Dto Map(Job job)
            {
                return new Dto()
                {
                    JobId = job.Id,
                    FranchiseSetId = job.FranchiseSetId,
                    ProjectNumber = job.ProjectNumber,
                    JobProgress = job.JobProgress,
                    StartDate = job.CreatedDate,
                    EndDate = job.JobProgress == JobProgress.NotSoldCancelled || job.JobProgress == JobProgress.Closed ? DateTime.UtcNow : new DateTime?(),
                    Customer = MapContact(job)
                };
            }

            private Contact MapContact(Models.Job job)
            {
                Models.Contact customer = job.Customer;
                return new Contact()
                {
                    FirstName = customer?.FirstName,
                    LastName = customer?.LastName,
                    Address = MapAddress(job.LossAddress)
                };
            }

            private Address MapAddress(Models.Address address)
            {
                return new Address()
                {
                    Address1 = address?.Address1,
                    Address2 = address?.Address2,
                    City = address?.City,
                    PostalCode = address?.PostalCode,
                    State = address != null ? MapState(address.State) : null
                };
            }

            private State MapState(Models.State state)
            {
                return new State()
                {
                    StateId = state != null ? state.StateId : Guid.Empty,
                    StateName = state?.StateName,
                    StateAbbreviation = state?.StateAbbreviation,
                    CountryId = state != null ? state.CountryId : Guid.Empty,
                    CountryName = state?.CountryName
                };
            }
        }
    }
}
