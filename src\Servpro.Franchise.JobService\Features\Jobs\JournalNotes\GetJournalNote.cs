﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class GetJournalNote
    {
        public class Query : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid JournalNoteId { get; set; }
        }

        public class Validator : AbstractValidator<GetJournalNote.Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.JournalNoteId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? JobId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public Guid CategoryId { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public DateTime? ActionDate { get; set; }
            public DateTime CreatedDate { get; set; }
            public bool? IncludeInSummaryCoverPage { get; set; }
            public List<int> RuleIds { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext jobDataContext)
            {
                _db = jobDataContext;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var journalNote = await _db.JournalNote
                    .AsNoTracking()
                    .FirstAsync(n => n.JobId == request.JobId && n.Id == request.JournalNoteId, cancellationToken: cancellationToken);

                if (journalNote == null)
                    throw new ResourceNotFoundException("JournalNote not found.");

                journalNote.ActionDate = (journalNote.ActionDate != null) ? journalNote.ActionDate : DateTime.UtcNow;
                return Map(journalNote);
            }

            private static GetJournalNote.Dto Map(JournalNote journalNote)
                => new GetJournalNote.Dto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Note = journalNote.Note,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    Subject = journalNote.Subject,
                    ActionDate = journalNote.ActionDate,
                    VisibilityId = journalNote.VisibilityId,
                    IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage,
                    RuleIds = journalNote.RuleIds?.ToList()
                };
        }
    }
}