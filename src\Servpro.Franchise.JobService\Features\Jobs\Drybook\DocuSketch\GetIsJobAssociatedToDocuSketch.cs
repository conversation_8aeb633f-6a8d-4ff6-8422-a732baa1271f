﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.LookupService.Constants;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.DocuSketch
{
    public class GetIsJobAssociatedToDocuSketch
    {
        public class Query : IRequest<bool>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; set; }
        }

        public class Handler : IRequestHandler<Query, bool>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<Handler> _logger;

            public Handler(JobReadOnlyDataContext context, ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<bool> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobId = request.JobId;

                using var logScope = _logger.BeginScope(new { jobId });
                _logger.LogInformation("Checking if job {jobId} is associated to DocuSketch", jobId);

                var isJobAssociated = await IsJobAssociatedToDocuSketchAsync(jobId, cancellationToken);
                return isJobAssociated;
            }

            private async Task<bool> IsJobAssociatedToDocuSketchAsync(Guid jobId, CancellationToken cancellationToken)
            {
                /*
                   To determine if a job is associated with DocuSketch:

                   - **Newer jobs**: These have an entry in the `JobAdditionalInfo` table indicating their assigned sketch source.
                   - **Older jobs**: These do not have this record. To support them, we check the `MediaMetadata` table
                     for any associated DocuSketch artifacts.
                */

                // For jobs that have a record in JobAdditionalInfo, check if SketchSourceId is DocuSketch
                var jobAdditionalInfoMatch = await _context.JobAdditionalInfo
                    .AnyAsync(ja => ja.JobId == jobId && ja.SketchSourceId == Servpro.Franchise.JobService.Common.Constants.SketchSource.DocuSketch, cancellationToken);

                if (jobAdditionalInfoMatch)
                {
                    return true;
                }

                // For jobs without a record in JobAdditionalInfo, check MediaMetadata for DocuSketch 
                var excludedArtifactTypeIds = new Guid[]
                {
                    ArtifactTypes.MobileSketchJson,
                    ArtifactTypes.MobileSketchXML,
                    ArtifactTypes.MobileSketchESX
                };

                var mediaMetadataMatch = await _context.MediaMetadata
                    .AnyAsync(mmd => mmd.JobId == jobId &&
                                     (mmd.ArtifactTypeId == ArtifactTypes.DocuSketchSketch ||
                                      mmd.ArtifactTypeId == ArtifactTypes.DocuSketch360Photo) &&
                                     !excludedArtifactTypeIds.Contains(mmd.ArtifactTypeId),
                                     cancellationToken);

                return mediaMetadataMatch;
            }
        }
    }
}
