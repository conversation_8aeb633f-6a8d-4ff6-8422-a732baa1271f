﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.TaskUpdatedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Microsoft.Extensions.Logging;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class EditTaskJournalNote
    {
        #region Command
        public class Command : IRequest
        {
            public Guid TaskId { get; set; }
            public Guid JournalNoteId { get; set; }
            public Guid JobId { get; set; }
            public string Note { get; set; }
            public DateTime ActionDate { get; set; }
            public Guid JournalNoteVisibilityId { get; set; }
        }
        #endregion
        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.TaskId).NotEmpty();
                RuleFor(x => x.ActionDate)
                    .NotEqual(DateTime.MinValue)
                    .LessThan(DateTime.UtcNow);
                RuleFor(x => x.JournalNoteId).NotEmpty();
                RuleFor(x => x.JournalNoteVisibilityId).NotEmpty();
                RuleFor(x => x.Note).MinimumLength(1);
            }
        }
        #endregion
        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILogger<EditTaskJournalNote> _logger;

            public Handler(JobDataContext context,
                 ILookupServiceClient lookupServiceClient,
                 ISessionIdAccessor sessionIdAccessor,
                 IUserInfoAccessor userInfo,
                 ILogger<EditTaskJournalNote> logger)
            {
                _context = context;
                _lookupServiceClient = lookupServiceClient;
                _sessionIdAccessor = sessionIdAccessor;
                _userInfo = userInfo;
                _logger = logger;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var logScope = _logger.BeginScope("{jobId}", request.JobId);

                _logger.LogInformation("Begin handler with {@request}", request);

                var task = await _context.Tasks
                    .Include(x => x.Job)
                    .Include(x => x.JournalNotes)
                    .FirstOrDefaultAsync(x => x.Id == request.TaskId
                        && x.JobId == request.JobId, cancellationToken);
                var userInfo = _userInfo.GetUserInfo();
                if (task is null)
                    throw new ResourceNotFoundException($"Task not found (Id: {request.TaskId}");

                if(task.Job != null)
                {
                    task.Job.JobLocks = await _context.JobLock.Where(x => x.JobId == task.Job.Id).ToListAsync(cancellationToken);
                    _logger.LogDebug("JobLocks returned: {@jobLocks}", task.Job.JobLocks);
                }
                var journalNote = task.JournalNotes.FirstOrDefault(x => x.Id == request.JournalNoteId);
                if(journalNote is null)
                    throw new ResourceNotFoundException($"JournalNote not found (Id: {request.JournalNoteId}");
                if (JobLockUtils.HasLockConflict(task.Job.CurrentJobLock, userInfo.Id))
                {
                    _logger.LogError($"JobLock conflict with user: {userInfo.Id} and joblock: {task.Job.CurrentJobLock}");
                    throw new JobConflictException(task.Job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(task.Job.CurrentJobLock));
                }

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == task.TaskTypeId);

                if(taskType.ShouldCloseWhenDiaryEntryIsComplete && !string.IsNullOrEmpty(request.Note))
                {
                    _logger.LogError("Updating Task: {@taskId}", task.Id);
                    task.PercentComplete = 100;
                    task.TaskStatusId = TaskStatuses.Completed;

                    // update task
                    task.ModifiedBy = userInfo.Username;
                    task.ModifiedDate = DateTime.UtcNow;

                    //Publish event for TaskUpdated
                    var eventTaskUpdated = GenerateUpdateTaskEvent(request, task, userInfo);
                    _context.OutboxMessages.Add(eventTaskUpdated);
                    _logger.LogDebug("Event Task Updated Successfully");
                }

                // update journal notes
                journalNote.Note = request.Note;
                journalNote.VisibilityId = request.JournalNoteVisibilityId;
                journalNote.ActionDate = request.ActionDate;
                journalNote.ModifiedBy = userInfo.Username;
                journalNote.ModifiedDate = DateTime.UtcNow;

                //Publish event for JournalNoteUpdated
                var eventJournalNoteUpdated = GenerateUpdateJorunalNoteEvent(request, journalNote, userInfo);
                _context.OutboxMessages.Add(eventJournalNoteUpdated);

                _logger.LogDebug("Handler Completed Successfully for command: @{request}", request);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private OutboxMessage GenerateUpdateTaskEvent(
                Command request,
                Models.Drybook.Task task,
                UserInfo userInfo)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var taskUpdatedEvent = new TaskUpdatedEvent(
                        GetTaskUpdatedEventDto(request, task, userInfo),
                        correlationId);
                    return new OutboxMessage(taskUpdatedEvent.ToJson(),
                        nameof(TaskUpdatedEvent),
                        correlationId,
                        userInfo.Username);
            }

            private TaskUpdatedEventDto GetTaskUpdatedEventDto(Command request, Models.Drybook.Task task, UserInfo userInfo)
                => new TaskUpdatedEventDto
                {
                    JobId = request.JobId,
                    Id = task.Id,
                    Body = task.Body,
                    CancellationDate = task.CancellationDate,
                    CompletionDate = task.CompletionDate,
                    DueDate = task.DueDate,
                    EstimateId = task.EstimateId,
                    FranchiseSetId = task.FranchiseSetId,
                    IsSystem = task.IsSystem,
                    IsTemplate = task.IsTemplate,
                    JobTriStateQuestionId = task.JobTriStateQuestionId,
                    JobVisitId = task.JobVisitId,
                    PercentComplete = task.PercentComplete,
                    ReminderDate = task.ReminderDate,
                    StartDate = task.StartDate,
                    Subject = task.Subject,
                    TaskPriorityId = task.TaskPriorityId,
                    TaskStatusId = task.TaskStatusId,
                    TaskTypeId = task.TaskTypeId,
                    WorkOrderId = task.WorkOrderId,
                    ZoneId = task.ZoneId,
                    ZoneReadingId = task.ZoneReadingId,
                    ModifiedBy = userInfo.Id,
                    ModifiedDate = DateTime.UtcNow
                };


            private OutboxMessage GenerateUpdateJorunalNoteEvent(
                Command request,
                JournalNote journalNote,
                UserInfo userInfo)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var journalNoteUpdatedEvent = new JournalNoteUpdatedEvent(
                        GetJournalNoteUpdatedEventDto(request, journalNote, userInfo),
                        _sessionIdAccessor.GetCorrelationGuid());
                return new OutboxMessage(journalNoteUpdatedEvent.ToJson(),
                    nameof(JournalNoteUpdatedEvent),
                    correlationId,
                    userInfo.Username);
            }

            private JournalNoteUpdatedEvent.JournalNoteDto GetJournalNoteUpdatedEventDto(Command request, JournalNote journalNote, UserInfo userInfo)
                => new JournalNoteUpdatedEvent.JournalNoteDto
                {
                    JobId = request.JobId,
                    ActionDate = journalNote.ActionDate,
                    Author = journalNote.Author,
                    CategoryId = journalNote.CategoryId,
                    Id = journalNote.Id,
                    Message = journalNote.Note,
                    Subject= journalNote.Subject,
                    TypeId = journalNote.TypeId,
                    UpdatedById = userInfo.Id,
                    UpdatedDate = DateTime.UtcNow ,
                    VisibilityId = journalNote.VisibilityId
            };
        }
    }
    #endregion
}
