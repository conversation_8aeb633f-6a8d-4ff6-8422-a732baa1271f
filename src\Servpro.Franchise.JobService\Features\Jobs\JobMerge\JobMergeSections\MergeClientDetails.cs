﻿using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;

using System;
using System.Collections.Generic;
using System.Linq;

using JobContactTypes = Servpro.Franchise.JobService.Common.JobContactTypes;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeClientDetails : IJobMergeSection
    {
        public void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            if (!targetJob.CorporateJobNumber.HasValue)
            {
                if (sourceJob.InsuranceCarrierId.HasValue &&
                sourceJob.InsuranceCarrierId != InsuranceCarrierTypes.Unknown)
                {
                    targetJob.InsuranceCarrierId = sourceJob.InsuranceCarrierId;
                }

                if (!string.IsNullOrWhiteSpace(sourceJob.InsuranceClaimNumber))
                    targetJob.InsuranceClaimNumber = sourceJob.InsuranceClaimNumber;

                if (!string.IsNullOrWhiteSpace(sourceJob.InsurancePolicyNumber))
                    targetJob.InsurancePolicyNumber = sourceJob.InsurancePolicyNumber;
            }

            if (sourceJob.DeductibleAmount > 0)
                targetJob.DeductibleAmount = sourceJob.DeductibleAmount;

            targetJob.WorkOrderNumber = sourceJob.WorkOrderNumber;
            targetJob.PurchaseOrderNumber = sourceJob.PurchaseOrderNumber;
            targetJob.NteAmount = sourceJob.NteAmount;
            targetJob.CollectDeductible = sourceJob.CollectDeductible;
            targetJob.SiteReferenceNumber = sourceJob.SiteReferenceNumber;
            targetJob.IsBidRequested = sourceJob.IsBidRequested;
            MergeClientJobContacts(targetJob, sourceJob);
        }

        private static void MergeClientJobContacts(Job targetJob, Job sourceJob)
        {
            var sourceContacts = sourceJob.JobContacts;
            var targetContacts = targetJob.JobContacts;
            var clientContactTypes = new List<Guid>
                { JobContactTypes.Agent, JobContactTypes.Adjuster, JobContactTypes.ReferralSource };

            var jobContactsToMerge = sourceContacts
                            .Where(x => clientContactTypes.Contains(x.JobContactTypeId) &&
                                    !targetContacts.Any(y => y.ContactId == x.ContactId
                                                    && y.JobContactTypeId == x.JobContactTypeId))
                            .Select(x => MapJobContact(x, targetJob.Id));

            foreach (var jobContact in jobContactsToMerge)
            {
                var targetJobContactMap = targetJob.JobContacts
                    .FirstOrDefault(x => x.JobContactTypeId == jobContact.JobContactTypeId);
                
                if(targetJobContactMap != null) targetJob.JobContacts.Remove(targetJobContactMap);
                
                targetJob.JobContacts.Add(jobContact);
            }
        }

        private static JobContactMap MapJobContact(JobContactMap x, Guid targetJobId)
        => new JobContactMap()
        {
            ContactId = x.ContactId,
            JobContactTypeId = x.JobContactTypeId,
            JobId = targetJobId,
            CreatedDate = DateTime.UtcNow,
            IsBusinessContact = x.IsBusinessContact,
            TitleId = x.TitleId
        };
    }
}