﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobAreaMaterialUpdatedEvent;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetMaterialReadings
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Dto
        {
            public Dto(
                Guid zoneId,
                string zoneName,
                string description,
                IEnumerable<VisitReadingValue> notes,
                IEnumerable<RoomDto> rooms)
            {
                ZoneId = zoneId;
                ZoneName = zoneName;
                Description = description;
                Notes = notes;
                Rooms = rooms;
            }

            public Guid ZoneId { get; }
            public string ZoneName { get; }
            public string Description { get; }
            public IEnumerable<VisitReadingValue> Notes { get; }
            public IEnumerable<RoomDto> Rooms { get; }

            public class VisitReadingValue
            {
                public VisitReadingValue(
                    Guid? value,
                    Guid? readingId,
                    Guid visitId,
                    bool includedInVisit)
                {
                    Value = value;
                    ReadingId = readingId;
                    VisitId = visitId;
                    IncludedInVisit = includedInVisit;
                }

                public Guid? Value { get; }
                public Guid? ReadingId { get; }
                public Guid VisitId { get; }
                public bool IncludedInVisit { get; }
            }

            public class RoomDto
            {
                public RoomDto(
                    Guid jobAreaId,
                    string name,
                    string description,
                    IEnumerable<MaterialDto> materials)
                {
                    JobAreaId = jobAreaId;
                    Name = name;
                    Description = description;
                    Materials = materials;
                }

                public Guid JobAreaId { get; }
                public string Name { get; }
                public string Description { get; }
                public IEnumerable<MaterialDto> Materials { get; }
            }

            public class MaterialDto
            {
                public MaterialDto(
                    Guid jobMaterialId,
                    Guid jobAreaMaterialId,
                    string materialName,
                    string objectName,
                    string goal,
                    Guid? removedOnVisitId,
                    IEnumerable<VisitValuePair<int?>> previousReadings,
                    IEnumerable<VisitValuePair<int?>> currentReadings,
                    IEnumerable<VisitValuePair<int?>> nextReadings)
                {
                    JobMaterialId = jobMaterialId;
                    JobAreaMaterialId = jobAreaMaterialId;
                    MaterialName = materialName;
                    ObjectName = objectName;
                    Goal = goal;
                    RemovedOnVisitId = removedOnVisitId;
                    PreviousReadings = previousReadings;
                    CurrentReadings = currentReadings;
                    NextReadings = nextReadings;
                }

                public Guid JobMaterialId { get; }
                public Guid JobAreaMaterialId { get; }
                public string MaterialName { get; }
                public string ObjectName { get; }
                public string Goal { get; }
                public Guid? RemovedOnVisitId { get; }
                public IEnumerable<VisitValuePair<int?>> PreviousReadings { get; }
                public IEnumerable<VisitValuePair<int?>> CurrentReadings { get; }
                public IEnumerable<VisitValuePair<int?>> NextReadings { get; }
            }

            public class VisitValuePair<T>
            {
                public VisitValuePair(
                    Guid visitId,
                    T value,
                    bool includedInVisit,
                    Guid? readingId)
                {
                    VisitId = visitId;
                    Value = value;
                    IncludedInVisit = includedInVisit;
                    ReadingId = readingId;
                }

                public Guid VisitId { get; }
                public T Value { get; }
                public bool IncludedInVisit { get; }
                public Guid? ReadingId { get; }
            }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GetMaterialReadings> _logger;

            public Handler(
                JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient,
                ILogger<GetMaterialReadings> logger)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Begin handler with: {@request}", request);

                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                               .Include(x => x.JobVisits)
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.Zones = await _context.Zones
                                    .Include(x => x.ZoneReadings)
                                    .Include(x => x.JobAreas)
                                        .ThenInclude(x => x.BeginJobVisit)
                                    .Include(x => x.JobAreas)
                                        .ThenInclude(x => x.JobAreaMaterials)
                                            .ThenInclude(x => x.JobMaterial)
                                    .Include(x => x.JobAreas)
                                        .ThenInclude(x => x.JobAreaMaterials)
                                            .ThenInclude(x => x.JobAreaMaterialReadings)
                                    .Include(x => x.JobAreas)
                                        .ThenInclude(x => x.JobAreaMaterials)
                                            .ThenInclude(x => x.RemovedOnJobVisit)
                                    .Where(x => x.JobId == request.JobId)
                                    .AsNoTracking()
                                    .ToListAsync(cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var objectLookup = lookups.Objects?.ToDictionary(x => x.Id, x => x.Name)
                    ?? new Dictionary<Guid, string>();
                var orderedVisits = job.JobVisits.OrderBy(x => x.Date).ToList();

                _logger.LogDebug("JobVisits for Job {jobId}: {@dto}", request.JobId, orderedVisits);

                var materialReadings = job.Zones
                    .Where(x => x.ZoneTypeId == ZoneTypes.Drying)
                    .Select(x => Map(x, orderedVisits, objectLookup));

                _logger.LogDebug("Material Readings for Job {jobId}: {@dto}", request.JobId, materialReadings);

                return materialReadings;
            }

            static Dto Map(
                Zone zone,
                IEnumerable<JobVisit> visits,
                IDictionary<Guid, string> objectLookup)
            {
                var isIncludedInVisit = visits.ToDictionary(x => x.Id, x => IsIncludedInVisit(zone, x));
                var readingsLookup = zone.ZoneReadings.ToDictionary(x => x.JobVisitId);
                var notes = visits.Select(x => MapVisitReadingValue(x, readingsLookup, isIncludedInVisit));
                var rooms = zone.JobAreas.Select(x => MapRoom(x, visits, objectLookup));
                return new Dto(zone.Id, zone.Name, zone.Description, notes, rooms);
            }

            static Dto.RoomDto MapRoom(
                JobArea jobArea,
                IEnumerable<JobVisit> visits,
                IDictionary<Guid, string> objectLookup)
            {
                var materials = jobArea.JobAreaMaterials.Select(x => MapMaterial(x, visits, objectLookup));
                return new Dto.RoomDto(jobArea.Id, jobArea.Name, string.Empty, materials);
            }

            static Dto.MaterialDto MapMaterial(
                JobAreaMaterial jobAreaMaterial,
                IEnumerable<JobVisit> visits,
                IDictionary<Guid, string> objectLookup)
            {
                var prevVisits = visits.ToDictionary(
                    x => x.Id,
                    x => visits
                        .Where(v => v.Date < x.Date)
                        .OrderByDescending(v => v.Date)
                        .FirstOrDefault());
                var followingVisits = visits.ToDictionary(
                    x => x.Id,
                    x => visits
                        .Where(v => v.Date > x.Date)
                        .OrderBy(v => v.Date)
                        .FirstOrDefault());
                var readingsLookup = jobAreaMaterial.JobAreaMaterialReadings.DistinctBy(x => x.JobVisitId).ToDictionary(x => x.JobVisitId);
                var isGoalMet = jobAreaMaterial.GoalMetOnJobVisitId.HasValue;
                var nextReadings = visits.Select(x => MapNextPreviousValue(x, readingsLookup, followingVisits));
                var previousReadings = visits.Select(x => MapNextPreviousValue(x, readingsLookup, prevVisits));
                var currentReadings = visits.Select(x => MapReading(x, jobAreaMaterial.RemovedOnJobVisit, readingsLookup, isGoalMet));

                return new Dto.MaterialDto(
                    jobAreaMaterial.JobMaterialId,
                    jobAreaMaterial.Id,
                    jobAreaMaterial.JobMaterial.Name,
                    objectLookup.ContainsKey(jobAreaMaterial.JobMaterial.ObjectId)
                    ? objectLookup[jobAreaMaterial.JobMaterial.ObjectId]
                    : string.Empty,
                    jobAreaMaterial.JobMaterial.GoalDisplayText,
                    jobAreaMaterial.RemovedOnJobVisitId,
                    previousReadings,
                    currentReadings,
                    nextReadings);
            }

            static Dto.VisitValuePair<int?> MapNextPreviousValue(
                JobVisit visit,
                IDictionary<Guid, JobAreaMaterialReading> readings,
                IDictionary<Guid, JobVisit> visits)
            {
                var previousVisit = visits.GetOrDefault(visit.Id);
                var value = previousVisit != null &&
                    readings.ContainsKey(previousVisit.Id)
                    ? readings[previousVisit.Id].Value
                    : null;

                var readingId = previousVisit != null && readings.ContainsKey(previousVisit.Id)
                    ? readings[previousVisit.Id].Id
                    : (Guid?)null;

                return new Dto.VisitValuePair<int?>(visit.Id, value, false, readingId);
            }

            static Dto.VisitValuePair<int?> MapReading(
                JobVisit visit,
                JobVisit removedOnJobVisit,
                IDictionary<Guid, JobAreaMaterialReading> readings,
                bool isGoalMet)
            {
                var reading = readings.GetOrDefault(visit.Id);
                return new Dto.VisitValuePair<int?>(
                    visit.Id,
                    reading?.Value,
                    // if the goal is met then there must be a value for the given visit
                    // otherwise true
                    IsIncludedInVisit(readings, visit, removedOnJobVisit, isGoalMet),
                    reading?.Id);
            }

            static Dto.VisitReadingValue MapVisitReadingValue(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue(
                    readingsLookup.GetOrDefault(visit.Id)?.JournalNoteId,
                    readingsLookup.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);

            static bool IsIncludedInVisit(IDictionary<Guid, JobAreaMaterialReading> readings, JobVisit jobVisit, JobVisit removedOnJobVisit, bool isGoalMet)
            {
                return (removedOnJobVisit != null ? removedOnJobVisit.Date.RemoveSecondsFromDateTime() > jobVisit.Date.RemoveSecondsFromDateTime() : true)
                    && (!isGoalMet || (readings.GetOrDefault(jobVisit.Id)?.Value.HasValue ?? false));
            }

            static bool IsIncludedInVisit(Zone zone, JobVisit visit)
            {
                var zoneDate = zone.JobAreas
                    .Where(x => !x.EndJobVisitId.HasValue)
                    .Select(x => x.BeginJobVisit?.Date)
                    .Min(x => x);

                zoneDate = zoneDate.HasValue
                  ? zoneDate.Value.RemoveSecondsFromDateTime(zoneDate.Value)
                  : zoneDate;
                return zoneDate.HasValue && zoneDate <= visit.Date;
            }
        }
    }
}
