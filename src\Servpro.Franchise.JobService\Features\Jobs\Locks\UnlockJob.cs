﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Locks
{
    public class UnlockJob
    {
        public class Command : IRequest<GetJobLock.Dto>
        {
            public Guid JobId { get; set; }
            public string DeviceId { get; set; }
            public bool IsForced { get; set; }
        }

        public class Handler : IRequestHandler<Command, GetJobLock.Dto>
        {
            private readonly JobDataContext _context;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILogger<UnlockJob> _logger;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor,
                ILogger<UnlockJob> logger)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
                _logger = logger;
            }

            public class CommandValidator : AbstractValidator<Command>
            {
                public CommandValidator()
                {
                    RuleFor(x => x.JobId).NotNull();
                }
            }
            
            public async Task<GetJobLock.Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}{eventName}", request.JobId, nameof(UnlockJob));
                _logger.LogInformation("Begin command with request: {@request}", request);

                if (string.IsNullOrEmpty(request.DeviceId))
                    throw new ValidationException($"DeviceId must not be null");

                var job = await _context.Jobs
                    .Include(x => x.JobLocks)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var jobLocks = job.JobLocks.Where(a => a.IsLocked);

                var userInfo = _userInfo.GetUserInfo();
                if (!jobLocks.Any())
                {
                    _logger.LogWarning("No job locks were found for job.");
                    return new GetJobLock.Dto
                    {
                        IsLocked = false,
                    };
                }

                var jobLock = jobLocks.OrderByDescending(x => x.LockedTime).FirstOrDefault();

                // unlock if userIds match (or it was locked by DBM) and device id match
                var shouldUnlock = ((jobLock.LockedByUserId == userInfo.Id || jobLock.LockedByApplicationId == ClientApplications.DrybookMobile)
                    && jobLock.LockedByDeviceId.Equals(request.DeviceId)) || request.IsForced;

                _logger.LogInformation("Found job lock {@jobLock}. Should Unlock? {shouldUnlock}", jobLock, shouldUnlock);

                if (jobLock.IsLocked && shouldUnlock)
                {
                    _logger.LogInformation("Unlocking job lock with id: {jobLockId}", jobLock.Id);

                    //Update jobLock
                    jobLock.IsLocked = false;
                    jobLock.UnlockedByUserId = userInfo.Id;
                    jobLock.UnlockedByDeviceId = request.DeviceId;
                    jobLock.UnlockedTime = DateTime.UtcNow;
                    jobLock.ModifiedBy = userInfo.Username;
                    jobLock.ModifiedDate = DateTime.UtcNow;

                    //Post a new message
                    if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                        correlationId = Guid.NewGuid();
                    
                    var newEvent = new OutboxMessage(new JobUnlockEvent(MapUnlockEventDto(jobLock), correlationId).ToJson(),
                        nameof(JobUnlockEvent),
                        correlationId,
                        userInfo.Username);
                    await _context.OutboxMessages.AddAsync(newEvent, cancellationToken);
                    
                    await _context.SaveChangesAsync(cancellationToken);

                    _logger.LogInformation("Successfully unlocked job!");
                }

                return GetJobLock.Handler.Map(jobLock);
            }
           
            public JobLockDto MapUnlockEventDto(JobLock jobLock)
            {
                return new JobLockDto
                {
                    Id = jobLock.Id,
                    JobId = jobLock.JobId,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.LockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId,
                    IsAleradyLocked = jobLock.IsLocked
                };
            }
        }
    }
}