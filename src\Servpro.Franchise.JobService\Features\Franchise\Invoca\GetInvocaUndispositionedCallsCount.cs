﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Invoca;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class GetInvocaUndispositionedCallsCount
    {
        public class Query : IRequest<Dto>
        {
            public Guid? FranchiseId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public DateTime StartDate { get; set; }
            public DateTime EndDate { get; set; }
        }

        public class Dto
        {
            public int Count { get; set; }

        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetInvocaFranchiseCalls> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IConfiguration _config;

            public Handler(IConfiguration config, 
                ILogger<GetInvocaFranchiseCalls> logger,
                IFranchiseServiceClient franchiseServiceClient,
                JobReadOnlyDataContext context)
            {
                _config = config;
                _logger = logger;
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;   
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                Dto result;
                using var scope = _logger.BeginScope("{franchiseSetId},{franchiseId}", request.FranchiseSetId, request.FranchiseId);
                if (request.FranchiseId.HasValue)
                {
                    _logger.LogDebug("Getting Undispositioned Invoca Calls Count for Franchise");
                    result = await GetFranchiseCallsCount(request, cancellationToken);
                }
                else
                {
                    _logger.LogDebug("Getting Undispositioned Invoca Calls Count for Franchise Set");
                    result = await GetFranchiseSetCallsCount(request, cancellationToken);
                }

                return result;
            }

            public async Task<Dto> GetFranchiseCallsCount(Query request, CancellationToken cancellationToken)
            {
                var franchise = await _franchiseServiceClient.GetFranchiseAsync(request.FranchiseId.Value, cancellationToken);
                var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(request.FranchiseSetId, includeFeatureSets: true, cancellationToken);
                if (franchise == null)
                {
                    _logger.LogWarning("Franchise does not exist.");
                    return MapResult();
                }

                var franchiseCalls = await GetUndispositionedCallsAsync(franchiseSet, 
                    request.FranchiseSetId, 
                    request.FranchiseId, 
                    request.StartDate, 
                    request.EndDate, 
                    cancellationToken);

                return MapResult(franchiseCalls);
            }

            public async Task<Dto> GetFranchiseSetCallsCount(Query request, CancellationToken cancellationToken)
            {
                var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(request.FranchiseSetId, includeFeatureSets: true, cancellationToken);
                if (franchiseSet == null)
                {
                    _logger.LogWarning("Franchise set does not exist.");
                    return MapResult();
                }

                var franchiseSetCalls = await GetUndispositionedCallsAsync(franchiseSet, 
                    request.FranchiseSetId, 
                    null, 
                    request.StartDate, 
                    request.EndDate, 
                    cancellationToken);

                return MapResult(franchiseSetCalls);
            }

            private async Task<List<ExternalMarketingCall>> GetUndispositionedCallsAsync(GetFranchiseSetDto franchiseSet, 
                Guid franchiseSetId, 
                Guid? franchiseId, 
                DateTime startDate, 
                DateTime endDate, 
                CancellationToken cancellationToken)
            {
                var franchiseCallsQuery = _context.ExternalMarketingCalls
                    .Where(j => j.CallReceivedDateTime >= startDate
                               && j.CallReceivedDateTime <= endDate);

                if (franchiseId.HasValue)
                    franchiseCallsQuery = franchiseCallsQuery.Where(m => m.FranchiseId == franchiseId);
                else
                    franchiseCallsQuery = franchiseCallsQuery.Where(m => m.FranchiseSetId == franchiseSetId);

                var franchiseCalls = await franchiseCallsQuery
                    .ToListAsync(cancellationToken);

                franchiseCalls = franchiseCalls
                    .Where(x => x.CallDuration > 0 || (
                        x.CallDuration == 0 && (DateTime.UtcNow - x.CallReceivedDateTime).TotalHours < 3))
                    .ToList();

                var filterCalls = new Dictionary<string, ExternalMarketingCall>();

                foreach (var franchiseCall in franchiseCalls)
                {
                    if (franchiseCall.CallRecordingId == null)
                        continue;
                    var repeatedCall = filterCalls.ContainsKey(franchiseCall.CallRecordingId);
                    if (repeatedCall)
                    {
                        if (filterCalls[franchiseCall.CallRecordingId].Disposition != Guid.Empty)
                            continue;
                        if (franchiseCall.Disposition != Guid.Empty)
                        {
                            filterCalls[franchiseCall.CallRecordingId] = franchiseCall;
                        }
                        else if (franchiseCall.CallDuration != 0)
                        {
                            filterCalls[franchiseCall.CallRecordingId] = franchiseCall;
                        }
                    }
                    else
                    {
                        filterCalls.Add(franchiseCall.CallRecordingId, franchiseCall);
                    }
                }

                return filterCalls.Values.Where(call => call.Disposition == Guid.Empty).ToList();
            }

            private Dto MapResult(List<ExternalMarketingCall> calls = null)
            {
                return new Dto
                {
                    Count = calls?.Count ?? 0
                };
            }
        }
    }
}
