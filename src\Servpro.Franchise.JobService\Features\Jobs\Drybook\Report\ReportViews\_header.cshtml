@model Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers.HeaderHelper
<style>
        /* defining explicit font-size solves the scaling issue */
        html, body {
            font-family: Verdana, Geneva, sans-serif;
            font-size: 8px;
        }

        .container {
            display: flex;
            flex-wrap: nowrap;
            align-items: flex-end;
            justify-content: center;
        }

        .container > div {
            margin: 10px;
            text-align: center;
            width: 300px;
        }
        
        .container .logo {
            max-width: 160px;
        }

        .container .bold {
            font-weight: bold;
        }
</style>
<header>
    <div class="container">
        <div>
            <img alt="SERVPRO" class="logo" src="@Model.ImageUrl" />
            <div class="bold">@Model.CustomerName.ToUpper()</div>
        </div>
        <div>
            <div class="bold">@Model.CustomerName.ToUpper()</div>
            <div class="bold">Claim #: @Model.ClaimNumber</div>
            <div class="bold">SERVPRO® Proj#: @Model.ProjectNumber</div>
        </div>
    </div>
</header>