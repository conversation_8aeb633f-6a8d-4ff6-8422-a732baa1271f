﻿using FluentValidation;
using FluentValidation.Results;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections;
using Servpro.Franchise.JobService.Features.Jobs.Mapping;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Features.Jobs.JobMerge.Mapping.JobMergeMappers;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.RoomDeletedEvent;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes.JournalNoteCreatedEvent;

using Task = System.Threading.Tasks.Task;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge
{
    public class MergeJobs
    {
        public class Command : IRequest<Dto>
        {
            public Guid SourceJobId { get; set; }
            public Guid TargetJobId { get; set; }
            public IEnumerable<JobMergeOptionDto> MergeOptions { get; set; } = new List<JobMergeOptionDto>();

        }
        public class JobMergeOptionDto
        {
            public int JobMergeTypeId { get; set; }
            public string Name { get; set; }
            public bool Selected { get; set; }
            public bool PreviouslyMerged { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.SourceJobId).NotEmpty();
                RuleFor(m => m.TargetJobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Dto(string sourceProjectNumber, string targetProjectNumber, bool mergeSuccessful)
            {
                SourceProjectNumber = sourceProjectNumber;
                TargetProjectNumber = targetProjectNumber;
                MergeSuccessful = mergeSuccessful;
            }
            public string SourceProjectNumber { get; set; }
            public string TargetProjectNumber { get; set; }
            public bool MergeSuccessful { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly JobMergeContext _mergeContext;
            private readonly string _mergeNoteSubject = "Project merged";
            private readonly List<Guid> mobileSketchArtifactIds = new List<Guid>()
            {
                ArtifactTypes.MobileSketchESX,
                ArtifactTypes.MobileSketchJson,
                ArtifactTypes.MobileSketchXML
            };

            public Handler(JobDataContext db,
                ILogger<Handler> logger,
                IUserInfoAccessor userInfoAccessor,
                JobMergeContext mergeContext,
                ISessionIdAccessor sessionIdAccessor)
            {
                _db = db;
                _logger = logger;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _mergeContext = mergeContext;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("[{method}] Received request to merge source job {sourceJobId} into target job {targetJobId}: {@request}",
                    nameof(MergeJobs), request.SourceJobId, request.TargetJobId, request);
                var userInfo = _userInfoAccessor.GetUserInfo();

                var sourceJob = await GetJobAsync(request.SourceJobId, cancellationToken);
                var targetJob = await GetJobAsync(request.TargetJobId, cancellationToken);

                if (sourceJob is null || targetJob is null)
                {
                    var missingJobId = sourceJob is null ? request.SourceJobId : request.TargetJobId;
                    throw new ResourceNotFoundException($"Job not found: {missingJobId}");
                }

                _logger.LogDebug("[{method}] Found source job {sourceJobId} and target job {targetJobId}", nameof(MergeJobs), request.SourceJobId, request.TargetJobId);
                var validationErrors = ValidateRequest(sourceJob, targetJob);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("[{method}] Could not merge source job {sourceJobId} into target job {targetJobId}: {@error}",
                        nameof(MergeJobs), request.SourceJobId, request.TargetJobId, validationErrors);
                    throw new ValidationException(validationErrors);
                }

                // Lock both source and target jobs to ensure atomicity and prevent concurrent modifications by other application during the merge process.
                var canLockJobs = await LockJobs(request.SourceJobId, request.TargetJobId, userInfo, cancellationToken);
                if (!canLockJobs)
                    throw new ValidationException("One of the projects is locked.");

                try
                {
                    targetJob.LossTypeId = sourceJob.LossTypeId;
                    targetJob.CauseOfLossId = sourceJob.CauseOfLossId;
                    targetJob.ProjectNumber = MapProjectNumber(targetJob.ProjectNumber, sourceJob.ProjectNumber);

                    await MergeJobs(request, sourceJob, targetJob, cancellationToken);
                    GenerateMergeCorrespondenceNotes(sourceJob, targetJob, userInfo.Username);
                    sourceJob.MergeTarget = targetJob.Id;
                    targetJob.MergeSource = sourceJob.Id;

                    await _db.SaveChangesAsync(cancellationToken);
                    await _db.Entry(targetJob).ReloadAsync(cancellationToken);
                    await _db.Entry(sourceJob).ReloadAsync(cancellationToken);

                    List<OutboxMessage> events = GenerateEvents(request, targetJob, sourceJob, userInfo);
                    await _db.OutboxMessages.AddRangeAsync(events, cancellationToken);

                    await SaveMergeOptions(request, userInfo, cancellationToken);

                    await _db.SaveChangesAsync(cancellationToken);

                    return new Dto(sourceJob.ProjectNumber, targetJob.ProjectNumber, true);
                }
                finally
                {
                    await UnlockJobs(request.SourceJobId, request.TargetJobId, userInfo, cancellationToken);
                }
            }

            private async Task<bool> LockJobs(Guid sourceJobId, Guid targetJobId, UserInfo user, CancellationToken cancellationToken)
            {
                var jobs = await _db.Jobs
                  .Where(j => j.Id == sourceJobId || j.Id == targetJobId)
                  .Include(j => j.JobLocks)
                  .ToListAsync(cancellationToken);

                var sourceJob = jobs.FirstOrDefault(j => j.Id == sourceJobId);
                var targetJob = jobs.FirstOrDefault(j => j.Id == targetJobId);

                if (sourceJob == null || targetJob == null)
                {
                    _logger.LogWarning("One or both jobs not found for locking.");
                    return false;
                }

                if (IsJobLocked(sourceJob) || IsJobLocked(targetJob))
                {
                    return false;
                }

                LockJob(sourceJob, user);
                LockJob(targetJob, user);

                await _db.SaveChangesAsync(cancellationToken);

                return true;
            }

            private bool IsJobLocked(Job job)
            {
                return job.JobLocks.OrderByDescending(x => x.CreatedDate).FirstOrDefault()?.IsLocked ?? false;
            }

            private void LockJob(Job job, UserInfo user)
            {
                JobLock jobLock = CreateJobLock(job, user);
                ClearOldJobLocks(job);
                job.JobLocks.Add(jobLock);

                OutboxMessage jobLockEvent = GenerateJobLockEvent(jobLock, user);
                _db.OutboxMessages.Add(jobLockEvent);
            }

            private JobLock CreateJobLock(Job job, UserInfo user)
            {
                return new JobLock
                {
                    IsLocked = true,
                    JobId = job.Id,
                    LockedByUserId = user.Id,
                    LockedByUserFullName = user.Name,
                    LockedByDeviceId = "WCO Drybook",
                    LockedTime = DateTime.UtcNow,
                    LockedByApplicationId = Common.ClientApplications.WorkCenterOffice,
                    LockedByApplicationName = "WorkCenter Office",
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = user.Username,
                    ModifiedBy = user.Username
                };
            }

            private void ClearOldJobLocks(Job job)
            {
                foreach (var @lock in job.JobLocks.Where(x => x.IsLocked))
                {
                    @lock.IsLocked = false;
                }
            }

            private OutboxMessage GenerateJobLockEvent(JobLock jobLock, UserInfo user)
            {
                var jobLockDto = new JobLockDto
                {
                    Id = jobLock.Id,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.UnlockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId,
                    JobId = jobLock.JobId
                };

                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();

                return new OutboxMessage(new JobLockEvent(jobLockDto, correlationId).ToJson(),
                    nameof(JobLockEvent),
                    correlationId,
                    user.Name);
            }

            private async Task<bool> UnlockJobs(Guid sourceJobId, Guid targetJobId, UserInfo userInfo, CancellationToken cancellationToken)
            {
                var jobs = await _db.Jobs
                       .Where(j => j.Id == sourceJobId || j.Id == targetJobId)
                       .Include(j => j.JobLocks)
                       .ToListAsync(cancellationToken);

                var sourceJob = jobs.FirstOrDefault(j => j.Id == sourceJobId);
                var targetJob = jobs.FirstOrDefault(j => j.Id == targetJobId);

                if (sourceJob == null || targetJob == null)
                {
                    _logger.LogWarning("One or both jobs not found for unlocking.");
                    return false;
                }

                bool sourceUnlocked = TryUnlockJob(sourceJob, userInfo);
                bool targetUnlocked = TryUnlockJob(targetJob, userInfo);

                await _db.SaveChangesAsync(cancellationToken);

                return sourceUnlocked || targetUnlocked;
            }

            private bool TryUnlockJob(Job job, UserInfo userInfo)
            {
                var jobLock = job.JobLocks
                    .Where(a => a.IsLocked)
                    .OrderByDescending(a => a.LockedTime)
                    .FirstOrDefault();

                if (jobLock == null || jobLock.LockedByUserId != userInfo.Id || jobLock.LockedByApplicationId != Common.ClientApplications.WorkCenterOffice)
                {
                    return false;
                }

                jobLock.IsLocked = false;
                jobLock.UnlockedByUserId = userInfo.Id;
                jobLock.UnlockedByDeviceId = "WCO Drybook";
                jobLock.UnlockedTime = DateTime.UtcNow;
                jobLock.ModifiedBy = userInfo.Username;
                jobLock.ModifiedDate = DateTime.UtcNow;

                GenerateAndHandleUnlockEvent(jobLock, userInfo);

                return true;
            }

            private void GenerateAndHandleUnlockEvent(JobLock jobLock, UserInfo userInfo)
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();

                var newEvent = new OutboxMessage(new JobUnlockEvent(MapUnlockEventDto(jobLock), correlationId).ToJson(),
                    nameof(JobUnlockEvent),
                    correlationId,
                    userInfo.Username);
            }

            public JobLockDto MapUnlockEventDto(JobLock jobLock)
            {
                return new JobLockDto
                {
                    Id = jobLock.Id,
                    JobId = jobLock.JobId,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.LockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId,
                    IsAleradyLocked = jobLock.IsLocked
                };
            }

            public async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs
                    .Include(x => x.Customer)
                    .Include(x => x.JobBusinesses)
                        .ThenInclude(x => x.Business)
                    .Include(x => x.JobContacts)
                        .ThenInclude(x => x.Contact)
                            .ThenInclude(x => x.Business)
                    .Include(x => x.JobVisits)
                        .ThenInclude(x => x.JobVisitTriStateAnswers)
                    .Include(x => x.JournalNotes)
                    .Include(x => x.Tasks)
                    .Include(x=> x.MobileData)
                    .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                try
                {
                    job.JobBusinesses = await GetJobBusinesses(_db, jobId, cancellationToken);
                    job.JournalNotes = await GetJournalNotes(_db, jobId, cancellationToken);
                    job.JobTriStateAnswers = await GetJobTriStateAnswers(_db, jobId, cancellationToken);
                    job.MediaMetadata = await GetMediaMetadata(_db, jobId, cancellationToken);
                    job.JobLocks = await GetJobLock(_db, jobId, cancellationToken);
                    job.ExternalMarketingCall = await GetMarketingCall(_db, job, cancellationToken);
                    return job;
                }
                catch (Exception exception)
                {
                    _logger.LogError(exception, "Error loading job data");
                    throw;
                }
            }

            public async Task<ExternalMarketingCall> GetMarketingCall(JobDataContext db, Job job, CancellationToken cancellationToken)
            {
                try
                {
                    var call = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => 
                        x.CallRecordingId == job.CallRecordingId 
                        && x.AssociatedJobId == job.Id, cancellationToken);
                    return call;
                }
                catch (Exception exception)
                {
                    _logger.LogError(exception, "Error loading call data");
                    throw;
                }
            }

            private async Task<ICollection<JobBusinessMap>> GetJobBusinesses(JobDataContext db, Guid jobId, CancellationToken cancellationToken)
            {
                if (db.JobBusinessMaps.Any(x => x.JobId == jobId))
                    return await db.JobBusinessMaps
                        .Where(x => x.JobId == jobId).ToListAsync(cancellationToken);
                return new List<JobBusinessMap>();
            }

            private async Task<ICollection<JobLock>> GetJobLock(JobDataContext db, Guid jobId, CancellationToken cancellationToken)
            {
                if (await db.JobLock.AnyAsync(x => x.JobId == jobId, cancellationToken))
                    return await db.JobLock
                        .Where(x => x.JobId == jobId).ToListAsync(cancellationToken);
                return new List<JobLock>();
            }

            private async Task<ICollection<MediaMetadata>> GetMediaMetadata(JobDataContext db, Guid jobId, CancellationToken cancellationToken)
            {
                if (db.MediaMetadata.Any(x => x.JobId == jobId))
                    return await db.MediaMetadata
                       .Include(mm => mm.JobSketch)
                       .Include(mm => mm.JobInvoice)
                       .Where(x => x.JobId == jobId)
                       .ToListAsync(cancellationToken);
                return new List<MediaMetadata>();
            }

            private async Task<ICollection<JobTriStateAnswer>> GetJobTriStateAnswers(JobDataContext db, Guid jobId, CancellationToken cancellationToken)
            {
                if (db.JobTriStateAnswers.Any(x => x.JobId == jobId))
                    return await db.JobTriStateAnswers
                       .Where(x => x.JobId == jobId)
                       .ToListAsync(cancellationToken);
                return new List<JobTriStateAnswer>();
            }

            private async Task<ICollection<JournalNote>> GetJournalNotes(JobDataContext db, Guid jobId, CancellationToken cancellationToken)
            {
                if (db.JournalNote.Any(x => x.JobId == jobId))
                    return await db.JournalNote
                       .Where(x => x.JobId == jobId)
                       .ToListAsync(cancellationToken);
                return new List<JournalNote>();
            }

            IEnumerable<ValidationFailure> ValidateRequest(Job sourceJob, Job targetJob)
            {
                if (WasPreviouslyASourceJob(sourceJob))
                    yield return new ValidationFailure(
                        nameof(Command.SourceJobId),
                        "Invalid Source Project, the project has already been merged with another project.", sourceJob.Id);

                if (WasPreviouslyASourceJob(targetJob) || WasPreviouslyATargetJob(targetJob))
                    yield return new ValidationFailure(
                        nameof(Command.TargetJobId),
                        "Invalid Target Project, the project has already been merged with another project.", targetJob.Id);

                if (sourceJob.CurrentJobLock != null && sourceJob.CurrentJobLock.IsLocked)
                    yield return new ValidationFailure(nameof(Command.SourceJobId),
                        "One of the projects is locked.", sourceJob.Id);

                if (targetJob.CurrentJobLock != null && targetJob.CurrentJobLock.IsLocked)
                    yield return new ValidationFailure(nameof(Command.TargetJobId),
                        "One of the projects is locked.", targetJob.Id);


            }

            private static bool WasPreviouslyATargetJob(Job job)
                 => job.MergeSource != null;
            private static bool WasPreviouslyASourceJob(Job job)
                 => job.MergeTarget != null;
            private string MapProjectNumber(string targetProjectNumber, string sourceProjectNumber)
                => targetProjectNumber.Substring(0, CalculateProjectNumberIntLength(targetProjectNumber)) +
                sourceProjectNumber.Substring(CalculateProjectNumberIntLength(sourceProjectNumber));

            private int CalculateProjectNumberIntLength(string projectNumber)
            {
                return (from t in projectNumber
                        where char.IsDigit(t) || t.Equals('-')
                        select t).ToArray().Length;
            }

            private async Task MergeJobs(Command request, Job sourceJob, Job targetJob, CancellationToken cancellationToken)
            {
                _logger.LogDebug("[MergeJobs] Merging source job {sourceJobId} into target job {targetJobId}",
                    sourceJob.Id, targetJob.Id);
                _logger.LogInformation("[{method}] Merging sourceJob notes {@sourceJob} into targetJob notes {@targetJob}",
                    nameof(MergeJobs), sourceJob.JournalNotes, targetJob.JournalNotes);
                var isDryingDataCopied = request.MergeOptions.FirstOrDefault(x => x.JobMergeTypeId == JobMergeTypes.DryingData)?.Selected ?? false;
                var isAttachmentsCopied = request.MergeOptions.FirstOrDefault(x => x.JobMergeTypeId == JobMergeTypes.Attachments)?.Selected ?? false;
                var isAttachmentsMergeCompleted = false;
                var hasCallDispositionSourceJob = sourceJob.CallDisposition != Guid.Empty && sourceJob.CallDisposition != null;
                var hasCallDispositionTargetJob = targetJob.CallDisposition != Guid.Empty && targetJob.CallDisposition != null;
                List<MediaMetadata> sourceJobMedia = null;

                if (isAttachmentsCopied)
                {
                    sourceJobMedia = sourceJob.MediaMetadata
                        .Where(x => (x.MediaTypeId == Common.MediaTypes.Photo || x.MediaTypeId == Common.MediaTypes.Sketch)
                            && !x.IsDeleted)
                        .ToList();
                }

                if (isDryingDataCopied)
                    await UpdateDryingDataTargetIdsAsync(sourceJob, targetJob, cancellationToken);
               
                foreach (var mergeOption in request.MergeOptions)
                {
                    if (mergeOption.Selected)
                    {
                        bool isDefault = false;
                        _logger.LogDebug("[{method}] Merging {mergeSection} into {targetJobId}", nameof(MergeJobs), mergeOption.Name, targetJob.Id);
                        switch (mergeOption.JobMergeTypeId)
                        {
                            case JobMergeTypes.ContactInformation:
                                _mergeContext.SetJobMergeSection(new MergeContactInformation());
                                break;
                            case JobMergeTypes.ProjectSnapshot:
                                _mergeContext.SetJobMergeSection(new MergeProjectSnapshot());
                                break;
                            case JobMergeTypes.ClientDetails:
                                _mergeContext.SetJobMergeSection(new MergeClientDetails());
                                break;
                            case JobMergeTypes.Attachments:
                                _mergeContext.SetJobMergeSection(new MergeAttachments());
                                isAttachmentsMergeCompleted = true;
                                break;
                            case JobMergeTypes.DocumentsAndForms:
                                _mergeContext.SetJobMergeSection(new MergeDocumentsAndForms());
                                break;
                            case JobMergeTypes.Correspondence:
                                _mergeContext.SetJobMergeSection(new MergeCorrespondence());
                                break;
                            case JobMergeTypes.DryingData:
                                _mergeContext.SetJobMergeSection(new MergeDryingData(_db));
                                break;
                            case JobMergeTypes.FinancialInformation:
                                _mergeContext.SetJobMergeSection(new MergeFinancialInformation());
                                break;
                            default:
                                isDefault = true;
                                break;
                        }
                        if (!isDefault)
                        {
                            _mergeContext.ExecuteJobMergeSection(sourceJob, targetJob, isDryingDataCopied, hasCallDispositionSourceJob, hasCallDispositionTargetJob);

                            if (isAttachmentsMergeCompleted && isDryingDataCopied)
                            {
                                await UpdateDryingRelatedMediaMetaDataAsync(sourceJob, targetJob, sourceJobMedia, cancellationToken);
                                isAttachmentsMergeCompleted = false;
                            }
                        }
                    }
                }
                _logger.LogInformation("[{method}] Result notes into targetJob {@targetJobNotes}",
                    nameof(MergeJobs), targetJob.JournalNotes);
            }

            private async Task UpdateDryingRelatedMediaMetaDataAsync(Job sourceJob, Job targetJob, List<MediaMetadata> sourceJobMedia, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Attempting to update Media with drying related data.");

                if (sourceJobMedia is null)
                    return;

                var copiedSourceMedia = sourceJobMedia
                    .Select(x => MapMediaMetadata(x, sourceJob.Id))
                    .ToList();

                _logger.LogInformation("Updating SourceMedia Foreign keys to a copy.");
                Dictionary<Guid, Guid> jobAreaDict = new Dictionary<Guid, Guid>();
                Dictionary<Guid, Guid> jobVisitDict = new Dictionary<Guid, Guid>();
                Dictionary<Guid, Guid> zoneDict = new Dictionary<Guid, Guid>();
                foreach (var copiedMedia in copiedSourceMedia)
                {
                    await UpdateSourceMediaAsync(copiedMedia,
                                                sourceJob.Id,
                                                jobAreaDict,
                                                jobVisitDict,
                                                zoneDict,
                                                cancellationToken);
                    sourceJob.MediaMetadata.Add(copiedMedia);
                };
            }

            private async Task UpdateSourceMediaAsync(MediaMetadata sourceMedia,
                                                       Guid sourceJobId,
                                                       Dictionary<Guid, Guid> jobAreaDict,
                                                       Dictionary<Guid, Guid> jobVisitDict,
                                                       Dictionary<Guid, Guid> zoneDict,
                                                       CancellationToken cancellationToken)
            {
                //Make a copy of Drying entities to reference in Source Media,
                //since the original entities have been copied to target
                if (sourceMedia.JobAreaId.HasValue)
                {
                    _logger.LogDebug("Media with id {mediaId}, has the original JobArea. Creating copy of JobArea", sourceMedia.Id);
                    if (jobAreaDict.ContainsKey(sourceMedia.JobAreaId.Value))
                    {
                        sourceMedia.JobArea = null;
                        sourceMedia.JobAreaId = jobAreaDict.GetValueOrDefault(sourceMedia.JobAreaId.Value);
                    }
                    else
                    {
                        var jobArea = await _db.JobAreas
                            .AsNoTracking()
                            .FirstOrDefaultAsync(x => x.Id == sourceMedia.JobAreaId, cancellationToken);
                        var copiedJobArea = await MapAsync(jobArea, zoneDict, jobVisitDict, sourceJobId, cancellationToken);
                        sourceMedia.JobArea = copiedJobArea;
                        sourceMedia.JobAreaId = copiedJobArea.Id;
                        jobAreaDict.Add(jobArea.Id, copiedJobArea.Id);
                    }
                }

                if (sourceMedia.JobVisitId.HasValue)
                {
                    _logger.LogDebug("Media with id {mediaId}, has the original JobVisit. Creating copy of JobVisit", sourceMedia.Id);

                    var (copiedVisit, copiedVisitId) = await GetCopiedValues(jobVisitDict, 
                        sourceMedia.JobVisitId.Value, 
                        sourceJobId, 
                        GetJobVisitToCopy, 
                        Map, 
                        cancellationToken);

                    sourceMedia.JobVisit = copiedVisit;
                    sourceMedia.JobVisitId = copiedVisitId;
                }
            }

            private async Task UpdateDryingDataTargetIdsAsync(Job sourceJob, Job targetJob, CancellationToken cancellationToken)
            {
                DeleteTargetSketch(targetJob);
                await DeleteTargetLineItems(targetJob, cancellationToken);
                await DeleteTargetJobMaterials(targetJob, cancellationToken);
                await DeleteTargetTasks(targetJob, sourceJob, cancellationToken);
                await DeleteTargetJobAreas(targetJob, cancellationToken);
                await DeleteTargetZones(targetJob, cancellationToken);
                await DeleteTargetJobVisits(targetJob, cancellationToken);

                (await _db.JobAreas.Where(x => x.JobId == sourceJob.Id).ToListAsync(cancellationToken)).ForEach(x => x.JobId = targetJob.Id);
                (await _db.Zones.Where(x => x.JobId == sourceJob.Id).ToListAsync(cancellationToken)).ForEach(x => x.JobId = targetJob.Id);
                (await _db.JobVisit.Where(x => x.JobId == sourceJob.Id).ToListAsync(cancellationToken)).ForEach(x => x.JobId = targetJob.Id);
                (await _db.LineItems.Where(x => x.JobId == sourceJob.Id).ToListAsync(cancellationToken)).ForEach(x => x.JobId = targetJob.Id);
                (await _db.JobMaterials.Where(x => x.JobId == sourceJob.Id).ToListAsync(cancellationToken)).ForEach(x => x.JobId = targetJob.Id);
                (await _db.Tasks.Where(x => x.JobId == sourceJob.Id).ToListAsync(cancellationToken)).ForEach(x => x.JobId = targetJob.Id);
            }

            private void DeleteTargetSketch(Job targetJob)
            {
                var mobileSketchFiles = targetJob.MediaMetadata
                    .Where(x => x.MediaTypeId == MediaTypes.Document && mobileSketchArtifactIds.Contains(x.ArtifactTypeId))
                    .ToList();

                foreach (var file in mobileSketchFiles)
                {
                    file.ModifiedBy = nameof(MergeJobs);
                    file.ModifiedDate = DateTime.UtcNow;
                    file.IsDeleted = true;
                }
            }

            private async Task GeneratePlacementRemovedEvents(Job targetJob, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var removedEquipmentPlacements = _db.ChangeTracker.Entries()
                    .Where(x => x.Entity is EquipmentPlacement && x.State == EntityState.Deleted)
                    .Select(x => x.Entity as EquipmentPlacement);

                _logger.LogDebug("Generate EquipmentRemovedFromRoomEvent for removed equipment placements from Target Job. {EquipmentPlacement}",
                    removedEquipmentPlacements);

                if (removedEquipmentPlacements.Any())
                {
                    var events = removedEquipmentPlacements
                        .GroupBy(p => p.JobArea.Id)
                        .Select(x => new EquipmentRemovedFromRoomEvent(new EquipmentRemovedFromRoomDto
                        {
                            JobId = targetJob.Id,
                            FranchiseSetId = targetJob.FranchiseSetId,
                            JobAreaId = x.Key,
                            RoomId = x.First().JobArea.RoomId ?? Guid.Empty,
                            UserId = userInfo.Id,
                            Username = userInfo.Username,
                            Removals = x.Select(x => new EquipmentRemovedFromRoomDto.RemovalDto
                            {
                                Id = x.Id,
                                EquipmentId = x.EquipmentId
                            })
                        }, correlationId))
                        .Select(x => new OutboxMessage(x.ToJson(), nameof(EquipmentRemovedFromRoomEvent), correlationId, userInfo.Username));

                    await _db.OutboxMessages.AddRangeAsync(events, cancellationToken);
                }
            }

            private async Task DeleteTargetTasks(Job targetJob, Job sourceJob, CancellationToken cancellationToken)
            {
                //delete all target tasks except the ones attached to questions that are unanswered in source
                var sourceAnsweredQuestions = sourceJob.JobTriStateAnswers.Select(x => x.JobTriStateQuestionId);
                var targetOnlyQuestionIds = targetJob.JobTriStateAnswers
                        .Where(x => sourceAnsweredQuestions == null || sourceAnsweredQuestions.Count() == 0 || !sourceAnsweredQuestions.Contains(x.JobTriStateQuestionId))
                        .Select(x => x.JobTriStateQuestionId)
                        .ToList();

                var sourceAnsweredVisitQuestions = sourceJob.JobVisits.OrderBy(x => x.Date).FirstOrDefault()?.JobVisitTriStateAnswers.Select(x => x.JobTriStateQuestionId);
                var targetOnlyVisitQuestionIds = targetJob.JobVisits.OrderBy(x => x.Date).FirstOrDefault()?.JobVisitTriStateAnswers
                        .Where(x => sourceAnsweredVisitQuestions == null || sourceAnsweredVisitQuestions.Count() == 0 || !sourceAnsweredVisitQuestions.Contains(x.JobTriStateQuestionId))
                        .Select(x => x.JobTriStateQuestionId)
                        .ToList();

                var taskIdsToKeep = targetJob.Tasks
                        .Where(x => x.JobTriStateQuestionId.HasValue && targetOnlyQuestionIds.Concat(targetOnlyVisitQuestionIds ?? new List<Guid>()).Contains(x.JobTriStateQuestionId.Value))
                        .Select(x => x.Id)
                        .ToList();

                var tasksToDelete = await _db.Tasks
                       .Where(x => x.JobId == targetJob.Id && !taskIdsToKeep.Contains(x.Id))
                       .ToListAsync(cancellationToken);

                foreach (var taskToDelete in tasksToDelete)
                {
                    _db.Tasks.Remove(taskToDelete);
                    _db.JournalNote.RemoveRange(taskToDelete.JournalNotes);
                }
            }

            private async Task DeleteTargetLineItems(Job targetJob, CancellationToken cancellationToken)
            {
                var lineItemsToDelete = await _db.LineItems
                    .Include(x => x.Notes)
                    .Where(x => x.JobId == targetJob.Id)
                    .ToListAsync(cancellationToken);

                foreach (var lineItem in lineItemsToDelete)
                {
                    lineItem.IsDeleted = true;
                    lineItem.DeletedBy = nameof(MergeJobs);
                    lineItem.DeletedDate = DateTime.UtcNow;
                    lineItem.JobVisitId = null;
                    lineItem.RoomId = null;

                    foreach (var lineItemNote in lineItem.Notes)
                    {
                        lineItemNote.IsDeleted = true;
                        lineItemNote.LineItemId = null;
                        lineItemNote.RoomId = null;
                    }
                }
            }

            private async Task DeleteTargetJobMaterials(Job targetJob, CancellationToken cancellationToken)
            {
                var jobMaterialsToDelete = await _db.JobMaterials
                    .Include(x => x.JobAreaMaterials)
                        .ThenInclude(x => x.JobAreaMaterialReadings)
                    .Where(x => x.JobId == targetJob.Id)
                    .ToListAsync(cancellationToken);

                foreach (var jobMaterial in jobMaterialsToDelete)
                {
                    foreach (var jobAreaMaterial in jobMaterial.JobAreaMaterials)
                    {
                        foreach (var materialReading in jobAreaMaterial.JobAreaMaterialReadings)
                        {
                            _db.JobAreaMaterialReadings.Remove(materialReading);
                        }
                        jobAreaMaterial.JobAreaMaterialReadings.Clear();
                        _db.JobAreaMaterials.Remove(jobAreaMaterial);
                    }

                    jobMaterial.JobAreaMaterials.Clear();
                    _db.JobMaterials.Remove(jobMaterial);
                }
            }

            private async Task DeleteTargetJobVisits(Job targetJob, CancellationToken cancellationToken)
            {
                var jobVisitsToDelete = await _db.JobVisit
                    .Where(x => x.JobId == targetJob.Id)
                    .ToListAsync(cancellationToken);

                var userInfo = _userInfoAccessor.GetUserInfo();

                foreach (var jobVisit in jobVisitsToDelete)
                {
                    if (jobVisit != null)
                    {
                        var jobAreasAddedOnVisit = await _db.JobAreas
                            .Where(x => x.BeginJobVisitId == jobVisit.Id || x.EndJobVisitId == jobVisit.Id)
                            .ToListAsync(cancellationToken);

                        foreach (var jobArea in jobAreasAddedOnVisit)
                        {
                            jobArea.BeginJobVisitId = null;
                            jobArea.EndJobVisitId = null;
                            jobArea.ZoneId = null;
                            jobArea.ModifiedBy = userInfo.Username;
                            jobArea.ModifiedDate = DateTime.UtcNow;
                            jobArea.IsDeleted = true;
                        }

                        var roomsToDelete = await _db.Rooms
                            .Where(r => r.DryOnJobVisitId == jobVisit.Id)
                            .ToListAsync(cancellationToken);

                        _logger.LogInformation($"No. of rooms to remove: {roomsToDelete.Count} for JobId: {targetJob.Id}");
                        if (roomsToDelete.Count > 0)
                        {
                            _db.RemoveRange(roomsToDelete);

                            await GenerateRoomsDeleteEvents(targetJob.Id, cancellationToken);
                        }

                        var events = GenerateJobVisitDeletedEvents(jobVisit.JobId, jobVisit.Id, _sessionIdAccessor.GetCorrelationGuid(), userInfo);
                        await _db.OutboxMessages.AddRangeAsync(events, cancellationToken);

                        _db.Remove(jobVisit);
                    }
                }
            }

            private async Task DeleteTargetJobAreas(Job targetJob, CancellationToken cancellationToken)
            {
                var jobAreas = await _db.JobAreas
                    .Where(x => x.JobId == targetJob.Id)
                    .ToListAsync(cancellationToken);

                var jobAreaIds = jobAreas.Select(x => x.Id).ToList();

                var equipmentPlacements = await _db.EquipmentPlacements
                    .Where(x => jobAreaIds.Contains(x.JobAreaId))
                    .ToListAsync(cancellationToken);

                var equipmentPlacementIds = equipmentPlacements.Select(x => x.Id).ToList();

                var equipmentPlacementReadings = await _db.EquipmentPlacementReadings
                    .Where(x => equipmentPlacementIds.Contains(x.EquipmentPlacementId))
                    .ToListAsync(cancellationToken);

                var rooms = await _db.Rooms
                    .Where(x => jobAreaIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                _db.RemoveRange(equipmentPlacementReadings);
                _db.RemoveRange(equipmentPlacements);
                _db.RemoveRange(rooms);
                _db.RemoveRange(jobAreas);

                await GenerateRoomsDeleteEvents(targetJob.Id, cancellationToken);
                await GeneratePlacementRemovedEvents(targetJob, cancellationToken);
            }

            private async Task GenerateRoomsDeleteEvents(Guid jobId, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var roomDeletedEvents = _db.ChangeTracker.Entries()
                   .Where(x => x.Entity is Room && x.State == EntityState.Deleted)
                   .Select(x => x.Entity as Room)
                   .Select(x => new RoomDeletedEvent(MapRoomDeletedDto(x, userInfo, jobId).Result, correlationId))
                   .Select(x => new OutboxMessage(x.ToJson(), nameof(RoomDeletedEvent), correlationId, userInfo.Username));

                if (roomDeletedEvents.Any())
                {
                    await _db.OutboxMessages.AddRangeAsync(roomDeletedEvents, cancellationToken);
                }
            }

            private async Task<RoomDeletedDto> MapRoomDeletedDto(Room room, UserInfo userInfo, Guid jobId)
            {
                var roomDeleteDto = new RoomDeletedDto
                {
                    RoomId = Guid.Parse(room.DryOnJobVisitId.ToString()),
                    JobId = jobId,
                    DeletedBy = userInfo.Name,
                    DeletedDate = DateTime.UtcNow
                };

                return await Task<RoomDeletedDto>.FromResult(roomDeleteDto);
            }

            private IEnumerable<OutboxMessage> GenerateJobVisitDeletedEvents(
                Guid jobId,
                Guid jobVisitId,
                Guid correlationId,
                UserInfo userInfo)
            {
                var @event = new JobVisitDeletedEvent(MapJobVisitDeletedDto(jobId, jobVisitId, userInfo.Username, userInfo.Id).Result, correlationId);
                yield return new OutboxMessage(@event.ToJson(), nameof(JobVisitDeletedEvent), correlationId, userInfo.Username);

            }

            private async Task<JobVisitDeletedDto> MapJobVisitDeletedDto(Guid jobId, Guid jobVisitId, string username, Guid userId)
            {
                return new JobVisitDeletedDto
                {
                    JobId = jobId,
                    JobVisitId = jobVisitId,
                    Username = username,
                    UserId = userId
                };
            }

            private async Task DeleteTargetZones(Job targetJob, CancellationToken cancellationToken)
            {
                var zonesToDelete = await _db.Zones
                    .Include(z => z.Job)
                    .Where(x => x.JobId == targetJob.Id)
                    .ToListAsync(cancellationToken);
                var zonesToDeleteIds = zonesToDelete.Select(x => x.Id).ToList();

                var tasks = await _db.Tasks
                    .Include(t => t.JournalNotes)
                    .Where(t => t.ZoneId.HasValue && zonesToDeleteIds.Contains(t.ZoneId.Value))
                    .ToListAsync(cancellationToken);

                var zoneReadings = await _db.ZoneReadings
                    .Where(x => zonesToDeleteIds.Contains(x.ZoneId))
                    .ToListAsync(cancellationToken);

                foreach (var task in tasks)
                {
                    if (task.JournalNotes.Any())
                    {
                        _db.RemoveRange(task.JournalNotes);
                    }
                    _db.Remove(task);
                }

                _db.RemoveRange(zoneReadings);
                _db.RemoveRange(zonesToDelete);

                await GenerateZoneDeletedEvent(targetJob.Id, cancellationToken);
            }

            private async Task GenerateZoneDeletedEvent(Guid jobId, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var zoneDeletedEvents = _db.ChangeTracker.Entries()
                   .Where(x => x.Entity is Zone && x.State == EntityState.Deleted)
                   .Select(x => x.Entity as Zone)
                   .Select(x => new ZoneDeletedEvent(new ZoneDeletedEvent.ZoneDeletedDto
                   {
                       Id = x.Id,
                       JobId = jobId
                   }, correlationId))
                   .Select(x => new OutboxMessage(x.ToJson(), nameof(RoomDeletedEvent), correlationId, userInfo.Username));

                if (zoneDeletedEvents.Any())
                {
                    await _db.OutboxMessages.AddRangeAsync(zoneDeletedEvents, cancellationToken);
                }
            }

            private List<OutboxMessage> GenerateEvents(Command request, Job targetJob, Job sourceJob, UserInfo userInfo)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var events = new List<OutboxMessage>();
                var jobMergeEvent = GenerateJobMergeEvent(request, targetJob, correlationId, userInfo);
                events.Add(jobMergeEvent);
                if (request.MergeOptions.Any(x => x.JobMergeTypeId == JobMergeTypes.FinancialInformation && x.Selected))
                {
                    var financialJobDetailsUpdatedEvents = GenerateFinancialJobDetailsUpdatedEvents(sourceJob, targetJob, correlationId, userInfo);
                    events.AddRange(financialJobDetailsUpdatedEvents);
                }
                var journalNoteEvents = GenerateJournalNoteCreatedEvents(sourceJob, targetJob, correlationId, userInfo);
                events.AddRange(journalNoteEvents);

                return events;
            }

            private List<OutboxMessage> GenerateFinancialJobDetailsUpdatedEvents(Job sourceJob, Job targetJob, Guid correlationId, UserInfo userInfo)
            {
                var events = new List<OutboxMessage>();
                var sourceJobUpdatedDto = JobDetailsUpdatedMapper.MapJobDetailsUpdatedDto(sourceJob, true, userInfo);
                var sourceJobUpdatedEvent = new JobDetailsUpdatedEvent(sourceJobUpdatedDto, correlationId);

                var targetJobUpdatedDto = JobDetailsUpdatedMapper.MapJobDetailsUpdatedDto(targetJob, true, userInfo);
                var targetJobUpdatedEvent = new JobDetailsUpdatedEvent(targetJobUpdatedDto, correlationId);

                events.Add(new OutboxMessage(sourceJobUpdatedEvent.ToJson(), nameof(JobDetailsUpdatedEvent), correlationId, userInfo.Username));
                events.Add(new OutboxMessage(targetJobUpdatedEvent.ToJson(), nameof(JobDetailsUpdatedEvent), correlationId, userInfo.Username));
                return events;
            }

            private void GenerateMergeCorrespondenceNotes(Job sourceJob, Job targetJob, string username)
            {
                var sourceJobNote = $"This project was merged into {targetJob.ProjectNumber}";
                var targetJobNote = $"Project {sourceJob.ProjectNumber} was merged into this project";

                sourceJob.JournalNotes.Add(GenerateMergeJournalNote(sourceJob.Id, username, sourceJobNote));
                targetJob.JournalNotes.Add(GenerateMergeJournalNote(targetJob.Id, username, targetJobNote));
            }

            private JournalNote GenerateMergeJournalNote(Guid jobId, string username, string note)
                => new JournalNote()
                {
                    JobId = jobId,
                    Author = username,
                    Subject = _mergeNoteSubject,
                    Note = note,
                    CategoryId = JournalNotesCategoryTypes.OtherJobDetais,
                    TypeId = LookupService.Constants.JournalNoteTypes.OtherJobDetails,
                    VisibilityId = JournalNoteVisibilityTypes.FranchiseAndClient,
                    ActionDate = DateTime.Now
                };

            private OutboxMessage GenerateJobMergeEvent(Command request, Job targetJob, Guid correlationId, UserInfo userInfo)
            {
                var sourceJobId = request.SourceJobId;
                var targetJobId = request.TargetJobId;
                var jobMergeOptions = request.MergeOptions.ToList();

                var jobMergeOptionsDto = jobMergeOptions.Select(jmo =>
                {
                    return new JobMergeEvent.JobMergeOptionDto
                    {
                        JobMergeTypeId = (int)jmo.JobMergeTypeId,
                        Name = jmo.Name,
                        Selected = jmo.Selected
                    };
                }).ToList();

                var jobMergeEvent = new JobMergeEvent(sourceJobId, targetJobId, jobMergeOptionsDto, correlationId)
                {
                    MarketingFields = new JobMergeEvent.MarketingFieldsDto
                    {
                        TargetJob = MapMarketingJobDto(targetJob, new JobMergeEvent.JobDto())
                    }
                };
                return new OutboxMessage(jobMergeEvent.ToJson(), nameof(JobMergeEvent), correlationId, userInfo.Username);
            }
            private List<OutboxMessage> GenerateJournalNoteCreatedEvents(Job sourceJob, Job targetJob, Guid correlationId, UserInfo userInfo)
            {
                var events = new List<OutboxMessage>();
                var sourceJobMergeJournalNote = sourceJob.JournalNotes.FirstOrDefault(x => x.Subject == _mergeNoteSubject);
                var targetJobMergeJournalNote = targetJob.JournalNotes.FirstOrDefault(x => x.Subject == _mergeNoteSubject);

                if (sourceJobMergeJournalNote != null)
                {
                    var sourceJobJournalNoteCreatedDto = MapJournalNoteDto(sourceJobMergeJournalNote, userInfo);
                    var sourceJobJournalNoteCreatedEvent = new JournalNoteCreatedEvent(sourceJobJournalNoteCreatedDto, correlationId);
                    events.Add(new OutboxMessage(sourceJobJournalNoteCreatedEvent.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username));
                }

                if (targetJobMergeJournalNote != null)
                {
                    var targetJobJournalNoteCreatedDto = MapJournalNoteDto(targetJobMergeJournalNote, userInfo);
                    var targetJobJournalNoteCreatedEvent = new JournalNoteCreatedEvent(targetJobJournalNoteCreatedDto, correlationId);
                    events.Add(new OutboxMessage(targetJobJournalNoteCreatedEvent.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username));
                }

                return events;
            }

            private JournalNoteDto MapJournalNoteDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id
                };

            /// <summary>
            /// Map Media with a new Id and keep the original name
            /// </summary>
            /// <param name="sourceMedia"></param>
            /// <param name="jobId"></param>
            /// <returns></returns>
            private MediaMetadata MapMediaMetadata(MediaMetadata sourceMedia, Guid jobId)
                => new MediaMetadata
                {
                    Id = Guid.NewGuid(),
                    BucketName = sourceMedia.BucketName,
                    FranchiseSetId = sourceMedia.FranchiseSetId,
                    JobId = jobId,
                    MediaTypeId = sourceMedia.MediaTypeId,
                    ArtifactTypeId = sourceMedia.ArtifactTypeId,
                    FormTemplateId = sourceMedia.FormTemplateId,
                    Name = sourceMedia.Name,
                    Description = sourceMedia.Description,
                    MediaPath = sourceMedia.MediaPath,
                    IsDeleted = sourceMedia.IsDeleted,
                    IsForUpload = sourceMedia.IsForUpload,
                    UploadedSuccessfully = sourceMedia.UploadedSuccessfully,
                    JobSketch = MapJobSketch(sourceMedia.JobSketch, jobId),
                    JobInvoiceId = sourceMedia.JobInvoiceId,
                    JobInvoice = sourceMedia.JobInvoice,
                    SignedDate = sourceMedia.SignedDate,
                    FormVersion = sourceMedia.FormVersion,
                    SyncDate = sourceMedia.SyncDate,
                    ArtifactDate = sourceMedia.ArtifactDate,
                    Comment = sourceMedia.Comment,
                    JobAreaId = sourceMedia.JobAreaId,
                    JobAreaMaterialId = sourceMedia.JobAreaMaterialId,
                    JobVisitId = sourceMedia.JobVisitId,
                    ZoneId = sourceMedia.ZoneId,
                };

            private JobSketch MapJobSketch(JobSketch sourceJobSketch, Guid jobId)
            {
                if (sourceJobSketch is null)
                    return null;

                return new JobSketch
                {
                    JobId = jobId,
                    Name = sourceJobSketch.Name,
                    CanvasJson = sourceJobSketch.CanvasJson,
                };
            }

            private async Task<JobArea> MapAsync(JobArea jobArea,
                                Dictionary<Guid, Guid> zoneDict,
                                Dictionary<Guid, Guid> jobVisitDict,
                                Guid sourceJobId,
                                CancellationToken cancellationToken)
            {
                var area = new JobArea
                {
                    Id = Guid.NewGuid(),
                    JobId = sourceJobId,
                    JobAreaTypeId = jobArea.JobAreaTypeId,
                    Name = jobArea.Name,
                    SortOrder = jobArea.SortOrder,
                    IsUsedInValidation = jobArea.IsUsedInValidation,
                    IsDeleted = jobArea.IsDeleted,
                    ZoneId = jobArea.ZoneId,
                    BeginJobVisitId = jobArea.BeginJobVisitId,
                    EndJobVisitId = jobArea.EndJobVisitId,
                };

                if (jobArea.BeginJobVisitId.HasValue)
                {
                    var (copiedVisit, copiedVisitId) = await GetCopiedValues(jobVisitDict, 
                        jobArea.BeginJobVisitId.Value, 
                        sourceJobId, 
                        GetJobVisitToCopy, 
                        Map, 
                        cancellationToken);

                    area.BeginJobVisit = copiedVisit;
                    area.BeginJobVisitId = copiedVisitId;
                }

                if (jobArea.EndJobVisitId.HasValue)
                {
                    var (copiedVisit, copiedVisitId) = await GetCopiedValues(jobVisitDict, 
                        jobArea.BeginJobVisitId.Value, 
                        sourceJobId, 
                        GetJobVisitToCopy, 
                        Map, 
                        cancellationToken);

                    area.EndJobVisit = copiedVisit;
                    area.EndJobVisitId = copiedVisitId;
                }

                if (jobArea.ZoneId.HasValue)
                {
                    var (copiedZone, copiedZoneId) = await GetCopiedValues(zoneDict, 
                        jobArea.ZoneId.Value, 
                        sourceJobId, 
                        GetZoneToCopy, 
                        Map, 
                        cancellationToken);

                    area.Zone = copiedZone;
                    area.ZoneId = copiedZoneId;
                }

                return area;
            }

            private async Task<Zone> GetZoneToCopy(Guid id, CancellationToken cancellationToken)
                => await _db.Zones.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

            private async Task<JobVisit> GetJobVisitToCopy(Guid id, CancellationToken cancellationToken)
                => await _db.JobVisit.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

            private async Task<(T copiedEntity, Guid copiedEntityId)> GetCopiedValues<T>(Dictionary<Guid, Guid> entityMapping, 
                Guid originalId, 
                Guid sourceJobId,
                Func<Guid, CancellationToken, Task<T>> GetEntityFromDb, 
                Func<T, Guid, T> MapEntity,
                CancellationToken cancellationToken)
                where T : Entity<Guid>
            {
                if (entityMapping.ContainsKey(originalId))
                    return (null, entityMapping.GetValueOrDefault(originalId));
                
                var entityFromDb = await GetEntityFromDb(originalId, cancellationToken);
                var copiedEntity = MapEntity(entityFromDb, sourceJobId);
                entityMapping.Add(entityFromDb.Id, copiedEntity.Id);
                return (copiedEntity, copiedEntity.Id);
            }

            private JobVisit Map(JobVisit jobVisit, Guid sourceJobId)
                => new JobVisit
                {
                    Id = Guid.NewGuid(),
                    JobId = sourceJobId,
                    Date = jobVisit.Date,
                    EmployeeInitials = jobVisit.EmployeeInitials,
                    DepartureDate = jobVisit.DepartureDate,
                    DefaultNonPenetratingMeterEquipmentId = jobVisit.DefaultNonPenetratingMeterEquipmentId,
                    DefaultPenetratingMeterEquipmentId = jobVisit.DefaultPenetratingMeterEquipmentId,
                    DefaultThermoHygrometerEquipmentId = jobVisit.DefaultThermoHygrometerEquipmentId,
                    IsMissingVisit = jobVisit.IsMissingVisit,
                };

            private Zone Map(Zone zoneDb, Guid sourceJobId)
                => new Zone
                {
                    Id = Guid.NewGuid(),
                    JobId = sourceJobId,
                    ZoneTypeId = zoneDb.ZoneTypeId,
                    Name = zoneDb.Name,
                    Description = zoneDb.Description,
                    AirMoverCalculationTypeId = zoneDb.AirMoverCalculationTypeId,
                    WaterClassId = zoneDb.WaterClassId,
                    WaterCategoryId = zoneDb.WaterCategoryId,
                    SketchMediaContentId = zoneDb.SketchMediaContentId,
                    WaterClassOverridden = zoneDb.WaterClassOverridden,
                    CanValidateDehuCapacity = zoneDb.CanValidateDehuCapacity,
                    RequiredDehuCapacity = zoneDb.RequiredDehuCapacity,
                    AchievedDehuCapacity = zoneDb.AchievedDehuCapacity
                };

            private async Task SaveMergeOptions(Command request, UserInfo userInfo, CancellationToken cancellationToken)
            {
                var mergeOptions = request.MergeOptions.Select(Map).ToList();

                var jobMergeHistory = new JobMergeHistory
                {
                    SourceJobId = request.SourceJobId,
                    TargetJobId = request.TargetJobId,
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.UtcNow,
                    MergeOptions = request.MergeOptions.Select(Map).ToList()
                };

                await _db.JobMergeHistory.AddAsync(jobMergeHistory, cancellationToken);
            }

            private JobMergeOption Map(JobMergeOptionDto jobMergeOption)
                => new JobMergeOption
                {
                    JobMergeTypeId = jobMergeOption.JobMergeTypeId,
                    Name = jobMergeOption.Name,
                    Selected = jobMergeOption.Selected,
                    PreviouslyMerged = jobMergeOption.PreviouslyMerged
                };

        }
    }
}
