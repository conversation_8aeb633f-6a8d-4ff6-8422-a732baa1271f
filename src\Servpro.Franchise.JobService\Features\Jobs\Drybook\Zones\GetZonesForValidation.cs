﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones.ZoneValidation;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetZonesForValidation
    {
        public class Query : IRequest<IEnumerable<ZoneForValidationDto>>
        {
            public Guid JobId { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<ZoneForValidationDto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccesor;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GetZonesForValidation> _logger;
            private readonly IZoneValidationMapper _zoneValidationMapper;


            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILogger<GetZonesForValidation> logger,
                IZoneValidationMapper zoneValidationMapper,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfoAccesor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
                _zoneValidationMapper = zoneValidationMapper;
                _logger = logger;

            }

            public async Task<IEnumerable<ZoneForValidationDto>> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting zones for validation: {@request}", request);

                var userInfo = _userInfoAccesor.GetUserInfo();
                var job = await _context.Jobs
                       .AsNoTracking()
                       .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId.Value, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException("Job does not exist");

                job.Zones = await _context.Zones
                                        .Include(x => x.Tasks.Where(t => t.JobId == request.JobId))
                                            .ThenInclude(x => x.JournalNotes)
                                        .Include(x => x.JobAreas)
                                            .ThenInclude(x => x.EquipmentPlacements)
                                                .ThenInclude(x => x.Equipment)
                                                    .ThenInclude(x => x.EquipmentModel)
                                                            .ThenInclude(x => x.EquipmentType)
                                        .Include(x => x.JobAreas)
                                            .ThenInclude(x => x.Room)
                                            .ThenInclude(x => x.RoomFlooringTypesAffected)
                                        .Include(x => x.JobAreas)
                                            .ThenInclude(x => x.BeginJobVisit)
                                        .AsNoTracking()
                                        .Where(x => x.JobId == request.JobId && !x.IsDeleted)
                                        .ToListAsync(cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var waterClassLookups = lookups.WaterClasses.ToDictionary(x => x.Id);
                var dateReceived = job.GetDate(JobDateTypes.ReceivedDate);

                var validationZoneTasks = job.Zones
                    .Where(x => x.ZoneTypeId == ZoneTypes.Drying)
                    .Select(async x => await _zoneValidationMapper.ExecuteAsync(x, waterClassLookups, dateReceived))
                    .ToList();

                var resolvedZones = await Task.WhenAll(validationZoneTasks);
                _logger.LogInformation("Validation Zones: {@resolvedZones}", resolvedZones);

                return resolvedZones;
            }
        }
    }
}
