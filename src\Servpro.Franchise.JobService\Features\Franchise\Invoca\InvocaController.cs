﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    [Route("api/jobs/")]
    public class InvocaController : Controller
    {
        private readonly IMediator _mediator;
        public readonly ILogger<InvocaController> _logger;

        public InvocaController(IMediator mediator,
            ILogger<InvocaController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("queries/get-invoca-franchise-calls")]
        public async Task<ActionResult> GetInvocaFranchiseCalls([FromQuery] Guid? franchiseId,
                                                                [FromQuery] Guid franchiseSetId,
                                                                [FromQuery] DateTime startDate,
                                                                [FromQuery] DateTime endDate)
        {
            if (startDate > endDate)
                return BadRequest(nameof(startDate));

            var franchiseCalls = await _mediator.Send(
                new GetInvocaFranchiseCalls.Query
                {
                    FranchiseId = franchiseId,
                    FranchiseSetId = franchiseSetId,
                    StartDate = startDate,
                    EndDate = endDate
                });

            return Ok(franchiseCalls);
        }

        [HttpGet("queries/get-invoca-undispositioned-calls")]
        public async Task<ActionResult> GetInvocaUndispositionedCalls([FromQuery] Guid? franchiseId,
                                                                      [FromQuery] Guid franchiseSetId,
                                                                      [FromQuery] DateTime startDate,
                                                                      [FromQuery] DateTime endDate)
        {
            if (franchiseId == Guid.Empty)
                return BadRequest(nameof(franchiseId));

            if (startDate > endDate)
                return BadRequest(nameof(startDate));

            var undispositionedCalls = await _mediator.Send(
                new GetInvocaUndispositionedCalls.Query
                {
                    FranchiseId = franchiseId,
                    FranchiseSetId = franchiseSetId,
                    StartDate = startDate,
                    EndDate = endDate
                });

            return Ok(undispositionedCalls);
        }

        [HttpGet("queries/get-invoca-undispositioned-calls-count")]
        public async Task<ActionResult> GetInvocaUndispositionedCallsCount([FromQuery] Guid? franchiseId,
                                                                           [FromQuery] Guid franchiseSetId,
                                                                           [FromQuery] DateTime startDate,
                                                                           [FromQuery] DateTime endDate)
        {
            if (franchiseSetId == Guid.Empty)
                return BadRequest(nameof(franchiseId));

            if (startDate > endDate)
                return BadRequest(nameof(startDate));

            var undispositionedCallsCount = await _mediator.Send(
                new GetInvocaUndispositionedCallsCount.Query
                {
                    FranchiseId = franchiseId,
                    FranchiseSetId = franchiseSetId,
                    StartDate = startDate,
                    EndDate = endDate
                });

            return Ok(undispositionedCallsCount);
        }


        [HttpGet("queries/get-jobs-for-disposition")]
        public async Task<ActionResult> GetJobsForCallDisposition([FromQuery] Guid franchiseId,
                                                                  [FromQuery] string customerName = "",
                                                                  [FromQuery] string phoneNumber = "",
                                                                  [FromQuery] int take = 10,
                                                                  [FromQuery] int skip = 0)
        {
            var jobsForCallDisposition = await _mediator.Send(
                new GetJobsForCallDisposition.Query
                {
                    FranchiseId = franchiseId,
                    CustomerName = customerName,
                    PhoneNumber = phoneNumber,
                    Take = take,
                    Skip = skip
                });

            return Ok(jobsForCallDisposition);
        }


        [HttpPost("command/save-call-disposition")]
        public async Task<ActionResult> SaveCallDispositionn([FromBody] SaveCallDisposition.Command command)
        {
            if (!command.Calls.Any())
                return BadRequest(nameof(command.Calls));

            foreach (var updCall in command.Calls)
            {                
                if (updCall.CallId == Guid.Empty)
                {
                    return BadRequest(nameof(updCall.CallId));
                }
            }

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("command/save-exported-marketing-calls")]
        public async Task<ActionResult> SaveExportedMarketingCalls([FromBody] SaveExportedMarketingCalls.Command command)
        {
            if (command.Calls.Count == 0)
                return BadRequest(nameof(command.Calls));

            var response = await _mediator.Send(command);
            return Ok(response);
        }


        [HttpGet("queries/get-calls-for-export")]
        public async Task<ActionResult> GetCallForCsvExport([FromQuery] Guid? franchiseId,
                                                            [FromQuery] Guid? franchiseSetId,
                                                            [FromQuery] DateTime? startDate,
                                                            [FromQuery] DateTime? endDate)
        {
            var callForCsvExport = await _mediator.Send(
                new GetMarketingCallForCsvExport.Query
                {
                    FranchiseId = franchiseId,
                    FranchiseSetId = franchiseSetId,
                    StartDate = startDate,
                    EndDate = endDate
                });
            if (callForCsvExport == null)
                return Ok(new List<GetMarketingCallForCsvExport.Dto>());

            return Ok(callForCsvExport);
        }

        [HttpGet("queries/get-invoca-auto-match-data")]
        public async Task<ActionResult> GetInvocaAutoMatchData([FromQuery] Guid? franchiseId,
                                                                [FromQuery] Guid? franchiseSetId,
                                                                [FromQuery] DateTime? startDate,
                                                                [FromQuery] DateTime? endDate)
        {
            var franchiseCalls = await _mediator.Send(
                new GetInvocaAutoMatchData.Query
                {
                    FranchiseId = franchiseId,
                    FranchiseSetId = franchiseSetId,
                    StartDate = startDate,
                    EndDate = endDate
                });

            return Ok(franchiseCalls);
        }


        [HttpPost("command/save-auto-matched-calls")]
        public async Task<ActionResult> SaveAutoMatchedCalls([FromBody] SaveAutoMatchedCalls.Command command)
        {           
           var response = await _mediator.Send(command);
           return Ok(response);
        }

    }
}
