﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetJobMaterials
    {
        #region Query
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }
        #endregion Query

        #region Dto
        public class Dto
        {
            public Dto(
                Guid id,
                string name,
                string description,
                Guid materialReadingTypeId,
                int goal,
                Guid objectId)
            {
                Id = id;
                Name = name;
                Description = description;
                MaterialReadingTypeId = materialReadingTypeId;
                Goal = goal;
                ObjectId = objectId;
            }
            public Guid Id { get; }
            public string Name { get; }
            public string Description { get; }
            public Guid MaterialReadingTypeId { get; }
            public int Goal { get; }
            public Guid ObjectId { get; }
        }
        #endregion Dto

        #region Handler
        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;
            
            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobMaterials)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return job.JobMaterials
                    .Select(x => Map(x))
                    .ToList();
            }

            private Dto Map(JobMaterial material)
                => new Dto(material.Id,
                            material.Name,
                            material.OtherText,
                            material.MaterialReadingTypeId,
                            material.Goal,
                            material.ObjectId);
        }
        #endregion Handler
    }
}
