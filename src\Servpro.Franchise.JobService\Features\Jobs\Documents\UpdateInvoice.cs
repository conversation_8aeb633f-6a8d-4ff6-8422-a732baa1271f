﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Setup.FeatureFlags;
using Servpro.Franchise.JobService.Features.Jobs.Mapping;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class UpdateInvoice
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid MediaMetadataId { get; set; }
            public string InvoiceNumber { get; set; }
            public string Description { get; set; }
            public string Source { get; set; }
            public DateTime Date { get; set; }
            public double Amount { get; set; }
            public Guid Id { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.Id).NotEmpty();
            }
        }

        public class Dto
        {
            public bool UpdatedSuccessfully { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _db;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IFeatureFlagUtility _featureFlags;

            public Handler(JobDataContext db, IUserInfoAccessor userInfoAccessor, ISessionIdAccessor sessionIdAccessor, IFeatureFlagUtility featureFlags)
            {
                _db = db;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _featureFlags = featureFlags;
            }

            public async Task<Dto> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var existingInvoice = await _db.JobInvoices
                    .FirstOrDefaultAsync(q => q.Id == request.Id, cancellationToken: cancellationToken);

                var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == request.JobId,
                            cancellationToken: cancellationToken);
                job.TotalRevenue ??= 0;

                if (existingInvoice is null)
                {
                    var mediaMetadata =
                        await _db.MediaMetadata.FirstOrDefaultAsync(mm => mm.Id == request.MediaMetadataId,
                            cancellationToken: cancellationToken);

                    var newInvoice = new JobInvoice()
                    {
                        Id = request.Id,
                        InvoiceNumber = request.InvoiceNumber,
                        Description = request.Description,
                        Source = request.Source,
                        Date = request.Date,
                        Amount = request.Amount
                    };

                    mediaMetadata.JobInvoice = newInvoice;
                    mediaMetadata.JobInvoiceId = newInvoice.Id;
                    await RaiseCreatedEvent(request);
                }
                else
                {
                    job.TotalRevenue -= Convert.ToDecimal(existingInvoice.Amount);

                    existingInvoice.InvoiceNumber = request.InvoiceNumber;
                    existingInvoice.Description = request.Description;
                    existingInvoice.Source = request.Source;
                    existingInvoice.Date = request.Date;
                    existingInvoice.Amount = request.Amount;
                    await RaiseUpdatedEvent(request);
                }

                job.TotalRevenue += Convert.ToDecimal(request.Amount);
                await RaiseJobDetailsUpdatedEvent(job);

                var call = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => x.AssociatedJobId == job.Id, cancellationToken);
                if (call != null && call.RevenueInvoiced != job.TotalRevenue)
                {
                    if (call.RevenueInvoiced != null)
                        call.InvoiceAmountChanged = true;

                    call.InvoicedAmountLastUpdated = DateTime.UtcNow;
                    call.RevenueInvoiced = job.TotalRevenue;
                }

                await _db.SaveChangesAsync(cancellationToken);
                
                return new Dto
                {
                    UpdatedSuccessfully = true
                };
            }
            
            private async Task RaiseUpdatedEvent(Command request)
            {
                var user = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var eventDto = new InvoiceUpdatedEvent.InvoiceUpdatedDto()
                {
                    InvoiceId = request.Id,
                    InvoiceNumber = request.InvoiceNumber,
                    Description = request.Description,
                    Source = request.Source,
                    Amount = request.Amount,
                    Date = request.Date,
                    MediaMetadataId = request.MediaMetadataId,
                    JobId = request.JobId,
                    UpdatedBy = user.Username,
                    UpdatedUtc = DateTime.UtcNow
                };
                var newEvent = new InvoiceUpdatedEvent(eventDto, correlationId);
                await GenerateOutboxMessage(newEvent.ToJson(), nameof(InvoiceUpdatedEvent),
                    newEvent.CorrelationId, eventDto.UpdatedBy);
            }

            private async Task RaiseCreatedEvent(Command request)
            {
                var user = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var eventDto = new InvoiceCreatedEvent.InvoiceCreatedDto()
                {
                    InvoiceId = request.Id,
                    InvoiceNumber = request.InvoiceNumber,
                    Description = request.Description,
                    Source = request.Source,
                    Amount = request.Amount,
                    Date = request.Date,
                    MediaMetadataId = request.MediaMetadataId,
                    JobId = request.JobId,
                    CreatedBy = user.Username,
                    CreatedUtc = DateTime.UtcNow
                };
                var newEvent = new InvoiceCreatedEvent(eventDto, correlationId);
                await GenerateOutboxMessage(newEvent.ToJson(), nameof(InvoiceCreatedEvent),
                    newEvent.CorrelationId, eventDto.CreatedBy);
            }

            private async Task RaiseJobDetailsUpdatedEvent(Job job)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var eventDto = JobDetailsUpdatedMapper.MapJobDetailsUpdatedDto(job, true, userInfo);
                var newEvent = new JobDetailsUpdatedEvent(eventDto, correlationId);
                await GenerateOutboxMessage(newEvent.ToJson(), nameof(JobDetailsUpdatedEvent),
                    correlationId, userInfo.Username);
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _db.OutboxMessages.AddAsync(newEvent);
            }
        }
    }
}
