﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class WaterClasses
    {
        public static readonly Guid WaterClassEmpty = new Guid("00000000-0000-0000-0000-000000000000");
        public static readonly Guid WaterClassNotAssigned = new Guid("00000100-003B-5365-7276-70726F496E63");
        public static readonly Guid NoWaterDamage = new Guid("00000000-003B-5365-7276-70726F496E63");
        public static readonly Guid WaterClass1 = new Guid("00000001-003B-5365-7276-70726F496E63");
        public static readonly Guid WaterClass2 = new Guid("00000002-003B-5365-7276-70726F496E63");
        public static readonly Guid WaterClass3 = new Guid("00000003-003B-5365-7276-70726F496E63");
        public static readonly Guid WaterClass4 = new Guid("00000004-003B-5365-7276-70726F496E63");
    }
}
