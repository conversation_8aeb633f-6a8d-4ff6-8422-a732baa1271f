﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Common
{
    public static class DictionaryExtensions
    {
        public static TValue GetOrDefault<TKey, TValue>(
            this IDictionary<TKey, TValue> dict, TKey key)
            => dict.Contains<PERSON>ey(key)
            ? dict[key]
            : default;

        public static TValue GetOrDefault<TKey, TValue>(
            this IDictionary<TKey, TValue> dict, TKey key, TValue @default)
            => dict.ContainsKey(key)
            ? dict[key]
            : @default;

        public static void AddMany<TKey, TValue>(this IDictionary<TKey, TValue> target, IDictionary<TKey, TValue> source)
        {
            if (target == null) throw new ArgumentNullException("target cannot be null.");

            foreach (var keyValuePair in source)
            {
                target.Add(keyValuePair.Key, keyValuePair.Value);
            }
        }
    }
}
