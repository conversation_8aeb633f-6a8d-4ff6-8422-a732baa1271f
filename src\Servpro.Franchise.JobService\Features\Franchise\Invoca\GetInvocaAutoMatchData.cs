﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class GetInvocaAutoMatchData
    {
        public class Query : IRequest<Dto>
        {
            public Guid? FranchiseId { get; set; }
            public Guid? FranchiseSetId { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
        }

        public class Dto
        {
            public List<Call> Calls { get; set; }
            public List<Lead> Leads { get; set; }

            public Dto()
            {
                Calls = new List<Call>();
                Leads = new List<Lead>();
            }

        }

        public class Call
        {
            public Guid CallId { get; set; }
            public Guid FranchiseId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid? Disposition { get; set; }
            public DateTime CallReceivedDateTime { get; set; }
            public string Phone { get; set; }
        }

        public class Lead
        {
            public Guid JobId { get; set; }
            public Guid FranchiseId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public DateTime CreatedDate { get; set; }
            public string CallerPhone { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetInvocaAutoMatchData> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IConfiguration _config;
            private const string InvocaSearchDays = "Invoca:SearchDays";
            private static readonly ReadOnlyCollection<JobProgress> exceptJobprogress = new ReadOnlyCollection<JobProgress>(new[]
           {
                  JobProgress.Closed,
                  JobProgress.NotSoldCancelled,
                  JobProgress.OnHold
            });

            public Handler(IConfiguration config,
                ILogger<GetInvocaAutoMatchData> logger,
                IFranchiseServiceClient franchiseServiceClient,
                JobReadOnlyDataContext context)
            {
                _config = config;
                _logger = logger;
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{franchiseId}{franchiseSetId}", request.FranchiseId, request.FranchiseSetId);

                _logger.LogInformation("Getting Invoca Calls for Franchise: {franchise}", request.FranchiseId);
                var searchDays = _config.GetValue(InvocaSearchDays, 30);
                DateTime endDate = request.EndDate ?? DateTime.UtcNow;
                DateTime startDate = request.StartDate ?? endDate.AddDays(-(searchDays));


                var franchiseCallsQuery = _context.ExternalMarketingCalls
                     .Where(m =>
                         m.Disposition == Guid.Empty
                        && m.CallReceivedDateTime >= startDate
                        && m.CallReceivedDateTime <= endDate);


                if (request.FranchiseId.HasValue)
                {
                    franchiseCallsQuery = franchiseCallsQuery.Where(x=>x.FranchiseId == request.FranchiseId);
                }
                var franchiseCalls = await franchiseCallsQuery.ToListAsync(cancellationToken);

                var franchiseJobsQuery = _context.WipRecords
                    .Where(m => m.CreatedDate >= startDate
                       && m.CreatedDate <= endDate
                       && !exceptJobprogress.Contains(m.JobProgress)
                       && (m.CallDisposition == null || m.CallDisposition == Guid.Empty))
                    //Using this to prevent pulling back all wip record fields
                    // This projection should help limit out of memory exceptions
                    .Select(x => new Lead { CreatedDate = x.CreatedDate, 
                                            FranchiseId = x.FranchiseId, 
                                            FranchiseSetId = x.FranchiseSetId, 
                                            CallerPhone = x.CallerPhoneNumbers, 
                                            JobId = x.Id 
                                           });

                if (request.FranchiseId.HasValue)
                {
                    franchiseJobsQuery = franchiseJobsQuery.Where(x => x.FranchiseId == request.FranchiseId);
                }
                var franchiseJobs = await franchiseJobsQuery.ToListAsync(cancellationToken);


                return Map(franchiseCalls, franchiseJobs);

            }
            private Dto Map(List<ExternalMarketingCall> calls, List<Lead> jobs)
            {
                var dto = new Dto();

                foreach (var call in calls)
                {
                    var dtoCalls = new Call()
                    {
                        FranchiseId = call.FranchiseId,
                        Disposition = call.Disposition,
                        CallReceivedDateTime = call.CallReceivedDateTime,
                        FranchiseSetId = call.FranchiseSetId,
                        CallId = call.Id,
                        Phone = call.CallerPhoneNumber
                    };
                    dto.Calls.Add(dtoCalls);
                }

                dto.Leads.AddRange(jobs);
                

                return dto;
            }
        }
    }
}
