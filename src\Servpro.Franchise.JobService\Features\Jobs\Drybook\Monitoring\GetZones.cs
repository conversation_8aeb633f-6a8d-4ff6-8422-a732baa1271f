﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetZones
    {
        #region Query
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }
        #endregion

        #region Dto
        public class Dto
        {
            public Dto(
                Guid id,
                string name)
            {
                Id = id;
                Name = name;
            }

            public Guid Id { get; }
            public string Name { get; }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;
            public readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
            }


            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.Zones)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var zones =  job.Zones.Where(x => x.ZoneTypeId == ZoneTypes.Drying && !x.IsDeleted);

                return zones.Select(Map);
            }

            private Dto Map(Zone zone)
                => new Dto(
                    zone.Id,
                    zone.Name);
        }
        #endregion
    }
}
