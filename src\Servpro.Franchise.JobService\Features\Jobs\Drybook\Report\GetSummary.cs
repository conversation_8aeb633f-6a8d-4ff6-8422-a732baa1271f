﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities.Helpers;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetSummary
    {
        public class Query : IRequest<SummaryDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }

        public class Handler : IRequestHandler<Query, SummaryDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;

            public Handler(ILookupServiceClient lookupServiceClient,
                IFranchiseServiceClient franchiseServiceClient, ILogger<GenerateDryingReport> logger, JobReadOnlyDataContext context)
            {
                _lookupServiceClient = lookupServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
                _logger = logger;
                _context = context;
            }

            public decimal CalculateTotalSquareFeet(Job job)
            {
                _logger.LogDebug("DryingReportLog - calculating TotalSqFeet for jobId: {Id}", job.Id);

                var affectedFloorsSqFt = job.JobAreas.Where(ja => ja.Room != null)
                    .Select(ja => ja.Room.RoomFlooringTypesAffected.Sum(rfta => rfta.AffectedSquareFeet ?? 0.0m))
                    .Sum();

                var affectedCeilingSqFt = job.JobAreas.Where(ja => ja.Room != null)
                     .Select(ja => CalculatorHelper.SquareInchesToSquareFeet(ja.Room.AffectedCeilingAreaSquareInches)).Sum();

                var affectedWallSqFt = job.JobAreas.Where(ja => ja.Room != null).Select(ja =>
                {
                    return CalculatorHelper.SquareInchesToSquareFeet(ja.Room.AffectedWallAreaSquareInches);
                }).Sum();

                return affectedFloorsSqFt + affectedCeilingSqFt + affectedWallSqFt;
            }

            public async Task<SummaryDto> Handle(Query request, CancellationToken cancellationToken)
            {
                var model = new SummaryDto();
                var jobId = request.Job.Id;
                var job = await _context.Jobs
                                    .Include(job => job.Zones)
                                    .Include(job => job.JobTriStateAnswers)
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.Job.Id}");

                job.JobAreas = await _context.JobAreas
                                             .Include(x => x.Room)
                                             .Include(x => x.EquipmentPlacements)
                                             .Where(x => x.JobId == jobId)
                                             .AsNoTracking()
                                             .ToListAsync(cancellationToken);

                job.JobContacts = await _context.JobContactMap
                                                .Include(c => c.Contact)
                                                .Where(x => x.JobId == jobId)
                                                .AsNoTracking()
                                                .ToListAsync(cancellationToken);

                job.Zones = job.Zones.Where(x => !x.IsDeleted).ToList();


                _logger.LogInformation("DryingReportLog - starting GetSummary for jobId: {Id}", job.Id);
                await AddAffectedFlooring(job, cancellationToken);
                model.ClaimPoNumber = job.InsuranceClaimNumber ?? string.Empty;
                model.ProjectNumber = !string.IsNullOrEmpty(job.ProjectNumber) ? job.ProjectNumber : string.Empty;
                model.JobAddress1 = job.LossAddress == null ? string.Empty : job.LossAddress.Address1;
                model.JobAddress2 = job.LossAddress == null ? string.Empty : job.LossAddress.Address2;
                model.CityStateZip = job.LossAddress == null ? string.Empty : $"{job.LossAddress.City}, {job.LossAddress.State.StateAbbreviation} {job.LossAddress.PostalCode}";

                var insuranceClient = await _context.InsuranceClients.FirstOrDefaultAsync(ic => ic.Id == job.InsuranceCarrierId, cancellationToken);
                model.InsuranceClient = insuranceClient?.Name ?? string.Empty;

                var policyExists = !string.IsNullOrWhiteSpace(job.InsurancePolicyNumber);
                var woNumberExists = !string.IsNullOrWhiteSpace(job.WorkOrderNumber);
                var policyWoNumber = (policyExists ? job.InsurancePolicyNumber : string.Empty)
                                     + (policyExists && woNumberExists ? " / " : string.Empty)
                                     + (woNumberExists ? job.WorkOrderNumber : string.Empty);
                model.PolicyWoNumber = policyWoNumber;

                var customer = !job.CustomerId.IsNullOrEmpty()
                    ? job.JobContacts.FirstOrDefault(jc => jc.ContactId == job.CustomerId)?.Contact
                    : job.JobContacts.FirstOrDefault(jc => jc.JobContactTypeId == JobContactTypes.Customer)?.Contact;

                if (customer != null)
                {
                    model.CustomerName = $"{customer.FirstName ?? string.Empty} {customer.LastName}";
                    var customerPhoneNumber = customer.PhoneNumbers?.OrderBy(x => x.PhoneExtension).FirstOrDefault();
                    model.CustomerPhone = SummaryHelper.GetPhoneNoStringWithExtension(customerPhoneNumber);
                    model.CustomerEmail = customer.EmailAddress;
                }

                model.FranchiseSetTimeZone = await DryingReportHelper.GetTimeZoneString(request.FranchiseTimeZone);

                _logger.LogInformation("DryingReport - GetSummary Log for jobId: {Id} with JobDates: {@jobDates}", job.Id, job.JobDates);

                var dateReceived = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.ReceivedDate);
                model.LossReceived = dateReceived?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);

                var dateOfLoss = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.LossOccurred);
                model.DateOfLoss = dateOfLoss?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);

                var dryingComplete = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.DryingComplete);
                model.DryingComplete = dryingComplete?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);

                var customerCalled = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.CustomerCalled);
                model.CustomerCalled = customerCalled?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);

                var arrivalOnSite = job.JobDates.FirstOrDefault(jd => jd.JobDateTypeId == JobDateTypes.InitialOnSiteArrival);
                model.ArrivalOnSite = arrivalOnSite?.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone);

                _logger.LogDebug("DryingReportLog - getting lookups in GetSummary for jobId: {Id}", job.Id);
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                model.TypeOfLoss = lookups.LossTypes.FirstOrDefault(x => x.Id == new Guid(job.LossTypeId.ToString()))?.Name ?? "";

                _logger.LogDebug("DryingReportLog - Summary LookUps: {lookups}", lookups);

                // This is very intentional because some lame brain apparently got the labels for 
                // StructureType and PropertyType backward years ago so now we're stuck with this backwardness.
                model.StructureType = lookups.StructureTypes.FirstOrDefault(x => x.Id == job.StructureTypeId)?.Name ?? "";
                model.PropertyType = lookups.PropertyTypes.FirstOrDefault(x => x.Id == job.PropertyTypeId)?.Name ?? "";
                model.CountOfRoomsAffected = job.RoomsAffected;
                var electricityAvailableAnswer = job.JobTriStateAnswers.FirstOrDefault(a => a.JobTriStateQuestionId == JobTriStateQuestions.IsElectricityAvailable);
                model.ElectricityAvailable = electricityAvailableAnswer != null && electricityAvailableAnswer.Answer.HasValue ? (electricityAvailableAnswer.Answer.Value ? "Yes" : "No") : "Unknown";

                model.YearStructureBuilt = job.YearStructureBuilt;

                model.CauseOfLoss = lookups.CausesOfLoss.FirstOrDefault(x => x.LossTypeId == job.LossTypeId && x.CauseOfLossId == job.CauseOfLossId)?.Name ?? "";
                model.CountOfFloorsAffected = job.FlooringTypesAffected.Count;

                var franchiseId = job.FranchiseId;
                var franchise = await _franchiseServiceClient.GetFranchiseAsync(franchiseId, cancellationToken);

                if (franchise != null)
                {
                    model.FranchiseName = franchise.Name;
                    var fAddr = franchise.PrimaryAddress;
                    if (fAddr != null)
                    {
                        model.FranchiseAddress1 = fAddr == null ? string.Empty : fAddr.Address1;
                        model.FranchiseAddress2 = fAddr == null ? string.Empty : fAddr.Address2;
                        model.FranchiseCityStateZip = $"{fAddr.City}, {fAddr.State} {fAddr.PostalCode}";

                        var franchisePhoneNumber = franchise.PrimaryPhone;

                        var formattedPhoneNumber = string.Empty;
                        if (franchisePhoneNumber != null && !string.IsNullOrEmpty(franchisePhoneNumber.Number))
                        {
                            formattedPhoneNumber =
                            $"{Convert.ToInt64(string.Join(string.Empty, franchisePhoneNumber.Number.Where(char.IsDigit).Take(10))):(###) ###-####}";
                        }
                        model.FranchisePhone = formattedPhoneNumber;
                    }
                }

                model.CategoryOfWater = (job.Zones.Max(zone => lookups.WaterCategories.FirstOrDefault(wc => wc.Id == zone.WaterCategoryId)?.Ordinal)) ?? 0;

                model.ClassOfWater = job.Zones.Max(zone => lookups.WaterClasses.FirstOrDefault(waterClass => waterClass.Id == zone.WaterClassId)?.Severity) ?? 0;

                // Days to Achieve Dry Standard:  // date last piece removed minus date 1st piece placed
                var lastEquipmentRemoved = job.JobAreas.Max(ja =>
                    ja.EquipmentPlacements.Any<EquipmentPlacement>() && ja.EquipmentPlacements.All<EquipmentPlacement>(ep => ep.EndDate.HasValue)
                        ? ja.EquipmentPlacements.Max<EquipmentPlacement, DateTime?>(ep => ep.EndDate)
                        : (DateTime?)null);
                var firstEquipmentPlaced = job.JobAreas.Min(ja => ja.EquipmentPlacements.Any<EquipmentPlacement>() ? ja.EquipmentPlacements.Min<EquipmentPlacement, DateTime>(ep => ep.BeginDate) : (DateTime?)null);
                var daysToAchieveDryStandardTimeSpan = (lastEquipmentRemoved - firstEquipmentPlaced).GetValueOrDefault(TimeSpan.MaxValue);
                if (daysToAchieveDryStandardTimeSpan != TimeSpan.MaxValue)
                {
                    model.DaysToAchieveDryStandard = decimal.Round(Convert.ToDecimal(daysToAchieveDryStandardTimeSpan.TotalDays), 2);
                }

                model.RoomsAffected = job.RoomsAffected;
                model.DryingZones = job.Zones.Count(z => z.ZoneTypeId == ZoneTypes.Drying);
                model.TotalSquareFeet = CalculateTotalSquareFeet(job);
                _logger.LogInformation("DryingReportLog - GetSummary - completed for jobId: {Id}", job.Id);

                return model;
            }

            private async System.Threading.Tasks.Task AddAffectedFlooring(Job job, CancellationToken cancellationToken)
            {
                //get all room Ids 
                var jobAreasWithRoom = job.JobAreas.Where(ja => ja.RoomId != null);
                var roomsIds = jobAreasWithRoom.Select(x => x.RoomId);

                //get all the references to the room ids
                var affectedFloorings = await _context.RoomFlooringTypesAffected.Where(rfa => roomsIds.Contains(rfa.RoomId)).ToListAsync(cancellationToken);
                //grouping all results by room Ids
                var affectedFlooringByRoomId = from af in affectedFloorings
                                               group af by af.RoomId;
                //assigning grouped results to the correct Room
                foreach (var jobArea in jobAreasWithRoom)
                {
                    if (affectedFlooringByRoomId.Any(x => x.Key == jobArea.RoomId.Value))
                    {
                        jobArea.Room.RoomFlooringTypesAffected = affectedFlooringByRoomId.FirstOrDefault(x => x.Key == jobArea.RoomId.Value).ToList();
                    }
                }
            }
        }
    }
}