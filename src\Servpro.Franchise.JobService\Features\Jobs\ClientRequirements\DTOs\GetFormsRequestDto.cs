﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class GetFormsRequestDto
    {
        public Guid JobId { get; set; }
        public int InsuranceClientId { get; set; }
        public string LossType { get; set; }
        public string StructureType { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string FranchiseType { get; set; }
        public string JobSource { get; set; }
        public string EstimateType { get; set; }
        public bool IsStormJob { get; set; }
        public bool IsStreamlined { get; set; }
        public List<JobFormDto> JobForms { get; set; } = new List<JobFormDto>();

        public class JobFormDto
        {
            public Guid Id { get; set; }
            public Guid FormTemplateId { get; set; }
        }
    }
}