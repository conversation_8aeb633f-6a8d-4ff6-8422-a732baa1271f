﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobAreaMaterialReadingsDeletedEvent;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobAreaMaterialUpdatedEvent;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobMaterialCreatedEvent;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.JobMaterialUpdatedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class SaveJobMaterial
    {
        #region Command
        public class Command : IRequest<Guid>
        {
            public Guid? Id { get; set; }
            public Guid JobId { get; set; }
            public string Name { get; set; }
            public int Goal { get; set; }
            public Guid ObjectId { get; set; }
            public Guid MaterialReadingTypeId { get; set; }
        }
        #endregion

        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.Name).NotEmpty();
                RuleFor(x => x.ObjectId).NotEmpty();
                RuleFor(x => x.MaterialReadingTypeId).NotEmpty();
            }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Command, Guid>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Guid> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobMaterials)
                        .ThenInclude(y => y.JobAreaMaterials)
                            .ThenInclude(z => z.JobAreaMaterialReadings)
                                .ThenInclude(w => w.JobVisit)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var jobMaterial = job.JobMaterials.FirstOrDefault(x => x.Id == request.Id);

                if (jobMaterial == null)
                {
                    jobMaterial = Map(request, userInfo.Username);
                    job.JobMaterials.Add(jobMaterial);
                }
                else 
                {
                    jobMaterial.ModifiedBy = userInfo.Username;
                    jobMaterial.ModifiedDate = DateTime.UtcNow;
                    jobMaterial.Goal = request.Goal;
                    jobMaterial.ObjectId = request.ObjectId;
                    jobMaterial.MaterialReadingTypeId = request.MaterialReadingTypeId;

                    foreach (var jobAreaMaterial in jobMaterial.JobAreaMaterials) 
                    {
                        bool goalMet = false;
                        var readingsByDate = jobAreaMaterial.JobAreaMaterialReadings.OrderBy(x => x.JobVisit.Date);
                        foreach (var reading in readingsByDate) 
                        {
                            if (!goalMet && MaterialReadingGoalMet(request.MaterialReadingTypeId, request.Goal, reading.Value))
                            {
                                goalMet = true;
                                jobAreaMaterial.GoalMetOnJobVisitId = reading.JobVisitId;
                            }
                            else if (!goalMet && jobAreaMaterial.GoalMetOnJobVisitId == reading.JobVisitId)
                            {
                                jobAreaMaterial.GoalMetOnJobVisitId = null;
                            }
                            else if (goalMet)
                            {
                                jobAreaMaterial.JobAreaMaterialReadings.Remove(reading);
                            }
                        }
                    }
                }

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                IEnumerable<OutboxMessage> events = GenerateEvents(job, job.FranchiseSetId, correlationId, userInfo);
                _context.OutboxMessages.AddRange(events);

                await _context.SaveChangesAsync(cancellationToken);

                return jobMaterial.Id;
            }

            private JobMaterial Map(Command request, string username) =>
                new JobMaterial
                {
                    Id = Guid.NewGuid(),
                    CreatedBy = username,
                    CreatedDate = DateTime.UtcNow,
                    JobId = request.JobId,
                    Name = request.Name,
                    Goal = request.Goal,
                    ObjectId = request.ObjectId,
                    MaterialReadingTypeId = request.MaterialReadingTypeId,
                    MeterTypeId = MeterTypes.NonPenetrating
                };

            private bool MaterialReadingGoalMet(Guid materialReadingType, int goal, int? value = 0)
            {
                var readingGoal = (double) goal;
                if (materialReadingType == MaterialReadingTypes.Percentage)
                {
                    readingGoal *= 1.04;
                }
                return value <= readingGoal;
            }

            private IEnumerable<OutboxMessage> GenerateEvents(Job job, Guid franchiseSetId, Guid correlationId, UserInfo userInfo)
            {
                var jobMaterialCreated = GenerateJobMaterialCreatedEvents(correlationId, userInfo);
                var jobMaterialUpdated = GenerateJobMaterialUpdatedEvents(correlationId, userInfo);
                var jobAreaMaterialUpdated = GenerateJobAreaMaterialUpdatedEvents(correlationId, userInfo);
                var jobAreaMAterialReadingDeleted = GenerateJobAreaMaterialReadingsDeletedEvents(job.Id, correlationId, userInfo);

                return jobMaterialCreated
                    .Concat(jobMaterialUpdated)
                    .Concat(jobAreaMaterialUpdated)
                    .Concat(jobAreaMAterialReadingDeleted)                  
                    .ToList();
            }

            private IEnumerable<OutboxMessage> GenerateJobMaterialCreatedEvents(
             Guid correlationId,
             UserInfo userInfo)
            {
                var jobMaterialAdded = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobMaterial
                        && x.State == EntityState.Added)
                    .Select(x => x.Entity as JobMaterial);
                var events = jobMaterialAdded
                    .Select(x => new JobMaterialCreatedEvent(MapJobMaterialCreated(x), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JobMaterialCreatedEvent), correlationId, userInfo.Username));
                return events;
            }

            private IEnumerable<OutboxMessage> GenerateJobMaterialUpdatedEvents(
             Guid correlationId,
             UserInfo userInfo)
            {
                var jobMaterialUpdated = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobMaterial
                        && x.State == EntityState.Modified)
                    .Select(x => x.Entity as JobMaterial);
                var events = jobMaterialUpdated
                    .Select(x => new JobMaterialUpdatedEvent(MapJobMaterialUpdated(x, userInfo.Username), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JobMaterialUpdatedEvent), correlationId, userInfo.Username));
                return events;
            }

            private IEnumerable<OutboxMessage> GenerateJobAreaMaterialUpdatedEvents(
            Guid correlationId,
            UserInfo userInfo)
            {
                var jobMaterialUpdated = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobAreaMaterial
                        && x.State == EntityState.Modified)
                    .Select(x => x.Entity as JobAreaMaterial);
                var events = jobMaterialUpdated
                    .Select(x => new JobAreaMaterialUpdatedEvent(MapJobAreaMaterialUpdated(x), correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JobAreaMaterialUpdatedEvent), correlationId, userInfo.Username));
                return events;
            }

            private IEnumerable<OutboxMessage> GenerateJobAreaMaterialReadingsDeletedEvents(
            Guid jobId,
            Guid correlationId,
            UserInfo userInfo)
            {
                var jobMaterialReadingDeleted = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JobAreaMaterialReading
                        && x.State == EntityState.Deleted)
                    .Select(x => x.Entity as JobAreaMaterialReading);

                if (jobMaterialReadingDeleted.Any())
                {
                    var @event = new JobAreaMaterialReadingsDeletedEvent(MapJobAreaMaterialReadingsDeleted(jobId, jobMaterialReadingDeleted.ToList()), correlationId);
                    yield return new OutboxMessage(@event.ToJson(), nameof(JobAreaMaterialReadingsDeletedEvent), correlationId, userInfo.Username);
                }
            }

            private JobMaterialCreatedDto MapJobMaterialCreated(JobMaterial jobMaterial) =>
                new JobMaterialCreatedDto
                {
                    JobId = jobMaterial.JobId,
                    MaterialReadingTypeId = jobMaterial.MaterialReadingTypeId,
                    MeterTypeId = jobMaterial.MeterTypeId,
                    Goal = jobMaterial.Goal,
                    ObjectId = jobMaterial.ObjectId,
                    OtherText = jobMaterial.OtherText,
                    Name = jobMaterial.Name
                };

            private JobMaterialUpdatedDto MapJobMaterialUpdated(JobMaterial jobMaterial, string updatedBy) =>
               new JobMaterialUpdatedDto
               {
                   JobId = jobMaterial.JobId,
                   Goal = jobMaterial.Goal,
                   ObjectId = jobMaterial.ObjectId,
                   UpdatedBy = updatedBy
               };

            private JobAreaMaterialUpdatedDto MapJobAreaMaterialUpdated(JobAreaMaterial jobAreaMaterial) =>
               new JobAreaMaterialUpdatedDto
               {
                   Id = jobAreaMaterial.Id,
                   GoalMetOnJobVisitId = jobAreaMaterial.GoalMetOnJobVisitId ?? Guid.Empty,
                   Readings = jobAreaMaterial.JobAreaMaterialReadings.Select(MapJobAreaReadings)
               };

            private Readings MapJobAreaReadings(JobAreaMaterialReading reading) =>
                new Readings
                {
                    Id = reading.Id,
                    JobVisitId = reading.JobVisitId,
                    MaterialReadingTypeId = reading.MaterialReadingTypeId,
                    Value = reading.Value
                };

            private JobAreaMaterialReadingsDeletedDto MapJobAreaMaterialReadingsDeleted(Guid jobId, List<JobAreaMaterialReading> jobAreaMaterialReading) =>
                new JobAreaMaterialReadingsDeletedDto
                {
                    JobId = jobId,
                    JobAreaMaterialReadingIds = jobAreaMaterialReading.Select(s=> s.Id)
                };

        }
        #endregion
    }
}
