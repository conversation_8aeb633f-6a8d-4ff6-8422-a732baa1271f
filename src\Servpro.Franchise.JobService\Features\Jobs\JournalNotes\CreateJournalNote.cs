﻿using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Exceptions;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes.JournalNoteCreatedEvent;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class CreateJournalNote
    {
        public class Command : IRequest<JournalNote>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Dto JournalNote { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.FranchiseSetId).NotEmpty();

                RuleFor(m => m.JournalNote)
                    .NotNull()
                    .WithMessage("JournalNote is required.");

                When(m => m.JournalNote != null, () =>
                {
                    RuleFor(m => m.JournalNote.TypeId).NotEmpty();
                    RuleFor(m => m.JournalNote.VisibilityId).NotEmpty();

                    RuleFor(m => m.JournalNote.Subject)
                        .NotNull()
                        .NotEmpty()
                        .Length(2, 100);

                    RuleFor(m => m.JournalNote.Message)
                        .NotNull()
                        .NotEmpty();
                });
            }
        }


        public class Dto
        {
            public Guid JobId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Message { get; set; }
            public Guid? CategoryId { get; set; }
            public DateTime? ActionDate { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobVisitId { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
            public Guid? ZoneId { get; set; }
            public bool? IncludeInSummaryCoverPage { get; set; }
            public List<int> RuleIds { get; set; }
            public List<BreRuleDto> Rules { get; set; } = new List<BreRuleDto>();
        }

        public class BreRuleDto
        {
            public Guid Id { get; set; }
            public int RuleId { get; set; }
            public Guid? JobAreaId { get; set; }
            public Guid? JobVisitId { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
            public Guid? ZoneId { get; set; }
            public string MaterialType { get; set; }
        }

        public class Handler : IRequestHandler<Command, JournalNote>
        {
            private readonly JobDataContext _db;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db, ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfoAccessor, ILogger<Handler> logger)
            {
                _db = db;
                _sessionIdAccessor = sessionIdAccessor;
                _userInfoAccessor = userInfoAccessor;
                _logger = logger;
            }

            public async Task<JournalNote> Handle(Command request,
                CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler request: {request}", request);

                var user = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                _logger.LogInformation("Starting CreateJournalNote handler. JobId: {JobId}, FranchiseSetId: {FranchiseSetId}, User: {Username}",
                    request.JobId, request.FranchiseSetId, user?.Username);

                var job = await GetJob(request.JobId, cancellationToken);

                var journalNote = Map(request.JournalNote);
                var journalNoteCreatedEvent = GenerateJournalNoteCreatedEvent(journalNote, correlationId, user);

                await _db.JournalNote.AddAsync(journalNote, cancellationToken);
                await _db.OutboxMessages.AddAsync(journalNoteCreatedEvent, cancellationToken);

                //Add first notice activity
                await _db.FirstNoticeActivity.AddAsync(MapFnActivity(request.JobId, user), cancellationToken);

                var projectKeyFieldUpdatedEvent = GenerateProjectKeyFieldsUpdatedEvent(job, user, correlationId);
                await _db.OutboxMessages.AddAsync(projectKeyFieldUpdatedEvent, cancellationToken);

                _logger.LogDebug("Saving JournalNote to DB: {@JournalNote}", journalNote);
                _logger.LogDebug("Saving JournalNoteCreatedEvent: {@Event}", journalNoteCreatedEvent);

                var res = await _db.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("SaveChanges result: {Result}", res);

                return journalNote;
            }

            private async Task<Job> GetJob(Guid jobId, CancellationToken ct)
            {
                var job = await _db.Jobs
                    .AsNoTracking()
                    .Include(j => j.Customer).ThenInclude(c => c.Business)
                    .FirstOrDefaultAsync(j => j.Id == jobId, ct);

                if (job == null)
                {
                    _logger.LogWarning("Job not found. JobId: {JobId}", jobId);
                    throw new HttpResponseException(StatusCodes.Status404NotFound, $"Job with ID {jobId} not found.");
                }

                _logger.LogInformation("Loaded Job from DB. JobId: {JobId}, FranchiseId: {FranchiseId}", job.Id, job.FranchiseId);
                return job;
            }

            private JournalNote Map(Dto journalNote)
                => new JournalNote
                {
                    JobId = journalNote.JobId,
                    CreatedDate = DateTime.UtcNow,
                    Author = journalNote.Author,
                    Note = journalNote.Message,
                    ActionDate = journalNote.ActionDate ?? DateTime.UtcNow,
                    CategoryId = journalNote.CategoryId ?? JournalNoteCategories.Notes,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    JobVisitId = journalNote.JobVisitId.ValueOrNullIfEmpty(),
                    ZoneId = journalNote.ZoneId.ValueOrNullIfEmpty(),
                    JobAreaId = journalNote.JobAreaId.ValueOrNullIfEmpty(),
                    JobAreaMaterialId = journalNote.JobAreaMaterialId.ValueOrNullIfEmpty(),
                    IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage ?? true,
                    RuleIds = journalNote.RuleIds,
                    Rules = GetJournalNoteRule(journalNote.Rules)
                };

            private FirstNoticeActivity MapFnActivity(Guid jobId, UserInfo user)
            {
                return new FirstNoticeActivity
                {
                    ActivityTypeId = ActivityType.AddPriorityResponseJournal,
                    RevisedValue = "new note added",
                    ModifiedBy = user.Username,
                    CreatedBy = user.Username,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    RecordSourceId = LookupService.Constants.RecordSourceTypes.WorkCenter,
                    JobId = jobId,
                    Id = Guid.NewGuid()
                };
            }

            private JournalNoteDto MapJournalNoteDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id,
                    RuleIds = journalNote.RuleIds?.ToList()
                };

            private ProjectKeyFieldsUpdatedEvent.ProjectKeyFieldsUpdatedDto MapKeyFieldsUpdatedDto(Job job, UserInfo user)
            {
                return new ProjectKeyFieldsUpdatedEvent.ProjectKeyFieldsUpdatedDto
                {
                    FranchiseId = job.FranchiseId,
                    FranchiseSetId = job.FranchiseSetId,
                    JobId = job.Id,
                    JobProgress =
                        (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)job.JobProgress,
                    MentorId = job.MentorId,
                    ProjectNumber = job.ProjectNumber,
                    PropertyTypeId = job.PropertyTypeId,
                    UserId = user.Id,
                    Username = user.Username,
                    BusinessName = job.BusinessName,
                    LossTypeId = job.LossTypeId,
                    Customer = new ProjectKeyFieldsUpdatedEvent.ContactDto
                    {
                        FirstName = job.Customer?.FirstName,
                        LastName = job.Customer?.LastName
                    }
                };
            }

            private List<JournalNote.BreRule> GetJournalNoteRule(IEnumerable<BreRuleDto> rules)
            {
                return rules?.Select(r => new JournalNote.BreRule()
                {
                    Id = Guid.NewGuid(),
                    RuleId = r.RuleId,
                    JobAreaId = r.JobAreaId,
                    JobAreaMaterialId = r.JobAreaMaterialId,
                    JobVisitId = r.JobVisitId,
                    ZoneId = r.ZoneId,
                    MaterialType = r.MaterialType
                }).ToList() ?? new List<JournalNote.BreRule>();
            }

            private OutboxMessage GenerateJournalNoteCreatedEvent(JournalNote journalNote, Guid correlationId, UserInfo userInfo)
            {
                var journalNoteCreatedDto = MapJournalNoteDto(journalNote, userInfo);
                var journalNoteCreatedEvent = new JournalNoteCreatedEvent(journalNoteCreatedDto, correlationId);
                return new OutboxMessage(journalNoteCreatedEvent.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username);
            }

            private OutboxMessage GenerateProjectKeyFieldsUpdatedEvent(Job job, UserInfo user, Guid correlationId)
            {
                var eventDto = MapKeyFieldsUpdatedDto(job, user);

                _logger.LogInformation("ProjectKeyFieldsUpdatedDto: {@Dto}", eventDto);

                var @event = new ProjectKeyFieldsUpdatedEvent(eventDto, correlationId);
                var outboxMessage = new OutboxMessage(@event.ToJson(), nameof(ProjectKeyFieldsUpdatedEvent),
                    correlationId, user.Username);

                _logger.LogInformation("Generated event {eventName}: {@payload}", nameof(ProjectKeyFieldsUpdatedEvent), @event);

                return outboxMessage;
            }
        }
    }
}