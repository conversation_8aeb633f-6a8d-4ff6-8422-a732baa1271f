﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetRoomsByZone
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
            public bool IncludeDeleted { get; set; }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public Guid JobAreaId { get; set; }
            public Guid? ZoneId { get; set; }
            public string ZoneName { get; set; }
            public Guid? RoomId { get; set; }
            public Guid? RoomTypeId { get; set; }
            public Guid RoomShapeId { get; set; }
            public string RoomName { get; set; }
            public int RoomOrder { get; set; }
            public int Length1TotalInches { get; set; }
            public int Length2TotalInches { get; set; }
            public int Height1TotalInches { get; set; }
            public int Height2TotalInches { get; set; }
            public int Height3TotalInches { get; set; }
            public int Width1TotalInches { get; set; }
            public int Width2TotalInches { get; set; }
            public decimal AffectedCeilingAreaSquareFeet { get; set; }
            public decimal AffectedWallAreaSquareFeet { get; set; }
            public decimal WallAreaSquareFeet { get; set; }
            public decimal CeilingAreaSquareFeet { get; set; }
            public Guid WaterCategoryId { get; set; }
            public Guid WaterClassId { get; set; }
            public decimal TotalSquareFeet { get; set; }
            public DateTime VisitDate { get; set; }
            public ICollection<LineItemDto> LineItems { get; set; }
            public ICollection<RoomFlooringTypeAffectedDto> FlooringTypesAffected { get; set; }
        }

        public class LineItemDto
        {
            public Guid Id { get; set; }
            public Guid? LineItemId { get; set; }
            public string Category { get; set; }
            public string Code { get; set; }
            public string Description { get; set; }
            public string Info { get; set; }
            public string UnitOfMeasure { get; set; }
            public decimal Quantity { get; set; }
            public Guid? JobVisitId { get; set; }
            public DateTime? DeletedDate { get; set; }
            public string DeletedBy { get; set; }
            public Guid? RoomId { get; internal set; }
            public decimal Percentage { get; set; }
            public string ActivityCode { get; set; }
            public DateTime CreatedDate { get; set; }
            public string CreatedBy { get; set; }
            public Guid? XactUploadTransactionId { get; set; }
            public List<LineItemNoteDto> Notes { get; set; }
        }

        public class RoomFlooringTypeAffectedDto
        {
            public Guid FlooringTypeId { get; set; }
            public decimal? TotalSquareFeet { get; set; }
            public decimal? AffectedSquareFeet { get; set; }
            public decimal? AffectedPercentage { get; set; }
        }

        public class LineItemNoteDto
        {
            public Guid Id { get; set; }
            public string Note { get; set; }
            public bool IsEditable { get; set; }
            public DateTime CreatedDate { get; set; }
            public string CreatedBy { get; set; }
            public bool IsDeleted { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                         .Include(j => j.Zones)
                   .Include(j => j.LineItems).ThenInclude(x => x.Notes)
                   .AsNoTracking()
                   .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);


                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.JobAreas = await _context.JobAreas
                       .Include(x => x.Room)
                            .ThenInclude(y => y.JobVisit)
                       .Include(x => x.Room)
                            .ThenInclude(y => y.RoomFlooringTypesAffected)
                       .Include(x => x.Zone)
                       .AsNoTracking()
                       .Where(j => j.JobId == request.JobId)
                       .ToListAsync(cancellationToken);


                return job.JobAreas.Where(ja => ja.Room != null && ja.ZoneId.HasValue && ja.ZoneId.Value == request.ZoneId).Select(ja => new Dto
                {
                    RoomId = ja.RoomId,
                    JobAreaId = ja.Id,
                    ZoneId = ja.ZoneId,
                    //ZoneName = ja.Zone.Name,
                    JobId = ja.JobId,
                    //TODO: edit the save room because the JobVisit needs to be saved there
                    //VisitDate = ja.Room.JobVisit.Date,
                    RoomName = ja.Name,
                    RoomOrder = ja.Room.RoomOrder,
                    RoomShapeId = ja.Room.RoomShapeId,
                    RoomTypeId = ja.Room.RoomTypeId,
                    WaterClassId = ja.Zone.WaterClassId,
                    WaterCategoryId = ja.Zone.WaterCategoryId,
                    Height1TotalInches = ja.Room.Height1TotalInches,
                    Height2TotalInches = ja.Room.Height2TotalInches,
                    Height3TotalInches = ja.Room.Height3TotalInches,
                    Length1TotalInches = ja.Room.Length1TotalInches,
                    Length2TotalInches = ja.Room.Length2TotalInches,
                    Width1TotalInches = ja.Room.Width1TotalInches,
                    Width2TotalInches = ja.Room.Width2TotalInches,
                    AffectedCeilingAreaSquareFeet = ja.Room.AffectedCeilingAreaSquareFeet,
                    AffectedWallAreaSquareFeet = ja.Room.AffectedWallAreaSquareFeet,
                    WallAreaSquareFeet = ja.Room.WallAreaSquareFeet,
                    CeilingAreaSquareFeet = ja.Room.CeilingAreaSquareFeet,
                    FlooringTypesAffected = ja.Room.RoomFlooringTypesAffected
                    .Select(s => new RoomFlooringTypeAffectedDto
                    {
                        AffectedPercentage = s.AffectedPercentage,
                        AffectedSquareFeet = s.AffectedSquareFeet,
                        FlooringTypeId = s.FlooringTypeId,
                        TotalSquareFeet = s.TotalSquareFeet
                    }).ToList(),
                    LineItems = job.LineItems
                    .Where(w => w.RoomId == ja.RoomId &&
                        w.IsDeleted == request.IncludeDeleted)
                    .Select(s => new LineItemDto
                    {
                        Id = s.Id,
                        LineItemId = s.LineItemId,
                        RoomId = s.RoomId,
                        Percentage = s.Percentage,
                        Quantity = s.Quantity,
                        ActivityCode = s.ActivityCode,
                        Code = s.Code,
                        Category = s.Category,
                        Description = s.Description,
                        UnitOfMeasure = s.UnitOfMeasure,
                        Info = s.Info,
                        JobVisitId = s.JobVisitId,
                        CreatedDate = s.CreatedDate,
                        CreatedBy = s.CreatedBy,
                        DeletedDate = s.DeletedDate,
                        DeletedBy = s.DeletedBy,
                        XactUploadTransactionId = s.XactUploadTransactionId,
                        Notes = s.Notes
                        .Where(s => s.IsDeleted == false)
                        .Select(s => new LineItemNoteDto
                        {
                            Id = s.Id,
                            CreatedBy = s.CreatedBy,
                            CreatedDate = s.CreatedDate,
                            Note = s.Note,
                            IsEditable = s.IsEditable,
                            IsDeleted = s.IsDeleted,
                        }).ToList()
                    }).ToList()
                }).ToList();
            }
        }
    }
}