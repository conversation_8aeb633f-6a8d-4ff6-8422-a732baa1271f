@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.DailyNarrativeDto

@if (Model?.DailyNarratives != null && Model.DailyNarratives.Count > 0)
{
    <div class="floatLeft">
        <p class="title">{dailyNarrative}Daily Narrative{/dailyNarrative}</p>

        <table class="daily-narrative-table" summary="Daily Narrative">
            <tr>
                <th scope="col" class="table-caption leftColumn">Timestamp</th>
                <th scope="col" class="table-caption rightColumn">Narrative</th>
            </tr>
            @foreach (var narrative in Model.DailyNarratives)
            {
                var narrativeTimeStamp = $"{narrative.CreatedDate:M/d/yyyy h:mm tt}" + $" ({narrative.Timezone})";
                <tr class="row-data">
                    <td>@narrativeTimeStamp</td>
                    <td><span class="narrative-text">@narrative.NarrativeText</span></td>
                </tr>
            }
        </table>

    </div>

    <div style='page-break-before: always;'></div>
}