﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class UpdateExternalMarketingCall : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("7722701c-bcfb-432d-8a1b-6520fbfe0033"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<Guid>(
                name: "AssociatedJobId",
                table: "ExternalMarketingCalls",
                type: "char(36)",
                nullable: true,
                collation: "latin1_swedish_ci",
                oldClrType: typeof(Guid),
                oldType: "char(36)")
                .OldAnnotation("Relational:Collation", "latin1_swedish_ci");

            migrationBuilder.AddColumn<bool>(
                name: "InvoiceAmountChanged",
                table: "ExternalMarketingCalls",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "InvoicedAmountLastUpdated",
                table: "ExternalMarketingCalls",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "SiteAppointmentStartDateTimeLastUpdated",
                table: "ExternalMarketingCalls",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("a58ee08e-a266-4455-b414-b7e39eb25ae4"), null, new DateTime(2023, 2, 28, 14, 21, 19, 108, DateTimeKind.Utc).AddTicks(2247), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_ExternalMarketingCalls_AssociatedJobId",
                table: "ExternalMarketingCalls",
                column: "AssociatedJobId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ExternalMarketingCalls_Job_AssociatedJobId",
                table: "ExternalMarketingCalls",
                column: "AssociatedJobId",
                principalTable: "Job",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExternalMarketingCalls_Job_AssociatedJobId",
                table: "ExternalMarketingCalls");

            migrationBuilder.DropIndex(
                name: "IX_ExternalMarketingCalls_AssociatedJobId",
                table: "ExternalMarketingCalls");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("a58ee08e-a266-4455-b414-b7e39eb25ae4"));

            migrationBuilder.DropColumn(
                name: "InvoiceAmountChanged",
                table: "ExternalMarketingCalls");

            migrationBuilder.DropColumn(
                name: "InvoicedAmountLastUpdated",
                table: "ExternalMarketingCalls");

            migrationBuilder.DropColumn(
                name: "SiteAppointmentStartDateTimeLastUpdated",
                table: "ExternalMarketingCalls");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<Guid>(
                name: "AssociatedJobId",
                table: "ExternalMarketingCalls",
                type: "char(36)",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                collation: "latin1_swedish_ci",
                oldClrType: typeof(Guid),
                oldType: "char(36)",
                oldNullable: true)
                .OldAnnotation("Relational:Collation", "latin1_swedish_ci");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("7722701c-bcfb-432d-8a1b-6520fbfe0033"), null, new DateTime(2023, 2, 23, 23, 52, 48, 938, DateTimeKind.Utc).AddTicks(8241), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
