﻿using System;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class ArtifactTypes
    {
        public static readonly Guid AddressInternetSearch = new Guid("4301A762-2825-416F-ADB2-F2B1908085C9");
        public static readonly Guid CompanyBulletinSearch = new Guid("2E7E53AC-725A-40D4-83C7-DFDF77F3EAC4");
        public static readonly Guid CompanySearch = new Guid("93DDAB58-678E-46F4-9E0D-8B85CF73E6B9");
        public static readonly Guid CompanyOwnerSearch = new Guid("8D31D9BB-D71C-4809-A0AA-DC1BB35D660F");
        public static readonly Guid LienRecordsSearch = new Guid("ABADD729-7843-4E2B-B1E0-C2947461385F");
        public static readonly Guid PhoneNumberInternetSearch = new Guid("********-8BF3-4E05-917C-582872BBD6BD");
        public static readonly Guid TaxRecordsSearch = new Guid("22955E5D-4EC1-4169-938D-287054ABA0EC");
        public static readonly Guid OtherResearchDocument = new Guid("B19D4596-1767-4637-8B97-45BDEDD6DC68");
        public static readonly Guid ScopingDocument = new Guid("D30DBCD3-AA1F-48BB-AC8A-897B03F5DBD4");
        public static readonly Guid ExternalDocument = new Guid("16DDB670-AA6B-4247-8622-459C7F2D781F");
        public static readonly Guid AccountingDocument = new Guid("2F4F865B-16ED-40B2-8BC0-4987A11D668F");
        public static readonly Guid DryingWorkbook = new Guid("3C5FFC77-1936-4CD6-B7B9-95CA0081C5F8");
        public static readonly Guid XactimateEstimate = new Guid("BF2082F3-310F-4A81-8FA9-89B5679B3D32");
        public static readonly Guid Invoice = new Guid("82DE5A26-9555-49A1-A7A4-B8A3B92D3144");
        public static readonly Guid DryingReport = new Guid("2596E17E-55CF-43CA-8DF8-7862A3FF7BD9");
        public static readonly Guid AmericanBankersClaimSummary = new Guid("58CDEE2B-0C25-43CE-960E-6FBB5908B655");
        public static readonly Guid SubcontractInvoice = new Guid("39CFBDBA-04D2-4988-BB64-6313A37F2B19");
        public static readonly Guid ScanEREstimate = new Guid("9430D0E4-42CC-4867-A78A-CE70F8EC2229");
        public static readonly Guid ContentsStorage = new Guid("77D2C68C-E81E-403D-8875-4E1A47B17C29");
        public static readonly Guid Aerial = new Guid("AFDF6FC5-0D82-48B5-B9CD-C04F80B5EFCD");
        public static readonly Guid FrontofStructure = new Guid("CC420B00-2A15-4CCF-9B25-5810F934932B");
        public static readonly Guid AffectedAreaBefore = new Guid("C68EA273-800F-40BB-A025-84EEA520CFD1");
        public static readonly Guid AffectedAreaAfter = new Guid("69E6D218-5374-4D2D-83D2-AC50808A1F7B");
        public static readonly Guid CauseofLoss = new Guid("D8BF9081-DFFE-4CCD-96FF-0568AF6D1CE1");
        public static readonly Guid OtherImage = new Guid("D4BA5021-22E9-4BF5-912B-DCAC278581D0");
        public static readonly Guid RoomVisitImage = new Guid("8D699462-52BE-498C-9AD6-D401075D732C");
        public static readonly Guid PreMitigation = new Guid("E53D84D8-637C-42FB-B074-7255280F4D6F");
        public static readonly Guid DailyDeparture = new Guid("E3A15AC6-DE66-468B-B51E-C9860E56D1E2");
        public static readonly Guid PostMitigation = new Guid("B87255B6-FBE4-4343-9BAE-3CAF4EFA66B8");
        public static readonly Guid RemovedMaterial = new Guid("20B46819-09B7-4359-8F0A-5D7F2C063F1C");
        public static readonly Guid Demolition = new Guid("BA13B46E-597A-463F-9BC1-000F13DAB2A6");
        public static readonly Guid DumpsterBaggedDebris = new Guid("3E246DD7-BD5C-4EA2-ADD8-27EAD7D8A9B6");
        public static readonly Guid CabinetsandCountertops = new Guid("1C6C4B6E-5606-44BC-8374-20B1DEDCDA4A");
        public static readonly Guid Subrogation = new Guid("5DBA662D-D713-4544-9CFE-796669DCBF7C");
        public static readonly Guid PPE = new Guid("24B4F5AA-17EE-4299-947B-DBB7B3B19916");
        public static readonly Guid FnolReport = new Guid("71EB44EE-757B-4358-AF42-0145E3B071DA");
        public static readonly Guid DocuSketch360StillImage = new Guid("22F82C32-29B5-4D08-9A35-5A9AABAD304B");
    }
}