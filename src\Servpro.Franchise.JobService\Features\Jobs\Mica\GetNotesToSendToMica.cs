﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetNotesToSendToMica
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public List<Guid> NoteIds { get; set; }
        }

        public class Dto
        {
            public List<Note> Notes { get; set; }
            public class Note
            {
                public Guid Id { get; set; }
                public DateTime? ActionDate { get; set; }
                public string NoteText { get; set; }
                public Guid VisibilityId { get; set; }
                public string Author { get; set; }
                public bool IsDeleted { get; set; }
                public DateTime CreatedDate { get; set; }
            }
        }
        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetNotesToSendToMica> _logger;

            public Handler(JobReadOnlyDataContext context,
                ILogger<GetNotesToSendToMica> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Notes to send to Mica");

                var returnDto = new Dto()
                {
                    Notes = new List<Dto.Note>()
                };

                if (request.NoteIds.Any())
                {
                    var notes = await _context.JournalNote
                        .Where(x => request.NoteIds.Contains(x.Id))
                        .ToListAsync(cancellationToken);

                    foreach (var note in notes)
                    {
                        returnDto.Notes.Add(MapNote(note));
                    }
                }
                return returnDto;
            }

            private Dto.Note MapNote(JournalNote note)
            {
                var resultVar = new Dto.Note
                {
                    Id = note.Id,
                    ActionDate = note.ActionDate,
                    Author = note.Author,
                    NoteText = note.Note,
                    VisibilityId = note.VisibilityId,
                    CreatedDate = note.CreatedDate,
                    IsDeleted = note.IsDeleted
                };
                return resultVar;
            }

        }

    }
}
