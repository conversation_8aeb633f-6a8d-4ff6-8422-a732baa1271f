﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    /// <inheritdoc />
    public partial class FixDocusketchFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("7c7de1f4-804f-4e32-88ec-17cd2aaa68c3"));

            migrationBuilder.RenameColumn(
                name: "percentageContained",
                table: "Rooms",
                newName: "PercentageContained");

            migrationBuilder.AlterColumn<bool>(
                name: "RequiresContainment",
                table: "Rooms",
                type: "tinyint(1)",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("8fbc7d92-1225-42f4-ac66-693bed87df3e"), null, new DateTime(2024, 8, 1, 16, 59, 41, 747, DateTimeKind.Utc).AddTicks(9000), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("8fbc7d92-1225-42f4-ac66-693bed87df3e"));

            migrationBuilder.RenameColumn(
                name: "PercentageContained",
                table: "Rooms",
                newName: "percentageContained");

            migrationBuilder.AlterColumn<bool>(
                name: "RequiresContainment",
                table: "Rooms",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("7c7de1f4-804f-4e32-88ec-17cd2aaa68c3"), null, new DateTime(2024, 7, 30, 2, 42, 3, 977, DateTimeKind.Utc).AddTicks(124), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }
    }
}
