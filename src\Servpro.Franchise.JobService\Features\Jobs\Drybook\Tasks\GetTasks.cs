﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using ServproTask = Servpro.Franchise.JobService.Models.Drybook.Task;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Tasks
{
    public class GetTasks
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid? JobTriStateQuestionId { get; set; }
        }
        public class Dto
        {
            public Dto(
                Guid id,
                Guid? jobId,
                Guid taskPriorityId,
                Guid taskStatusId,
                Guid? jobTriStateQuestionId,
                IEnumerable<JournalNoteDto> journalNotes)
            {
                Id = id;
                JobId = jobId;
                TaskPriorityId = taskPriorityId;
                TaskStatusId = taskStatusId;
                JobTriStateQuestionId = jobTriStateQuestionId;
                JournalNotes = journalNotes;
            }
            public Guid Id { get; }
            public Guid? JobId { get; }
            public Guid TaskPriorityId { get; }
            public Guid TaskStatusId { get; }
            public Guid? JobTriStateQuestionId { get; }
            public IEnumerable<JournalNoteDto> JournalNotes { get; }

            public class JournalNoteDto
            {
                public JournalNoteDto(
                    Guid id,
                    string author,
                    string subject,
                    string note,
                    DateTime? actionDate,
                    Guid categoryId,
                    Guid typeId,
                    Guid visibilityId)
                {
                    Id = id;
                    Author = author;
                    Subject = subject;
                    Note = note;
                    ActionDate = actionDate;
                    CategoryId = categoryId;
                    TypeId = typeId;
                    VisibilityId = visibilityId;
                }
                public Guid Id { get; }
                public string Author { get; }
                public string Subject { get; }
                public string Note { get; }
                public DateTime? ActionDate { get; }
                public Guid CategoryId { get; }
                public Guid TypeId { get; }
                public Guid VisibilityId { get; }
            }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
                => _context = context;

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var tasksQuery = _context.Tasks
                    .Include(x => x.JournalNotes)
                    .AsNoTracking()
                    .Where(x => x.JobId == request.JobId);
                if (request.JobTriStateQuestionId.HasValue)
                    tasksQuery = tasksQuery
                        .Where(x => x.JobTriStateQuestionId == request.JobTriStateQuestionId.Value);
                var tasks = await tasksQuery.ToListAsync(cancellationToken);
                return tasks.Select(Map);
            }

            private Dto Map(ServproTask task)
                => new Dto(
                    task.Id,
                    task.JobId,
                    task.TaskPriorityId,
                    task.TaskStatusId,
                    task.JobTriStateQuestionId,
                    task.JournalNotes.Select(Map));

            private Dto.JournalNoteDto Map(JournalNote note)
                => new Dto.JournalNoteDto(
                    note.Id,
                    note.Author,
                    note.Subject,
                    note.Note,
                    note.ActionDate,
                    note.CategoryId,
                    note.TypeId,
                    note.VisibilityId);
        }
    }
}
