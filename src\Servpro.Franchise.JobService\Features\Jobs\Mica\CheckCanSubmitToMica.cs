﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Infrastructure.ClientRequirementsService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class CheckCanSubmitToMica
    {
        public class Query : IRequest<bool>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Query, bool>
        {
            private readonly JobDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IClientRequirementsServiceClient _crsClient;
            private readonly ILookupServiceClient _lookupsClient;
            private readonly IXactServiceClient _xactServiceClient;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext context,
                           ILogger<Handler> logger,
                           IFranchiseServiceClient franchiseServiceClient,
                           IClientRequirementsServiceClient crsClient,
                           ILookupServiceClient lookupsClient,
                           IXactServiceClient xactServiceClient)
            {
                _context = context;
                _logger = logger;
                _crsClient = crsClient;
                _lookupsClient = lookupsClient;
                _xactServiceClient = xactServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<bool> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobId = request.JobId;

                var job = await _context.Jobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                _logger.LogInformation("Job data obtained: {@job}", job);

                var insurance = await _context.InsuranceClients.FirstOrDefaultAsync(x => x.Id == job.InsuranceCarrierId, cancellationToken);
                if (insurance == null)
                {
                    _logger.LogWarning("No insurance carrier found for job.InsuranceCarrierId: {@insuranceCarrierId}", job.InsuranceCarrierId);
                    return false;
                }

                var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, true, cancellationToken);

                var parentInsuranceRequest = _context.InsuranceClients.FirstOrDefaultAsync(x => x.InsuranceNumber == insurance.ParentInsuranceNumber, cancellationToken);
                var xactMfnRequest = _xactServiceClient.GetJobMfnAsync(job.Id, cancellationToken);
                var lookupsRequest = _lookupsClient.GetLookupsAsync(cancellationToken);

                await Task.WhenAll(parentInsuranceRequest, xactMfnRequest, lookupsRequest);

                var parentInsurance = await parentInsuranceRequest;
                var xactMfn = await xactMfnRequest;
                var lookups = await lookupsRequest;
                var franchise = franchiseSet.Franchises.FirstOrDefault(x => x.Id == job.FranchiseId);

                var submissionDto = CreateSubmissionDto(job, lookups, insurance, parentInsurance, franchise, xactMfn?.MasterFileNumber);
                _logger.LogInformation("Submission dto generated: {@dto}", submissionDto);

                var canSubmit = await _crsClient.CanSubmitJobToMica(submissionDto, cancellationToken);

                _logger.LogInformation("Validator results: {@canSubmit}", canSubmit);

                return canSubmit;
            }

            private static AllowJobSubmissionToMicaDto CreateSubmissionDto(Job job, GetLookups.Dto lookups, InsuranceClient insurance, InsuranceClient parentInsurance, FranchiseDto franchise, string xactMFN)
            {
                return new AllowJobSubmissionToMicaDto()
                {
                    InsuranceName = insurance?.Name,
                    ParentInsuranceName = parentInsurance?.Name ?? insurance.Name,
                    JobInfo = new JobInfoDto()
                    {
                        JobId = job.Id,
                        InsuranceClaimNumber = job.InsuranceClaimNumber,
                        InsuranceClientId = insurance.InsuranceNumber,
                        FranchiseId = (int)franchise.FranchiseNumber, // CRS uses FranchiseNumber as FranchiseId
                        LossType = lookups.LossTypes?.FirstOrDefault(x => x.Id == job.LossTypeId)?.Name,
                        StructureType = job.PropertyTypeId, // CRS is using StructureType as PropertyType, therefore we have to modify the value being mapped
                        LossState = job.LossAddress?.State?.StateAbbreviation,
                        LossCountry = job.LossAddress?.State?.CountryName,
                        LossPostalCode = job.LossAddress?.PostalCode,
                        JobSource = lookups.JobSources?.FirstOrDefault(x => x.Id == job.JobSourceId)?.Name,
                        IsStormJob = job.StormId.HasValue,
                        XactMasterFileNumber = xactMFN
                    }
                };
            }
        }
    }
}