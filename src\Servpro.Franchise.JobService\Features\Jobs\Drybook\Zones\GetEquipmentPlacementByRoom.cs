﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetEquipmentPlacementByRoom
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
            public bool IsUsedInValidation { get; set; }
        }

        public class Dto
        {
            public Guid EquipmentPlacementId { get; set;}
            public Guid EquipmentId { get; set; }
            public DateTime BeginDate { get; set; }
            public DateTime? EndDate { get; set; }
            public string RoomName { get; set; }
            public string AssetNumber { get; set; }
            public string EquipmentTypeName { get; set; }
            public Guid EquipmentTypeId { get; set; }
            public string Manufacturer { get; set; }
            public string SerialNumber { get; set; }
            public string ModelName { get; set; }
            public int? VolumeRate { get; set; }
            public decimal? Amps { get; set; }

        }
        public class QueryValidator : AbstractValidator<Query>
        {
            public QueryValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.RoomId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfo)
            {
                _userInfo = userInfo;
                _context = context;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                        .ThenInclude(x => x.Equipment)
                        .ThenInclude(x => x.EquipmentModel)
                        .ThenInclude(x => x.EquipmentType)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId.Value, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job does not exist: {request.JobId}");

                var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId.HasValue && x.RoomId == request.RoomId);
                if(jobArea is null)
                    throw new ResourceNotFoundException($"Room does not exist: {request.RoomId}");

                return jobArea.EquipmentPlacements
                    .Where(x => x.IsUsedInValidation == request.IsUsedInValidation)
                    .Select(x => new Dto
                    {
                        RoomName = jobArea.Name,
                        EquipmentId = x.EquipmentId,
                        EquipmentPlacementId = x.Id,
                        BeginDate = x.BeginDate,
                        EndDate = x.EndDate,
                        VolumeRate = x.Equipment.VolumeRate,
                        Amps = x.Equipment.EquipmentModel.Amps,
                        AssetNumber = x.Equipment.AssetNumber,
                        EquipmentTypeId = x.Equipment.EquipmentModel.EquipmentTypeId,
                        EquipmentTypeName = x.Equipment.EquipmentModel.EquipmentType.Name,
                        Manufacturer = x.Equipment.EquipmentModel.ManufacturerName,
                        ModelName = x.Equipment.EquipmentModel.Name,
                        SerialNumber = x.Equipment.SerialNumber
                    });
            }
        }

    }
}
