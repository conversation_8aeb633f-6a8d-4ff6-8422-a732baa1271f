﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyBusiness
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> BusinessIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyBusiness>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyBusiness> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyBusiness> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(Business));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.BusinessIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var businessTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedBusinessIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(businessTargetIds, 
                    GetBusinessIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<Business, ResaleBusiness>(
                    request.ResaleId,
                    null,
                    alreadyCopiedBusinessIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    sourceEntity =>
                    {
                        // if the source is a Marketing Business, we need to remove the marketing flag
                        // this flag wasn't copied into job-service for whatever reason, add here if its ever added
                    },
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Businesses.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Business>> GetSourceEntitiesAsync(List<Guid> businessIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var businesses = await _context.Businesses
                    .Where(b => businessIds.Contains(b.Id))
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", businesses.Count);
                return businesses;
            }

            private async Task<List<Guid>> GetBusinessIdsAsync(List<Guid?> businessTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Businesses
                    .Where(x => businessTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
