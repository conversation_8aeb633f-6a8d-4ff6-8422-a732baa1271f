﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class EditJournalNote
    {
        public class Command : IRequest<Dto>
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public string Subject { get; set; }
            public string Message { get; set; }
            public Guid CategoryId { get; set; }
            public DateTime? ActionDate { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public List<int> RuleIds { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.TypeId).NotEmpty();
                RuleFor(m => m.VisibilityId).NotEmpty();
                RuleFor(command => command.Subject)
                    .NotNull()
                    .NotEmpty()
                    .Length(2, 64);
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? JobId { get; set; }
            public string Author { get; set; }
            public string Subject { get; set; }
            public string Message { get; set; }
            public Guid CategoryId { get; set; }
            public DateTime? ActionDate { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext context,
                ILookupServiceClient lookupServiceClient,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfo,
                ILogger<Handler> logger)
            {
                _lookupServiceClient = lookupServiceClient;
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
                _userInfo = userInfo;
                _logger = logger;
            }
            public async Task<Dto> Handle(Command request,
               CancellationToken cancellationToken)
            {
                _logger.LogInformation("Editing JournalNote {NoteId} for Job {JobId} by User {User}", request.Id, request.JobId, _userInfo.GetUserInfo().Username);

                var jobExists = await _context.Jobs
                    .AsNoTracking()
                    .AnyAsync(j => j.Id == request.JobId, cancellationToken);

                if (!jobExists)
                {
                    _logger.LogWarning("Job {JobId} not found. Aborting update.", request.JobId);
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");
                }

                var journalNote = await _context.JournalNote
                    .Include(x => x.Task)
                    .FirstOrDefaultAsync(q => q.Id == request.Id, cancellationToken);

                if (journalNote == null)
                {
                    _logger.LogWarning("JournalNote {NoteId} not found. Aborting update.", request.Id);
                    throw new ResourceNotFoundException($"JournalNote not found: {request.Id}");
                }

                var userInfo = _userInfo.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                //Update status of Task
                var task = journalNote.Task;
                if (task != null)
                {
                    var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                    var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == task.TaskTypeId);

                    if (taskType.ShouldCloseWhenDiaryEntryIsComplete && !string.IsNullOrEmpty(request.Message))
                    {
                        _logger.LogInformation("Task {TaskId} marked as completed due to message content.", task.Id);
                        task.PercentComplete = 100;
                        task.TaskStatusId = TaskStatuses.Completed;
                    }
                }

                journalNote.ActionDate = request.ActionDate;
                journalNote.CategoryId = request.CategoryId;
                journalNote.Note = request.Message;
                journalNote.Subject = request.Subject;
                journalNote.TypeId = request.TypeId;
                journalNote.VisibilityId = request.VisibilityId;
                journalNote.ModifiedDate = DateTime.UtcNow;
                journalNote.ModifiedBy = userInfo.Name;
                journalNote.RuleIds = request.RuleIds;

                var journalNoteCreatedEvent = GenerateJournalNoteUpdatedEvent(journalNote, correlationId, userInfo);
                _logger.LogDebug("Generated JournalNoteUpdatedEvent for NoteId {NoteId}, adding to outbox.", journalNote.Id);

                _context.OutboxMessages.Add(journalNoteCreatedEvent);

                _logger.LogDebug("Saving changes for JournalNote {NoteId}", journalNote.Id);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully updated JournalNote {NoteId}", journalNote.Id);
                return Map(journalNote);
            }

            private Dto Map(JournalNote journalNote)
                => new Dto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject
                };

            private JournalNoteUpdatedEvent.JournalNoteDto MapUpdateEventDto(JournalNote journalNote, UserInfo userInfo)
                => new JournalNoteUpdatedEvent.JournalNoteDto()
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId ?? Guid.Empty,
                    Author = journalNote.Author,
                    Subject = journalNote.Subject,
                    Message = journalNote.Note,
                    CategoryId = journalNote.CategoryId,
                    ActionDate = journalNote.ActionDate,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    UpdatedById = userInfo.Id,
                    UpdatedDate = DateTime.UtcNow,
                    RuleIds = journalNote.RuleIds?.ToList()
                };

            private OutboxMessage GenerateJournalNoteUpdatedEvent(JournalNote journalNote, Guid correlationId, UserInfo userInfo)
            {
                var updateDto = MapUpdateEventDto(journalNote, userInfo);
                var updateEvent = new JournalNoteUpdatedEvent(updateDto, correlationId);
                return new OutboxMessage(updateEvent.ToJson(), nameof(JournalNoteUpdatedEvent), correlationId, userInfo.Username);
            }
        }

    }

}
