﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.HistoricalJobs
{
    public class HistoricalMediaData
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId, SearchMedia mediaType)
            {
                JobId = jobId;
                MediaType = mediaType;
            }

            public Guid JobId { get; set; }
            public SearchMedia MediaType { get; set; }
        }

        public enum SearchMedia
        {
            Fnol,
            Photos,
            Forms,
            MisDocs
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public string BucketName { get; set; }
            public SearchMedia MediaType { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private  string _tableName;           
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";
            private readonly ILogger<HistoricalMediaData> _logger;


            private IAmazonDynamoDB _client { get; }
            public Handler(
                IAmazonDynamoDB client, 
                IConfiguration config, 
                IAmazonS3 clientS3, 
                ILogger<HistoricalMediaData> logger)
            {
                _client = client; 
                _config = config;  
                _clientS3 = clientS3;               
                _logger = logger;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {                
                var dataresult = await GetHistoricJobsAsync(request, cancellationToken);
                return dataresult;
            }

            async Task<IEnumerable<Dto>> GetHistoricJobsAsync(
                 Query request, CancellationToken cancellationToken)
            {
                _tableName = _config["DynamoDB:HistoricTableName"];

                _logger.LogInformation("Getting media:{mediaType} for JobId: {jobId} ", nameof(request.MediaType), request.JobId);

                var result = new List<Dto>();
                await foreach (var jobMedia in RunQuery(request.JobId, request.MediaType, cancellationToken))
                    result.Add(jobMedia);

                if (result.Count() > 0)
                    return result.Select(x=> GetPreSignedUrl(x)).ToList();                

                return result;
            }

            async IAsyncEnumerable<Dto> RunQuery(Guid jobId, SearchMedia mediaType, [EnumeratorCancellation] CancellationToken cancellationToken)
            {
                var mediaTypeValue = "";
                var artifactType = "";

                switch (mediaType)
                {
                    case SearchMedia.Fnol:                       
                        mediaTypeValue = MediaTypes.Document.ToString().ToLower();
                        artifactType = ArtifactTypes.FnolReport.ToString().ToLower();
                        break;
                    case SearchMedia.Photos:                       
                        mediaTypeValue = MediaTypes.Photo.ToString().ToLower();
                        break;
                    case SearchMedia.Forms:                       
                        mediaTypeValue = MediaTypes.Document.ToString().ToLower();
                        artifactType = Guid.Empty.ToString().ToLower();
                        break;
                    case SearchMedia.MisDocs:                        
                        mediaTypeValue = MediaTypes.Document.ToString().ToLower();
                        break;
                    default:
                        break;
                }
                var qRequest = new QueryRequest
                {
                   TableName = _tableName,
                   ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                   {
                     { ":hashValue",   new AttributeValue { S = HistoricServiceTable.JobPrefix+jobId.ToString().ToLower() } },
                     { ":rangeValue",   new AttributeValue { S = HistoricServiceTable.MediaPrefix } },
                     { ":mediaTypeValue",   new AttributeValue { S = mediaTypeValue } }
                   },
                   KeyConditionExpression = "HashKey = :hashValue and begins_with(RangeKey, :rangeValue)",
                   FilterExpression = "#MediaTypeId = :mediaTypeValue",
                   ExpressionAttributeNames = new Dictionary<string, string>
                   {
                     { "#MediaTypeId", HistoricServiceTable.MediaFields.MediaTypeId }                      
                   }
                };
                if (artifactType.Length > 0)
                {
                    qRequest.ExpressionAttributeNames.Add("#ArtifactTypeId", HistoricServiceTable.MediaFields.ArtifactTypeId);
                    qRequest.ExpressionAttributeValues[":artifactTypeValue"] = new AttributeValue { S = artifactType };
                    qRequest.FilterExpression += " and #ArtifactTypeId = :artifactTypeValue";
                }
               
                Dictionary<string, AttributeValue> lastKeyEvaluated = null;
                do
                {
                    qRequest.ExclusiveStartKey = lastKeyEvaluated;

                    var response = await _client.QueryAsync(qRequest, cancellationToken);                    
                    if (response != null)
                    {
                        _logger.LogInformation("Found {items} media Items for JobId: {jobId}", response.Count.ToString(), jobId.ToString());
                        foreach (var item in response.Items)
                        {
                            var dto = Map(item, mediaType);
                            if (dto?.Id != Guid.Empty)
                                yield return dto;
                        }
                    }
                    else
                    _logger.LogWarning("Dynamo Query Result for JobId: {jobId}, is null ", jobId.ToString());
                } while (lastKeyEvaluated != null && lastKeyEvaluated.Count != 0);
            }

            private Dto Map(Dictionary<string, AttributeValue> item,                            
                            SearchMedia mediaType)
            {
                var rangeKey = item["RangeKey"].S;
                var rangeId = rangeKey.Substring(HistoricServiceTable.MediaPrefix.Length);
                var id = Guid.Parse(rangeId);
                var fileName = item[nameof(HistoricServiceTable.MediaFields.FileName)].S;
                var sS3Url = item[nameof(HistoricServiceTable.MediaFields.MediaPath)].S;
                var bucketName = item[nameof(HistoricServiceTable.MediaFields.BucketName)].S;               
                var artifactTypeId = item[nameof(HistoricServiceTable.MediaFields.ArtifactTypeId)].S;


                if (mediaType != SearchMedia.MisDocs || 
                    IsMiscDocument(mediaType, artifactTypeId))
                    return new Dto() 
                    {
                        Id = id,
                        Name = fileName,
                        MediaPath = sS3Url,
                        MediaType = mediaType,
                        BucketName = bucketName,
                    };


                return new Dto();
            }

            private bool IsMiscDocument(SearchMedia mediaType, string artifactTypeId)
            {
                return mediaType == SearchMedia.MisDocs && artifactTypeId != Guid.Empty.ToString() && artifactTypeId != ArtifactTypes.FnolReport.ToString();
            }

            private Dto GetPreSignedUrl(Dto data)
            {
                var key = data.MediaPath;

                var s3ExpirationTimeSpan = new TimeSpan(24, 0, 0);
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = data.BucketName,
                    // We must prevent commas from being in the ContentDisposition
                    // otherwise Chrome has an error around multiple Content Dispositions.
                    ResponseHeaderOverrides = new ResponseHeaderOverrides()
                    {
                        ContentDisposition = $"attachment;filename=\"{data.Name.Replace(",", "")}\""
                    },
                    Key = key,
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };
                var preSignedUrlDto = new Dto
                {
                    Id = data.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = data.Name,
                    MediaPath = data.MediaPath,
                    BucketName = data.BucketName,
                    MediaType = data.MediaType
                };
                _logger.LogInformation("{downloadDocument}: Presigned DTO: {@preSignedUrlDto}", nameof(HistoricalMediaData), preSignedUrlDto);
                return preSignedUrlDto;
            }
        }
    }
}
