﻿using Newtonsoft.Json;

namespace Servpro.Franchise.JobService.Common.ExtensionMethods
{
    public static class JsonExtensions
    {
        
        public static T FromJson<T>(this string json, JsonSerializerSettings serializerSettings = null)
            where T : class
        {
            if (string.IsNullOrEmpty(json))
                return default;
            return JsonConvert.DeserializeObject<T>(json, serializerSettings);
        }
        public static object FromJson(this string value)
        {
            return JsonConvert.DeserializeObject<object>(value);
        }

        /// <summary>
        ///     Converts an object to JSON.
        /// </summary>
        /// <param name="obj">The object to serialize.</param>
        /// <param name="serializerSettings">The serializer settings.</param>
        /// <returns>The JSON Serialized object.</returns>
        public static string ToJson(this object obj, JsonSerializerSettings serializerSettings = null)
        {
            var defaultSerializerSettings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore
            };
            return JsonConvert.SerializeObject(obj, Formatting.None, serializerSettings ?? defaultSerializerSettings);
        }

    }
}
