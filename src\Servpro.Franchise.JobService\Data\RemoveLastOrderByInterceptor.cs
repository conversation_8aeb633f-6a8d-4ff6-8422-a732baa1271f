using System;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Diagnostics;
using NLog;

namespace Servpro.Franchise.JobService.Data
{
    public class RemoveLastOrderByInterceptor : DbCommandInterceptor
    {
        public const string QueryTag = "RemoveLastOrderBy";
        public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = new CancellationToken())
        {
            try
            {
                const string orderBy = "ORDER BY";
                if (command.CommandText.Contains(QueryTag) && command.CommandText.Contains(orderBy))
                {
                    int lastOrderBy = command.CommandText.LastIndexOf(orderBy, StringComparison.Ordinal);

                    command.CommandText = command.CommandText.Remove(lastOrderBy);
                    command.CommandText += ";";
                }
            }
            catch (Exception ex)
            {
                var logger = LogManager.GetCurrentClassLogger();
                logger.Error(ex, "Failed to intercept query.");
            }

            return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        }
    }
}