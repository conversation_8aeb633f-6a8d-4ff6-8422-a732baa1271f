﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion
{
    public class GetJobCompletionSummary
    {
        public class Query : IRequest<Dto>
        {
            public Guid JobId { get; set; }
        }

        public class Dto
        {
            public Dto(
                Guid jobId,
                bool? isFinalWalkThroughComplete,
                bool? isCertificateOfSatisfactionSigned,
                Guid? isFinalWalkThroughJournalNoteId,
                Guid? isCertificateSignedJournalNoteId,
                bool isEquipmentStillPlacedOnJob,
                DateTime? completeDate,
                DateTime? dryingCompleteDate,
                DateTime? lastJobVisitDate)
            {
                JobId = jobId;
                IsFinalWalkThroughComplete = isFinalWalkThroughComplete;
                IsCertificateOfSatisfactionSigned = isCertificateOfSatisfactionSigned;
                IsFinalWalkThroughJournalNoteId = isFinalWalkThroughJournalNoteId;
                IsCertificateSignedJournalNoteId = isCertificateSignedJournalNoteId;
                IsEquipmentStillPlacedOnJob = isEquipmentStillPlacedOnJob;
                CompleteDate = completeDate;
                DryingCompleteDate = dryingCompleteDate;
                LastJobVisitDate = lastJobVisitDate;
            }

            public Guid JobId { get; }
            public bool? IsFinalWalkThroughComplete { get; }
            public bool? IsCertificateOfSatisfactionSigned { get; }
            public Guid? IsFinalWalkThroughJournalNoteId { get; }
            public Guid? IsCertificateSignedJournalNoteId { get; }
            public bool IsEquipmentStillPlacedOnJob { get; }
            public DateTime? CompleteDate { get; }
            public DateTime? DryingCompleteDate { get; }
            public DateTime? LastJobVisitDate { get; }
            public bool IsDryingComplete => DryingCompleteDate.HasValue;
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobTriStateAnswers)
                    .Include(x => x.JobVisits)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.Tasks = await _context.Tasks
                                          .Include(x => x.JournalNotes)
                                          .AsNoTracking()
                                          .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                job.JobAreas = await _context.JobAreas
                                          .Include(x => x.EquipmentPlacements)
                                          .AsNoTracking()
                                          .Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var lastVisit = job.JobVisits.Any()
                    ? job.JobVisits.Max(x => x.Date)
                    : (DateTime?)null;
                var finalWalkthroughTask = job.Tasks
                    .Where(x => x.JobTriStateQuestionId == JobTriStateQuestions.IsCustomerFinalWalkThroughComplete)
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefault();
                var certificateSignedTask = job.Tasks
                    .Where(x => x.JobTriStateQuestionId == JobTriStateQuestions.WasCertificateOfSatisfactionSignedAndDatedByCustomer)
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefault();

                return new Dto(job.Id,
                    job.GetAnswer(JobTriStateQuestions.IsCustomerFinalWalkThroughComplete),
                    job.GetAnswer(JobTriStateQuestions.WasCertificateOfSatisfactionSignedAndDatedByCustomer),
                    finalWalkthroughTask?.JournalNotes.FirstOrDefault()?.Id,
                    certificateSignedTask?.JournalNotes.FirstOrDefault()?.Id,
                    job.JobAreas.SelectMany(x => x.EquipmentPlacements).Any(x => !x.EndDate.HasValue),
                    job.GetDate(JobDateTypes.Complete),
                    job.GetDate(JobDateTypes.DryingComplete),
                    lastVisit);
            }
        }
    }
}
