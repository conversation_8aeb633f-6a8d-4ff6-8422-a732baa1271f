using System;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Resale;
using Servpro.Franchise.JobService.Data;
using ResaleFranchise = Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto.Franchise;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Servpro.Franchise.JobService.Models.Drybook;
using System.Collections.Generic;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Events
{
    public class FranchiseResaleErrorCorrection
    {
        public class Event : FranchiseResaleErrorCorrectionEvent, IRequest
        {
            public Event(ResaleFranchise sellingFranchise,
                ResaleFranchise buyingFranchise,
                Guid correlationId)
                : base(sellingFranchise, buyingFranchise, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<FranchiseResaleErrorCorrection> _logger;
            private readonly JobDataContext _context;
            private readonly IConfiguration _config;

            public Handler(
                ILogger<FranchiseResaleErrorCorrection> logger,
                JobDataContext context,
                IConfiguration config)
            {
                _logger = logger;
                _context = context;
                _config = config;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var jobsForResale = await _context.Jobs
                    .Include(x => x.JobVisits)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.JobAreaMaterials)
                    .Where(x => x.FranchiseId == request.SellingFranchise.Id)
                    .ToListAsync(cancellationToken);

                await FixStormTransferBugJobsAsync(jobsForResale, cancellationToken);

                return Unit.Value;
            }

            private async System.Threading.Tasks.Task FixStormTransferBugJobsAsync(List<Job> jobsForResale, CancellationToken cancellationToken)
            {
                foreach (var job in jobsForResale)
                {
                    if (job.JobAreas != null && job.JobAreas.Any())
                    {
                        foreach (var jobArea in job.JobAreas)
                        {
                            if (jobArea.JobAreaMaterials != null && jobArea.JobAreaMaterials.Any())
                            {
                                foreach (var jobAreaMaterial in jobArea.JobAreaMaterials)
                                {
                                    await ProcessJobAreaMaterialsAsync(job, jobAreaMaterial, cancellationToken);
                                }
                            }
                        }
                    }
                }
            }

            private async System.Threading.Tasks.Task ProcessJobAreaMaterialsAsync(Job job, JobAreaMaterial jobAreaMaterial, CancellationToken cancellationToken)
            {
                var beginJobVisitId = jobAreaMaterial.BeginJobVisitId;
                var goalMetOnJobVisitId = jobAreaMaterial.GoalMetOnJobVisitId;
                var removedOnJobVisitId = jobAreaMaterial.RemovedOnJobVisitId;
                if (ShouldCheckJobVisits(job, beginJobVisitId, goalMetOnJobVisitId, removedOnJobVisitId))
                {
                    if (beginJobVisitId.HasValue)
                    {
                        await CheckForJobVisitCorrectionsAsync(job, beginJobVisitId.Value, x => 
                        {
                            jobAreaMaterial.BeginJobVisitId = x;
                        }, cancellationToken);
                    }

                    if (goalMetOnJobVisitId.HasValue)
                    {
                        await CheckForJobVisitCorrectionsAsync(job, goalMetOnJobVisitId.Value, x => 
                        {
                            jobAreaMaterial.GoalMetOnJobVisitId = x;
                        }, cancellationToken);
                    }

                    if (removedOnJobVisitId.HasValue)
                    {
                        await CheckForJobVisitCorrectionsAsync(job, removedOnJobVisitId.Value, x => 
                        {
                            jobAreaMaterial.RemovedOnJobVisitId = x;
                        }, cancellationToken);
                    }
                    
                    await _context.SaveChangesAsync(cancellationToken);
                }
            }

            private bool ShouldCheckJobVisits(Job job, Guid? beginJobVisitId, Guid? goalMetOnJobVisitId, Guid? removedOnJobVisitId)
                => job.JobVisits != null
                    && job.JobVisits.Any()
                    && (beginJobVisitId.HasValue || goalMetOnJobVisitId.HasValue || removedOnJobVisitId.HasValue);

            private async System.Threading.Tasks.Task CheckForJobVisitCorrectionsAsync(Job job, 
                Guid jobVisitId, 
                Action<Guid> updateJobAreaMaterial,
                CancellationToken cancellationToken)
            {
                //The jobVisit does not exists on the current job
                if (!job.JobVisits.Any(x => x.Id == jobVisitId))
                {
                    var jobVisit = await _context.JobVisit.FirstOrDefaultAsync(x => x.Id == jobVisitId, cancellationToken);

                    // job visit does not exist at all, should never happen
                    if (jobVisit is null)
                    {
                        _logger.LogError("No JobVisit found in the database for the requested job visit.");
                        return;
                    }

                    // job visit was found
                    var matchingJobVisit = job.JobVisits
                        .FirstOrDefault(x => x.Date == jobVisit.Date 
                            && x.EmployeeInitials == jobVisit.EmployeeInitials 
                            && x.JobId == job.Id);
                    
                    //No matching job visit found in source job, so create a new based off the jobVisit from the other job
                    if (matchingJobVisit is null)
                    {
                        matchingJobVisit = Map(jobVisit, job.Id);
                        job.JobVisits.Add(matchingJobVisit);
                    }

                    updateJobAreaMaterial(matchingJobVisit.Id);
                }
            }

            private JobVisit Map(JobVisit jobVisitToCopy, Guid jobId)
                => new JobVisit()
                {
                    Id = Guid.NewGuid(),
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "Resale Data-Fix",
                    JobId = jobId,
                    Date = jobVisitToCopy.Date,
                    EmployeeInitials = jobVisitToCopy.EmployeeInitials,
                    DepartureDate = jobVisitToCopy.DepartureDate,
                    DefaultPenetratingMeterEquipmentId = jobVisitToCopy.DefaultPenetratingMeterEquipmentId,
                    DefaultNonPenetratingMeterEquipmentId = jobVisitToCopy.DefaultNonPenetratingMeterEquipmentId,
                    DefaultThermoHygrometerEquipmentId = jobVisitToCopy.DefaultThermoHygrometerEquipmentId,
                    IsMissingVisit = jobVisitToCopy.IsMissingVisit,
                };
        }
    }
}