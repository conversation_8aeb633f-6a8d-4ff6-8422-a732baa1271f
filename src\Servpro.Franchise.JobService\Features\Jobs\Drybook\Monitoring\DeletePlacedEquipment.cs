﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class DeletePlacedEquipment
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public IEnumerable<Guid> EquipmentPlacements { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(
                ISessionIdAccessor sessionIdAccessor,
                JobDataContext context,
                IUserInfoAccessor userInfo)
            {
                _sessionIdAccessor = sessionIdAccessor;
                _context = context;
                _userInfo = userInfo;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                if (request.EquipmentPlacements == null || !request.EquipmentPlacements.Any())
                {
                    return Unit.Value;
                }

                var userInfo = _userInfo.GetUserInfo();
                var job = await GetJobAsync(request.JobId, userInfo.FranchiseSetId, cancellationToken);

                ValidateJobLocks(job, userInfo.Id);

                var equipmentPlacementsToRemove = GetEquipmentPlacementsToRemove(job, request.EquipmentPlacements);

                await GenerateEquipmentRemovedEvents(equipmentPlacementsToRemove, job, userInfo, cancellationToken);

                _context.EquipmentPlacements.RemoveRange(equipmentPlacementsToRemove);
                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task<Job> GetJobAsync(Guid jobId, Guid? franchiseSetId, CancellationToken cancellationToken)
            {
                if (franchiseSetId == null)
                {
                    throw new InvalidOperationException("FranchiseSetId cannot be null.");
                }

                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                    .FirstOrDefaultAsync(x => x.Id == jobId && x.FranchiseSetId == franchiseSetId, cancellationToken);

                if (job == null)
                {
                    throw new ResourceNotFoundException($"Job not found (Id: {jobId})");
                }

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == jobId).ToListAsync(cancellationToken);

                return job;
            }

            private void ValidateJobLocks(Job job, Guid userId)
            {
                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userId))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));
            }

            private static List<Models.Drybook.EquipmentPlacement> GetEquipmentPlacementsToRemove(Job job, IEnumerable<Guid> equipmentPlacements)
            {
                var equipPlacementToRemoveLookup = new HashSet<Guid>(equipmentPlacements.Distinct());

                return job.JobAreas
                    .SelectMany(x => x.EquipmentPlacements)
                    .Where(x => equipPlacementToRemoveLookup.Contains(x.Id))
                    .ToList();
            }

            private async Task GenerateEquipmentRemovedEvents(
                   List<Models.Drybook.EquipmentPlacement> equipmentPlacementsToRemove,
                   Job job,
                   UserInfo userInfo,
                   CancellationToken cancellationToken)
            {
                if (!userInfo.FranchiseSetId.HasValue)
                {
                    throw new InvalidOperationException("FranchiseSetId cannot be null when generating events.");
                }

                var correlationId = GetCorrelationId();

                var equipPlacementGroups = equipmentPlacementsToRemove
                    .GroupBy(x => x.JobAreaId);

                var jobAreaRoomIdMap = job.JobAreas?.ToDictionary(x => x.Id, x => x.RoomId)
                    ?? new Dictionary<Guid, Guid?>();

                foreach (var equipPlacementGroup in equipPlacementGroups)
                {
                    var equipmentRemovedDto = new EquipmentRemovedFromRoomDto
                    {
                        JobId = job.Id,
                        FranchiseSetId = userInfo.FranchiseSetId.Value,
                        JobAreaId = equipPlacementGroup.Key,
                        RoomId = jobAreaRoomIdMap.ContainsKey(equipPlacementGroup.Key)
                            ? jobAreaRoomIdMap[equipPlacementGroup.Key] ?? Guid.Empty
                            : Guid.Empty,
                        UserId = userInfo.Id,
                        Username = userInfo.Username,
                        Removals = equipPlacementGroup.Select(x => new EquipmentRemovedFromRoomDto.RemovalDto
                        {
                            Id = x.Id,
                            EquipmentId = x.EquipmentId
                        })
                    };

                    var equipmentRemovedEvent = new EquipmentRemovedFromRoomEvent(equipmentRemovedDto, correlationId)
                    {
                        Source = EventSource.Other
                    };

                    var outboxMessage = new OutboxMessage(equipmentRemovedEvent.ToJson(), nameof(EquipmentRemovedFromRoomEvent), correlationId, userInfo.Username);
                    await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                }
            }

            public Guid GetCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();

                return correlationId;
            }
        }
    }
}
