﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetRoomSummary
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Dto
        {
            public Dto(
                Guid jobId,
                Guid jobAreaId,
                Guid? roomId,
                string roomName,
                Guid zoneId,
                string zoneName,
                DateTime visitDate,
                decimal? totalSquareFeet,
                decimal? affectedSquareFeet,
                decimal affectedPercentage,
                bool hasMissingFlooring,
                bool hasMissingDimensions,
                bool hasEquipmentPlacements,
                int equipmentPlacementCount)
            {
                JobId = jobId;
                JobAreaId = jobAreaId;
                RoomId = roomId;
                RoomName = roomName;
                ZoneId = zoneId;
                ZoneName = zoneName;
                VisitDate = visitDate;
                TotalSquareFeet = totalSquareFeet;
                AffectedSquareFeet = affectedSquareFeet;
                AffectedPercentage = affectedPercentage;
                HasMissingFlooring = hasMissingFlooring;
                HasMissingDimensions = hasMissingDimensions;
                HasEquipmentPlacements = hasEquipmentPlacements;
                EquipmentPlacementCount = equipmentPlacementCount;
            }

            public Guid JobId { get; set; }
            public Guid JobAreaId { get; set; }
            public Guid? RoomId { get; set; }
            public string RoomName { get; set; }
            public Guid ZoneId { get; set; }
            public string ZoneName { get; set; }
            public DateTime VisitDate { get; set; }
            public decimal? TotalSquareFeet { get; set; }
            public decimal? AffectedSquareFeet { get; set; }
            public decimal AffectedPercentage { get; set; }
            public bool HasMissingFlooring { get; set; }
            public bool HasMissingDimensions { get; set; }
            public bool HasEquipmentPlacements { get; set; }
            public int EquipmentPlacementCount { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobQueryList = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(y => y.Room)
                            .ThenInclude(x => x.RoomFlooringTypesAffected)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.Zone)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.BeginJobVisit)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (jobQueryList == null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId}");

                return Map(jobQueryList);
            }

            private IEnumerable<Dto> Map(Job job)
            {
                var list = new List<Dto>();
                if (job.JobAreas != null)
                {
                    var jobAreas = job.JobAreas.Where(x => !x.IsDeleted);
                    foreach (var jobArea in jobAreas)
                    {
                        if (jobArea != null && jobArea.Room != null)
                        {
                            var equipmentPlacementCount = jobArea?.EquipmentPlacements?.Count ?? 0;

                            var zone = jobArea.Zone;
                            var room = jobArea.Room;
                            var roomData = RoomUtils.CalculateRoomData(room, equipmentPlacementCount);

                            list.Add(new Dto(job.Id,
                            jobArea.Id,
                            room.Id,
                            jobArea.Name,
                            zone?.Id ?? Guid.Empty,
                            zone?.Name ?? "",
                            jobArea.BeginJobVisit?.Date ?? DateTime.UtcNow,
                            roomData.TotalSqFt,
                            roomData.AffectedSqFeet,
                            roomData.AffectedPercentage,
                            roomData.HasMissingFlooring,
                            roomData.HasMissingDimensions,
                            roomData.HasEquipmentPlacements,
                            equipmentPlacementCount));
                        }
                    }
                }
                return list;
            }
        }
    }
}
