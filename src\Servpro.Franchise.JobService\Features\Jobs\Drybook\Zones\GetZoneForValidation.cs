﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones.ZoneValidation;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetZoneForValidation
    {
        public class Query : IRequest<ZoneForValidationDto>
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
        }

        public class Handler : IRequestHandler<Query, ZoneForValidationDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccesor;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GetZoneForValidation> _logger;
            private readonly IZoneValidationMapper _zoneValidationMapper;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                IZoneValidationMapper zoneValidationMapper,
                ILogger<GetZoneForValidation> logger,
                ILookupServiceClient lookupServiceClient)
            {
                _context = context;
                _userInfoAccesor = userInfoAccessor;
                _logger = logger;
                _zoneValidationMapper = zoneValidationMapper;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<ZoneForValidationDto> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting zone for validation: {@request}", request);

                var userInfo = _userInfoAccesor.GetUserInfo();
                var job = await _context.Jobs
                                        .AsNoTracking()
                                        .FirstOrDefaultAsync(x => x.Id == request.JobId
                                                                && x.FranchiseSetId == userInfo.FranchiseSetId.Value,
                                                                cancellationToken);

                _logger.LogInformation("Got job information: {@job}", job);

                if (job is null)
                    throw new ResourceNotFoundException("Job does not exist");

                job.Zones = await _context.Zones
                        .Include(x => x.Tasks.Where(t => t.JobId == request.JobId))
                            .ThenInclude(x => x.JournalNotes)
                        .Include(x => x.JobAreas)
                            .ThenInclude(x => x.EquipmentPlacements)
                                .ThenInclude(x => x.Equipment)
                                    .ThenInclude(x => x.EquipmentModel)
                                         .ThenInclude(x => x.EquipmentType)
                        .Include(x => x.JobAreas)
                            .ThenInclude(x => x.Room)
                            .ThenInclude(x => x.RoomFlooringTypesAffected)
                        .Include(x => x.JobAreas)
                            .ThenInclude(x => x.BeginJobVisit)
                        .AsNoTracking()
                        .Where(x => x.JobId == request.JobId && !x.IsDeleted)
                        .ToListAsync(cancellationToken);

                var zone = job.Zones.FirstOrDefault(x => x.Id == request.ZoneId);
                if (zone is null)
                    throw new ResourceNotFoundException("Zone does not exist");

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var waterClassLookups = lookups.WaterClasses.ToDictionary(x => x.Id);
                var dateReceived = job.GetDate(JobDateTypes.ReceivedDate);
                _logger.LogInformation("Date received: {@dateReceived}", dateReceived);

                var validationZone = await _zoneValidationMapper.ExecuteAsync(zone, waterClassLookups, dateReceived);
                _logger.LogInformation("Validation Zone: {@validationZone}", validationZone);

                return validationZone;
            }
        }
    }
}
