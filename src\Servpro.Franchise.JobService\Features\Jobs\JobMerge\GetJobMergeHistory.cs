﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge
{
    public class GetJobMergeHistory
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId) 
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Dto(bool jobHasBeenMerged, JobMergeHistoryDto jobMergeHistory)
            {
                JobHasBeenMerged = jobHasBeenMerged;
                MergeHistory = jobMergeHistory;
            }

            public bool JobHasBeenMerged { get; set; }
            public JobMergeHistoryDto MergeHistory { get; set; }
        }

        public class JobMergeHistoryDto
        {
            public Guid SourceJobId { get; set; }
            public Guid TargetJobId { get; set; }
            public IEnumerable<JobMergeOptionDto> MergeOptions { get; set; }
            public DateTime MergeDate { get; set; }
            public string MergedBy { get; set; }
        }

        public class JobMergeOptionDto
        {
            public int JobMergeTypeId { get; set; }
            public string Name { get; set; }
            public bool Selected { get; set; }
            public bool PreviouslyMerged { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext db) => _db = db;

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var mergeHistory = await _db.JobMergeHistory.FirstOrDefaultAsync(x => 
                    request.JobId == x.SourceJobId || request.JobId == x.TargetJobId, cancellationToken);

                if(mergeHistory != null)
                    return new Dto(true, Map(mergeHistory));

                return new Dto(false, null);
            }

            private static JobMergeHistoryDto Map(JobMergeHistory mergeHistory)
                => new JobMergeHistoryDto
                {
                    SourceJobId = mergeHistory.SourceJobId,
                    TargetJobId = mergeHistory.TargetJobId,
                    MergeDate = mergeHistory.CreatedDate,
                    MergeOptions = mergeHistory.MergeOptions.Select(Map),
                    MergedBy = mergeHistory.CreatedBy
                };

            private static JobMergeOptionDto Map(JobMergeOption mergeOption)
                => new JobMergeOptionDto
                {
                    JobMergeTypeId = mergeOption.JobMergeTypeId,
                    Name = mergeOption.Name,
                    Selected = mergeOption.Selected,
                    PreviouslyMerged = mergeOption.PreviouslyMerged
                };
        }
    }
}
