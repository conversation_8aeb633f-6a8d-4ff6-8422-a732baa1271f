﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ClientConditionsPublished
    {
        public class Event : ClientConditionsPublishedEvent, IRequest
        {
            public Event(ClientConditionsPublishedEvent.ClientConditionsPublishedDto eventDto, Guid correlationId) :
                base(eventDto, correlationId) { }

            public class Handler : IRequestHandler<Event>
            {
                private readonly JobDataContext _db;
                private readonly ILogger<Handler> _logger;

                public Handler(JobDataContext dataContext, ILogger<Handler> logger)
                {
                    _db = dataContext;
                    _logger = logger;
                }

                public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
                {
                    _logger.LogDebug("Received {request}", request);

                    var job = await _db.Jobs
                        .FirstOrDefaultAsync(j => j.Id == request.ClientConditionsPublished.JobId, cancellationToken: cancellationToken);

                    if (job == null)
                    {
                        _logger.LogWarning("JobId not found for {request}", request);
                        return Unit.Value;
                    }

                    // otherwise update the ClientConditions on the job
                    job.ClientConditions = request.ClientConditionsPublished.ClientConditions;
                    await _db.SaveChangesAsync(cancellationToken);

                    return Unit.Value;
                }
            }
        }
    }
}