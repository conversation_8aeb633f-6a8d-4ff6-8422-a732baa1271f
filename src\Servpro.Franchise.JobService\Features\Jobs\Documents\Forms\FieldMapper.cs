﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Forms
{
    public class FieldMapper
    {
        private readonly Job _job;
        private readonly FranchiseDto _franchise;
        private readonly GetLookups.Dto _lookups;
        private readonly List<InsuranceClient> _insuranceClients;
        private readonly ILogger _logger;


        public FieldMapper(Job job, FranchiseDto franchise, GetLookups.Dto lookups, ILogger logger, List<InsuranceClient> insuranceClients)
        {
            _insuranceClients = insuranceClients;
            _job = job;
            _franchise = franchise;
            _lookups = lookups;
            _lookups = lookups;
            _logger = logger;
        }

        private void LogInfo(string data)
        {
            if (_logger != null)
                _logger.LogInformation("FieldMapperLogger:" + data);
        }


        #region Properties

        /// <summary>
        /// Adjuster Email
        /// </summary>
        public string AdjusterEmail
        {
            get
            {
                var holder = GetAdjuster();
                LogInfo($"Holder for AdjusterEmail: {holder.ToJson()}");
                return holder.Contact?.EmailAddress;
            }
        }

        /// <summary>
        /// Adjuster Name
        /// </summary>
        public string AdjusterName
        {
            get
            {
                try
                {
                    var adjuster = GetAdjuster();
                    LogInfo($"Holder for AdjusterName: {adjuster.ToJson()}");
                    return $"{adjuster?.Contact?.FirstName} {adjuster?.Contact?.LastName}";
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Adjuster Phone
        /// </summary>
        public string AdjusterPhone
        {
            get
            {
                try
                {
                    var adjuster = GetAdjuster();
                    LogInfo($"Holder for AdjusterPhone: {adjuster?.Contact.ToJson()}");
                    return GetDefaultPhone(adjuster?.Contact);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// After Hours Contact Name
        /// </summary>
        public string AfterHoursContactName
        {
            get
            {
                var holder = GetContact(JobContactTypes.AfterHours)?.Contact?.FullName;
                LogInfo($"Holder for AfterHoursContac: {holder.ToJson()}");
                return GetContact(JobContactTypes.AfterHours)?.Contact?.FullName;
            }
        }

        /// <summary>
        /// After Hours Contact Phone
        /// </summary>
        public string AfterHoursContactPhone
        {
            get
            {
                try
                {
                    var contact = GetContact(JobContactTypes.AfterHours)?.Contact;
                    LogInfo($"Holder for AfterHoursContactPhone: {contact.ToJson()}");
                    return GetDefaultPhone(contact);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Agent Email
        /// </summary>
        public string AgentEmail
        {
            get
            {
                var holder = GetAgent()?.Contact?.EmailAddress;
                LogInfo($"Holder for AgentEmail: {holder.ToJson()}");
                return GetAgent()?.Contact?.EmailAddress;
            }
        }

        /// <summary>
        /// Agent Name
        /// </summary>
        public string AgentName
        {
            get
            {
                var holder = GetAgent()?.Contact?.FullName;
                LogInfo($"Holder for AgentName: {holder.ToJson()}");
                return GetAgent()?.Contact?.FullName;
            }
        }

        /// <summary>
        /// Agent Phone
        /// </summary>
        public string AgentPhone
        {
            get
            {
                try
                {
                    var contact = GetAgent()?.Contact;
                    LogInfo($"Holder for AgentPhone: {contact.ToJson()}");
                    return GetDefaultPhone(contact);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Initial Call Date
        /// </summary>
        public DateTime? ArrivalDate
        {
            get
            {
                var holder = GetJobDate(JobDateTypes.InitialOnSiteArrival)?.Date;
                LogInfo($"Holder for ArrivalDate: {holder.ToJson()}");
                return GetJobDate(JobDateTypes.InitialOnSiteArrival)?.Date;
            }
        }

        /// <summary>
        /// Call Received By
        /// </summary>
        public string CallReceivedBy
        {
            get
            {
                var holder = GetJournalNote(JournalNoteTypes.InitialCall)?.Author;
                LogInfo($"Holder for CallReceivedBy: {holder.ToJson()}");
                return GetJournalNote(JournalNoteTypes.InitialCall)?.Author;
            }
        }

        /// <summary>
        /// Call Received Date
        /// </summary>
        public DateTime? CallReceivedDate
        {
            get
            {
                var holder = GetJournalNote(JournalNoteTypes.InitialCall)?.ActionDate;
                LogInfo($"Holder for CallReceivedD: {holder.ToJson()}");
                return GetJournalNote(JournalNoteTypes.InitialCall)?.ActionDate;
            }
        }

        /// <summary>
        /// Call Received Time
        /// </summary>
        public DateTime? CallReceivedTime
        {
            get
            {
                var holder = GetJournalNote(JournalNoteTypes.InitialCall)?.ActionDate;
                LogInfo($"Holder for CallReceivedT: {holder.ToJson()}");
                return GetJournalNote(JournalNoteTypes.InitialCall)?.ActionDate;
            }
        }

        /// <summary>
        /// Caller Email
        /// </summary>
        public string CallerEmail
        {
            get
            {
                var holder = GetCaller()?.Contact?.EmailAddress;
                LogInfo($"Holder for CallerEmail: {holder.ToJson()}");
                return GetCaller()?.Contact?.EmailAddress;
            }
        }

        /// <summary>
        /// Caller Company
        /// </summary>
        public string CallerCompany
        {
            get
            {
                var holder = GetCaller()?.Contact?.Business?.Name;
                LogInfo($"Holder for CallerCompany: {holder.ToJson()}");
                return GetCaller()?.Contact?.Business?.Name;
            }
        }

        /// <summary>
        /// Caller Name
        /// </summary>
        public string CallerName
        {
            get
            {
                var holder = GetCaller()?.Contact?.FullName;
                LogInfo($"Holder for CallerName: {holder.ToJson()}");
                return GetCaller()?.Contact?.FullName;
            }
        }

        /// <summary>
        /// Caller Phone
        /// </summary>
        public string CallerPhone
        {
            get
            {
                try
                {
                    var contact = GetCaller()?.Contact;
                    LogInfo($"Holder for CallerPhone: {contact.ToJson()}");
                    return GetDefaultPhone(contact);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Claim Number
        /// </summary>
        public string ClaimNumber
        {
            get
            {
                LogInfo($"Holder for ClaimNumber: {_job.ToJson()}");
                return _job.InsuranceClaimNumber;
            }
        }

        /// <summary>
        /// Deductible Amount
        /// </summary>
        public Decimal? DeductibleAmountToCollect
        {
            get
            {
                LogInfo($"Holder for  DeductibleAmountToCollect: {_job.ToJson()}");
                return _job.DeductibleAmountDue;
            }
        }

        /// <summary>
        /// Emergency Services Amount to Collect
        /// </summary>
        public decimal? ESAToCollect
        {
            get
            {
                return null; // ESA To Collect does not exist
            }
        }

        /// <summary>
        /// Floors Affected
        /// </summary>
        public int? FloorsAffected
        {
            get
            {
                LogInfo($"Holder for  FloorsAffected: {_job.ToJson()}");
                return _job?.LevelsAffected;
            }
        }

        /// <summary>
        /// Franchise Name
        /// </summary>
        public string FullFranchiseName
        {
            get
            {
                try
                {
                    var servproName = _franchise?.Name?.ToLower().StartsWith("servpro of") == true ? _franchise?.Name[10..].Trim() : _franchise?.Name;
                    return $"SERVPRO of {servproName}";
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Franchise Name
        /// </summary>
        public string FranchiseName
        {
            get
            {
                try
                {
                    var servproName = _franchise?.Name?.ToLower().StartsWith("servpro of") == true ? _franchise?.Name[10..].Trim() : _franchise?.Name;

                    return servproName;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Short Franchise Name
        /// </summary>
        public string ShortFranchiseName
        {
            get
            {
                try
                {
                    var servproName = _franchise?.Name?.ToLower().StartsWith("servpro of") == true ? _franchise?.Name[10..].Trim() : _franchise?.Name;

                    return servproName;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Franchise Number
        /// </summary>
        public string FranchiseNumber
        {
            get
            {
                LogInfo($"Holder for FranchiseNumber: {_franchise.ToJson()}");
                return _franchise?.FranchiseNumber.ToString();
            }
        }

        /// <summary>
        /// Franchise Legal Name
        /// </summary>
        public string FranchiseLegalName
        {
            get
            {
                LogInfo($"Holder for FranchiseLegalName: {_franchise.ToJson()}");
                return _franchise?.LegalName;
            }
        }

        /// <summary>
        /// Franchise Operating Entity Name
        /// </summary>
        public string FranchiseOperatingEntityName
        {
            get
            {
                LogInfo($"Holder for FranchiseOperatingEntityName: {_franchise.ToJson()}");
                return _franchise?.Name; // No Operating Entity Name Exists In This Dto
            }
        }

        /// <summary>
        /// Franchise Operating Entity Name or Franchise Legal Name
        /// </summary>
        public string OperatingEntityFranchiseLegalName
        {
            get
            {
                LogInfo($"Holder for OperatingEntityFranchiseLegalName: {_franchise.ToJson()}");
                return _franchise?.LegalName;
            }
        }

        /// <summary>
        /// Franchise Address
        /// </summary>
        public string FranchiseAddress
        {
            get
            {
                LogInfo($"Holder for FranchiseAddress: {_franchise.ToJson()}");
                return _franchise?.PrimaryAddress?.Address1;
            }
        }

        /// <summary>
        /// Franchise City
        /// </summary>
        public string FranchiseCity
        {
            get
            {
                LogInfo($"Holder for FranchiseCity: {_franchise.ToJson()}");
                return _franchise?.PrimaryAddress?.City;
            }
        }

        /// <summary>
        /// Franchise Phone
        /// </summary>
        public string FranchisePhoneNumber
        {
            get
            {
                var phone = _franchise?.PrimaryPhone;
                if (phone == null)
                    return null;

                var phoneNumber = phone.Number;
                if (!string.IsNullOrEmpty(phone.Extension))
                {
                    phoneNumber += " Ext. " + phone.Extension;
                }

                return phoneNumber;
            }
        }

        /// <summary>
        /// Franchise State
        /// </summary>
        public string FranchiseState
        {
            get
            {
                LogInfo($"Holder for FranchiseState: {_franchise.ToJson()}");
                return _franchise?.PrimaryAddress?.State;
            }
        }

        /// <summary>
        /// Franchise Zip Code
        /// </summary>
        public string FranchiseZipCode
        {
            get
            {
                LogInfo($"Holder for FranchiseZipCode: {_franchise.ToJson()}");
                return _franchise?.PrimaryAddress?.PostalCode;
            }
        }

        /// <summary>
        /// Initial Call Date
        /// </summary>
        public DateTime? InitialCallDate
        {
            get
            {
                var holder = GetJournalNote(JournalNoteTypes.InitialCall);
                LogInfo($"Holder for InitialCallDa: {holder.ToJson()}");
                return GetJournalNote(JournalNoteTypes.InitialCall)?.ActionDate;
            }
        }

        /// <summary>
        /// Initial On Site Arrival Date
        /// </summary>
        public DateTime? InitialOnSiteArrival
        {
            get
            {
                var holder = GetJobDate(JobDateTypes.InitialOnSiteArrival);
                LogInfo($"Holder for InitialOnSite: {holder.ToJson()}");
                return GetJobDate(JobDateTypes.InitialOnSiteArrival)?.Date;
            }
        }

        /// <summary>
        /// Insurance Company
        /// </summary>
        public string InsuranceCompany
        {
            get
            {
                var insuranceCarrier = _insuranceClients.FirstOrDefault(x => x.Id == _job.InsuranceCarrierId);
                return insuranceCarrier?.Name ?? String.Empty;
            }
        }

        /// <summary>
        /// Insured Company
        /// </summary>
        public string InsuredCompany
        {
            get
            {
                var contact = GetCustomer()?.Contact;
                LogInfo($"Holder for InsuredCompany: {contact.ToJson()}");
                return contact?.Business?.PreferredName ?? contact?.Business?.Name;
            }
        }

        /// <summary>
        /// Insured Email
        /// </summary>
        public string InsuredEmail
        {
            get
            {
                var contact = GetCustomer()?.Contact;
                LogInfo($"Holder for InsuredEmail: {contact.ToJson()}");
                return contact?.EmailAddress;
            }
        }

        /// <summary>
        /// Insured Name
        /// </summary>
        public string InsuredName
        {
            get
            {
                var contact = GetCustomer()?.Contact;
                LogInfo($"Holder for InsuredName: {contact.ToJson()}");
                return contact?.FullName;
            }
        }
        /// <summary>
        /// Insured Phone
        /// </summary>
        public string InsuredPhone
        {
            get
            {
                var contact = GetCustomer()?.Contact;
                LogInfo($"Holder for InsuredPhone: {contact.ToJson()}");

                return contact?.PhoneNumbers?.FirstOrDefault()?.PhoneNumber;
            }
        }

        /// <summary>
        /// Insured Phone2
        /// </summary>
        public string InsuredPhone2
        {
            get
            {
                var contact = GetCustomer()?.Contact;
                LogInfo($"Holder for InsuredPhone2: {contact.ToJson()}");

                return contact?.PhoneNumbers?.Skip(1).FirstOrDefault()?.PhoneNumber;
            }
        }

        /// <summary>
        /// Job Number
        /// </summary>
        public int? JobNumber
        {
            get
            {
                LogInfo($"Job: {_job}");
                return _job.CorporateJobNumber;
            }
        }

        /// <summary>
        /// Loss Address
        /// </summary>
        public string LossAddress
        {
            get
            {
                LogInfo($"Job: {_job}");
                return _job.LossAddress?.Address1;
            }
        }

        /// <summary>
        /// Loss Address Directions
        /// </summary>
        public string LossAddressDirections
        {
            get
            {
                LogInfo($"Job: {_job}");
                return null;
            }
        }

        /// <summary>
        /// Loss Cause
        /// </summary>
        public string LossCause
        {
            get
            {
                try
                {
                    return _lookups.CausesOfLoss.FirstOrDefault(x => x.Id == _job.CauseOfLossId)?.Name;
                }
                catch
                {
                    return null;
                }
            }
        }
        /// <summary>
        /// Loss City
        /// </summary>
        public string LossCity
        {
            get
            {
                LogInfo($"Job: {_job}");
                return _job.LossAddress?.City;
            }
        }

        /// <summary>
        /// Loss Occured Date
        /// </summary>
        public DateTime? LossOccurredDate
        {
            get
            {
                var date = GetJobDate(JobDateTypes.LossOccurred);
                LogInfo($"Looking for LossOccuredDate: {date}");
                return date?.Date;
            }
        }

        /// <summary>
        /// Loss Occured Date
        /// </summary>
        public DateTime? LossOccurredTime
        {
            get
            {
                return GetJobDate(JobDateTypes.LossOccurred)?.Date;
            }
        }

        /// <summary>
        /// Loss Notes
        /// </summary>
        public string LossNotes
        {
            get
            {
                var holder = GetJournalNote(JournalNoteTypes.InitialCall);
                LogInfo($"Job: {holder}");
                return holder?.Note;
            }
        }

        /// <summary>
        /// Loss State
        /// </summary>
        public string LossState
        {
            get
            {
                LogInfo($"Holder for LossState: {_job.ToJson()}");
                return _job.LossAddress?.State?.StateName;
            }
        }

        /// <summary>
        /// Loss Type
        /// </summary>
        public string LossType
        {
            get
            {
                LogInfo($"Looking for LossType: {_job.LossTypeId}");
                return _lookups.LossTypes?.FirstOrDefault(x => x.Id == _job.LossTypeId)?.Name;
            }
        }

        /// <summary>
        /// Loss Zip Code
        /// </summary>
        public string LossZipCode
        {
            get
            {
                LogInfo($"Looking for LossZipCode: {_job}");
                return _job.LossAddress?.PostalCode;
            }
        }

        /// <summary>
        /// Occupant Home Phone
        /// </summary>
        public string OccupantHomePhone
        {
            get
            {
                try
                {
                    var contactMap = GetOccupant();
                    LogInfo($"Looking for OccupantHomePhone: {contactMap}");
                    var phone = GetPhoneByPerson(contactMap?.Contact, PhoneType.Home);
                    return GetPhoneByNumber(phone);
                }
                catch
                {
                    return null;
                }
            }
        }
        /// <summary>
        /// Occupant Other Phone
        /// </summary>
        public string OccupantOtherPhone
        {
            get
            {
                try
                {
                    var contactMap = GetOccupant();
                    LogInfo($"Looking for OccupantHomePhone: {OccupantOtherPhone}");
                    var phone = GetPhoneByPerson(contactMap?.Contact, PhoneType.Other);
                    return GetPhoneByNumber(phone);
                }
                catch
                {
                    return null;
                }
            }
        }
        /// <summary>
        /// Occupant Work Phone
        /// </summary>
        public string OccupantWorkPhone
        {
            get
            {
                try
                {
                    var contactMap = GetOccupant();
                    LogInfo($"Looking for OccupantWorkPhone: {contactMap}");
                    var phone = GetPhoneByPerson(contactMap?.Contact, PhoneType.Office);
                    return GetPhoneByNumber(phone);
                }
                catch
                {
                    return null;
                }
            }
        }


        /// <summary>
        /// Production Manager
        /// </summary>
        public string ProductionManager
        {
            get
            {
                var contactMap = GetProductionManager();
                LogInfo($"Looking for ProductionManager: {contactMap}");
                return contactMap?.Contact?.FullName;
            }
        }

        /// <summary>
        /// Policy Number
        /// </summary>
        public string PolicyNumber
        {
            get
            {
                LogInfo($"Looking for PolicyNumber: {_job}");
                return _job.InsurancePolicyNumber;
            }
        }

        /// <summary>
        /// Rooms Affected
        /// </summary>
        public int? RoomsAffected 
        {
            get {
                LogInfo($"Looking for RoomsAffected: {_job}");
                return _job.RoomsAffected;
                }
            }

        /// <summary>
        /// Scheduling Service Date
        /// </summary>
        public DateTime? ServiceSchedulingDate 
        {
            get {
                var date = GetJobDate(JobDateTypes.EmergencyServicesScheduled);
                LogInfo($"Looking for ServiceSchedulingDate: {date}");
                return date?.Date;
                }
            }

        /// <summary>
        /// Scheduling Service Time
        /// </summary>
        public DateTime? ServiceSchedulingTime 
        {
            get {
                var date = GetJobDate(JobDateTypes.EmergencyServicesScheduled);
                LogInfo($"Looking for ServiceSchedulingTime: {date}");
                return date?.Date;
                }
            }

        /// <summary>
        /// Property Type
        /// </summary>
        public string PropertyType
        {
            get
            {
                return _lookups.PropertyTypes?
                    .Where(x => x.Id == _job.PropertyTypeId)
                    .Select(x => x.Name)
                    .FirstOrDefault();
            }
        }

        /// <summary>
        /// Year Structure Built
        /// </summary>
        public int? YearBuilt
        {
            get
            {
                try
                {
                    var year = _job.YearStructureBuilt;
                    if (year.HasValue && year.Value > 0)
                    {
                        return year.Value;
                    }

                    return null;
                }
                catch
                {
                    return null;
                }
            }
        }

        public DateTime TodaysDate => DateTime.UtcNow;

        #endregion

        #region Helper Methods

        private JobContactMap GetAdjuster()
        {
            return GetContact(JobContactTypes.Adjuster);
        }

        private JobContactMap GetAgent()
        {
            return GetContact(JobContactTypes.Agent);
        }

        public JobContactMap GetCustomer()
        {
            return GetContact(JobContactTypes.Customer);
        }

        private JobContactMap GetOccupant()
        {
            return GetContact(JobContactTypes.Occupant);
        }

        private JobContactMap GetProductionManager()
        {
            return GetContact(JobContactTypes.ProductionManagerCrewChief);
        }

        private JobContactMap GetCaller()
        {
            return GetContact(JobContactTypes.Caller);
        }

        private JobContactMap GetContact(Guid contactType)
        {
            return _job.JobContacts.FirstOrDefault(x => x.JobContactTypeId == contactType);
        }

        private JobDate GetJobDate(Guid jobDateType)
        {
            LogInfo($"Holder for GetJobDate: {jobDateType}");
            JobDate jobDate = _job.JobDates?.FirstOrDefault(x => x.JobDateTypeId == jobDateType);
            
            return jobDate != null ? new JobDate(jobDate.JobDateTypeId, DateTime.SpecifyKind(jobDate.Date, DateTimeKind.Utc)) : null;
        }

        private JournalNote GetJournalNote(Guid journalNoteType)
        {
            return _job.JournalNotes?.FirstOrDefault(x => x.TypeId == journalNoteType);
        }

        private static string GetDefaultPhone(Contact contact)
        {
            var phone = GetPhoneByPerson(contact);

            return GetPhoneByNumber(phone);
        }

        private static Phone GetPhoneByPerson(Contact contact)
        {
            return contact?.MainPhone;
        }

        private static Phone GetPhoneByPerson(Contact contact, PhoneType phoneType)
        {
            return contact?.PhoneNumbers?.FirstOrDefault(x => x.PhoneType == phoneType);
        }

        private static string GetPhoneByNumber(Phone phone)
        {
            if (phone == null)
                return null;

            var number = $"{Convert.ToInt64(phone.PhoneNumber):(###) ###-####}";

            var phoneNumber = phone.PhoneType switch
            {
                PhoneType.Mobile => $"C: {number}",
                PhoneType.Fax => $"F: {number}",
                PhoneType.Home => $"H: {number}",
                PhoneType.Other => $"O: {number}",
                PhoneType.Office => $"W: {number}",
                _ => null
            };

            if (!string.IsNullOrEmpty(phone.PhoneExtension))
            {
                phoneNumber += " Ext. " + phone.PhoneExtension;
            }

            return phoneNumber;
        }

        #endregion
    }
}