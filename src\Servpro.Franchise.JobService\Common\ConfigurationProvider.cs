﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;

using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Common
{
    public interface IProvideConfiguration
    {
        IConfigurationSection GetSection(string key);
        IEnumerable<IConfigurationSection> GetChildren();
        IChangeToken GetReloadToken();
        bool GetValue(string key, bool defaultValue);
        T GetValue<T>(string key);
    }

    public class ConfigurationProvider : IProvideConfiguration
    {
        private readonly IConfiguration _configuration;

        public ConfigurationProvider(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IConfigurationSection GetSection(string key)
        {
            return _configuration.GetSection(key);
        }

        public IEnumerable<IConfigurationSection> GetChildren()
        {
            return _configuration.GetChildren();
        }

        public IChangeToken GetReloadToken()
        {
            return _configuration.GetReloadToken();
        }

        public string this[string key]
        {
            get => _configuration[key];
            set => _configuration[key] = value;
        }

        public bool GetValue(string key, bool defaultValue)
        {
            return _configuration.GetValue(key, defaultValue);
        }

        public T GetValue<T>(string key)
        {
            return _configuration.GetValue<T>(key);
        }
    }
}