﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;

using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.FacilityStructureType
{
    public class GetFacilityAndStructureType
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Dto(Guid? structureTypeId, int? facilityTypeId)
            {
                FacilityTypeId = facilityTypeId;
                StructureTypeId = structureTypeId;
            }

            public int? FacilityTypeId { get; set; }
            public Guid? StructureTypeId { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _jobDataContext;

            public Handler(JobReadOnlyDataContext jobDataContext)
            {
                _jobDataContext = jobDataContext;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _jobDataContext.Jobs.FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");
                
                return new Dto(job.StructureTypeId, job.FacilityTypeId);
            }
        }
    }
}
