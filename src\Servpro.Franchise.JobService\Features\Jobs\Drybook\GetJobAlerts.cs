﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Data;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using ServproTask = Servpro.Franchise.JobService.Models.Drybook.Task;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetJobAlerts
    {
        public class Dto
        {
            public Guid TaskId { get; set; }
            public Guid? JobId { get; set; }            
            public string Subject { get; set; }
            public DateTime CreatedDate { get; set; }            
            public Guid? ZoneId { get; set; }
            public Guid TaskStatusId { get; set; }            
            public Guid TaskTypeId { get; set; }
        }

        public class Query : IRequest<ICollection<Dto>>
        {
            public Guid? JobId { get; set; }
            public List<Guid> TaskStatusIds { get; set; }
        }

        public class QueryValidator : AbstractValidator<Query>
        {
            public QueryValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.TaskStatusIds).NotEmpty();                
            }
        }
        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            } 

            public async Task<ICollection<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var tasks = await _context.Tasks
                    .Where(x => request.TaskStatusIds.Contains(x.TaskStatusId) &&
                                x.JobId == request.JobId)
                    .ToListAsync(cancellationToken);

                var activeAlerts = Map(tasks);
                return activeAlerts;                                    
            }

            private ICollection<Dto> Map(List<ServproTask> task)
                => task.Select(t => new Dto
                {
                    TaskId = t.Id,
                    JobId = t.JobId,                    
                    Subject = t.Subject,
                    CreatedDate = t.CreatedDate,                    
                    ZoneId = t.ZoneId,
                    TaskStatusId = t.TaskStatusId,
                    TaskTypeId = t.TaskTypeId
                }).ToList();
        }
    }
}