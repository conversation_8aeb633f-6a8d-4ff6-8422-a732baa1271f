﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Features.WipBoards;

namespace Servpro.Franchise.JobService.Features.Jobs.LossAddress
{
    public class GetJobLossAddressGeoLocation
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Dto(Guid jobId, decimal? latitude, decimal? longitude)
            {
                JobId = jobId;
                Latitude = latitude;
                Longitude = longitude;
            }
            public Guid JobId { get; }
            public decimal? Latitude { get; set; }
            public decimal? Longitude { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _jobDataContext;

            public Handler(JobReadOnlyDataContext jobDataContext)
            {
                _jobDataContext = jobDataContext;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _jobDataContext.Jobs.FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return new Dto(
                    job.Id,
                    job.LossAddress?.Latitude,
                    job.LossAddress?.Logitude);
            }
        }
    }
}
