﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GetDocument
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Query(Guid id, Guid franchiseSetId)
            {
                JobId = id;
                FranchiseSetId = franchiseSetId;
            }
            public Guid JobId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto 
        {
            public Guid Id { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public string Name { get; set; }
            public bool IsForUpload { get; set; }
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public bool UploadedSuccessfully { get; set; }
        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext db) => _db = db;

            public async Task<ICollection<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var mediaMetaData = await _db.MediaMetadata.Where(x => x.JobId == request.JobId &&
                                                                 x.FranchiseSetId == request.FranchiseSetId &&
                                                                 x.MediaTypeId == MediaTypes.Document && 
                                                                 !x.IsDeleted && !x.FormTemplateId.HasValue && x.MediaPath != "ProjectRevenuePlaceholder").ToListAsync(cancellationToken);
                
                return mediaMetaData.Select(Map).ToList();
            }

            private Dto Map(MediaMetadata media)
                => new Dto
                {
                    Id = media.Id,
                    JobId = media.JobId,
                    FranchiseSetId = media.FranchiseSetId,
                    IsForUpload = media.IsForUpload,
                    Name = media.Name,
                    ArtifactTypeId = media.ArtifactTypeId,
                    UploadedSuccessfully = media.UploadedSuccessfully
                        || (!string.IsNullOrEmpty(media.BucketName) && !string.IsNullOrEmpty(media.MediaPath))
                };
        }
    }
}