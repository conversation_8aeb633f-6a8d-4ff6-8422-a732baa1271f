﻿using FluentValidation;
using FluentValidation.Results;
using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Contacts;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads.Contacts;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ResourceNotFoundException =
    Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions.ResourceNotFoundException;

namespace Servpro.Franchise.JobService.Features.Jobs.JobContacts
{
    public class SaveJobContact
    {
        public class Command : IRequest<bool>
        {
            public Guid JobId { get; set; }
            public Guid ContactId { get; set; }
            public Guid JobContactTypeId { get; set; }
            public bool IsReplacement { get; set; }
            public ContactDto Contact { get; set; }

            public class ContactDto
            {
                public ContactDto()
                {
                }

                public ContactDto(string firstName,
                    string lastName,
                    ICollection<PhoneDto> phones,
                    AddressDto address,
                    string emailAddress,
                    Guid franchiseSetId,
                    string createdBy,
                    Guid id)
                {
                    if (franchiseSetId == Guid.Empty)
                        throw new ArgumentOutOfRangeException(nameof(franchiseSetId));

                    Id = id;
                    FirstName = firstName;
                    LastName = lastName;
                    PhoneNumbers = phones;
                    Address = address;
                    FranchiseSetId = franchiseSetId;
                    EmailAddress = emailAddress;
                    CreatedBy = createdBy;
                }

                public Guid Id { get; set; }
                public string FirstName { get; set; }
                public string MiddleName { get; set; }
                public string Salutation { get; set; }
                public string Suffix { get; set; }
                public string CreatedBy { get; set; }
                public string LastName { get; set; }
                public string BusinessName { get; set; }
                public Guid? BusinessId { get; set; }
                public string EmailAddress { get; set; }
                public Guid FranchiseSetId { get; set; }

                public ICollection<PhoneDto> PhoneNumbers { get; set; }
                public AddressDto Address { get; set; }
            }

            public class AddressDto
            {
                public AddressDto() { }

                public AddressDto(
                    string address1,
                    string city,
                    string postalCode,
                    StateDto state,
                    AddressType addressType)
                {
                    Address1 = address1;
                    City = city;
                    PostalCode = postalCode;
                    AddressType = addressType;
                    State = state;
                }

                public string Address1 { get; set; }
                public string Address2 { get; set; }
                public string City { get; set; }
                public string PostalCode { get; set; }
                public StateDto State { get; set; }
                public decimal? Latitude { get; set; }
                public decimal? Longitude { get; set; }
                public AddressType AddressType { get; set; }
            }

            public class StateDto
            {
                public StateDto() { }
                public StateDto(Guid stateId, string stateName, Guid countryId, string countryName, string countyName)
                {
                    StateId = stateId;
                    StateName = stateName;
                    CountryId = countryId;
                    CountryName = countryName;
                    CountyName = countyName;
                }
                public Guid StateId { get; set; }
                public string StateName { get; set; }
                public Guid CountryId { get; set; }
                public string CountryName { get; set; }
                public string CountyName { get; set; }
                public string StateAbbreviation { get; set; }
            }

            public class PhoneDto
            {
                public PhoneDto() { }
                public PhoneDto(
                    string phoneNumber,
                    PhoneType phoneType,
                    string extension = null)
                {
                    PhoneNumber = phoneNumber;
                    PhoneType = phoneType;
                    Extension = extension;
                }

                public string PhoneNumber { get; set; }
                public string Extension { get; set; }
                public PhoneType PhoneType { get; set; }
            }

            public class EmailDto
            {
                public EmailDto() { }

                public EmailDto(
                    Guid id,
                    string address,
                    Guid emailAddressTypeId,
                    int sequenceNumber,
                    bool isUsedForNotifications)
                {
                    Id = id;
                    Address = address;
                    EmailAddressTypeId = emailAddressTypeId;
                    SequenceNumber = sequenceNumber;
                    IsUsedForNotifications = isUsedForNotifications;
                }

                public Guid Id { get; set; }
                public string Address { get; set; }
                public Guid EmailAddressTypeId { get; set; }
                public int SequenceNumber { get; set; }
                public bool IsUsedForNotifications { get; set; }
            }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.ContactId).NotEmpty();
                RuleFor(x => x.JobContactTypeId).NotEmpty();
                RuleFor(x => x.Contact).NotNull();

                When(x => x.Contact != null, () =>
                {
                    RuleFor(x => x.Contact.FirstName)
                        .NotEmpty().WithMessage("Contact.FirstName is required.");
                    RuleFor(x => x.Contact.LastName)
                        .NotEmpty().WithMessage("Contact.LastName is required.");
                });
            }
        }

        public class Handler : IRequestHandler<Command, bool>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILogger<Handler> _logger;

            private readonly List<Guid> BusinessJobContactTypes = new List<Guid>
            {
                JobContactTypes.Adjuster,
                JobContactTypes.IndependentAdjuster,
                JobContactTypes.InsuranceAdjuster,
                JobContactTypes.PublicAdjuster
            };

            public Handler(JobDataContext context, IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor, ILogger<Handler> logger)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _logger = logger;
            }

            public async Task<bool> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler request: {request}", request);

                ValidateContactPayload(request);

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var job = await LoadJobAsync(request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job '{request.JobId}' was not found.");

                var userInfo = _userInfoAccessor.GetUserInfo();

                if (request.IsReplacement)
                {
                    var jobContacts = job.JobContacts
                        .Where(jc => jc.JobContactTypeId == request.JobContactTypeId).ToList();

                    if (jobContacts.Any())
                    {
                        jobContacts.ForEach(c =>
                        {
                            job.JobContacts.Remove(c);
                        });
                    }
                }

                var created = false;

                var contact = await _context.Contacts
                    .FirstOrDefaultAsync(c => c.Id == request.ContactId, cancellationToken);

                if (contact is null)
                {
                    contact = MapModel(request.Contact);
                    created = true;
                }
                else
                {
                    await UpdateExistingContactAndRaiseEventAsync(contact, request.Contact, job, correlationId, userInfo.Username);
                }

                var existingMap = job.JobContacts
                    .FirstOrDefault(jc =>
                        jc.ContactId == request.ContactId &&
                        jc.JobContactTypeId == request.JobContactTypeId);

                if (existingMap == null)
                {
                    var jobContactMap = new JobContactMap()
                    {
                        Id = Guid.NewGuid(),
                        Contact = contact,
                        ContactId = request.ContactId,
                        JobContactTypeId = request.JobContactTypeId,
                        TitleId = request.JobContactTypeId,
                        JobId = request.JobId,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        IsBusinessContact = BusinessJobContactTypes.Contains(request.JobContactTypeId)
                    };

                    job.JobContacts.Add(jobContactMap);

                    await RaiseEvents(request, job, jobContactMap.Id);
                }

                await SaveFirstNoticeActivity(request);

                await _context.SaveChangesAsync(cancellationToken);

                return created;
            }

            private static void ValidateContactPayload(Command request)
            {
                if (request.Contact == null || IsContactEmpty(request.Contact))
                {
                    throw new ValidationException(new[]
                    {
                        new ValidationFailure("contact", "Contact information must not be empty.")
                    });
                }

                if (request.ContactId != request.Contact.Id)
                {
                    throw new ValidationException(new[]
                    {
                        new ValidationFailure(
                            "contactId",
                            $"Bad request: 'ContactId' must match 'Contact.Id' in the payload. " +
                            $"Received ContactId={request.ContactId}, Contact.Id={request.Contact.Id}.")
                    });
                }
            }

            private static bool IsContactEmpty(Command.ContactDto contact)
            {
                return string.IsNullOrWhiteSpace(contact.FirstName) &&
                       string.IsNullOrWhiteSpace(contact.LastName) &&
                       string.IsNullOrWhiteSpace(contact.EmailAddress) &&
                       (contact.PhoneNumbers == null || !contact.PhoneNumbers.Any()) &&
                       contact.Address == null &&
                       contact.FranchiseSetId == Guid.Empty;
            }

            private async Task<Job> LoadJobAsync(Guid jobId, CancellationToken ct)
            {
                var job = await _context.Jobs
                    .Include(x => x.JobContacts).ThenInclude(x => x.Contact)
                    .Include(x => x.Customer).ThenInclude(c => c.Business)
                    .FirstOrDefaultAsync(j => j.Id == jobId, ct);

                if (job == null)
                    throw new ResourceNotFoundException($"Job '{jobId}' was not found.");

                return job;
            }

            private async Task UpdateExistingContactAndRaiseEventAsync(
             Models.Contact entity,
             Command.ContactDto dto,
             Job job,
             Guid correlationId,
             string username)
            {
                entity.FirstName = dto.FirstName;
                entity.LastName = dto.LastName;
                entity.EmailAddress = dto.EmailAddress;
                entity.PhoneNumbers = dto.PhoneNumbers?.Select(MapModel).ToList();
                entity.Address = dto.Address != null ? MapModel(dto.Address) : null;

                var fsId = ResolveFranchiseSetId(dto, job);

                var updatedDto = new ContactUpdatedEvent.ContactUpdatedDto(
                    dto.FirstName,
                    dto.LastName,
                    dto.PhoneNumbers?.Select(MapToUpdatedPhoneDto).ToList() ?? new List<ContactUpdatedEvent.PhoneDto>(),
                    MapToUpdatedAddressDto(dto.Address),
                    dto.EmailAddress != null
                        ? new ContactUpdatedEvent.EmailDto
                        {
                            Id = dto.Id,
                            Address = dto.EmailAddress,
                            EmailAddressTypeId = Guid.Empty,
                            SequenceNumber = 1,
                            IsUsedForNotifications = true
                        }
                        : null,
                    fsId,
                    dto.CreatedBy,
                    dto.Id,
                    entity.CreatedDate
                );

                var evt = new ContactUpdatedEvent(updatedDto, correlationId);
                await GenerateOutboxMessage(evt.ToJson(), nameof(ContactUpdatedEvent), correlationId, username);
            }

            private static Guid ResolveFranchiseSetId(Command.ContactDto contact, Job job) =>
            contact.FranchiseSetId != Guid.Empty ? contact.FranchiseSetId : job.FranchiseSetId;


            private async Task RaiseEvents(Command command, Job job, Guid contactMapId)
            {
                var user = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var contactDto = Map(command.Contact, job.FranchiseSetId);
                var eventDto = new ContactAssociatedEvent.ContactAssociatedDto(
                    contactMapId, command.JobId, contactDto,
                    command.JobContactTypeId, command.JobContactTypeId)
                {
                    IsBusinessContact = BusinessJobContactTypes.Contains(command.JobContactTypeId),
                    CreatedBy = user.Username
                };
                var generatedEvent = new ContactAssociatedEvent(eventDto, correlationId);
                await GenerateOutboxMessage(generatedEvent.ToJson(), nameof(ContactAssociatedEvent),
                    generatedEvent.CorrelationId, user.Username);

                var projectKeyFieldsUpdated = new ProjectKeyFieldsUpdatedEvent.ProjectKeyFieldsUpdatedDto
                {
                    FranchiseId = job.FranchiseId,
                    FranchiseSetId = job.FranchiseSetId,
                    JobId = job.Id,
                    JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)job.JobProgress,
                    MentorId = job.MentorId,
                    ProjectNumber = job.ProjectNumber,
                    PropertyTypeId = job.PropertyTypeId,
                    UserId = user.Id,
                    Username = user.Username,
                    BusinessName = job.BusinessName,
                    LossTypeId = job.LossTypeId,
                    Customer = new ProjectKeyFieldsUpdatedEvent.ContactDto
                    {
                        FirstName = job.Customer?.FirstName,
                        LastName = job.Customer?.LastName
                    }
                };
                var projectKeyFieldsUpdatedEvent = new ProjectKeyFieldsUpdatedEvent(projectKeyFieldsUpdated, correlationId);
                await GenerateOutboxMessage(projectKeyFieldsUpdatedEvent.ToJson(), nameof(ProjectKeyFieldsUpdatedEvent),
                    projectKeyFieldsUpdatedEvent.CorrelationId, user.Username);
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _context.OutboxMessages.AddAsync(newEvent);
            }

            private async Task SaveFirstNoticeActivity(Command request)
            {
                var user = _userInfoAccessor.GetUserInfo();

                _context.FirstNoticeActivity.Add(new FirstNoticeActivity
                {
                    ActivityTypeId = ActivityType.AddLeadContacts,
                    RevisedValue = $"{request.Contact.FirstName} {request.Contact.LastName}",
                    ModifiedBy = user.Username,
                    CreatedBy = user.Username,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    RecordSourceId = RecordSourceTypes.WorkCenter,
                    JobId = request.JobId,
                    Id = Guid.NewGuid()
                });
            }

            private Models.Contact MapModel(Command.ContactDto contact)
                => new Models.Contact
                {
                    Id = contact.Id,
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    EmailAddress = contact.EmailAddress,
                    PhoneNumbers = contact.PhoneNumbers != null ? contact.PhoneNumbers.Select(MapModel).ToList() : null,
                    Address = contact.Address != null ? MapModel(contact.Address) : null
                };

            private Models.Address MapModel(Command.AddressDto a)
                => new Models.Address(a.Address1, a.City, a.PostalCode, MapModel(a.State), (Models.AddressType)a.AddressType)
                {
                    Address2 = a.Address2,
                    Latitude = a.Latitude,
                    Logitude = a.Longitude
                };

            private Models.State MapModel(Command.StateDto s)
                => new Models.State(s.StateId, s.StateName, s.StateAbbreviation, s.CountryId, s.CountryName, s.CountyName);

            private Models.Phone MapModel(Command.PhoneDto phone)
                => new Models.Phone(phone.PhoneNumber, (Models.PhoneType)phone.PhoneType)
                {
                    Id = Guid.NewGuid(),
                    PhoneExtension = phone.Extension
                };

            private static ContactAssociatedEvent.ContactDto Map(Command.ContactDto contact, Guid franchiseSetId)
                => new ContactAssociatedEvent.ContactDto
                {
                    Id = contact.Id,
                    FirstName = contact.FirstName,
                    LastName = contact.LastName,
                    EmailAddress = contact.EmailAddress != null ? contact.EmailAddress : null,
                    PhoneNumbers = contact.PhoneNumbers?.Select(Map).ToList()
                       ?? new List<ContactAssociatedEvent.PhoneDto>(),
                    Address = contact.Address != null ? Map(contact.Address) : null,
                    FranchiseSetId = franchiseSetId
                };

            private static ContactAssociatedEvent.AddressDto Map(Command.AddressDto a)
            {
                if (a == null) return null;
                return new ContactAssociatedEvent.AddressDto(a.Address1, a.City, a.PostalCode, Map(a.State), (FranchiseSystems.Framework.Messaging.Events.Common.Enums.AddressType)a.AddressType)
                {
                    Address2 = a.Address2,
                    Latitude = a.Latitude,
                    Longitude = a.Longitude
                };
            }

            private static ContactAssociatedEvent.StateDto Map(Command.StateDto s)
                => new ContactAssociatedEvent.StateDto(s.StateId, s.StateName, s.CountryId, s.CountryName, s.CountyName);

            private static ContactAssociatedEvent.PhoneDto Map(Command.PhoneDto phone)
                => new ContactAssociatedEvent.PhoneDto(phone.PhoneNumber, (FranchiseSystems.Framework.Messaging.Events.Common.Enums.PhoneType)phone.PhoneType)
                {
                    PhoneExtension = phone.Extension
                };

            private static ContactUpdatedEvent.PhoneDto MapToUpdatedPhoneDto(Command.PhoneDto phone)
                => new ContactUpdatedEvent.PhoneDto(
                        phone.PhoneNumber,
                        (Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums.PhoneType)phone.PhoneType
                    )
                {
                    PhoneExtension = phone.Extension
                };

            private static ContactUpdatedEvent.AddressDto MapToUpdatedAddressDto(Command.AddressDto a)
            {
                if (a == null) return null;

                var state = a.State != null ? MapToUpdatedStateDto(a.State) : null;

                return new ContactUpdatedEvent.AddressDto(
                    a.Address1,
                    a.City,
                    a.PostalCode,
                    state,
                    (Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums.AddressType)a.AddressType)
                {
                    Address2 = a.Address2,
                    County = a.State?.CountyName ?? string.Empty,
                    CountryId = a.State?.CountryId ?? Guid.Empty
                };
            }

            private static ContactUpdatedEvent.StateDto MapToUpdatedStateDto(Command.StateDto s)
                => s == null ? null
                : new ContactUpdatedEvent.StateDto(s.StateId, s.StateName, s.CountryId, s.CountryName, s.CountyName)
                {
                    StateAbbreviation = s.StateAbbreviation
                };
        }

    }
}