﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.JobUpload;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class CorporateJobSyncCompleted
    {
        public class Event : CorporateJobSyncCompletedEvent, IRequest
        {
            public Event(CorporateJobSyncCompletedEvent.JobSyncCompletedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly IServiceProvider _serviceProvider;
            private readonly ILogger<CorporateJobSyncCompleted> _logger;
            private readonly IMediator _mediator;

            public Handler(IServiceProvider serviceProvider, ILogger<CorporateJobSyncCompleted> logger, IMediator mediator)
            {
                _serviceProvider = serviceProvider;
                _logger = logger;
                _mediator = mediator;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{incomingEvent} started", incomingEvent);
                using var serviceScope = _serviceProvider.CreateScope();

                var db = serviceScope.ServiceProvider.GetService<JobDataContext>();

                var job = await db.Jobs
                    .FirstOrDefaultAsync(j => j.Id == incomingEvent.JobUpload.JobId, cancellationToken: cancellationToken);

                if (job == null)
                {
                    _logger.LogWarning("JobId not found for {incomingEvent}", incomingEvent);
                    return Unit.Value;
                }

                if (job.CorporateJobNumber == null && incomingEvent.JobUpload.CorporateJobNumber != null)
                    job.CorporateJobNumber = incomingEvent.JobUpload.CorporateJobNumber;

                Guid jobUploadTypeId;
                switch (incomingEvent.JobUpload.UploadType)
                {
                    case CorporateJobSyncCompletedEvent.UploadType.Initial:
                        job.MostRecentInitialUploadCompleted = incomingEvent.JobUpload.UploadedDateTime;
                        jobUploadTypeId = JobUploadTypes.JobInitial;
                        break;
                    case CorporateJobSyncCompletedEvent.UploadType.Daily:
                        job.MostRecentDailyUploadCompleted = incomingEvent.JobUpload.UploadedDateTime;
                        jobUploadTypeId = JobUploadTypes.JobDaily;
                        break;
                    case CorporateJobSyncCompletedEvent.UploadType.Final:
                        job.MostRecentFinalUploadCompleted = incomingEvent.JobUpload.UploadedDateTime;
                        jobUploadTypeId = JobUploadTypes.JobFinal;
                        break;
                    default:
                        _logger.LogWarning("Invalid upload type received for {jobId}", incomingEvent.JobUpload.JobId);
                        return Unit.Value;
                }              

                var jobUploadLock = await db.JobUploadLocks
                    .FirstOrDefaultAsync(x => x.JobId == incomingEvent.JobUpload.JobId && x.JobUploadTypeId == jobUploadTypeId, cancellationToken: cancellationToken);

                if (jobUploadLock != null)
                {
                    db.JobUploadLocks.Remove(jobUploadLock);
                }

                await db.SaveChangesAsync(cancellationToken);

                var comRequest = new UpdateMediaUploadedToCorporate.Command(incomingEvent.JobUpload.JobId);
                var _jobID = await _mediator.Send(comRequest, cancellationToken);

                return Unit.Value;
            }

        }
    }
}