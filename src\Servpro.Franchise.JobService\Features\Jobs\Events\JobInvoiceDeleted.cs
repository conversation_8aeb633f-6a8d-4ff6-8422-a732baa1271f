﻿using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobInvoiceDeleted
    {
        public class Event : InvoiceDeletedEvent, IRequest
        {
            public Event(InvoiceDeletedDto invoiceDeleted, Guid correlationId) : base(invoiceDeleted, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly IServiceProvider _serviceProvider;
            private readonly ILogger<JobInvoiceDeleted> _logger;
            private readonly JobDataContext _context;

            public Handler(
                IServiceProvider serviceProvider,
                ILogger<JobInvoiceDeleted> logger,
                JobDataContext context)
            {
                _serviceProvider = serviceProvider;
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler began with {@incomingEvent}", incomingEvent);

                var invoice = incomingEvent.InvoiceDeleted;

                //Get job
                var media = await _context.MediaMetadata
                    .FirstOrDefaultAsync(q => q.JobId == invoice.JobId && q.JobInvoiceId == invoice.InvoiceId, cancellationToken: cancellationToken);
                if (media != null)
                {
                    media.IsDeleted = true;
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("For the Job: {jobId}, invoice {invoiceId} has been deleted", invoice.JobId, invoice.InvoiceId);
                }
                return Unit.Value;
            }
        }
    }
}
