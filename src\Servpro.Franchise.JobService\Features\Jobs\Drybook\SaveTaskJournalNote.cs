﻿using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class SaveTaskJournalNote
    {
        #region Command
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid TaskTypeId { get; set; }
            public string Subject { get; set; }
            public string Body { get; set; }
            public string Note { get; set; }
            public DateTime ActionDate { get; set; }
            public Guid JournalNoteVisibilityId { get; set; }
            public Guid JournalNoteTypeId { get; set; }
            public Guid JobTriStateQuestionId { get; set; }
            public Guid JournalNoteCategoryId { get; set; }
        }
        #endregion
        #region Validation
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.TaskTypeId).NotEmpty();
                RuleFor(x => x.ActionDate).NotEqual(DateTime.MinValue);
                RuleFor(x => x.JournalNoteTypeId).NotEmpty();
                RuleFor(x => x.JournalNoteVisibilityId).NotEmpty();
                RuleFor(x => x.JournalNoteCategoryId).NotEmpty();
                RuleFor(x => x.JobTriStateQuestionId).NotEmpty();
                RuleFor(x => x.Note).MinimumLength(0);
            }
        }
        #endregion
        #region Handler
        public class Handler : IRequestHandler<Command>
        {
            private readonly ILogger<Handler> _logger;
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(ILogger<Handler> logger,
                JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient,
                ISessionIdAccessor sessionIdAccessor)
            {
                _logger = logger;
                _context = context;
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Starting {method} , JobId: {jobid}", nameof(SaveTaskJournalNote), request?.JobId);
               
                var job = await _context.Jobs
                    .Include(x => x.JobLocks)
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var userInfo = _userInfo.GetUserInfo();

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var taskType = lookups.TaskTypes
                    .FirstOrDefault(x => x.Id == request.TaskTypeId);

                if(taskType is null)
                    throw new ValidationException(new List<ValidationFailure>
                    {
                        new ValidationFailure(nameof(request.TaskTypeId), "Invalid TaskType")
                    });

                var currentDateTime = DateTime.UtcNow;
                if (!IsActionDateValid(request.ActionDate, currentDateTime))
                    request.ActionDate = currentDateTime;

                var task = new Models.Drybook.Task
                {
                    JobId = request.JobId,
                    TaskPriorityId = TaskPriorities.Medium,
                    TaskTypeId = request.TaskTypeId,
                    TaskStatusId = taskType.ShouldCloseWhenDiaryEntryIsComplete && !string.IsNullOrWhiteSpace(request.Note)
                        ? TaskStatuses.Completed
                        : TaskStatuses.Active,
                    PercentComplete = taskType.ShouldCloseWhenDiaryEntryIsComplete && !string.IsNullOrWhiteSpace(request.Note)
                        ? 100
                        : 0,
                    JobTriStateQuestionId = request.JobTriStateQuestionId,
                    FranchiseSetId = userInfo.FranchiseSetId,
                    Subject = request.Subject,
                    Body = request.Body,
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.UtcNow,
                    JournalNotes = new List<JournalNote> { Map(request, userInfo) },
                };
                _context.Tasks.Add(task);

                //Publish Events
                _logger.LogInformation("Publish events {method} , JobId: {jobid}", nameof(SaveTaskJournalNote), request?.JobId);
            var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var events = GenerateEvents(correlationId, userInfo);
                await _context.OutboxMessages.AddRangeAsync(events, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);
                return Unit.Value;
            }

            private bool IsActionDateValid(DateTime actionDate, DateTime currentTime)
            {
                return actionDate < currentTime;
            }

            private IEnumerable<OutboxMessage> GenerateEvents(Guid correlationId, UserInfo user)
            {
                var taskCreatedEvents = GenerateTaskCreatedEvents(correlationId, user);
                var journalNoteCreatedEvents = GenerateJournalNoteCreatedEvents(correlationId, user);

                var events = taskCreatedEvents
                    .Concat(journalNoteCreatedEvents)
                    .ToList();

                return events;
            }

            #region Methods for generating TaskCreatedEvents
            private IEnumerable<OutboxMessage> GenerateTaskCreatedEvents(Guid correlationId, UserInfo userInfo)
            {
                _logger.LogInformation("Generate {event}", nameof(GenerateTaskCreatedEvents));
                var tasksCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Models.Drybook.Task && x.State == EntityState.Added)
                    .Select(x => x.Entity as Models.Drybook.Task)
                    .Select(MapTaskCreatedDto)
                    .Select(x => new TaskCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(TaskCreatedEvent), correlationId, userInfo.Username));

                return tasksCreatedEvents;
            }

            private TaskCreatedEvent.TaskCreatedDto MapTaskCreatedDto(Models.Drybook.Task task) =>
                new TaskCreatedEvent.TaskCreatedDto
                {
                    Id = task.Id,
                    JobId = task.JobId,
                    Body = task.Body,
                    CancellationDate = task.CancellationDate,
                    CompletionDate = task.CompletionDate,
                    DueDate = task.DueDate,
                    EstimateId = task.EstimateId,
                    FranchiseSetId = task.FranchiseSetId,
                    IsSystem = task.IsSystem,
                    IsTemplate = task.IsTemplate,
                    StartDate = task.StartDate,
                    Subject = task.Subject,
                    ZoneId = task.ZoneId,
                    WorkOrderId = task.WorkOrderId,
                    JobTriStateQuestionId = task.JobTriStateQuestionId,
                    JobVisitId = task.JobVisitId,
                    PercentComplete = task.PercentComplete,
                    ReminderDate = task.ReminderDate,
                    TaskPriorityId = task.TaskPriorityId,
                    ZoneReadingId = task.ZoneReadingId,
                    TaskStatusId = task.TaskStatusId,
                    TaskTypeId = task.TaskTypeId
                };


            #endregion Methods for generating TaskCreatedEvents

            #region Methods for generating JournalNoteCreatedEvents
            private IEnumerable<OutboxMessage> GenerateJournalNoteCreatedEvents(Guid correlationId, UserInfo userInfo)
            {
                _logger.LogInformation("Generate {event}", nameof(GenerateJournalNoteCreatedEvents));
                var journalNoteCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JournalNote && x.State == EntityState.Added)
                    .Select(x => x.Entity as JournalNote)
                    .Select(MapJournalNoteDto)
                    .Select(x => new JournalNoteCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username));

                return journalNoteCreatedEvents;
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteDto(JournalNote journalNote) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedById = _userInfo.GetUserInfo().Id,
                    CreatedBy = _userInfo.GetUserInfo().Username
                };
            #endregion Methods for generating JournalNoteCreatedEvents

            private JournalNote Map(Command request, UserInfo userInfo)
                => new JournalNote
                {
                    JobId = request.JobId,
                    CategoryId = request.JournalNoteCategoryId,
                    TypeId = request.JournalNoteTypeId,
                    VisibilityId = request.JournalNoteVisibilityId,
                    Subject = request.Subject,
                    Note = request.Note,
                    Author = userInfo.Name,
                    ActionDate = request.ActionDate,
                    CreatedBy = userInfo.Username,
                    CreatedDate = DateTime.UtcNow
                };
        }
        #endregion
    }
}
