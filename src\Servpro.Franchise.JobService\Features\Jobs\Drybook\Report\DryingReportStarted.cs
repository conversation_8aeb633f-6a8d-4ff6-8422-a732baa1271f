﻿using Amazon.S3;
using Amazon.S3.Transfer;

using MediatR;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Features.Jobs.Documents;
using Servpro.Franchise.JobService.Infrastructure.JobService;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Features.Jobs.Documents.SaveDocument;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class DryingReportStarted
    {
        public class MediaS3Request
        {
            public Guid JobId { get; set; }
            public byte[] Document { get; set; }
            public string FranchiseSetId { get; set; }
            public Guid MediaId { get; set; }
            public string MediaName { get; set; }
            public string Key { get; set; }
            public string BucketName { get; set; }

        }
        public class Event : DryingReportStartedEvent, IRequest
        {
            public Event(Guid franchiseSetId, 
                         Guid jobId,
                         DryingReportStartedEvent.Dto presigned, 
                         string token, 
                         string username,
                         Guid correlationId) : base(franchiseSetId, 
                                                    jobId, 
                                                    presigned,
                                                    token,
                                                    username, 
                                                    correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly IJobServiceClient _jobServiceClient;
            private readonly ILogger<DryingReportStarted> _logger;
            private readonly IAmazonS3 _s3Client;
            private readonly IMediator _mediator;

            public Handler(IJobServiceClient jobService,
                            IAmazonS3 s3Client,
                            IMediator mediator,
                            ILogger<DryingReportStarted> logger)
            {
                _logger = logger;
                _s3Client = s3Client;
                _jobServiceClient = jobService;
                _mediator = mediator;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var jobId = request.JobId;
                var franchiseSetId = request.FranchiseSetId;

                _logger.LogInformation("DryingReportLog: Started handling event {eventName} for request {@request}", nameof(DryingReportStarted), request);
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();

                try
                {
                    //We have to reach out via job-service endpoint - job-service has to talk to itself here becuase the JSReportFeature needs an HttpContext
                    // The only way to build that context is to make an HTTP request
                    var dryingReport = await _jobServiceClient.GenerateDryingReport(franchiseSetId, jobId, request.CorrelationId, request.Token, cancellationToken);
                    _logger.LogInformation("DryingReportLog: Drying report generated successfully for jobId: {jobId} in {totalSeconds} seconds", jobId, stopwatch.Elapsed.TotalSeconds);

                    stopwatch.Reset();
                    _logger.LogInformation("DryingReportLog: Saving Drying report for jobId: {jobId} to s3 storage", jobId);
                    //Save the document to the Cloud(s3) - this way the client can download it once its ready
                    await SaveDryingReportToCloud(dryingReport, request, cancellationToken);
                    _logger.LogInformation("DryingReportLog: Finished saving Drying Report to s3 storage for jobId: {jobId} in {totalSeconds}. The Drying Report should now be downloadable.", jobId, stopwatch.Elapsed.TotalSeconds);
                
                }
                catch (Exception)
                {
                    //uploads an empty file so that it can be detected as an error
                    // and stop processing on the client.
                    await SaveErrorDocumentToCloud(request, cancellationToken);
                }

                // returning successfully no matter what on purpose
                //  because we dont want to retry the drying report again and again
                //  let the client retry itself
                return Unit.Value;                
            }
 
            private async Task SaveDryingReportToCloud(byte[] dryingReport, Event request, CancellationToken cancellationToken)
            {
                var s3Request = new MediaS3Request
                {
                    Document = dryingReport,
                    FranchiseSetId = request.FranchiseSetId.ToString(),
                    JobId = request.JobId,
                    MediaName = request.PresignedDto.Name,
                    MediaId = request.PresignedDto.Id,
                    Key = request.PresignedDto.Key,
                    BucketName = request.PresignedDto.BucketName
                };

                await SaveDocumentToS3(s3Request, cancellationToken);

                var mediaToSave = GenerateMediaToStore(request);
                var mediaSavedIds = await SaveMediaMetaData(mediaToSave, cancellationToken);
                if (!mediaSavedIds.Any())
                {
                    throw new ApplicationException("Unable to store MediaMetadata");
                }
            }

            private async Task SaveErrorDocumentToCloud(DryingReportStartedEvent request, CancellationToken cancellationToken)
            {
                var s3Request = new MediaS3Request
                {
                    Document = new byte[0],
                    FranchiseSetId = request.FranchiseSetId.ToString(),
                    JobId = request.JobId,
                    MediaName = "An Error has occurred",
                    MediaId = request.PresignedDto.Id,
                    Key = request.PresignedDto.Key,
                    BucketName = request.PresignedDto.BucketName
                };

                await SaveDocumentToS3(s3Request, cancellationToken);
            }

            private async Task SaveDocumentToS3(MediaS3Request request, CancellationToken cancellationToken)
            {
                var fileTransferUtility = new TransferUtility(_s3Client);

                using (var ms = new MemoryStream(request.Document))
                {
                    var transferRequest = new TransferUtilityUploadRequest
                    {
                        BucketName = request.BucketName,
                        Key = request.Key,
                        InputStream = ms,
                        ContentType = "application/pdf",
                        Headers = {ContentDisposition = $"attachment; filename={request.MediaName}"}
                    };

                    await fileTransferUtility.UploadAsync(transferRequest, cancellationToken);
                }
            }

            private static Command GenerateMediaToStore(DryingReportStartedEvent request)
            {
                var mediaToSave = new MediaDto
                {
                    ArtifactTypeId = ArtifactTypes.DryingReport,
                    IsForUpload = true,
                    Name = request.PresignedDto.Name,
                    Id = request.PresignedDto.Id,
                    MediaPath = request.PresignedDto.Key
                };
                var mediaMetadata = new SaveDocument.Command
                {
                    JobId = request.JobId,
                    FranchiseSetId = request.FranchiseSetId,
                    Media = new List<MediaDto>(),
                    Username = request.Username
                };
                mediaMetadata.Media.Add(mediaToSave);
                return mediaMetadata;
            }

            private async Task<List<Guid>> SaveMediaMetaData(Command request, CancellationToken cancellationToken)
            {
                var s3SaveResponse = await _mediator.Send(request, cancellationToken);
                return s3SaveResponse;
            }

        }
    }
}