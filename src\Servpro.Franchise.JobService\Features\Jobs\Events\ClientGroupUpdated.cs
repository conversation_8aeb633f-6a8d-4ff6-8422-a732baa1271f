﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Client;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ClientGroupUpdated
    {
        #region Event
        public class Event : ClientGroupUpdatedEvent, IRequest
        {
            public Event(ClientGroupDto clientGroup, Guid correlationId) : base(clientGroup, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db,
                ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Event {event} receieved", nameof(ClientGroupUpdatedEvent));

                var clientGroupDto = request.ClientGroupUpdated;

                var insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(x => x.InsuranceNumber == clientGroupDto.MemberId, cancellationToken: cancellationToken);

                if (insuranceClient == null)
                {
                    insuranceClient = MapPlaceholderClient(clientGroupDto.MemberId, clientGroupDto.ParentId);
                    _db.InsuranceClients.Add(insuranceClient);
                    _logger.LogWarning("{event}: Inserting Placeholder Insurance Client with Id {insuranceClientId} and InsuranceNumber {insuranceNumber}, {@insuranceClient}", nameof(ClientGroupUpdatedEvent), insuranceClient.Id, insuranceClient.InsuranceNumber, insuranceClient);
                }
                else
                {
                    insuranceClient.ParentInsuranceNumber = clientGroupDto.ParentId;
                    insuranceClient.ModifiedDate = clientGroupDto.LastModified;
                    _logger.LogInformation("{event}: Updating Insurance {insuranceClientId} Parent Number {insuranceNumber}, {@insuranceClient}", nameof(ClientGroupUpdatedEvent), insuranceClient.Id, insuranceClient.ParentInsuranceNumber, insuranceClient);
                }

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{event} saved: Id {id}", nameof(ClientGroupUpdatedEvent), insuranceClient.Id);

                return Unit.Value;
            }

            private InsuranceClient MapPlaceholderClient(int insuranceNumber, int parentInsuranceNumber)
                => new InsuranceClient
                {
                    Id = Guid.NewGuid(),
                    Name = "Placeholder Corporate Insurance",
                    InsuranceNumber = insuranceNumber,
                    ParentInsuranceNumber = parentInsuranceNumber,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                };
        }
        #endregion Handler
    }
}
