﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Equipments;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class EquipmentModelUpdated
    {
        public class Event : EquipmentModelUpdatedEvent, IRequest
        {
            public Event(EquipmentModelUpdatedDto equipmentModelUpdatedDto, Guid correlationId) : base(equipmentModelUpdatedDto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<EquipmentModelUpdated> _logger;
            private readonly IEquipmentServiceClient _equipmentServiceClient;
            private readonly JobDataContext _context;

            public Handler(JobDataContext context, 
                ILogger<EquipmentModelUpdated> logger, 
                IEquipmentServiceClient equipmentServiceClient)
            {
                _context = context;
                _logger = logger;
                _equipmentServiceClient = equipmentServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{equipmentModelId}", request.EquipmentModelUpdatedDto.Id);
                _logger.LogInformation("Handler began with {@incomingEvent}", request);

                var dto = request.EquipmentModelUpdatedDto;

                var equipmentModel = await _context.EquipmentModels
                    .FirstOrDefaultAsync(q => q.Id == dto.Id, cancellationToken);
                if (equipmentModel is null)
                {
                    _logger.LogInformation($"EquipmentModel not found: {dto.Id}");
                    return Unit.Value;
                }

                var equipmentType = await _context.EquipmentTypes.FirstOrDefaultAsync(x =>
                        x.Id == dto.EquipmentTypeId, cancellationToken);
                if (equipmentType is null)
                {
                    var newEquipmentType = await _equipmentServiceClient
                        .GetEquipmentTypeAsync(dto.EquipmentTypeId, cancellationToken);

                    if (newEquipmentType is null)
                    {
                        _logger.LogWarning("Couldn't Update EquipmentModel, EquipmentType with Id: {id} not found.", dto.EquipmentTypeId);
                        return Unit.Value;
                    }

                    await _context.EquipmentTypes.AddAsync(Map(newEquipmentType), cancellationToken);
                }

                equipmentModel.EquipmentTypeId = dto.EquipmentTypeId;
                equipmentModel.Name = dto.Name;
                equipmentModel.ManufacturerName = dto.ManufacturerName;
                equipmentModel.IsSymbol = dto.IsSymbol;
                equipmentModel.IsValidModel = dto.IsValidModel;
                equipmentModel.Description = dto.Description;
                equipmentModel.ManufacturerModelNumber = dto.ManufacturerModelNumber;
                equipmentModel.Notes = dto.Notes;
                equipmentModel.CubicFeetPerMinute = dto.CubicFeetPerMinute;
                equipmentModel.PintsPerDay = dto.PintsPerDay;
                equipmentModel.Amps = dto.Amps;
                equipmentModel.IsPenetratingMeter = dto.IsPenetratingMeter;
                equipmentModel.IsNotPenetratingMeter = dto.IsNotPenetratingMeter;
                equipmentModel.IsCurrent = dto.IsCurrent;
                equipmentModel.IsDeleted = dto.IsDeleted;
                equipmentModel.ModifiedBy = dto.ModifiedBy;
                equipmentModel.ModifiedDate = dto.ModifiedDate;
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Handler completed successfully");

                return Unit.Value;
            }

            public EquipmentType Map(GetEquipmentTypeDto equipmentType)
            {
                return new EquipmentType
                {
                    Id = equipmentType.Id,
                    BaseEquipmentTypeId = equipmentType.BaseEquipmentTypeId,
                    Name = equipmentType.Name,
                    IsDeleted = equipmentType.IsDeleted,
                    FranchiseSetId = equipmentType.FranchiseSetId
                };
            }
        }
    }
}
