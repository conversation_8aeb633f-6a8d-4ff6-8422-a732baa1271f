﻿using System;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class CrsValidationJobInfo
    {
        public string EstimateType { get; set; }
        public bool ActiveInAudit { get; set; }
        public Guid EvergreenJobId { get; set; }
        public string Country { get; set; }
        public string JobSource { get; set; }
        public string FranchiseType { get; set; }
        public string StructureType { get; set; }
        public string LossType { get; set; }
        public string State { get; set; }
        public bool IsStormJob { get; set; }
        public int? InsuranceClientId { get; set; }
        public DateTime ValidatedOn { get; set; }
        public DateTime? MemoizedOn { get; set; }
    }
}