@using Microsoft.CodeAnalysis.CSharp.Syntax
@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.JournalNotesDto
@if (Model?.DiaryNotes != null && Model.DiaryNotes.Count > 0)
{
    <div class="floatLeft">
        <span class="title">{diaryNotes}Diary Notes{/diaryNotes}</span>
        <table class="diaryNoteTable" summary="Diary Note">
            <tr>
                <th scope="col" class="table-caption">Timestamp</th>
                <th scope="col" class="table-caption rightColumn">Subject/Note</th>
            </tr>
            @foreach (var diaryNote in Model.DiaryNotes.OrderBy(n => n.ActionDate ?? DateTime.MaxValue))
            {
                string noteTimeStamp = "";
                if(diaryNote.ActionDate.HasValue)
                {
                    noteTimeStamp = $"{diaryNote.ActionDate.Value:M/d/yyyy h:mm tt} {Model.FranchiseSetTimeZone}";
                }
                <tr class="diaryEntryTableData">
                    <td class="diaryRowBorder cellAlignTop">@noteTimeStamp</td>
                    <td class="diaryRowBorder">
                        <span class="rightColumn bold">@diaryNote.Subject</span>
                        <span class="break-word rightColumn">@diaryNote.Note</span>
                    </td>
                </tr>
            }
        </table>
    </div>
    <div style='page-break-before: always;'></div>
}