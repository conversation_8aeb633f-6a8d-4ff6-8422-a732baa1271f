﻿using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Common
{
    public class ServiceException : Exception
    {
        public ServiceException(
            string serviceName,
            Uri requestUri,
            HttpStatusCode statusCode,
            string response)
        {
            ServiceName = serviceName;
            RequestUri = requestUri;
            StatusCode = statusCode;
            Response = response;
        }

        public string ServiceName { get; }
        public Uri RequestUri { get; }
        public HttpStatusCode StatusCode { get; }
        public string Response { get; }
    }
    public static class HttpResponseMessageExtensions
    {
        private static async Task HandleServiceResponseAsync(this HttpResponseMessage response, string serviceName)
        {
            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                throw new ServiceException(serviceName,
                    response.RequestMessage.RequestUri,
                    response.StatusCode,
                    responseBody);
            }
        }

        public static Task HandleFranchiseServiceResponseAsync(this HttpResponseMessage response)
            => HandleServiceResponseAsync(response, HttpClients.FranchiseService);

        public static Task HandleLookupServiceResponseAsync(this HttpResponseMessage response)
            => HandleServiceResponseAsync(response, HttpClients.LookupService);

        public static Task HandleClientRequirementServiceResponseAsync(this HttpResponseMessage response)
            => HandleServiceResponseAsync(response, HttpClients.ClientRequirementsService);

        public static Task HandleEquipmentServiceResponseAsync(this HttpResponseMessage response)
            => HandleServiceResponseAsync(response, HttpClients.EquipmentService);

        public static Task HandleXactServiceResponseAsync(this HttpResponseMessage response)
            => HandleServiceResponseAsync(response, HttpClients.XactService);
    }
}
