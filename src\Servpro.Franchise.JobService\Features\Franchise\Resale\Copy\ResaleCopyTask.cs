﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyTask
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult ZoneResult { get; set; }
            public ProcessEntityResult JobVisitResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> TaskIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyTask>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyTask> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyTask> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobService.Models.Drybook.Task));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.TaskIds.ToList(), 
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var taskTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedTaskIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(taskTargetIds, 
                    GetTaskIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobService.Models.Drybook.Task, ResaleTask>(
                    request.ResaleId,
                    task =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (task.ZoneId.HasValue && request.ZoneResult.FailedEntities.Contains(task.ZoneId.Value))
                            failedDependencies.Add((nameof(Zone), task.ZoneId.Value));
                        if (task.JobVisitId.HasValue && request.JobVisitResult.FailedEntities.Contains(task.JobVisitId.Value))
                            failedDependencies.Add((nameof(JobVisit), task.JobVisitId.Value));
                        if (task.JobId.HasValue && request.JobResult.FailedEntities.Contains(task.JobId.Value))
                            failedDependencies.Add((nameof(Job), task.JobId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedTaskIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Tasks.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Servpro.Franchise.JobService.Models.Drybook.Task>> GetSourceEntitiesAsync(List<Guid> taskIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var tasks = await _context.Tasks
                    .Where(jv => taskIds.Contains(jv.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", tasks.Count);
                return tasks;
            }

            private async Task<List<Guid>> GetTaskIdsAsync(List<Guid?> taskTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Tasks
                    .Where(x => taskTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
