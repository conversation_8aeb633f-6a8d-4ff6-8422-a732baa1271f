using jsreport.AspNetCore;
using jsreport.Types;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.DocuSketch;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones;
using Servpro.Franchise.JobService.Models.Drybook.DryingReportParser;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.GenerateDryingReport;

using GetAtmosphericReadings = Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring.GetAtmosphericReadings;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    [Route("api/jobs/{jobId}/drybook")]
    public class DrybooksController : Controller
    {
        private readonly IMediator _mediator;
        private readonly IConfiguration _config;
        private readonly IJsReportMVCService _jsReportMvcService;
        private const string ViewRoot = "~/Features/Jobs/Drybook/Report/ReportViews/";
        private const string DryingReportViewName = "DryingReport.cshtml";
        private const string DryingReportHeaderViewName = "_header.cshtml";
        private const string DryingReportFooterViewName = "_footer.cshtml";

        public readonly ILogger<DrybooksController> _logger;

        public DrybooksController(
            IMediator mediator, 
            IConfiguration config,
            IJsReportMVCService jsReportMvcService,
            ILogger<DrybooksController> logger)
        {
            _mediator = mediator;
            _jsReportMvcService = jsReportMvcService;
            _logger = logger;
            _config = config;
        }

        [HttpGet("queries/is-job-associated-to-docusketch")]
        public async Task<ActionResult<bool>> GetIsJobAssociatedToDocuSketchAsync(Guid jobId)
        {
            var result = await _mediator.Send(new GetIsJobAssociatedToDocuSketch.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-initial-inspection")]
        public async Task<ActionResult<GetInitialInspection.Dto>> GetInitialInspectionAsync(Guid jobId)
        {
            var result = await _mediator.Send(new GetInitialInspection.Query(jobId));
            return Ok(result);
        }


        [HttpGet("queries/get-rooms-summary")]
        public async Task<ActionResult<IEnumerable<GetRoomSummary.Dto>>> GetRooms(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetRoomSummary.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-zones-summary")]
        public async Task<ActionResult<IEnumerable<GetZonesSummary.Dto>>> GetZones(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetZonesSummary.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-job-summary")]
        public async Task<ActionResult<GetJobSummary.Dto>> GetJobSummary(Guid jobId)
        {
            var result = await _mediator.Send(new GetJobSummary.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-drybook-mobile-options")]
        public async Task<ActionResult<GetDBMOptions.Dto>> GetDBMOptions(Guid jobId)
        {
            var result = await _mediator.Send(new GetDBMOptions.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-alerts")]
        public async Task<ActionResult<IEnumerable<GetJobAlerts>>> GetActiveAlerts(Guid jobId)
        {
            var result = await _mediator.Send(new GetJobAlerts.Query { JobId = jobId, TaskStatusIds = new List<Guid>() { TaskStatuses.Active, TaskStatuses.Completed } });
            return Ok(result);
        }

        [HttpGet("queries/get-active-alerts-count")]
        public async Task<ActionResult<IEnumerable<GetJobAlerts>>> GetActiveAlertsCount(Guid jobId)
        {
            var result = await _mediator.Send(new GetJobAlerts.Query { JobId = jobId, TaskStatusIds = new List<Guid>() { TaskStatuses.Active } });
            return Ok(result.Count);
        }

        [HttpPut("commands/save-initial-inspection")]
        public async Task<IActionResult> SaveInitialInspectionAsync(Guid jobId, [FromBody] SaveInitialInspection.Command request)
        {
            if (jobId != request.JobId)
                return BadRequest();

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPut("commands/save-task-journalnote")]
        public async Task<ActionResult> SaveTaskWithJournalNote(Guid jobId, [FromBody] SaveTaskJournalNote.Command request)
        {
            if (jobId != request.JobId)
                return BadRequest();

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPut("commands/edit-task-journalnote")]
        public async Task<ActionResult> EditTaskWithJournalNote(Guid jobId, [FromBody] EditTaskJournalNote.Command request)
        {
            if (jobId != request.JobId)
                return BadRequest();

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpGet("queries/get-journalnote/{journalNoteId}")]
        public async Task<ActionResult<GetJournalNote.Dto>> GetJournalNoteAsync(Guid jobId, Guid journalNoteId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (journalNoteId == Guid.Empty)
                return BadRequest(nameof(journalNoteId));

            var response = await _mediator.Send(new GetJournalNote.Query { JobId = jobId, JournalNoteId = journalNoteId });
            return Ok(response);
        }

        [HttpPut("commands/edit-journalnote/{journalNoteId}")]
        public async Task<ActionResult> EditJournalNoteAsync(Guid jobId, Guid journalNoteId, [FromBody] EditJournalNote.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (journalNoteId == Guid.Empty)
                return BadRequest(nameof(journalNoteId));

            var response = await _mediator.Send(request);
            return Ok(response);
        }

        [HttpPost("commands/save-room")]
        public async Task<ActionResult> SaveRoom(Guid jobId, [FromBody] SaveRoom.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            request.JobId = jobId;
            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPut("commands/edit-room")]
        public async Task<ActionResult> EditRoom(Guid jobId, [FromBody] EditRoom.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            request.JobId = jobId;
            await _mediator.Send(request);
            return NoContent();

        }

        [HttpPut("commands/copy-room")]
        public async Task<ActionResult> CopyRoom(Guid jobId, [FromBody] CopyRoom.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            request.JobId = jobId;
            await _mediator.Send(request);
            return NoContent();

        }

        [HttpDelete("commands/delete-room/{roomId}")]
        public async Task<ActionResult> DeleteRoom(Guid jobId, Guid roomId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new DeleteRoom.Command(jobId, roomId, User.UserInfo()));
            return Ok(result);

        }

        [HttpPut("commands/assign-room-to-zone/{zoneId}")]
        public async Task<ActionResult> AssignRoomToZone(Guid jobId, Guid zoneId, [FromBody] object request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var rooms = JsonConvert.DeserializeObject<ICollection<AssignRoomToZone.RoomDto>>(request.ToString());
            var result = await _mediator.Send(new AssignRoomToZone.Command(jobId, zoneId, rooms));
            return Ok(result);

        }

        [HttpPut("commands/remove-room-from-zone/{zoneId}")]
        public async Task<ActionResult> RemoveRoomFromZone(Guid jobId, Guid zoneId, [FromBody] object request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var rooms = JsonConvert.DeserializeObject<ICollection<RemoveRoomFromZone.RoomDto>>(request.ToString());
            var result = await _mediator.Send(new RemoveRoomFromZone.Command(jobId, zoneId, rooms));
            return Ok(result);
        }


        [HttpGet("queries/get-visits")]
        public async Task<ActionResult<GetJobVisits.Dto>> GetJobVisits(Guid jobId)
        {

            var result = await _mediator.Send(new GetJobVisits.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-room-detail/{roomId}")]
        public async Task<ActionResult<GetRoomDetail.Dto>> GetRoomDetail(Guid jobId, Guid roomId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetRoomDetail.Query(jobId, roomId));
            return Ok(result);
        }

        [HttpGet("queries/get-equipment-placement-detail")]
        public async Task<ActionResult<GetEquipmentPlacementDetails.Dto>> GetEquipmentPlacementDetails(Guid jobId)
        {

            var result = await _mediator.Send(new GetEquipmentPlacementDetails.Query(jobId));
            return Ok(result);
        }

        [HttpPost("commands/save-zone")]
        public async Task<ActionResult> SaveZone(Guid jobId, [FromBody] SaveZone.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            request.JobId = jobId;
            var result = await _mediator.Send(request);
            return Ok(result);
        }

        [HttpPut("commands/update-zone")]
        public async Task<ActionResult> UpdateZone(Guid jobId, [FromBody] UpdateZone.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            request.JobId = jobId;
            var result = await _mediator.Send(request);
            return Ok(result);
        }

        [HttpDelete("commands/Delete-zone/{zoneId}")]
        public async Task<ActionResult> DeleteZone(Guid jobId, Guid zoneId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (zoneId == Guid.Empty)
                return BadRequest(nameof(zoneId));

            var request = new DeleteZone.Command
            {
                JobId = jobId,
                ZoneId = zoneId
            };

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpGet("queries/zones/{zoneId}/rooms")]
        public async Task<ActionResult<GetRoomsByZone.Dto>> GetRoomsByZone(GetRoomsByZone.Query query)
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("zones/{zoneId}/queries/validation/get-zone")]
        public async Task<ActionResult<ZoneForValidationDto>> GetZonesForValidation(
            Guid jobId,
            Guid zoneId,
            GetZoneForValidation.Query query)
        {
            if (jobId != query.JobId)
                return BadRequest();
            if (zoneId != query.ZoneId)
                return BadRequest();
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("queries/validation/get-zones")]
        public async Task<ActionResult<IEnumerable<ZoneForValidationDto>>> GetZonesForValidation(
            Guid jobId,
            GetZonesForValidation.Query query)
        {
            if (jobId != query.JobId)
                return BadRequest();
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("equipment-placements/queries/get-for-validation")]
        public async Task<ActionResult<GetEquipmentPlacementLookup.Dto>> GetEquipmentPlacementLookup(
            GetEquipmentPlacementLookup.Query query)
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("rooms/{roomId}/queries/get-equipment-placement")]
        public async Task<ActionResult<IEnumerable<GetEquipmentPlacementByRoom.Dto>>> GetEquipmentPlacementByRoom(GetEquipmentPlacementByRoom.Query request)
        {
            var placements = await _mediator.Send(request);
            return Ok(placements);
        }

        [HttpPost("commands/place-equipment")]
        public async Task<ActionResult<IEnumerable<GetEquipmentPlacementByRoom.Dto>>> PlaceEquipmentAsync(Guid jobId, [FromBody] PlaceEquipment.Command command)
        {
            if (jobId != command.JobId)
                return BadRequest();

            var jsonEquipment = JsonConvert.SerializeObject(command);
            _logger.LogInformation($"Entering into PlaceEquipment with command : {jsonEquipment} ");
            var placements = await _mediator.Send(command);
            return Ok(placements);
        }

        [HttpPost("commands/remove-placed-equipment")]
        public async Task<ActionResult> RemoveEquipmentAsync(Guid jobId, [FromBody] RemovePlacedEquipment.Command command)
        {
            if (jobId != command.JobId)
                return BadRequest();
            await _mediator.Send(command);
            return NoContent();
        }

        [HttpPost("zones/{zoneId}/commands/confirm")]
        public async Task<ActionResult> ConfirmZoneAsync(Guid jobId, Guid zoneId, [FromBody] ConfirmZone.Command command)
        {
            if (jobId != command.JobId)
                return BadRequest();
            if (zoneId != command.ZoneId)
                return BadRequest();
            await _mediator.Send(command);
            return NoContent();
        }

        [HttpPost("zones/{zoneId}/commands/unconfirm")]
        public async Task<ActionResult> UnconfirmZoneAsync(Guid jobId, Guid zoneId, [FromBody] UnconfirmZone.Command command)
        {
            if (jobId != command.JobId)
                return BadRequest();
            if (zoneId != command.ZoneId)
                return BadRequest();
            await _mediator.Send(command);
            return NoContent();
        }

        [HttpGet("job-visits/{jobVisitId}")]
        public async Task<ActionResult<GetJobVisit.Dto>> GetJobVisitById(Guid jobId, Guid jobVisitId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobVisitId == Guid.Empty)
                return BadRequest(nameof(jobVisitId));

            var result = await _mediator.Send(new GetJobVisit.Query(jobId, jobVisitId));
            return Ok(result);
        }

        [HttpPut("commands/save-visit")]
        public async Task<ActionResult> EditJobVisit(Guid jobId, [FromBody] SaveJobVisit.Command command)
        {
            if (jobId != command.JobId)
                return BadRequest();
            await _mediator.Send(command);
            return NoContent();
        }

        [HttpDelete("commands/delete-visit/{jobVisitId}")]
        public async Task<ActionResult> DeleteJobVisit(Guid jobId, Guid jobVisitId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobVisitId == Guid.Empty)
                return BadRequest(nameof(jobVisitId));

            var request = new DeleteJobVisit.Command
            {
                JobId = jobId,
                JobVisitId = jobVisitId
            };

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpGet("queries/get-atmospheric-readings")]
        public async Task<ActionResult<GetAtmosphericReadings.Dto>> GetAtmosphericReadings(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            var dto = await _mediator.Send(new GetAtmosphericReadings.Query(jobId));
            return Ok(dto);
        }

        [HttpGet("queries/get-dehumidifier-readings")]
        public async Task<ActionResult<IEnumerable<GetDehumidifierReadings.Dto>>> GetDehumidifierReadings(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            var dto = await _mediator.Send(new GetDehumidifierReadings.Query(jobId));
            return Ok(dto);
        }

        [HttpPut("commands/save-journal-note")]
        public async Task<ActionResult> SaveJournalNote(Guid jobId, [FromBody] SaveJournalNote.Command command)
        {
            if (jobId == Guid.Empty)
                return BadRequest();

            command.JobId = jobId;
            var resp = await _mediator.Send(command);

            return Ok(resp);

        }

        [HttpPost("commands/create-hvac")]
        public async Task<ActionResult<Guid>> SaveHvacZone(Guid jobId, [FromBody] SaveHvacZone.Command command)
        {
            if (jobId != command.JobId)
                return BadRequest();
            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpGet("queries/zones-lookup")]
        public async Task<ActionResult<IEnumerable<GetZones.Dto>>> GetZonesByJobId(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetZones.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/equipment-placements-monitoring")]
        public async Task<ActionResult<IEnumerable<GetEquipmentPlacements.Dto>>> GetEquipmentPlacementsForMonitoring(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetEquipmentPlacements.Query(jobId));
            return Ok(result);
        }

        [HttpPost("commands/save-atmospheric-readings")]
        public async Task<ActionResult> SaveAtmosphericReadings(Guid jobId, [FromBody] SaveAtmosphericReadings.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            if (jobId != request.JobId)
                return BadRequest();

            var result = await _mediator.Send(request);
            return Ok(result);
        }

        [HttpPost("commands/save-dehumidifier-readings")]
        public async Task<ActionResult> SaveDehumidifierReadings(Guid jobId, [FromBody] SaveDehumidifierReadings.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            if (jobId != request.JobId)
                return BadRequest();

            var result = await _mediator.Send(request);
            return Ok(result);
        }

        [HttpGet("queries/materials")]
        public async Task<ActionResult<IEnumerable<GetJobMaterials.Dto>>> GetJobMaterialsAsync(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetJobMaterials.Query(jobId));
            return Ok(result);
        }

        [HttpGet("queries/get-material-readings")]
        public async Task<ActionResult<IEnumerable<GetMaterialReadings.Dto>>> GetMaterialReadingsAsync(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var result = await _mediator.Send(new GetMaterialReadings.Query(jobId));
            return Ok(result);
        }

        [HttpPost("commands/save-material")]
        public async Task<ActionResult<Guid>> SaveJobMaterial(Guid jobId, [FromBody] SaveJobMaterial.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            if (jobId != request.JobId)
                return BadRequest();

            var response = await _mediator.Send(request);
            return Ok(response);
        }

        [HttpGet("queries/drying-complete")]
        public async Task<ActionResult<GetDryingComplete.Dto>> GetDryingComplete(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var response = await _mediator.Send(new GetDryingComplete.Query(jobId));
            return Ok(response);
        }

        [HttpPost("commands/delete-material")]
        public async Task<ActionResult> DeleteJobMaterial(Guid jobId, [FromBody] DeleteJobMaterial.Command command)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            await _mediator.Send(command);
            return NoContent();
        }

        [HttpPost("commands/save-material-readings")]
        public async Task<ActionResult<Guid>> SaveJobMaterialReadings(Guid jobId, [FromBody] SaveMaterialReadings.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            var response = await _mediator.Send(request);
            return Ok(response);
        }

        [HttpPost("commands/delete-job-area-material")]
        public async Task<ActionResult<Guid>> DeleteJobAreaMaterial(Guid jobId, [FromBody] DeleteJobAreaMaterial.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPost("commands/remove-job-area-material-on-visit")]
        public async Task<ActionResult<Guid>> RemoveJobAreaMaterialOnVisit(Guid jobId, [FromBody] DeleteJobAreaMaterialOnVisit.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPost("queries/job-completion-validation-summary")]
        public async Task<ActionResult<GetDryingComplete.Dto>> GetJobCompletionValidationSummary(Guid jobId, [FromBody] GetValidationSummary.Query query)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (query.JobId != jobId)
                return BadRequest(nameof(jobId));

            var response = await _mediator.Send(query);
            return Ok(response);
        }

        [HttpGet("queries/job-completion-summary")]
        public async Task<ActionResult<GetJobCompletionSummary.Dto>> GetJobCompletionSummary(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var response = await _mediator.Send(new GetJobCompletionSummary.Query { JobId = jobId });
            return Ok(response);
        }

        [HttpGet("queries/active-placed-equipment")]
        public async Task<ActionResult<GetJobCompletionSummary.Dto>> GetActivePlacedEquipment(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var response = await _mediator.Send(new GetActivePlacedEquipment.Query { JobId = jobId });
            return Ok(response);
        }

        [HttpPost("commands/save-job-tri-state-answer")]
        public async Task<ActionResult<GetJobCompletionSummary.Dto>> SaveJobTriStateAnswer(Guid jobId, [FromBody] SaveJobTriStateAnswer.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPost("commands/update-placed-equipment")]
        public async Task<ActionResult> UpdateEquipmentPlacement(Guid jobId, [FromBody] EditEquipmentPlacement.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPost("commands/drying-finished")]
        public async Task<ActionResult<GetJobCompletionSummary.Dto>> SaveDryingFinished(Guid jobId, [FromBody] SaveDryingFinished.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPost("commands/complete-job")]
        public async Task<ActionResult<GetJobCompletionSummary.Dto>> CompleteJob(Guid jobId, [FromBody] CompleteJob.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }

        [HttpPost("commands/delete-placed-equipment")]
        public async Task<ActionResult<GetJobCompletionSummary.Dto>> DeletePlacedEquipment(Guid jobId, [FromBody] DeletePlacedEquipment.Command request)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (jobId != request.JobId)
                return BadRequest(nameof(jobId));

            await _mediator.Send(request);
            return NoContent();
        }
        /// <summary>
        /// This endpoint generates a presigned url dto and publishes the DryingReportStartedEvent directly to the
        /// job-service SQS queue.  This allows the endpoint to be async and quickly respond.  It also means that clients
        /// will have to use the presigned url to determine if the report has been uploaded and ready for download.
        /// </summary>
        [HttpPost("commands/start-drying-report")]
        public async Task<ActionResult<DryingReportStartedEvent.Dto>> StartDryingReport(Guid jobId, Guid? correlationId = null)
        {
            if (jobId == Guid.Empty) 
                return BadRequest(nameof(jobId));

            if (!User.FranchiseSetId().HasValue)
                return BadRequest("FranchiseSetId");

            var franchiseSetId = User.FranchiseSetId().Value;

            //We need to get the token here on this request so that it can be used
            // later on in the DryingReportStartedEvent
            Request.Headers.TryGetValue("Authorization", out var initialToken);
            var username = User.Username();

            var request = new StartDryingReport.Command(franchiseSetId, jobId, initialToken, username, correlationId);
            var dto = await _mediator.Send(request);

            return dto;
        }

        [HttpPost("commands/generate-drying-report")]
        [MiddlewareFilter(typeof(JsReportPipeline))]
        public async Task<IActionResult> GenerateDryingReport(Guid jobId, [FromBody] GenerateDryingReport.Command command, CancellationToken cancellationToken, [FromQuery] DryingReportTestScenarios isMock = 0)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));
            using var scope = _logger.BeginScope("{jobId}", jobId);
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            if (_config.GetValue("IntegrationTesting", false) == false)
            {
#if DEBUG
                isMock = DryingReportTestScenarios.MockData;
#endif
            }

            Dto dryingReport;
            try
            {
                _logger.LogInformation("DryingReportLog: Starting GenerateDryingReport for jobId: {jobId}", jobId);
                 dryingReport = isMock != 0 ? DryingReportGeneratedDtoMockData.GetMockForPreview(isMock) :
                                             await _mediator.Send(command, cancellationToken);
                _logger.LogInformation("DryingReportLog: Data retrieval for jobId: {jobId} in: {totalSeconds} Seconds", jobId, stopwatch.Elapsed.TotalSeconds);
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "There was an error gathering data for the Drying Report with jobId: {jobId}", jobId);
                return Problem(ex.Message, string.Empty, 500);
            }

            try
            {
                stopwatch.Restart();

                _logger.LogInformation("DryingReportLog: Starting to Render Drying Report for jobId: {jobId}", jobId);
                //Render the Header. 
                var headerInfo = new HeaderHelper(dryingReport.SummaryModel.FranchiseName, dryingReport.SummaryModel.CustomerName, dryingReport.SummaryModel.ClaimPoNumber, dryingReport.SummaryModel.ProjectNumber);


                string header = await _jsReportMvcService.RenderViewToStringAsync(
                    HttpContext, RouteData, $"{ViewRoot}{DryingReportHeaderViewName}", headerInfo);

                ////Render the Footer.
                string footer = await _jsReportMvcService.RenderViewToStringAsync(
                    HttpContext, RouteData, $"{ViewRoot}{DryingReportFooterViewName}", new FooterHelper(dryingReport.FranchiseLocalTime, "Drying Report"));


                //Configure the report to convert this view to a PDF using JSReport
                dryingReport.UploadId = Guid.NewGuid();
                var reportName = $"DryingReport-{dryingReport.UploadId}.pdf";

                HttpContext.JsReportFeature()
                        .DebugLogsToResponse()
                        .Recipe(Recipe.ChromePdf)
                        .Configure((r) =>
                        {
                            r.Template.Chrome = new Chrome
                            {
                                DisplayHeaderFooter = true,
                                HeaderTemplate = header,
                                FooterTemplate = footer,
                                Format = "A4",
                                MarginTop = "3cm",
                                MarginLeft = "1cm",
                                MarginBottom = "2cm",
                                MarginRight = "1cm"
                            };

                            r.Template.PdfMeta = new PdfMeta
                            {                                
                                Keywords = String.Format("JobId:{0} DryingReportUploadId:{1} DryingReportModelVersion:{2}", 
                                    jobId, 
                                    dryingReport.UploadId, 
                                    new SerializableVersion(2, 0, 0, 0))
                            };

                            r.Options = new RenderOptions()
                            {
                               Timeout = _config.GetValue(AppSettings.JsReportTimeoutMs, 600000)
                            };
                        })
                        .OnAfterRender((r) =>
                        {
                            _logger.LogInformation("DryingReportLog: JsReport Logs for jobId: {jobId}, logs: {@logs}", jobId, r.Meta.Logs);
                            _logger.LogInformation("DryingReportLog: Starting to Add bookmarks for jobId: {jobId}", jobId);
                            HttpContext.Response.Headers["Content-Disposition"] = $"attachment; filename={reportName}";
                            HttpContext.Response.Headers["Content-Type"] = "application/pdf";
                            var document = dryingReport.BookMarks.CreateBookMarks(r.Content);
                            DryingReportHelper.AttachDryingReportModelXml(document, dryingReport);
                            var pdfStream = new MemoryStream();
                            document.Save(pdfStream);
                            r.Content = pdfStream;

                            _logger.LogInformation("DryingReportLog: Finished Rendering Drying Report for jobId: {jobId} in {totalSeconds} seconds", jobId, stopwatch.Elapsed.TotalSeconds);

                        });

                return View($"{ViewRoot}{DryingReportViewName}", dryingReport);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "There was an error rendering the Drying report for jobId: {jobId}", jobId);
                return Problem(ex.Message, string.Empty, 500);
            }
        }
    }
}
