using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Models
{
    public class ResaleLookup
    {
        public ResaleLookup(HashSet<Guid> jobIds,
            HashSet<Guid> mobileDataIds,
            HashSet<Guid> contactIds,
            HashSet<Guid> businessIds,
            HashSet<Guid> mediaIds,
            HashSet<Guid> jobVisitIds,
            HashSet<Guid> journalNoteIds,
            HashSet<Guid> jobAreaIds,
            HashSet<Guid> jobAreaMaterialIds,
            HashSet<Guid> roomIds,
            HashSet<Guid> zoneIds,
            HashSet<Guid> taskIds,
            HashSet<Guid> equipmentTypeIds,
            HashSet<Guid> equipmentModelIds,
            HashSet<Guid> equipmentPlacementIds)
        {
            JobIds = jobIds;
            MobileDataIds = mobileDataIds;
            ContactIds = contactIds;
            BusinessIds = businessIds;
            MediaIds = mediaIds;
            JobVisitIds = jobVisitIds;
            JournalNoteIds = journalNoteIds;
            JobAreaIds = jobAreaIds;
            JobAreaMaterialIds = jobAreaMaterialIds;
            RoomIds = roomIds;
            ZoneIds = zoneIds;
            TaskIds = taskIds;
            EquipmentTypeIds = equipmentTypeIds;
            EquipmentModelIds = equipmentModelIds;
            EquipmentPlacementIds = equipmentPlacementIds;
        }

        public HashSet<Guid> JobIds { get; set; }
        public HashSet<Guid> MobileDataIds { get; set; }
        public HashSet<Guid> ContactIds { get; set; }
        public HashSet<Guid> BusinessIds { get; set; }
        public HashSet<Guid> MediaIds { get; set; }
        public HashSet<Guid> JobVisitIds { get; set; }
        public HashSet<Guid> JournalNoteIds { get; set; }
        public HashSet<Guid> JobAreaIds { get; set; }
        public HashSet<Guid> JobAreaMaterialIds { get; set; }
        public HashSet<Guid> RoomIds { get; set; }
        public HashSet<Guid> ZoneIds { get; set; }
        public HashSet<Guid> TaskIds { get; set; }
        public HashSet<Guid> EquipmentTypeIds { get; set; }
        public HashSet<Guid> EquipmentModelIds { get; set; }
        public HashSet<Guid> EquipmentPlacementIds { get; set; }
    }
}
