﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Jobs.DocuSign
{
    public class GetDocumentDataForDocuSign
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public ICollection<Document> Documents { get; set; }
            public string UserEmail { get; set; }
            public string CustomerEmail { get; set; }
        }

        public class Document
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public bool WaterFormRequired { get; set; }
            public bool MoldFormRequired { get; set; }
            public bool FireFormRequired { get; set; }
            public bool? IsRequiredForResidentialJob { get; set; }
            public bool? IsRequiredForCommercialJob { get; set; }
            public bool? IsRequiredForAllLossTypes { get; set; }
            public string State { get; set; }
            public string Country { get; set; }
            public bool IsActive { get; set; }
            public string InsuranceClient { get; set; }
            public bool IsUploaded { get; set; }
            public bool IsSentToBeSigned { get; set; }
            public bool IsSigned { get; set; }
            public string FormCategoryDesc { get; set; }
            public int GroupOrder { get; set; }
            [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
            public Guid? PackageId { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetDocumentDataForDocuSign> _logger;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            public IAmazonDynamoDB _dynamoClient;
            private readonly string _docuSignTableName;
            private readonly IFormsService _formsService;

            public Handler(
                ILogger<GetDocumentDataForDocuSign> logger,
                IUserInfoAccessor userInfoAccessor,
                IFranchiseServiceClient franchiseServiceClient,
                IAmazonDynamoDB amazonDynamoDB,
                IConfiguration config,
                JobReadOnlyDataContext context,
                IFormsService formsService)
            {
                _logger = logger;
                _userInfoAccessor = userInfoAccessor;
                _franchiseServiceClient = franchiseServiceClient;
                _dynamoClient = amazonDynamoDB;
                _context = context;
                _docuSignTableName = config[SignDocumentTable.DynamoTableLocation];
                _formsService = formsService;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Documents and email addresses for DocuSign");

                var job = await GetJobMediaAndCustomerAsync(request.JobId, cancellationToken);
                var forms = await _formsService.GetFormsAsync(job, cancellationToken);
                var uploadedFormIds = job.MediaMetadata.Select(x => x.FormTemplateId).ToHashSet();
                var sentForms = await GetSentDocumentsAsync(request.JobId, cancellationToken);
                var formTemplateToPackageIdMap = GetUniqueFormTemplateToPackageIdMap(sentForms);
                var sentFormTemplateIds = sentForms.Select(x => x.FormTemplateId).ToHashSet();
                var signedFormIds = sentForms
                    .Where(x => x.Status == SignDocumentStatus.DownloadAndUploadComplete)
                    .Select(x => x.FormTemplateId)
                    .ToHashSet();

                var templateIdToMediaPathMap = await GetTemplateIdToMediaPathMapAsync(forms, cancellationToken);
                var docs = MapFormsToDocumentDto(forms, uploadedFormIds, sentFormTemplateIds, 
                    signedFormIds, formTemplateToPackageIdMap, templateIdToMediaPathMap);

                var franchiseEmail = await GetFranchiseUserEmail(cancellationToken);
                var customerEmail = job.Customer?.EmailAddress;

                return new Dto() { Documents = docs, CustomerEmail = customerEmail, UserEmail = franchiseEmail };
            }

            private List<Document> MapFormsToDocumentDto(List<FormTemplateModel> forms, 
                HashSet<Guid?> uploadedFormIds, 
                HashSet<Guid> sentFormTemplateIds, 
                HashSet<Guid> signedFormIds, 
                Dictionary<Guid, Guid?> formTemplateToPackageIdMap, 
                Dictionary<Guid, string> templateIdToMediaPathMap)
            {
                return forms
                    .Select(x =>
                        Map(x, uploadedFormIds, sentFormTemplateIds, signedFormIds, 
                            formTemplateToPackageIdMap.TryGetValue(x.FormTemplateId, out var packageId) 
                                ? packageId 
                                : null,
                            templateIdToMediaPathMap))
                    .ToList();
            }

            private async Task<string> GetFranchiseUserEmail(CancellationToken cancellationToken)
            {
                var userinfo = _userInfoAccessor.GetUserInfo();
                var user = await _franchiseServiceClient.GetEmployee(userinfo.Id, cancellationToken);

                return user?.Email;
            }

            private async Task<Dictionary<Guid, string>> GetTemplateIdToMediaPathMapAsync(List<FormTemplateModel> forms, CancellationToken cancellationToken)
            {
                var templateIdToMediaPathMap = new Dictionary<Guid, string>();

                if (!forms.Any())
                    return templateIdToMediaPathMap;

                //do not change this structure from a hashset without also modifying the foreach below
                //right now it makes the assumption that these should be unique, which they should
                var uniqueFormTemplateIds = forms.Select(x => x.FormTemplateId).ToHashSet();
                var formTemplateMediaPaths = await _context.FormTemplates
                    .Where(x => uniqueFormTemplateIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                foreach (var formTemplateId in uniqueFormTemplateIds)
                {
                    templateIdToMediaPathMap.Add(formTemplateId, formTemplateMediaPaths.FirstOrDefault(x => x.Id == formTemplateId)?.MediaPath);
                }

                return templateIdToMediaPathMap;
            }

            private async Task<Job> GetJobMediaAndCustomerAsync(Guid jobId, CancellationToken cancellationToken)
            {
                return await _context.Jobs
                    .Where(x => x.Id == jobId)
                    .Include(y => y.MediaMetadata
                        .Where(x => x.MediaTypeId == LookupService.Constants.MediaTypes.Document &&
                                x.FormTemplateId.HasValue &&
                                x.UploadedSuccessfully &&
                                !x.IsDeleted))
                    .Include(x => x.Customer)
                    .FirstOrDefaultAsync(cancellationToken);
            }

            private Dictionary<Guid, Guid?> GetUniqueFormTemplateToPackageIdMap(List<SignDocumentRecord> sentForms)
            {
                var formTemplateToPackageIdMap = new Dictionary<Guid, Guid?>();

                if (!sentForms.Any())
                    return formTemplateToPackageIdMap;

                // some jobs have multiple records with the same formTemplateId due to early dev bugs
                // this prioritizes the latest sent forms and utilizes that record in the UI
                foreach (var sentForm in sentForms.OrderByDescending(x => x.CreatedDate))
                {
                    if (formTemplateToPackageIdMap.ContainsKey(sentForm.FormTemplateId))
                        continue;

                    Guid? packageId = sentForm.PackageId != Guid.Empty ? (Guid?)sentForm.PackageId : null;
                    formTemplateToPackageIdMap.Add(sentForm.FormTemplateId, packageId);
                }

                return formTemplateToPackageIdMap;
            }

            public async Task<List<SignDocumentRecord>> GetSentDocumentsAsync(Guid jobId, CancellationToken cancellationToken)
            {
                try
                {
                    var query = new QueryRequest
                    {
                        TableName = _docuSignTableName,
                        IndexName = SignDocumentTable.JobIdIndexName,
                        ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                        {
                            { ":jobId", new AttributeValue { S = jobId.ToString() } },
                            { ":rescindCompleted", new AttributeValue { N = ((int)SignDocumentStatus.RescindCompleted).ToString() } },
                            { ":deletionComplete", new AttributeValue { N = ((int)SignDocumentStatus.DeletionComplete).ToString() } },
                        },
                        ExpressionAttributeNames = new Dictionary<string, string>
                        {
                            { "#hk", nameof(SignDocumentRecord.JobId) },
                            { "#status", nameof(SignDocumentRecord.Status) }
                        },
                        KeyConditionExpression = "#hk = :jobId",
                        FilterExpression = "#status <> :rescindCompleted AND #status <> :deletionComplete"
                    };

                    var response = await _dynamoClient.QueryAsync(query, cancellationToken);

                    if (response.Items.Any())
                    {
                        return response.Items
                            .Select(x => new SignDocumentRecord(x))
                            .ToList();
                    }

                    return new List<SignDocumentRecord>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error retrieving sent documents for jobId: {jobId}");
                    throw;
                }
            }

            private Document Map(FormTemplateModel formTemplate, 
                HashSet<Guid?> uploadedFormIds, 
                HashSet<Guid> sentFormIds, 
                HashSet<Guid> signedFormIds, 
                Guid? packageId, 
                Dictionary<Guid, string> templateIdToMediaPathMap)
            {
                return new Document
                {
                    Id = formTemplate.FormTemplateId,
                    Name = formTemplate.Name,
                    MediaPath = templateIdToMediaPathMap.GetValueOrDefault(formTemplate.FormTemplateId),
                    FireFormRequired = formTemplate.IsFireFormRequired,
                    WaterFormRequired = formTemplate.IsWaterFormRequired,
                    MoldFormRequired = formTemplate.IsMoldFormRequired,
                    IsRequiredForCommercialJob = formTemplate.IsRequiredForCommercialJob,
                    IsRequiredForResidentialJob = formTemplate.IsRequiredForResidentialJob,
                    IsUploaded = uploadedFormIds.Contains(formTemplate.FormTemplateId),
                    IsSentToBeSigned = sentFormIds.Contains(formTemplate.FormTemplateId),
                    IsSigned = signedFormIds.Contains(formTemplate.FormTemplateId),
                    IsRequiredForAllLossTypes = formTemplate.IsRequiredForAllLossTypes,
                    State = formTemplate.State,
                    Country = formTemplate.Country,
                    IsActive = formTemplate.IsActive,
                    InsuranceClient = formTemplate.InsuranceClient,
                    GroupOrder = formTemplate.GroupOrder,
                    FormCategoryDesc = formTemplate.FormCategoryDesc,
                    PackageId = packageId
                };
            }
        }
    }
}
