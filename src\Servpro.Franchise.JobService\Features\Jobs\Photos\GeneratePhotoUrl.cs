﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Data;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class GeneratePhotoUrl
    {
        public class Command : IRequest<ICollection<PreSignedDto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public ICollection<MediaDto> Media { get; set; }
        }

        public class MediaDto
        {
            public Guid ArtifactTypeId { get; set; }
            public bool IsForUpload { get; set; }
            public string Name { get; set; }
            public string ContentType { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class PreSignedDto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public string Key { get; set; }
        }

        public class Handler : IRequestHandler<Command, ICollection<PreSignedDto>>
        {
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(IConfiguration config, IAmazonS3 clientS3)
            {
                _config = config;
                _clientS3 = clientS3;
            }

            public async Task<ICollection<PreSignedDto>> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var s3urls = request.Media.AsEnumerable().Select(media => GetPreSignedUrl(media, request)).ToList();
                return s3urls;
            }


            private PreSignedDto GetPreSignedUrl(MediaDto media, Command request)
            {
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                var Id = Guid.NewGuid();

                var key = $"{request.FranchiseSetId}/{request.JobId}/Photos/{Id}.{media.Name.Split('.')[1]}";

                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = _config[S3MediaBucketNameKey],
                    Key = key,
                    Verb = HttpVerb.PUT,
                    ContentType = media.ContentType ?? "multipart/form-data",
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };

                var preSignedUrlDto = new PreSignedDto
                {
                    Id = Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = media.Name,
                    Key = key
                };

                return preSignedUrlDto;
            }
        }
    }
    
}