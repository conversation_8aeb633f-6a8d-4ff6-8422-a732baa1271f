﻿using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Audit;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobAuditStageUpdated
    {
        public class Event : JobAuditStageUpdatedEvent, IRequest
        {
            public Event(JobAuditStageUpdatedDto jobAuditStageUpdatedDto, Guid correlationId) : base(jobAuditStageUpdatedDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobAuditStageUpdated> _logger;

            public Handler(JobDataContext db, ILogger<JobAuditStageUpdated> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Begin handler with: {@request}", request);

                var wipRecord = await _db.WipRecords.FirstOrDefaultAsync(x => x.Id == request.JobAuditStageUpdated.JobId, cancellationToken);

                _logger.LogInformation("Wip record found: {@wipRecord}", wipRecord);

                if (wipRecord == null)
                {
                    _logger.LogWarning("{event} The Job or WipRecord with Id: {id} Does Not Exist", nameof(JobAuditStageUpdatedEvent), request.JobAuditStageUpdated.JobId);
                    return Unit.Value;
                }

                wipRecord.AuditStage = request.JobAuditStageUpdated.AuditStage;
                wipRecord.JobAuditMasterId = request.JobAuditStageUpdated.MasterId;

                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}
