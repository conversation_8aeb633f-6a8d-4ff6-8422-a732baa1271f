﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.JobService.Models.MicaAutomation;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.Standards.Drying.Calculators.Abstracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Zones
{
    public class ConfirmZone
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
            public bool CanValidate { get; set; }
            public int? RequiredPPD { get; set; }
            public int? AchievedPPD { get; set; }
            public int? RequiredCFM { get; set; }
            public int? AchievedCFM { get; set; }
            // this is only required if there are validation issues when confirming zone
            public JournalNoteInfo JournalNote { get; set; }

            public class JournalNoteInfo
            {
                public Guid JournalNoteVisibilityId { get; set; }
                public DateTime ActionDate { get; set; }
                public string Note { get; set; }
            }
        }
        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.ZoneId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<ConfirmZone> _logger;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMicaAutomationUtility _micaAutomationUtility;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient,
                ILogger<ConfirmZone> logger,
                ISessionIdAccessor sessionIdAccessor,
                IMicaAutomationUtility micaAutomationUtility)
            {
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
                _context = context;
                _logger = logger;
                _sessionIdAccessor = sessionIdAccessor;
                _micaAutomationUtility = micaAutomationUtility;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfo.GetUserInfo();
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var job = await GetJobWithLocksAsync(request.JobId, userInfo.FranchiseSetId.Value, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var zone = await GetZoneAsync(request.ZoneId, cancellationToken);

                if (zone is null)
                    throw new ResourceNotFoundException($"Zone not found: {request.ZoneId}");

                await MapDehuValidationData(request, zone, cancellationToken);

                // Ensure there's at least one ZoneNotConfirmed task before updating
                var zoneNotConfirmedTasks = zone.Tasks
                    .Where(x => x.TaskTypeId == TaskTypes.ZoneNotConfirmed && x.JobId == request.JobId)
                    .ToList();

                if (!zoneNotConfirmedTasks.Any())
                {
                    var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == TaskTypes.ZoneNotConfirmed);
                    var newTask = new Models.Drybook.Task
                    {
                        Id = Guid.NewGuid(),
                        FranchiseSetId = userInfo.FranchiseSetId,
                        JobId = zone.JobId,
                        TaskTypeId = TaskTypes.ZoneNotConfirmed,
                        TaskStatusId = TaskStatuses.Completed,
                        TaskPriorityId = TaskPriorities.Medium,
                        Subject = taskType?.DefaultSubject ?? "Zone Not Confirmed",
                        PercentComplete = 100,
                        CompletionDate = DateTime.UtcNow,
                        CreatedBy = userInfo.Username,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedBy = userInfo.Username,
                        ModifiedDate = DateTime.UtcNow,
                    };
                    zone.Tasks.Add(newTask);
                    zoneNotConfirmedTasks.Add(newTask);
                }

                // Update all ZoneNotConfirmed tasks instead of just the first one,
                // since multiple tasks of this type can exist for a single zone.
                zoneNotConfirmedTasks.ForEach(task =>
                {
                    task.CompletionDate = DateTime.UtcNow;
                    task.PercentComplete = 100;
                    task.ModifiedBy = userInfo.Username;
                    task.ModifiedDate = DateTime.UtcNow;
                    task.TaskStatusId = TaskStatuses.Completed;
                });


                // if journal note was provided then the zone was not valid and user
                // was required to enter an exception in which case we need to add a new task
                // and mark it as completed
                if (request.JournalNote != null)
                {
                    var equipmentValidationTask = MapToTask(request, zone, lookups, userInfo);
                    zone.Tasks.Add(equipmentValidationTask);

                    _logger.LogInformation("Publish events {method} , JobId: {jobid}", nameof(ConfirmZone), request?.JobId);
                    var events = GenerateJournalNoteCreatedEvents(userInfo, correlationId, equipmentValidationTask.JournalNotes);
                    await _context.OutboxMessages.AddRangeAsync(events, cancellationToken);
                }
                await _context.SaveChangesAsync(cancellationToken);
                await _micaAutomationUtility.HandleMicaAutomationConditionallyAsync(job,
                    MicaAutomationTrigger.ConfirmZone,
                    correlationId,
                    true,
                    cancellationToken);

                return Unit.Value;
            }

            private async System.Threading.Tasks.Task<Zone> GetZoneAsync(Guid zoneId, CancellationToken cancellationToken)
                => await _context.Zones
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.Room)
                    .Include(x => x.Tasks)
                        .ThenInclude(x => x.JournalNotes)
                    .FirstOrDefaultAsync(x => x.Id == zoneId, cancellationToken);

            private async System.Threading.Tasks.Task<Job> GetJobWithLocksAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
                => await _context.Jobs
                        .Include(x => x.JobCustomAttributes)
                        .Include(x => x.JobLocks)
                    .FirstOrDefaultAsync(x => x.FranchiseSetId == franchiseSetId
                        && x.Id == jobId, cancellationToken);

            private async System.Threading.Tasks.Task MapDehuValidationData(Command request, Zone zone, CancellationToken cancellationToken)
            {
                zone.CanValidateDehuCapacity = request.CanValidate;

                switch (await GetDehuCalculationType(zone, cancellationToken))
                {
                    case DehumidifierCalculationType.PPD:
                        zone.RequiredDehuCapacity = request.RequiredPPD;
                        zone.AchievedDehuCapacity = request.AchievedPPD;
                        break;
                    case DehumidifierCalculationType.CFM:
                        zone.RequiredDehuCapacity = request.RequiredCFM;
                        zone.AchievedDehuCapacity = request.AchievedCFM;
                        break;
                    default:
                        break;
                }
            }

            private async Task<DehumidifierCalculationType> GetDehuCalculationType(Zone zone, CancellationToken cancellationToken)
            {
                var equipmentIds = zone.JobAreas
                    .SelectMany(x => x.EquipmentPlacements)
                    .Select(x => x.EquipmentId);
                var equipments = await _context.Equipments
                    .Include(x => x.EquipmentModel)
                        .ThenInclude(x => x.EquipmentType)
                    .Where(x => equipmentIds.Contains(x.Id)).ToListAsync(cancellationToken);
                var dehuTypesPlaced = equipments
                    .Where(x => x.EquipmentModel.EquipmentType.BaseEquipmentTypeId == BaseEquipmentTypes.Dehumidifier)
                    .Select(x => MapToDehuType(x.EquipmentModel.EquipmentTypeId))
                    .ToList();

                var dehuCalculationType = DehumidifierCalculationType.NA;
                if (dehuTypesPlaced.Any(x => x == DehuType.Desiccant))
                {
                    if (dehuTypesPlaced.Any(x => x == DehuType.Conventional || x == DehuType.LGR))
                        dehuCalculationType = DehumidifierCalculationType.PPD;
                    else
                        dehuCalculationType = DehumidifierCalculationType.CFM;
                }
                else
                {
                    dehuCalculationType = DehumidifierCalculationType.PPD;
                }
                return dehuCalculationType;
            }
            static DehuType MapToDehuType(Guid equipmentTypeId)
            {
                if (equipmentTypeId == EquipmentTypes.ConventionalDehumidifier)
                    return DehuType.Conventional;
                if (equipmentTypeId == EquipmentTypes.DesiccantDehumidifier)
                    return DehuType.Desiccant;
                if (equipmentTypeId == EquipmentTypes.LowGrainRefrigerantDehumidifier)
                    return DehuType.LGR;
                return DehuType.Conventional;
            }

            Models.Drybook.Task MapToTask(
                Command request,
                Models.Drybook.Zone zone,
                GetLookups.Dto lookups,
                UserInfo userInfo)
            {
                var taskType = lookups.TaskTypes.FirstOrDefault(x => x.Id == TaskTypes.EquipmentValidationException);
                var equipmentValidationAlert = new Models.Drybook.Task
                {
                    Id = Guid.NewGuid(),
                    FranchiseSetId = userInfo.FranchiseSetId,
                    PercentComplete = 100,
                    TaskStatusId = TaskStatuses.Completed,
                    CompletionDate = DateTime.UtcNow,
                    JobId = zone.JobId,
                    Subject = $"{taskType.DefaultSubject}: {zone.Name}",
                    TaskTypeId = TaskTypes.EquipmentValidationException,
                    TaskPriorityId = TaskPriorities.Medium,
                    JournalNotes = new List<Models.JournalNote>
                    {
                        new Models.JournalNote
                        {
                            ActionDate = request.JournalNote.ActionDate,
                            Author = userInfo.Name,
                            CategoryId = JournalNoteCategories.Validation,
                            VisibilityId = request.JournalNote.JournalNoteVisibilityId,
                            TypeId = JournalNoteTypes.Day1EquipmentValidationException,
                            Subject = $"{taskType.DefaultDiaryEntrySubject}: {zone.Name}",
                            Note = request.JournalNote.Note,
                            CreatedBy = userInfo.Username,
                            CreatedDate = DateTime.UtcNow,
                            JobId = request.JobId,
                        }
                    }
                };
                return equipmentValidationAlert;
            }

            private IEnumerable<OutboxMessage> GenerateJournalNoteCreatedEvents(UserInfo userInfo, Guid correlationId, IEnumerable<JournalNote> createdJournalNotes)
            {
                _logger.LogInformation("Generate {event}", nameof(GenerateJournalNoteCreatedEvents));

                var journalNoteCreatedEvents = createdJournalNotes
                    .Select(x => MapJournalNoteCreatedDto(x, userInfo))
                    .Select(x => new JournalNoteCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username))
                    .ToList();

                return journalNoteCreatedEvents;
            }

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteCreatedDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id
                };
        }
    }
}
