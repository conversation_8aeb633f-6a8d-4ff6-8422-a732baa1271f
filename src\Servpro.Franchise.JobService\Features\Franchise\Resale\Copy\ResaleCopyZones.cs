﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyZones
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> ZoneIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyZones>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyZones> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyZones> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(Zone));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.ZoneIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var zoneTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedZoneIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(zoneTargetIds, 
                    GetZoneIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<Zone, ResaleZone>(
                    request.ResaleId,
                    zone =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(zone.JobId))
                            failedDependencies.Add((nameof(Job), zone.JobId));

                        return failedDependencies;
                    },
                    alreadyCopiedZoneIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Zones.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Zone>> GetSourceEntitiesAsync(List<Guid> zoneIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var zones = await _context.Zones
                    .Where(z => zoneIds.Contains(z.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", zones.Count);
                return zones;
            }

            private async Task<List<Guid>> GetZoneIdsAsync(List<Guid?> zoneTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Zones
                    .Where(x => zoneTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
