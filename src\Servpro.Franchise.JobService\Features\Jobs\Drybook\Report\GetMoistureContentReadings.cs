﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetMoistureContentReadings
    {

        public class Query : IRequest<MoistureContentReadingsDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }

        public class Handler : IRequestHandler<Query, MoistureContentReadingsDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                ILookupServiceClient lookupServiceClient,
                ILogger<GenerateDryingReport> logger)
            {
                _context = context;
                _lookupServiceClient = lookupServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
                _logger = logger;
            }

            public async Task<MoistureContentReadingsDto> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobId = request.Job.Id;
                var job = await _context.Jobs
                    .Include(i => i.JobAreas)
                        .ThenInclude(a => a.JobAreaMaterials)
                            .ThenInclude(jam => jam.JobMaterial)
                    .Include(j => j.JobAreas)
                        .ThenInclude(ja => ja.Zone)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken: cancellationToken);
                _logger.LogInformation("DryingReportLog - starting GetMoistureContentReadings for jobId: {Id}", job.Id);

                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == jobId).AsNoTracking().ToListAsync(cancellationToken);
                job.Zones = await _context.Zones.Where(x => x.JobId == jobId).AsNoTracking().ToListAsync(cancellationToken);

                _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - getting lookups for jobId: {Id}", job.Id);
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                var materialReadingTypes = new Dictionary<Guid, string>
                {
                    {MaterialReadingTypes.Points, "Pts"},
                    {MaterialReadingTypes.Percentage, "%"},
                };

                var visits = job.JobVisits
                    .Select(x => new JobVisit
                    {
                        Id = x.Id,
                        Date = x.Date.GetLocalFranchiseDateTime(request.FranchiseTimeZone),
                        EmployeeInitials = x.EmployeeInitials,
                        IsMissingVisit = false
                    })
                .OrderBy(x => x.Date)
                .ToList();

                if (!visits.Any())
                {
                    _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - no visits for jobId: {Id}", job.Id);
                    return new MoistureContentReadingsDto();
                }

                _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - getting job areas for jobId: {Id}", job.Id);
                var areas = job
                    .JobAreas
                    .Where(x => !(x.ZoneId is null)) //some areas could not be assigned to a zone
                    .ToList();

                var jobAreaZones = (from jobArea in areas
                                    let beginVisit = visits.FirstOrDefault(v => v.Id == jobArea.BeginJobVisitId)
                                    let endVisit = visits.FirstOrDefault(v => v.Id == jobArea.EndJobVisitId)
                                    let zoneType = jobArea.Zone?.ZoneTypeId
                                    select new
                                    {
                                        JobAreaId = jobArea.Id,
                                        jobArea.ZoneId,
                                        jobArea.Zone?.ZoneTypeId,
                                        BeginVisitDate = beginVisit?.Date,
                                        EndVisitDate = endVisit?.Date,
                                        ZoneType = lookups.ZoneTypes.FirstOrDefault(x => x.Id == zoneType)?.Name,
                                        ZoneName = jobArea.Zone?.Name,
                                        JobAreaName = jobArea.Name,
                                        jobArea.IsUsedInValidation
                                    }).ToList();

                _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - getting materials for jobId: {Id}", job.Id);
                var materials = areas
                    .Where(ja => ja.Zone?.ZoneTypeId == ZoneTypes.Drying)
                    .SelectMany(ja => ja.JobAreaMaterials
                        .Select(jobAreaMaterial => new
                        {
                            jobAreaMaterial.JobAreaId,
                            JobAreaMaterialId = jobAreaMaterial.Id,
                            RoomName = ja.Name,
                            MaterialName = jobAreaMaterial.JobMaterial.Name,
                            ObjectName = lookups.Objects.First(z => z.Id == jobAreaMaterial.JobMaterial.ObjectId)?.Name ?? "",
                            jobAreaMaterial.JobMaterial.Goal,
                            jobAreaMaterial.JobMaterial.MaterialReadingTypeId,
                            jobAreaMaterial.RemovedOnJobVisitId,
                        }))
                    .ToList();

                // Add missing visits...
                var firstVisitDate = visits.First().Date.Date;
                var lastVisitDate = visits.Last().Date.Date;
                const int firstDayIndex = 0;
                var lastDayIndex = 1 + lastVisitDate.Subtract(firstVisitDate).Days;
                var allDates = Enumerable
                    .Range(firstDayIndex, lastDayIndex)
                    .Select(offset => new JobVisit
                    {
                        Id = Guid.Empty,
                        Date = firstVisitDate.AddDays(offset),
                        EmployeeInitials = string.Empty,
                        IsMissingVisit = true
                    })
                    .ToArray();
                _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - dates for job: {allDates}  jobId: {Id}", allDates, job.Id);

                var existingDates = from d in allDates
                                    join v in visits
                                    on d.Date equals v.Date.Date
                                    select d;

                var missingDates = allDates.Except(existingDates);
                visits.AddRange(missingDates.ToList());
                var indexedVisits = visits
                    .OrderBy(v => v.Date)
                    .AsEnumerable()
                    .Select((v, i) => new
                    {
                        v.Id,
                        v.EmployeeInitials,
                        v.Date,
                        v.IsMissingVisit,
                        Index = i + 1,
                    })
                    .ToList();

                _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - getting jobAreaMaterialReadings for jobId: {Id}", job.Id);
                var readings = await _context.JobAreaMaterialReadings
                    .Where(jamr => jamr.JobVisit.JobId == job.Id)
                    .Select(jamr => new
                    {
                        jamr.JobAreaMaterial.JobAreaId,
                        jamr.JobVisitId,
                        jamr.JobAreaMaterialId,
                        jamr.Value,
                        VisitDate = jamr.JobVisit.Date,
                    })
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                var readingsWithZone = readings
                    .Select(r => new
                    {
                        reading = r,
                        jobAreaZone = jobAreaZones
                            .FirstOrDefault(jaz => jaz.JobAreaId == r.JobAreaId
                            && jaz.BeginVisitDate <= r.VisitDate
                            && (!jaz.EndVisitDate.HasValue || jaz.EndVisitDate >= r.VisitDate)
                            )
                    })
                    .Where(rj => rj.jobAreaZone != null)
                    .Select(rj => new
                    {
                        rj.reading.JobVisitId,
                        rj.reading.JobAreaMaterialId,
                        rj.reading.JobAreaId,
                        rj.reading.Value,
                        rj.jobAreaZone.ZoneId,
                    })
                    .ToList();

                var zoneVisitMaterialReadings = (from jaz in jobAreaZones.Where(x => x.ZoneTypeId == ZoneTypes.Drying)
                                                 join m in materials on jaz.JobAreaId equals m.JobAreaId
                                                 join v in indexedVisits on 1 equals 1
                                                 let reading = readingsWithZone
                                                    .Where(r => r.ZoneId == jaz.ZoneId && r.JobAreaId == jaz.JobAreaId && r.JobVisitId == v.Id && r.JobAreaMaterialId == m.JobAreaMaterialId)
                                                    .OrderByDescending(r => r.Value)
                                                    .FirstOrDefault()
                                                 select new
                                                 {
                                                     // IDs
                                                     jaz.ZoneId,
                                                     jaz.JobAreaId,
                                                     m.JobAreaMaterialId,
                                                     JobVisitId = v.Id,
                                                     //
                                                     Zone = jaz.ZoneName,
                                                     Room = jaz.JobAreaName,
                                                     Material = $"{m.MaterialName} - {m.ObjectName}",
                                                     RemovedOnVisit = v.Id == m.RemovedOnJobVisitId,
                                                     PointsOrPercent = materialReadingTypes[m.MaterialReadingTypeId],
                                                     m.Goal,
                                                     VisitIndex = v.Index,
                                                     VisitDate = v.Date,
                                                     Technician = v.EmployeeInitials,
                                                     v.IsMissingVisit,
                                                     ReadingValue = reading?.Value,
                                                 })
                                                 .ToList();

                var withPreviousReading = (from x in zoneVisitMaterialReadings
                                           let prevReading = zoneVisitMaterialReadings
                                               .Where(prev =>
                                                      !prev.IsMissingVisit
                                                   && prev.ReadingValue.HasValue
                                                   && prev.ZoneId == x.ZoneId
                                                   && prev.VisitIndex < x.VisitIndex
                                                   && prev.JobAreaId == x.JobAreaId
                                                   && prev.JobAreaMaterialId == x.JobAreaMaterialId)
                                               .OrderBy(zvmr => zvmr.VisitDate)
                                               .FirstOrDefault()
                                           select new
                                           {
                                               x.Zone,
                                               x.Room,
                                               x.Material,
                                               x.PointsOrPercent,
                                               x.Goal,
                                               x.VisitIndex,
                                               x.VisitDate,
                                               x.Technician,
                                               x.IsMissingVisit,
                                               x.RemovedOnVisit,
                                               x.ReadingValue,
                                               PreviousReadingValue = prevReading == null ? (int?)null : prevReading.ReadingValue,
                                           })
                                        .ToList();

                _logger.LogDebug("DryingReportLog - GetMoistureContentReadings - getting withPreviousReading's withDryingStatus for jobId: {Id}", job.Id);
                var withDryingStatus = (from x in withPreviousReading
                                        select new MoistureContentReading
                                        {
                                            Zone = x.Zone,
                                            Room = x.Room,
                                            Material = x.Material,
                                            PointsOrPercent = x.PointsOrPercent,
                                            Goal = x.Goal,
                                            VisitIndex = x.VisitIndex,
                                            VisitDate = x.VisitDate,
                                            Technician = x.Technician,
                                            IsMissingVisit = x.IsMissingVisit,
                                            ReadingValue = x.ReadingValue,
                                            PreviousReadingValue = x.PreviousReadingValue,
                                            RemovedOnVisit = x.RemovedOnVisit,
                                            GoalMet = x.ReadingValue <= x.Goal,
                                            NotDrying = x.ReadingValue >= x.PreviousReadingValue,
                                        })
                                        .ToList();

                _logger.LogInformation("DryingReportLog - GetMoistureContentReadings - completed for jobId: {Id}", job.Id);
                return new MoistureContentReadingsDto
                {
                    MoistureContentReadings = withDryingStatus
                };
            }
        }
    }
}
