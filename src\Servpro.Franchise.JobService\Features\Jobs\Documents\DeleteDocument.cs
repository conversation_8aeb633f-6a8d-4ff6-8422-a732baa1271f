﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Mapping;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class DeleteDocument
    {
        public class Command : IRequest<bool>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public ICollection<Guid> Ids { get; set; } = new List<Guid>();
        }
        
        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.Ids).NotEmpty();
                RuleForEach(m => m.Ids).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, bool>
        {
            private readonly JobDataContext _db;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILogger<DeleteDocument> _logger;
            private readonly IDocuSignUtility _docuSignUtility;

            public Handler(JobDataContext db, 
                IUserInfoAccessor userInfoAccessor, 
                ISessionIdAccessor sessionIdAccessor,
                ILogger<DeleteDocument> logger,
                IDocuSignUtility docuSignUtility)
            {
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _db = db;
                _logger = logger;
                _docuSignUtility = docuSignUtility;
            }

            public async Task<bool> Handle(Command request,
                CancellationToken cancellationToken)
            {
                using var jobIdScope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Handler began with request: {@payload}", request);

                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                
                var mediaMetaData = await _db.MediaMetadata
                    .Include(x=> x.JobInvoice)
                    .Where(x => x.JobId == request.JobId &&
                                x.FranchiseSetId == request.FranchiseSetId &&
                                x.MediaTypeId == MediaTypes.Document &&
                                request.Ids.Contains(x.Id) &&
                                !x.IsDeleted)
                    .ToListAsync(cancellationToken);

                var deletedDocuments = new List<DocumentDeletedEvent.DocumentDto>();
                foreach (var media in mediaMetaData)
                {
                    media.IsDeleted = true;
                    media.ModifiedDate = DateTime.UtcNow;
                    await _docuSignUtility.DeleteFormIfExistsInDocuSignAsync(media.JobId, media.Id, media.FranchiseSetId, cancellationToken);
                    deletedDocuments.Add(new DocumentDeletedEvent.DocumentDto(media.Id,
                        media.JobId,
                        media.FranchiseSetId,
                        media.ModifiedDate ?? DateTime.UtcNow,
                        media.FormTemplateId));
                }

                await HandleInvoiceData(request.JobId, mediaMetaData, userInfo, correlationId, cancellationToken);
                await RaiseDocumentDeletedEventAsync(deletedDocuments, correlationId, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Handler completed successfully!");

                return true;
            }
            
            private async Task RaiseDocumentDeletedEventAsync(List<DocumentDeletedEvent.DocumentDto> documentsDeleted, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising DocumentDeletedEvent");
                var documentDeletedEvent = new DocumentDeletedEvent(documentsDeleted, correlationId);
                var outboxMessage = new OutboxMessage(documentDeletedEvent.ToJson(), nameof(DocumentDeletedEvent), correlationId, nameof(DeleteDocument));
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }

            private async Task HandleInvoiceData(
                Guid jobId,
                List<MediaMetadata> mediaMetadata,
                UserInfo userInfo,
                Guid correlationId,
                CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);
                var invoices = mediaMetadata
                    .Where(x => x.JobInvoice != null)
                    .Select(x => x.JobInvoice)
                    .ToList();

                if (!invoices.Any())
                    return;

                // Generate InvoiceDeleted events
                foreach (var invoice in invoices)
                {
                    job.TotalRevenue -= Convert.ToDecimal(invoice.Amount);

                    var invoiceEventDto = MapInvoiceDeletedDto(jobId, invoice, userInfo.Username);
                    var invoiceEvent = new InvoiceDeletedEvent(invoiceEventDto, correlationId);
                    var outboxMessage = new OutboxMessage(invoiceEvent.ToJson(), nameof(InvoiceDeletedEvent),
                        correlationId, userInfo.Username);
                    await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                }

                await GenerateJobDetailsUpdatedEvent(job, userInfo);
            }

            private InvoiceDeletedEvent.InvoiceDeletedDto MapInvoiceDeletedDto(Guid jobId, JobInvoice invoice, string user)
            {
                return new InvoiceDeletedEvent.InvoiceDeletedDto()
                {

                    JobId = jobId,
                    InvoiceId = invoice.Id,
                    MediaMetadataId = invoice.MediaMetadataId,
                    DeletedUtc = DateTime.UtcNow,
                    DeletedBy = user

                };
            }

            private async Task GenerateJobDetailsUpdatedEvent(Job job, UserInfo userInfo)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var eventDto = JobDetailsUpdatedMapper.MapJobDetailsUpdatedDto(job, false, userInfo);
                var newEvent = new JobDetailsUpdatedEvent(eventDto, correlationId);
                var outboxMessage = new OutboxMessage(newEvent.ToJson(), nameof(JobDetailsUpdatedEvent),
                         correlationId, userInfo.Username);
                await _db.OutboxMessages.AddAsync(outboxMessage);
            }
        }
    }
}