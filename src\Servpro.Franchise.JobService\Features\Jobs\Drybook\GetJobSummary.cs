﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetJobSummary
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public Guid LossTypeId { get; set; }
            public JobProgress JobProgress { get; set; }
            public Guid? SketchSourceId { get; set; } // identifies if a job is associated with Xactimate, Docusketch, or other sketch sources
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                   .Include(j => j.JobAdditionalInfo)
                   .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.JobId})");

                return MapToDto(job);
            }

            private static Dto MapToDto(Job job) => new Dto
            {
                JobId = job.Id,
                LossTypeId = job.LossTypeId,
                JobProgress = job.JobProgress,
                SketchSourceId = job.JobAdditionalInfo?.SketchSourceId
            };
        }
    }
}