﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class ChangedHelpdeskMenuItem : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("8a11252b-c333-4c27-999b-ab744d764990"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.UpdateData(
                table: "MenuItem",
                keyColumn: "Id",
                keyValue: new Guid("9f431564-1812-4998-b373-9e24b9480394"),
                column: "Items",
                value: "[{\"Order\":1,\"Name\":\"Preferences\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Franchise Preferences\",\"Url\":\"FranchisePreferences\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"587231b2-c63b-4003-bd5e-37d0f23bf040\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":4,\"Name\":\"User Management/Alert Subscription\",\"Url\":\"admin/subscription\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"bbc2b295-c725-4913-954f-1484b0546ec4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":5,\"Name\":\"Alert Management\",\"Url\":\"admin/management\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ca31c9d2-5c69-4283-81b1-09ebeb4d23cb\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":6,\"Name\":\"Xact Options\",\"Url\":\"Xactimate\",\"Roles\":[\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseDirectors\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"84561a8c-0a38-496e-85e6-fe07734b4fd1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":7,\"Name\":\"Training Data Refresh\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9c292ab8-baa7-458a-bf13-dc6ee50bbc1e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}],\"Id\":\"36025296-81a9-4010-9c85-2045a3a28b25\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":2,\"Name\":\"Navigation\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Dispatch Status Management\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"581ce55d-591a-484e-ae25-62258957adb4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":2,\"Name\":\"Emergency READY Profile\",\"Url\":\"Account/Login\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9ff4a581-08c0-4633-aeb0-fe64aa0a484b\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":3,\"Name\":\"Job File Audit\",\"Url\":\"jobfileaudit\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"0176aea4-326c-412e-bccb-ea1399b5d674\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":4,\"Name\":\"Manage Active Claims\",\"Url\":\"franchiseclaimsinfo/ActiveClaims.aspx\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d084b944-55fe-4144-9147-85c64e780fdd\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":5,\"Name\":\"Royalty Reporting\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseReceptionistDispatchers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"5d705364-6cb1-48ba-b492-98eeeee04478\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":6,\"Name\":\"ServproNET\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"4a4f5313-5a50-4cf7-a81e-a68b276a5f69\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":7,\"Name\":\"SSO User Management\",\"Url\":\"ARWebAdmin/\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9474842b-d051-441f-8873-67294a04fac2\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":8,\"Name\":\"Disaster Response Manager\",\"Url\":\"StormManager\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"043b3ac6-155c-452d-87c5-2ea7ae592bc7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":9,\"Name\":\"Salesforce Marketing\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"Users - Corporate\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"8dab9bf2-0e40-11eb-9edc-0242ac120004\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}],\"Id\":\"66401d3e-30a3-4668-a430-b9c97d3403d5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":3,\"Name\":\"Help\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"1800 Support\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"533ad319-386a-4e65-a1a8-c9140d3ce4f4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}],\"Id\":\"24e3b472-f222-4280-9f7c-93516bd2d6b0\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}]");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("ff24f295-e0da-42a3-8adc-74469d64a04a"), null, new DateTime(2022, 12, 6, 21, 15, 13, 37, DateTimeKind.Utc).AddTicks(9640), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("ff24f295-e0da-42a3-8adc-74469d64a04a"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.UpdateData(
                table: "MenuItem",
                keyColumn: "Id",
                keyValue: new Guid("9f431564-1812-4998-b373-9e24b9480394"),
                column: "Items",
                value: "[{\"Order\":1,\"Name\":\"Preferences\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Franchise Preferences\",\"Url\":\"FranchisePreferences\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"587231b2-c63b-4003-bd5e-37d0f23bf040\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":4,\"Name\":\"User Management/Alert Subscription\",\"Url\":\"admin/subscription\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"bbc2b295-c725-4913-954f-1484b0546ec4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":5,\"Name\":\"Alert Management\",\"Url\":\"admin/management\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ca31c9d2-5c69-4283-81b1-09ebeb4d23cb\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":6,\"Name\":\"Xact Options\",\"Url\":\"Xactimate\",\"Roles\":[\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseDirectors\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"84561a8c-0a38-496e-85e6-fe07734b4fd1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":7,\"Name\":\"Training Data Refresh\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9c292ab8-baa7-458a-bf13-dc6ee50bbc1e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}],\"Id\":\"36025296-81a9-4010-9c85-2045a3a28b25\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":2,\"Name\":\"Navigation\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Dispatch Status Management\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"581ce55d-591a-484e-ae25-62258957adb4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":2,\"Name\":\"Emergency READY Profile\",\"Url\":\"Account/Login\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9ff4a581-08c0-4633-aeb0-fe64aa0a484b\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":3,\"Name\":\"Job File Audit\",\"Url\":\"jobfileaudit\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"0176aea4-326c-412e-bccb-ea1399b5d674\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":4,\"Name\":\"Manage Active Claims\",\"Url\":\"franchiseclaimsinfo/ActiveClaims.aspx\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d084b944-55fe-4144-9147-85c64e780fdd\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":5,\"Name\":\"Royalty Reporting\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseReceptionistDispatchers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"5d705364-6cb1-48ba-b492-98eeeee04478\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":6,\"Name\":\"ServproNET\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"4a4f5313-5a50-4cf7-a81e-a68b276a5f69\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":7,\"Name\":\"SSO User Management\",\"Url\":\"ARWebAdmin/\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9474842b-d051-441f-8873-67294a04fac2\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":8,\"Name\":\"Disaster Response Manager\",\"Url\":\"StormManager\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\",\"F_AllFranchiseConstructionCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"043b3ac6-155c-452d-87c5-2ea7ae592bc7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":9,\"Name\":\"Salesforce Marketing\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"Users - Corporate\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"8dab9bf2-0e40-11eb-9edc-0242ac120004\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}],\"Id\":\"66401d3e-30a3-4668-a430-b9c97d3403d5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null},{\"Order\":3,\"Name\":\"Help\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Helpdesk\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"533ad319-386a-4e65-a1a8-c9140d3ce4f4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}],\"Id\":\"24e3b472-f222-4280-9f7c-93516bd2d6b0\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null}]");

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("8a11252b-c333-4c27-999b-ab744d764990"), null, new DateTime(2022, 12, 5, 15, 1, 16, 836, DateTimeKind.Utc).AddTicks(7231), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });
        }
    }
}
