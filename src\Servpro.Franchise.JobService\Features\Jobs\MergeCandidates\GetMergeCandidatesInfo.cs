﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.RawSqlModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.Franchise.JobService.Common.MergeCandidates.Common;

namespace Servpro.Franchise.JobService.Features.Jobs.MergeCandidates
{
    public class GetMergeCandidatesInfo
    {
        public class Command : IRequest<IEnumerable<Dto>>
        {
            public Guid TargetJobId { get; set; }
        }
        public class Dto
        {
            public Dto(
                Guid id,
                string projectNumber,
                string lossType,
                string customerName,
                string status,
                DateTime lossDate,
                string address,
                bool isLocked)
            {
                Id = id;
                ProjectNumber = projectNumber;
                LossType = lossType;
                CustomerName = customerName;
                Status = status;
                LossDate = lossDate;
                Address = address;
                IsLocked = isLocked;
            }
            public Guid Id { get; }
            public string ProjectNumber { get; }
            public string LossType { get; }
            public string CustomerName { get; }
            public string Status { get; }
            public DateTime LossDate { get; }
            public string Address { get; }
            public bool IsLocked { get; }
        }

        public class WipRecordDto
        {
            public Guid Id { get; set; }
            public Guid FranchiseId { get; set; }
            public Guid? InsuranceCompanyName { get; set; }
            public string LossAddress1 { get; set; }
        }

        public class Handler : IRequestHandler<Command, IEnumerable<Dto>>
        {
            private readonly JobDataContext _db;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GetMergeCandidatesInfo> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient ;

            private List<JobProgress> ExceptionList = new List<JobProgress> { JobProgress.NotSoldCancelled, JobProgress.OnHold, JobProgress.TurnedDown };
            private Guid SelfPayInsurance = new Guid("000006b3-002f-5365-7276-70726f496e63");

            public Handler(JobDataContext db,
                IUserInfoAccessor userInfo,
                ILookupServiceClient lookupServiceClient,
                ILogger<GetMergeCandidatesInfo> logger,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _db = db;
                _userInfo = userInfo;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
                _franchiseServiceClient = franchiseServiceClient;

            }

            public async Task<IEnumerable<Dto>> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Getting merge candidates information: {@mergeCandidates}", request);
                var userInfo = _userInfo.GetUserInfo();
                
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var lossTypes = lookups.LossTypes.ToDictionary(x => x.Id, x => x.Name);
                var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(userInfo.FranchiseSetId.Value, cancellationToken);
                var timeZone = TimeZoneHelper.GetWindowsTimeZoneIdentifier(franchiseSet.PrimaryTimeZoneId);

                _logger.LogDebug("HandleMergeCandidates: Handling Merge Candidate Check for job: {jobId}", request.TargetJobId);
                List<MergeCandidate> mergeCandidates = await _db.Set<MergeCandidate>()
                    .FromSqlRaw("CALL findMergeCandidates({0})", request.TargetJobId)
                    .ToListAsync(cancellationToken);
                _logger.LogDebug("HandleMergeCandidates: Job {jobId}: Merge Candidates Found: {@mergeCandidates}", request.TargetJobId, mergeCandidates);

                WipRecord wipRecord = await _db.WipRecords.FirstOrDefaultAsync(x => x.Id == request.TargetJobId, cancellationToken);

                var filteredMergeCandidates = mergeCandidates.Where(x => IsMergeCandidate(wipRecord, x));

                //When an address is changed, the jobs with the old address do not get updated. This fixes the rare occurance
                if (wipRecord.MergeCandidatesCount != filteredMergeCandidates.Count())
                {
                    wipRecord.MergeCandidatesCount = filteredMergeCandidates.Count();
                    await _db.SaveChangesAsync(cancellationToken);
                }

                IEnumerable<Guid> mergeCandidateIds = filteredMergeCandidates
                    .Select(x => x.Id);

                var mergeCandidatesInfo = await _db.Jobs
                    .AsNoTracking()
                    .Include(x => x.Customer)
                    .Include(x => x.JobLocks)
                    .Where(x => mergeCandidateIds.Contains(x.Id) && x.FranchiseSetId == userInfo.FranchiseSetId)
                    .Select(x => new { x.Id, x.ProjectNumber, x.Customer, x.JobProgress, x.JobDates, x.LossAddress, x.LossTypeId, x.CurrentJobLock })
                    .ToListAsync(cancellationToken);

                return mergeCandidatesInfo
                    .Select(x => new Dto(
                        x.Id,
                        x.ProjectNumber,
                        lossTypes.ContainsKey(x.LossTypeId) ? lossTypes[x.LossTypeId] : "unkown",
                        x.Customer?.FullName,
                        x.JobProgress.DisplayName(),
                        x.JobDates.FirstOrDefault(x => x.JobDateTypeId == JobDateTypes.LossOccurred)?.Date.ConvertDateFromUtc(timeZone) ?? DateTime.MinValue,
                        x.LossAddress.ToString(),
                        (x.CurrentJobLock != null ? x.CurrentJobLock.IsLocked : false)));
            }
        }
    }
}
