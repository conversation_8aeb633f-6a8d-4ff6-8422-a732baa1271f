﻿using System;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class AllowJobSubmissionForDispatchDto
    {
        public JobInfoDto JobInfo { get; set; }
        public string InsuranceName { get; set; }
        public string ParentInsuranceName { get; set; }
    }
    public class AllowJobSubmissionForDispatchResponseDto
    {
        public bool Allowed { get; set; }
        public JobInfoDto JobInfo { get; set; }
        public string InsuranceName { get; set; }
        public string ParentInsuranceName { get; set; }
    }
    public class JobInfoDto
    {
        public Guid JobId { get; set; }
        public Guid CorporateJobNumber { get; set; }
        public int FranchiseId { get; set; }
        public string InsuranceClaimNumber { get; set; }
        public Guid LegacyRecordId { get; set; }
        public string LossType { get; set; }
        public Guid StructureType { get; set; }
        public string LossState { get; set; }
        public string LossCountry { get; set; }
        public string LossPostalCode { get; set; }
        public string JobSource { get; set; }
        public string FranchiseNumber { get; set; }
        public string FranchiseType { get; set; }
        public bool IsStormJob { get; set; }
        public bool IsStreamlined { get; set; }
        public int InsuranceClientId { get; set; }
        public string XactMasterFileNumber { get; set; }
    }

}
