﻿using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeFinancialInformation : IJobMergeSection
    {
        public void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            var sourceInvoices = sourceJob.MediaMetadata
                .Where(x => x.ArtifactTypeId == ArtifactTypes.Invoice).ToList();

            foreach (var invoice in sourceInvoices)
            {
                invoice.JobId = targetJob.Id;
            }

            targetJob.TotalAmountDue += sourceJob.TotalAmountDue;
            targetJob.TotalRevenue += sourceJob.TotalRevenue;
            sourceJob.TotalAmountDue = 0;
            sourceJob.TotalRevenue = 0;
        }
    }
}