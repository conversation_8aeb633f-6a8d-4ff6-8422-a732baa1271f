﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetRoomDetail
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId, Guid roomId)
            {
                JobId = jobId;
                RoomId = roomId;
            }

            public Guid JobId { get; set; }
            public Guid RoomId { get; set; }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public Guid JobAreaId { get; set; }
            public Guid? RoomTypeId { get; set; }
            public Guid RoomId { get; set; }
            public string RoomName { get; set; }
            public Guid? ZoneId { get; set; }
            public string ZoneName { get; set; }
            public DateTime? VisitDate { get; set; }
            public DateTime? ReassignedDated { get; set; }
            public decimal TotalSquareFeet { get; set; }
            public decimal AffectedSquareFeet { get; set; }
            public decimal AffectedPercentage { get; set; }
            public DateTime InsertionDate { get; set; }
            public DateTime? LastModified { get; set; }
            public Guid RoomShapeId { get; set; }
            public bool IsOddShaped { get; set; }
            public int Width1TotalInches { get; set; }
            public int Width2TotalInches { get; set; }
            public int Length1TotalInches { get; set; }
            public int Length2TotalInches { get; set; }
            public int Length3TotalInches { get; set; }
            public int Height1TotalInches { get; set; }
            public int Height2TotalInches { get; set; }
            public int Height3TotalInches { get; set; }
            public int Width3TotalInches { get; set; }
            public int RoomOrder { get; set; }
            public decimal AffectedCeilingAreaSquareFeet { get; set; }
            public decimal AffectedWallAreaSquareFeet { get; set; }
            public DimensionDetailDto DimensionDetail { get; set; }
            public IEnumerable<AdjustmentDto> Adjustments { get; set; }
            public IEnumerable<MissingSpaceDto> MissingSpaces { get; set; }
            public IEnumerable<AffectedFlooringDto> AffectedFlooring { get; set; }
            public PreExistingConditionsDto PreExistingConditions { get; set; }
            public long TotalOffsetsSquareFootage { get; set; }
            public long TotalInsetsSquareFootage { get; set; }
            public long TotalOpeningsSquareFootage { get; set; }
            public Guid? ExpressDimensionImageKey { get; set; }
            public bool? RequiresContainment { get; set; }
            public long PercentageContained { get; set; }
            public long NetVolume { get; set; }
        }

        public class DimensionDetailDto
        {
            public bool IsSlopeOrientedAlongLength { get; set; }
            public long RoomVolumeCubicInches { get; set; }
            public long MissingRoomVolumeCubicInches { get; set; }
            public long OffsetRoomVolumeCubicInches { get; set; }
            public CalculationDto Calculations { get; set; }
            public FloorDimensionDto FloorDimensions { get; set; }
            public CeilingDimensionDto CeilingDimensions { get; set; }
            public WallDimensionDto WallDimensions { get; set; }
        }

        public class CalculationDto
        {
            public decimal FloorAreaSquareFeet { get; set; }
            public decimal OffsetFloorAreaSquareFeet { get; set; }
            public decimal MissingFloorAreaSquareFeet { get; set; }
            public decimal FloorAreaTotalSquareFeet { get; set; }
            public decimal FloorAreaSquareYards { get; set; }
            public decimal OffsetFloorAreaSquareYards { get; set; }
            public decimal MissingFloorAreaSquareYards { get; set; }
            public decimal FloorAreaTotalSquareYards { get; set; }
            public decimal FloorPerimeterFeet { get; set; }
            public decimal OffsetFloorPerimeterFeet { get; set; }
            public decimal MissingFloorPerimeterFeet { get; set; }
            public decimal FloorTotalPerimeterFeet { get; set; }
            public decimal CeilingAreaSquareFeet { get; set; }
            public decimal OffsetCeilingAreaSquareFeet { get; set; }
            public decimal MissingCeilingAreaSquareFeet { get; set; }
            public decimal CeilingAreaTotalSquareFeet { get; set; }
            public decimal CeilingAreaSquareYards { get; set; }
            public decimal OffsetCeilingAreaSquareYards { get; set; }
            public decimal MissingCeilingAreaSquareYards { get; set; }
            public decimal CeilingAreaTotalSquareYards { get; set; }
            public decimal CeilingPerimeterFeet { get; set; }
            public decimal OffsetCeilingPerimeterFeet { get; set; }
            public decimal MissingCeilingPerimeterFeet { get; set; }
            public decimal CeilingTotalPerimeterFeet { get; set; }
            public decimal WallAreaSquareFeet { get; set; }
            public decimal OffsetWallAreaSquareFeet { get; set; }
            public decimal MissingWallAreaSquareFeet { get; set; }
            public decimal WallAreaTotalSquareFeet { get; set; }
            public decimal WallAreaSquareYards { get; set; }
            public decimal OffsetWallAreaSquareYards { get; set; }
            public decimal MissingWallAreaSquareYards { get; set; }
            public decimal WallAreaTotalSquareYards { get; set; }
            public decimal RoomVolumeCubicFeet { get; set; }
            public decimal OffsetRoomVolumeCubicFeet { get; set; }
            public decimal MissingRoomVolumeCubicFeet { get; set; }
            public decimal RoomVolumeTotalCubicFeet { get; set; }
            public decimal WallAndCeilingAreaSquareFeet { get; set; }
            public decimal OffsetWallAndCeilingAreaSquareFeet { get; set; }
            public decimal MissingWallAndCeilingAreaSquareFeet { get; set; }
            public decimal WallAndCeilingAreaTotalSquareFeet { get; set; }


            public decimal NetFloorSquareFeet { get; set; }
            public decimal NetFloorPerimeterFeet { get; set; }
            public decimal NetCeilingSquareFeet { get; set; }
            public decimal NetCeilingPerimeter { get; set; }
            public decimal NetWallSquareFootageFeet { get; set; }
        }

        public class FloorDimensionDto
        {
            public int MissingAreaSquareInches { get; set; }
            public int MissingPerimeterInches { get; set; }
            public int OffsetAreaSquareInches { get; set; }
            public int OffsetPerimeterInches { get; set; }
            public decimal AreaSquareInches { get; set; }
            public int PerimeterInches { get; set; }
            public long NetFloorSquareFootage { get; set; }
            public long NetFloorPerimeter { get; set; }
        }

        public class CeilingDimensionDto
        {
            public int MissingAreaSquareInches { get; set; }
            public int MissingPerimeterInches { get; set; }
            public int OffsetAreaSquareInches { get; set; }
            public int OffsetPerimeterInches { get; set; }
            public int AreaSquareInches { get; set; }
            public int PerimeterInches { get; set; }
            public decimal AffectedAreaSquareInches { get; set; }
            public long NetCeilingSquareFootage { get; set; }
            public long NetCeilingPerimeter { get; set; }
        }

        public class WallDimensionDto
        {
            public int MissingAreaSquareInches { get; set; }
            public int OffsetAreaSquareInches { get; set; }
            public decimal AreaSquareInches { get; set; }
            public decimal AreaBelow2FeetSquareInches { get; set; }
            public decimal AffectedAreaSquareInches { get; set; }
            public decimal AffectedWallAreaAbove2FeetSquareInches { get; set; }
            public decimal AffectedWallAreaBelow2FeetSquareInches { get; set; }
            public long NetWallSquareFootage { get; set; }
        }

        public class AdjustmentDto : SaveRoom.AdjustmentDto
        {
            public Guid RoomAdjustmentId { get; set; }
            public int AreaSquareInches { get; set; }
            public int VolumeCubicInches { get; set; }
        }

        public class MissingSpaceDto : SaveRoom.MissingSpaceDto
        {
            public Guid MissingSpaceId { get; set; }
        }

        public class PreExistingConditionsDto
        {
            public Guid Id { get; set; }
            public string Note { get; set; }
            public DateTime NoteDate { get; set; }
            public string Subject { get; set; }
            public Guid VisibilityId { get; set; }
            public string VisibilityText { get; set; }
        }

        public class AffectedFlooringDto : SaveRoom.AffectedFlooringDto
        {
            public Guid Id { get; set; }
            public Guid RoomId { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobArea = await _context.JobAreas
                    .Include(ja => ja.Room)
                        .ThenInclude(r => r.RoomFlooringTypesAffected)
                    .Include(ja => ja.BeginJobVisit)
                    .Include(ja => ja.Zone)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(ja => ja.RoomId == request.RoomId && ja.JobId == request.JobId && !ja.IsDeleted, cancellationToken);

                jobArea.Job = await _context.Jobs.Include(j => j.JournalNotes)
                                                 .AsNoTracking()
                                                 .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                return Map(jobArea);
            }

            private Dto Map(JobArea jobArea)
            {
                var dto = new Dto();
                if (jobArea != null)
                {
                    var room = jobArea.Room;
                    if (room != null)
                    {
                        var zone = jobArea.Zone;
                        var zoneId = zone?.Id ?? Guid.Empty;
                        var zoneName = zone?.Name ?? "";
                        var journalNote = jobArea.Job.JournalNotes
                            .FirstOrDefault(x => x.Id == room.PreExistingConditionsDiaryNoteId);
                        var visitDate = jobArea?.BeginJobVisit?.Date ?? DateTime.UtcNow;
                        var roomData = RoomUtils.CalculateRoomData(room, 0);
                        PreExistingConditionsDto preExistingConditions = null;
                        if (journalNote != null)
                        {
                            preExistingConditions = new PreExistingConditionsDto
                            {
                                Id = journalNote.Id,
                                Note = journalNote.Note,
                                NoteDate = journalNote.CreatedDate,
                                Subject = journalNote.Subject,
                                VisibilityId = journalNote.VisibilityId,
                                VisibilityText = JournalNoteVisibilityNames.FranchiseClientAndCustomer
                            };
                        }

                        dto = new Dto
                        {
                            JobId = jobArea.JobId,
                            JobAreaId = jobArea.Id,
                            RoomTypeId = room.RoomTypeId,
                            RoomId = room.Id,
                            RoomName = jobArea.Name,
                            ZoneId = zoneId,
                            ZoneName = zoneName,
                            VisitDate = visitDate,
                            TotalSquareFeet = roomData.TotalSqFt,
                            AffectedSquareFeet = roomData.AffectedSqFeet,
                            AffectedPercentage = roomData.AffectedPercentage,
                            InsertionDate = room.CreatedDate,
                            LastModified = room.ModifiedDate,
                            RoomShapeId = room.RoomShapeId,
                            IsOddShaped = false,
                            Width1TotalInches = room.Width1TotalInches,
                            Width2TotalInches = room.Width2TotalInches,
                            Length1TotalInches = room.Length1TotalInches,
                            Length2TotalInches = room.Length2TotalInches,
                            Height1TotalInches = room.Height1TotalInches,
                            Height2TotalInches = room.Height2TotalInches,
                            Height3TotalInches = room.Height3TotalInches,
                            Length3TotalInches = room.Length3TotalInches,
                            Width3TotalInches = room.Width3TotalInches,
                            RoomOrder = room.RoomOrder,
                            AffectedCeilingAreaSquareFeet = room.AffectedCeilingAreaSquareInches / 144m,
                            AffectedWallAreaSquareFeet = room.AffectedWallAreaSquareInches / 144m,
                            Adjustments = MapAdjustments(room.OffsetSpaces),
                            MissingSpaces = MapMissingSpaces(room.MissingSpaces),
                            PreExistingConditions = preExistingConditions,
                            AffectedFlooring = MapAffectedFlooring(room.RoomFlooringTypesAffected),
                            DimensionDetail = MapDimensionDetail(room),
                            TotalOffsetsSquareFootage = room.TotalOffsetsSquareFootage,
                            TotalInsetsSquareFootage = room.TotalInsetsSquareFootage,
                            TotalOpeningsSquareFootage = room.TotalOpeningsSquareFootage,
                            ExpressDimensionImageKey = room.ExpressDimensionImageKey,
                            RequiresContainment = room.RequiresContainment,
                            PercentageContained = room.PercentageContained,
                            NetVolume = room.NetVolume,
                        };
                    }
                }
                return dto;
            }

            private IEnumerable<AdjustmentDto> MapAdjustments(ICollection<OffsetSpace> offsetSpaces)
            {
                foreach (var offsetSpace in offsetSpaces)
                {
                    yield return new AdjustmentDto
                    {
                        RoomAdjustmentId = offsetSpace.Id,
                        RoomAdjustmentTypeId = offsetSpace.OffsetSpaceTypeId,
                        Name = offsetSpace.Name,
                        DepthTotalInches = offsetSpace.DepthTotalInches,
                        HeightTotalInches = offsetSpace.HeightTotalInches,
                        WidthTotalInches = offsetSpace.WidthTotalInches,
                        IsAdjoiningWallRemoved = offsetSpace.IsAdjoiningWallRemoved,
                        IsOppositeWallRemoved = offsetSpace.IsOppositeWallRemoved,
                        IsLeftWallRemoved = offsetSpace.IsLeftWallRemoved,
                        IsRightWallRemoved = offsetSpace.IsRightWallRemoved,
                        IsCeilingAffected = offsetSpace.IsCeilingAffected,
                        Volume = offsetSpace.Volume,
                        CeilingSquareFootage = offsetSpace.CeilingSquareFootage,
                        FloorSquareFootage = offsetSpace.FloorSquareFootage,
                        WallSquareFootage = offsetSpace.WallSquareFootage,
                        Subtype = offsetSpace.Subtype
                    };
                }
            }

            private IEnumerable<MissingSpaceDto> MapMissingSpaces(ICollection<MissingSpace> missingSpaces)
            {
                foreach (var missingSpace in missingSpaces)
                {
                    yield return new MissingSpaceDto
                    {
                        DepthTotalInches = missingSpace.DepthTotalInches,
                        HeightTotalInches = missingSpace.HeightTotalInches,
                        LengthTotalInches = missingSpace.LengthTotalInches,
                        MissingSpaceId = missingSpace.Id,
                        WidthTotalInches = missingSpace.WidthTotalInches,
                        Quantity = missingSpace.Quantity,
                        Name = missingSpace.Name,
                        OtherReason = missingSpace.OtherReason,
                        MissingSpaceRemoveFromTypeId = missingSpace.MissingSpaceRemoveFromTypeId,
                        MissingSpaceReasonTypeId = missingSpace.MissingSpaceReasonTypeId
                    };
                }
            }

            private IEnumerable<AffectedFlooringDto> MapAffectedFlooring(
                ICollection<RoomFlooringTypeAffected> affectedFlooringTypes)
            {
                foreach (var affectedFlooring in affectedFlooringTypes)
                {
                    yield return new AffectedFlooringDto
                    {
                        Id = affectedFlooring.Id,
                        RoomId = affectedFlooring.RoomId,
                        FlooringTypeId = affectedFlooring.FlooringTypeId,
                        OtherText = affectedFlooring.OtherText,
                        IsSalvageable = affectedFlooring.IsSalvageable,
                        IsPadRestorable = affectedFlooring.IsPadRestorable,
                        TotalSquareFeet = affectedFlooring.TotalSquareFeet ?? 0.0m,
                        AffectedPercentage = affectedFlooring.AffectedPercentage ?? 0.0m,
                        SavedSquareFeet = affectedFlooring.SavedSquareFeet ?? 0.0m,
                        SavedPercentage = affectedFlooring.SavedSquareFeet ?? 0.0m
                    };
                }
            }

            private DimensionDetailDto MapDimensionDetail(Room room)
            {
                var dimensions = new DimensionDetailDto
                {
                    IsSlopeOrientedAlongLength = room.IsSlopeOrientedAlongLength,
                    WallDimensions = new WallDimensionDto
                    {
                        MissingAreaSquareInches = room.MissingWallAreaSquareInches,
                        OffsetAreaSquareInches = room.OffsetWallAreaSquareInches,
                        AreaSquareInches = room.WallAreaSquareInches,
                        AreaBelow2FeetSquareInches = room.WallAreaBelow2FeetSquareInches,
                        AffectedAreaSquareInches = room.AffectedWallAreaSquareInches,
                        AffectedWallAreaAbove2FeetSquareInches = room.AffectedWallAreaAbove2FeetSquareInches,
                        AffectedWallAreaBelow2FeetSquareInches = room.AffectedWallAreaBelow2FeetSquareInches,
                        NetWallSquareFootage = room.NetWallSquareFootage,
                    },
                    CeilingDimensions = new CeilingDimensionDto
                    {
                        MissingAreaSquareInches = room.MissingCeilingAreaSquareInches,
                        MissingPerimeterInches = room.MissingCeilingPerimeterInches,
                        OffsetAreaSquareInches = room.OffsetCeilingAreaSquareInches,
                        OffsetPerimeterInches = room.OffsetCeilingPerimeterInches,
                        AreaSquareInches = room.CeilingAreaSquareInches,
                        PerimeterInches = room.CeilingPerimeterInches,
                        AffectedAreaSquareInches = room.AffectedCeilingAreaSquareInches,
                        NetCeilingSquareFootage = room.NetCeilingSquareFootage,
                        NetCeilingPerimeter = room.NetCeilingPerimeter,
                    },
                    FloorDimensions = new FloorDimensionDto
                    {
                        MissingAreaSquareInches = room.MissingFloorAreaSquareInches,
                        MissingPerimeterInches = room.MissingFloorPerimeterInches,
                        OffsetAreaSquareInches = room.OffsetFloorAreaSquareInches,
                        OffsetPerimeterInches = room.OffsetFloorPerimeterInches,
                        AreaSquareInches = room.FloorAreaSquareInches,
                        PerimeterInches = room.FloorPerimeterInches,
                        NetFloorSquareFootage = room.NetFloorSquareFootage,
                        NetFloorPerimeter = room.NetFloorPerimeter,
                    },

                    MissingRoomVolumeCubicInches = room.MissingRoomVolumeCubicInches,
                    OffsetRoomVolumeCubicInches = room.OffsetRoomVolumeCubicInches,
                    RoomVolumeCubicInches = room.RoomVolumeCubicInches
                };

                dimensions.Calculations = MapDimensionCalculation(dimensions);

                return dimensions;
            }

            private CalculationDto MapDimensionCalculation(DimensionDetailDto dimensions)
            {
                var floorDetail = dimensions.FloorDimensions;
                var ceilingDetail = dimensions.CeilingDimensions;
                var wallDetail = dimensions.WallDimensions;
                return new CalculationDto
                {
                    FloorAreaSquareFeet = SquareInchesToSquareFeet(floorDetail.AreaSquareInches),
                    OffsetFloorAreaSquareFeet = SquareInchesToSquareFeet(floorDetail.OffsetAreaSquareInches),
                    MissingFloorAreaSquareFeet = SquareInchesToSquareFeet(floorDetail.MissingAreaSquareInches),
                    FloorAreaTotalSquareFeet = SquareInchesToSquareFeet(floorDetail.AreaSquareInches + floorDetail.OffsetAreaSquareInches - floorDetail.MissingAreaSquareInches),

                    FloorAreaSquareYards = SquareInchesToSquareYards(floorDetail.AreaSquareInches),
                    OffsetFloorAreaSquareYards = SquareInchesToSquareYards(floorDetail.OffsetAreaSquareInches),
                    MissingFloorAreaSquareYards = SquareInchesToSquareYards(floorDetail.MissingAreaSquareInches),
                    FloorAreaTotalSquareYards = SquareInchesToSquareYards(floorDetail.AreaSquareInches + floorDetail.OffsetAreaSquareInches - floorDetail.MissingAreaSquareInches),

                    FloorPerimeterFeet = InchesToFeet(floorDetail.PerimeterInches),
                    OffsetFloorPerimeterFeet = InchesToFeet(floorDetail.OffsetPerimeterInches),
                    MissingFloorPerimeterFeet = InchesToFeet(floorDetail.MissingPerimeterInches),
                    FloorTotalPerimeterFeet = InchesToFeet(floorDetail.PerimeterInches + floorDetail.OffsetPerimeterInches - floorDetail.MissingPerimeterInches),

                    /*Docusketch*/
                    NetFloorSquareFeet = SquareInchesToSquareFeet(floorDetail.NetFloorSquareFootage),
                    NetFloorPerimeterFeet = InchesToFeet(floorDetail.NetFloorPerimeter),

                    CeilingAreaSquareFeet = SquareInchesToSquareFeet(ceilingDetail.AreaSquareInches),
                    OffsetCeilingAreaSquareFeet = SquareInchesToSquareFeet(ceilingDetail.OffsetAreaSquareInches),
                    MissingCeilingAreaSquareFeet = SquareInchesToSquareFeet(ceilingDetail.MissingAreaSquareInches),
                    CeilingAreaTotalSquareFeet = SquareInchesToSquareFeet(ceilingDetail.AreaSquareInches + ceilingDetail.OffsetAreaSquareInches - ceilingDetail.MissingAreaSquareInches),

                    CeilingAreaSquareYards = SquareInchesToSquareYards(ceilingDetail.AreaSquareInches),
                    OffsetCeilingAreaSquareYards = SquareInchesToSquareYards(ceilingDetail.OffsetAreaSquareInches),
                    MissingCeilingAreaSquareYards = SquareInchesToSquareYards(ceilingDetail.MissingAreaSquareInches),
                    CeilingAreaTotalSquareYards = SquareInchesToSquareYards(ceilingDetail.AreaSquareInches + ceilingDetail.OffsetAreaSquareInches - ceilingDetail.MissingAreaSquareInches),

                    CeilingPerimeterFeet = InchesToFeet(ceilingDetail.PerimeterInches),
                    OffsetCeilingPerimeterFeet = InchesToFeet(ceilingDetail.OffsetPerimeterInches),
                    MissingCeilingPerimeterFeet = InchesToFeet(ceilingDetail.MissingPerimeterInches),
                    CeilingTotalPerimeterFeet = InchesToFeet(ceilingDetail.PerimeterInches + ceilingDetail.OffsetPerimeterInches - ceilingDetail.MissingPerimeterInches),

                    /*Docusketch*/
                    NetCeilingSquareFeet = SquareInchesToSquareFeet(ceilingDetail.NetCeilingSquareFootage),
                    NetCeilingPerimeter = InchesToFeet(ceilingDetail.NetCeilingPerimeter),

                    WallAreaSquareFeet = SquareInchesToSquareFeet(wallDetail.AreaSquareInches),
                    OffsetWallAreaSquareFeet = SquareInchesToSquareFeet(wallDetail.OffsetAreaSquareInches),
                    MissingWallAreaSquareFeet = SquareInchesToSquareFeet(wallDetail.MissingAreaSquareInches),
                    WallAreaTotalSquareFeet = SquareInchesToSquareFeet(wallDetail.AreaSquareInches + wallDetail.OffsetAreaSquareInches - wallDetail.MissingAreaSquareInches),

                    WallAreaSquareYards = SquareInchesToSquareYards(wallDetail.AreaSquareInches),
                    OffsetWallAreaSquareYards = SquareInchesToSquareYards(wallDetail.OffsetAreaSquareInches),
                    MissingWallAreaSquareYards = SquareInchesToSquareYards(wallDetail.MissingAreaSquareInches),
                    WallAreaTotalSquareYards = SquareInchesToSquareYards(wallDetail.AreaSquareInches + wallDetail.OffsetAreaSquareInches - wallDetail.MissingAreaSquareInches),

                    RoomVolumeCubicFeet = CubicInchesToCubicFeet(dimensions.RoomVolumeCubicInches),
                    OffsetRoomVolumeCubicFeet = CubicInchesToCubicFeet(dimensions.OffsetRoomVolumeCubicInches),
                    MissingRoomVolumeCubicFeet = CubicInchesToCubicFeet(dimensions.MissingRoomVolumeCubicInches),
                    RoomVolumeTotalCubicFeet = CubicInchesToCubicFeet(dimensions.RoomVolumeCubicInches + dimensions.OffsetRoomVolumeCubicInches - dimensions.MissingRoomVolumeCubicInches),

                    /*Docusketch*/
                    NetWallSquareFootageFeet = SquareInchesToSquareFeet(wallDetail.NetWallSquareFootage),

                    WallAndCeilingAreaSquareFeet = SquareInchesToSquareFeet(wallDetail.AreaSquareInches + ceilingDetail.AreaSquareInches),
                    OffsetWallAndCeilingAreaSquareFeet = SquareInchesToSquareFeet(wallDetail.OffsetAreaSquareInches + ceilingDetail.OffsetAreaSquareInches),
                    MissingWallAndCeilingAreaSquareFeet = SquareInchesToSquareFeet(wallDetail.MissingAreaSquareInches - ceilingDetail.MissingAreaSquareInches),
                    WallAndCeilingAreaTotalSquareFeet = SquareInchesToSquareFeet(wallDetail.AreaSquareInches + ceilingDetail.AreaSquareInches +
                                                                                 wallDetail.OffsetAreaSquareInches + ceilingDetail.OffsetAreaSquareInches -
                                                                                 wallDetail.MissingAreaSquareInches - ceilingDetail.MissingAreaSquareInches),
                };
            }

            private decimal SquareInchesToSquareFeet(decimal inches)
            {
                return Math.Round(inches / 144m, 2);
            }

            private static decimal SquareInchesToSquareYards(decimal inches)
            {
                return Math.Round(inches / 1296m, 2);
            }

            private decimal InchesToFeet(int inches)
            {
                return Math.Round(inches / 12m, 2);
            }

            private decimal InchesToFeet(long inches)
            {
                return Math.Round(inches / 12m, 2);
            }

            private static decimal CubicInchesToCubicFeet(long inches)
            {
                return Math.Round(inches / 1728m, 2);
            }
        }
    }
}
