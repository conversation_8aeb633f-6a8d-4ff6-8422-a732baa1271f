﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class JournalNoteTypes
    {
        public static readonly Guid AcknowledgementNote = new Guid("75171D32-30B5-49A4-9D9F-9B535FAB03D6");
        public static readonly Guid ClientNotificationNote = new Guid("35E3FEC9-3EB4-4CD1-B5DD-5DB94FE44BCD");
        public static readonly Guid ExceptionNote = new Guid("1B939A6C-88C9-4DFB-A1FB-30C169934AE9");
        public static readonly Guid InitialCall = new Guid("9a755018-5f65-4a61-8d42-5f98f85ffa9f");
        public static readonly Guid LeadNote = new Guid("D82EFEE6-7DF7-4290-B71E-312B784E8998");
        public static readonly Guid StatusNote = new Guid("6C4660A1-17F7-4A1F-B4F6-EF98E6AB068E");
        public static readonly Guid ZoneWaterClassOverride = new Guid("6C4660A1-17F7-4A1F-B4F6-EF98E6AB068E");
        public static readonly Guid AdditonalEmergencyResponseServices = new Guid("35f4c5c9-38b2-4078-9039-edeaf764b0ab");
        public static readonly Guid Day1EquipmentValidationException = new Guid("9FA0519F-DB78-4EA9-8ED8-CAB1E9E6D5BC");
        public static readonly Guid RecordOfFixPlacementReadingsDeletion = new Guid("F519BA17-3824-4850-81C9-368D5F4DDEE5");
        public static readonly Guid RecordOfJobMaterialDeletion = new Guid("dd5c9e9e-e35e-499e-9855-6e1e46eeaf14");
    }
}
