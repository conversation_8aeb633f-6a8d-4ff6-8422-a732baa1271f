﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class SaveDocumentMetadata
    {
        public class Command : IRequest<ICollection<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public ICollection<MediaDto> Media { get; set; }
        }

        public class MediaDto
        {
            public Guid ArtifactTypeId { get; set; }
            public bool IsForUpload { get; set; }
            public string Name { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Command, ICollection<Dto>>
        {
            private readonly JobDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(JobDataContext db, IConfiguration config, IAmazonS3 clientS3)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
            }

            public async Task<ICollection<Dto>> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var mediaMetadata = request.Media.Select(x => Map(request, x)).ToList();
                _db.MediaMetadata.AddRange(mediaMetadata);
                await _db.SaveChangesAsync(cancellationToken);

                return (mediaMetadata.AsEnumerable().Select(metadata => GetPreSignedUrl(metadata))).ToList();
            }


            private Dto GetPreSignedUrl(MediaMetadata metadata)
            {
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = _config[S3MediaBucketNameKey],
                    Key = metadata.GetKey(),
                    Verb = HttpVerb.PUT,
                    ContentType = "multipart/form-data",
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };

                var preSignedUrlDto = new Dto
                {
                    Id = metadata.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = metadata.Name                   
                };

                return preSignedUrlDto;
            }

            private MediaMetadata Map(Command command, MediaDto media)
                => new MediaMetadata
                {
                    JobId = command.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    FranchiseSetId = command.FranchiseSetId,
                    IsDeleted = false,
                    IsForUpload = media.IsForUpload,
                    Name = media.Name,
                    MediaTypeId = MediaTypes.Document,
                    ArtifactTypeId = media.ArtifactTypeId
                };
        }
    }
}