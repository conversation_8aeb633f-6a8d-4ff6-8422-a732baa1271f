﻿using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    [Route("api/JournalNotes")]
    public class JournalNotesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<JournalNotesController> _logger;

        public JournalNotesController(
            IMediator mediator,
            ILogger<JournalNotesController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("/api/{jobId}/JournalNotes", Name = "GetJournalNotes")]
        public async Task<ActionResult<IEnumerable<GetJournalNotes.Dto>>> GetAsync(Guid jobId, [FromQuery(Name = "franchiseSetId")] Guid franchiseSetId)
        {
            if (franchiseSetId == Guid.Empty)
                return BadRequest();

            if (User.FranchiseSetId().HasValue && User.FranchiseSetId() != franchiseSetId)
                return StatusCode(StatusCodes.Status403Forbidden);

            var journalNotes = await _mediator.Send(
                new GetJournalNotes.Query { JobId = jobId, FranchiseSetId = franchiseSetId });
            return Ok(journalNotes);
        }

        [HttpGet("/api/{jobId}/JournalNotes/{journalNoteId}", Name = "GetJournalNote")]
        public async Task<ActionResult<GetJournalNote.Dto>> GetJournalNoteAsync(Guid jobId, Guid journalNoteId, [FromQuery(Name = "franchiseSetId")] Guid franchiseSetId)
        {
            if (franchiseSetId == Guid.Empty)
                return BadRequest();

            if (User.FranchiseSetId().HasValue && User.FranchiseSetId() != franchiseSetId)
                return StatusCode(StatusCodes.Status403Forbidden);

            var journalNote = await _mediator.Send(new GetJournalNote.Query()
            {
                JobId = jobId,
                JournalNoteId = journalNoteId
            });

            return Ok(journalNote);
        }

        [HttpPost("/api/{jobId}/JournalNotes", Name = "CreateJournalNote")]
        public async Task<ActionResult<JournalNote>> PostAsync(Guid jobId, [FromBody] CreateJournalNote.Command command)
        {
            if (command.JournalNote == null)
                return BadRequest("JournalNote is required.");

            if (command.FranchiseSetId == Guid.Empty)
                return BadRequest("FranchiseSetId is required.");

            var journalNoteJobId = command.JournalNote.JobId;
            var commandJobId = command.JobId;

            if ((journalNoteJobId != Guid.Empty && journalNoteJobId != jobId) ||
                (commandJobId != Guid.Empty && commandJobId != jobId))
            {
                return BadRequest("The jobId in the request body must match the jobId in the URL.");
            }

            var userFranchiseSetId = User.FranchiseSetId();
            if (userFranchiseSetId.HasValue && userFranchiseSetId != command.FranchiseSetId)
            {
                return StatusCode(StatusCodes.Status403Forbidden, new
                {
                    error = "You are not authorized to access this franchise set."
                });
            }

            command.JournalNote.Author = User.FullName();
            command.JournalNote.JobId = jobId;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpDelete("/api/{jobId}/JournalNotes/{journalNoteId}/delete-journal-note")]
        public async Task<ActionResult> DeleteJournalNote(Guid jobId, Guid journalNoteId, [FromQuery(Name = "franchiseSetId")] Guid franchiseSetId)
        {
            if (franchiseSetId == Guid.Empty)
                return BadRequest();

            if (User.FranchiseSetId().HasValue && User.FranchiseSetId() != franchiseSetId)
                return StatusCode(StatusCodes.Status403Forbidden);

            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (journalNoteId == Guid.Empty)
                return BadRequest(nameof(journalNoteId));

            var command = new DeleteJournalNote.Command()
            {
                Id = journalNoteId,
                JobId = jobId
            };

            var response = await _mediator.Send(command);

            return Ok(response);
        }

        [HttpPut("/api/{jobId}/JournalNotes/{journalNoteId}/edit-journal-note")]
        public async Task<ActionResult<EditJournalNote.Dto>> EditJournalNote
            (Guid jobId, Guid journalNoteId, [FromQuery(Name = "franchiseSetId")] Guid franchiseSetId, [FromBody] EditJournalNote.Command request)
        {
            if (franchiseSetId == Guid.Empty)
                return BadRequest();

            if (User.FranchiseSetId().HasValue && User.FranchiseSetId() != franchiseSetId)
            {
                return StatusCode(StatusCodes.Status403Forbidden, new
                {
                    error = "You are not authorized to access this franchise set."
                });
            }

            var commandJobId = request.JobId;

            if (commandJobId != Guid.Empty && commandJobId != jobId)
            {
                return BadRequest("The jobId in the request body must match the jobId in the URL.");
            }

            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            if (journalNoteId == Guid.Empty)
                return BadRequest(nameof(journalNoteId));

            if (request.Id != Guid.Empty && request.Id != journalNoteId)
            {
                return BadRequest("The journalNoteId in the request body must match the journalNoteId in the URL.");
            }

            request.Id = journalNoteId;

            var editedJournalNote = await _mediator.Send(request);

            return Ok(editedJournalNote);
        }

        [HttpPut("/api/{jobId}/JournalNotes/edit-journal-notes")]
        public async Task<ActionResult<EditJournalNote.Dto>> EditJournalNotes(Guid jobId, [FromBody] EditJournalNotes.Command request)
        {
            if (request.FranchiseSetId == Guid.Empty)
                return BadRequest();

            if (User.FranchiseSetId().HasValue && User.FranchiseSetId() != request.FranchiseSetId)
                return StatusCode(StatusCodes.Status403Forbidden);

            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var journalNotes = await _mediator.Send(request);

            return Ok(journalNotes);
        }

        [HttpGet("/api/contacts/{contactId}/queries/get-journal-notes")]
        public async Task<ActionResult<IEnumerable<GetContactJournalNotes.Dto>>> GetContctJournalNotes
            (Guid contactId, [FromQuery] GetContactJournalNotes.Query query)
        {
            var fsId = User.FranchiseSetId();
            if (!fsId.HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            query.FranchiseSetId = fsId.Value;
            query.ContactId = contactId;

            var journalNotes = await _mediator.Send(query);
            return Ok(journalNotes);
        }

    }
}
