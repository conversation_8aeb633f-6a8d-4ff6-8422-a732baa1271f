﻿using Aspose.Pdf;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook.DryingReportParser;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;

using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using TimeZoneConverter;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class DryingReportHelper
    {
        private const string DateOnlyFormat = "M/d/yyyy";

        private static DateTime ConvertToFranchiseTimeFromUtc(DateTime dateToConvertInUtc, string targetTimeZoneId)
        {
            if (targetTimeZoneId.ToUpper() == "UTC")
            {
                return dateToConvertInUtc;
            }

            // if the DateTimeKind of the incoming value is incompatible,
            // conversion will fail.
            // create a new DateTime with Kind.Unspecified
            // use that for the conversion
            dateToConvertInUtc = new DateTime(dateToConvertInUtc.Year,
                dateToConvertInUtc.Month, dateToConvertInUtc.Day, dateToConvertInUtc.Hour,
                dateToConvertInUtc.Minute, dateToConvertInUtc.Second, DateTimeKind.Unspecified);

            var shouldUseIana = TZConvert.TryWindowsToIana(targetTimeZoneId, out var ianaTimeZoneId);

            var tzi = TZConvert.GetTimeZoneInfo(shouldUseIana ? ianaTimeZoneId: targetTimeZoneId);
            var convertedValue = TimeZoneInfo.ConvertTimeFromUtc(dateToConvertInUtc, tzi);

            return convertedValue;
        }

        public static DateTime? GetLocalFranchiseDateTime(this DateTime? utcDateTime, TimeZoneDto primaryTimeZone)
        {
            if (primaryTimeZone != null)
                return utcDateTime == null ? (DateTime?)null : ConvertToFranchiseTimeFromUtc(utcDateTime.Value, primaryTimeZone.WindowsTimeZoneIdentifier);
            else
                throw new Exception("TimeZone is invalid!");
        }

        public static DateTime GetLocalFranchiseDateTime(this DateTime utcDateTime, TimeZoneDto primaryTimeZone)
        {
            if (primaryTimeZone != null)
                return ConvertToFranchiseTimeFromUtc(utcDateTime, primaryTimeZone.WindowsTimeZoneIdentifier);
            else
                throw new Exception("TimeZone is invalid!");
        }

        public static async Task<TimeZoneDto> GetFranchiseSetPrimaryTimeZone(Guid franchiseSetId,
                IFranchiseServiceClient franchiseServiceClient,
                ILookupServiceClient lookupServiceClient,
                ILogger<GenerateDryingReport> logger)
        {
            //Try to get the PrimaryTimeZoneId from the FranchiseSet
            var franchiseSetInfo = await franchiseServiceClient.GetFranchiseSetAsync(franchiseSetId);
            var lookups = await lookupServiceClient.GetLookupsAsync();
            var lookupsCount = lookups.TimeZones.Count();

            logger.LogDebug("DryingReportLog - retrieving timezone for {franchiseSetId} using timezone lookups with a count of {lookupsCount}, using franchiseSetInfo: {franchiseSetInfo}", franchiseSetId, lookupsCount, franchiseSetInfo);
            //Find assigned timezone
            var timezone = lookups.TimeZones.FirstOrDefault(x => x.Id == franchiseSetInfo.PrimaryTimeZoneId);

            //Return the timezone
            return timezone;
        }

        public static async Task<string> GetTimeZoneString(TimeZoneDto timeZone)
        {
            var timeZoneName = "";
            if (timeZone != null)
                timeZoneName = string.IsNullOrEmpty(timeZone.ShortName) ? "" : timeZone.ShortName;
            return timeZoneName;
        }

        public static async Task<string> GetTimeZoneString(Guid franchiseSetId,
            IFranchiseServiceClient franchiseServiceClient,
            ILookupServiceClient lookupServiceClient,
            ILogger<GenerateDryingReport> logger)
        {

            var timeZone = await GetFranchiseSetPrimaryTimeZone(franchiseSetId, franchiseServiceClient, lookupServiceClient, logger);
            logger.LogDebug("DryingReportLog - timeZone response in DryingReportHelper {timeZone}", timeZone);
            var timeZoneName = "";
            if (timeZone != null)
                timeZoneName = string.IsNullOrEmpty(timeZone.ShortName) ? "" : timeZone.ShortName;
            return timeZoneName;
        }

        public static Document AttachDryingReportModelXml(Document document, GenerateDryingReport.Dto dryingReport)
        {
            var dryingReportModel = DryingReportModelMapper.Map(dryingReport);
            var modelXmlFileBytes = dryingReportModel.AsXmlFileBytes();

            // The Drying Report Parser is expecting the file name of a single underscore. Changing this will result in a failure
            // of the Parser.  Further, the filename contains no extension because this prevents most PDF readers from 
            // opening/viewing/saving the xml file.
            const string expectedFileName = "_";
            var docWithXml = document.EmbedFileAsAttachmentInPdf(modelXmlFileBytes, expectedFileName);
            return docWithXml;
        }

        public static Document EmbedFileAsAttachmentInPdf(this Document doc, byte[] fileToAttach, string nameOfFileToEmbed)
        {
            using (var fileToEmbedStream = new MemoryStream(fileToAttach))
            using (var outputStream = new MemoryStream())
            {
                var fileSpec = new FileSpecification(fileToEmbedStream, nameOfFileToEmbed);
                doc.EmbeddedFiles.Add(fileSpec);
                return doc;
            }
        }
    }
}