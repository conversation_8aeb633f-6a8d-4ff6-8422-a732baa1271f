﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook.ZoneCreatedEvent;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class SaveZone
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public string ZoneName { get; set; }
            public Guid WaterClassId { get; set; }
            public Guid WaterCategoryId { get; set; }
            public bool WaterClassOverridden { get; set; }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid? WaterClassOverrideJournalNoteId { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.ZoneName).NotNull();
                RuleFor(x => x.ZoneName).NotEmpty();
                RuleFor(x => x.WaterCategoryId).NotEmpty();

            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo, ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfo = userInfo;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                var user = _userInfo.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var job = await _context.Jobs
                    .Include(j => j.JobLocks)
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, user.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var zone = new Zone
                {
                    JobId = request.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = user.Username,
                    ModifiedBy = user.Username,
                    Name = request.ZoneName,
                    CanValidateDehuCapacity = false,
                    AchievedDehuCapacity = 0,
                    RequiredDehuCapacity = 0,
                    WaterClassId = request.WaterClassId != WaterClasses.WaterClassEmpty
                        ? request.WaterClassId : WaterClasses.WaterClassNotAssigned,
                    WaterCategoryId = request.WaterCategoryId,
                    AirMoverCalculationTypeId = ZoneTypes.AirMoverCalculationTypeId,
                    ZoneTypeId = ZoneTypes.Drying,
                    WaterClassOverridden = request.WaterClassOverridden,
                    Tasks = GenerateZoneNotConfirmedTask(request)
                };

                if (zone.WaterClassOverridden)
                {
                    var overridenTask = GenerateWaterClassOverridenAlert(request, user);
                    zone.Tasks.Add(overridenTask);
                }

                _context.Zones.Add(zone);

                var events = GenerateEvents(correlationId, user).ToList();
                _context.OutboxMessages.AddRange(events);
                await _context.SaveChangesAsync(cancellationToken);

                return Map(zone);
            }

            private Dto Map(Zone zone)
            {
                Guid? journalId = zone.WaterClassOverridden
                   ? (Guid?)zone.Tasks
                   .FirstOrDefault(q => q.TaskTypeId == TaskTypes.WaterClassOverriden)
                   .JournalNotes.FirstOrDefault().Id
                   : null;

                return new Dto
                {
                    Id = zone.Id,
                    WaterClassOverrideJournalNoteId = journalId
                };
            }

            private static Models.Drybook.Task GenerateWaterClassOverridenAlert(Command request, UserInfo user)
            {
                return new Models.Drybook.Task
                {
                    JobId = request.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = user.Username,
                    ModifiedBy = user.Username,
                    IsSystem = false,
                    IsTemplate = false,
                    TaskPriorityId = TaskPriorities.Medium,
                    TaskStatusId = TaskStatuses.Active,
                    TaskTypeId = TaskTypes.WaterClassOverriden,
                    Subject = $"{request.ZoneName} - {ZoneTypes.ZoneWaterClassOverriden}",
                    Body = ZoneTypes.ZoneWaterClassOverriden,
                    StartDate = DateTime.UtcNow,
                    PercentComplete = 0,
                    JournalNotes = new List<JournalNote>
                        {
                            new JournalNote {
                                JobId = request.JobId,
                                Author = user.Username,
                                Subject = $"{request.ZoneName} - {ZoneTypes.ZoneWaterClassOverriden}",
                                Note = string.Empty,
                                TypeId = JournalNoteTypes.ZoneWaterClassOverride,
                                CategoryId = JournalNoteCategories.Notes,
                                VisibilityId = JournalNoteVisibilities.Franchise,
                                ActionDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc),
                                IsDeleted = false,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                CreatedBy = user.Username,
                                ModifiedBy = user.Username
                            }
                        }
                };
            }

            private ICollection<Models.Drybook.Task> GenerateZoneNotConfirmedTask(Command request)
            {
                var user = _userInfo.GetUserInfo();
                var list = new List<Models.Drybook.Task>
                {
                    new Models.Drybook.Task
                    {
                        JobId = request.JobId,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        CreatedBy = user.Username,
                        ModifiedBy = user.Username,
                        IsSystem = false,
                        IsTemplate = false,
                        TaskPriorityId = TaskPriorities.Medium,
                        TaskStatusId = TaskStatuses.Active,
                        TaskTypeId = TaskTypes.ZoneNotConfirmed,
                        Subject =  $"{request.ZoneName} - {ZoneTypes.ZoneNotConfirmed}",
                        Body = ZoneTypes.ZoneNotConfirmed,
                        StartDate = DateTime.UtcNow,
                        PercentComplete = 0
                    }
                };
                return list;
            }

            private IEnumerable<OutboxMessage> GenerateEvents(Guid correlationId, UserInfo user)
            {
                var zoneCreatedEvents = GenerateZoneCreatedEvents(correlationId, user);
                var taskCreatedEvents = GenerateTaskCreatedEvents(correlationId, user);
                var journalNoteCreatedEvents = GenerateJournalNoteCreatedEvents(correlationId, user);

                var events = zoneCreatedEvents
                    .Concat(taskCreatedEvents)
                    .Concat(journalNoteCreatedEvents)
                    .ToList();

                return events;
            }

            private IEnumerable<OutboxMessage> GenerateZoneCreatedEvents(Guid correlationId, UserInfo userInfo)
            {
                var zoneCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Zone && x.State == EntityState.Added)
                    .Select(x => x.Entity as Zone)
                    .Select(MapZoneCreatedDto)
                    .Select(x => new ZoneCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(ZoneCreatedEvent), correlationId, userInfo.Username));

                return zoneCreatedEvents;
            }

            private IEnumerable<OutboxMessage> GenerateTaskCreatedEvents(Guid correlationId, UserInfo userInfo)
            {
                var tasksCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is Models.Drybook.Task && x.State == EntityState.Added)
                    .Select(x => x.Entity as Models.Drybook.Task)
                    .Select(MapTaskCreatedDto)
                    .Select(x => new TaskCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(TaskCreatedEvent), correlationId, userInfo.Username));

               return tasksCreatedEvents;
            }

            private IEnumerable<OutboxMessage> GenerateJournalNoteCreatedEvents(Guid correlationId, UserInfo userInfo)
            {
                var journalNoteCreatedEvents = _context.ChangeTracker.Entries()
                    .Where(x => x.Entity is JournalNote && x.State == EntityState.Added)
                    .Select(x => x.Entity as JournalNote)
                    .Select(MapJournalNoteDto)
                    .Select(x => new JournalNoteCreatedEvent(x, correlationId))
                    .Select(x => new OutboxMessage(x.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username));

                return journalNoteCreatedEvents;
            }

            private ZoneCreatedDto MapZoneCreatedDto(Zone zone) =>
            new ZoneCreatedDto
            {
                Id = zone.Id,
                JobId = zone.JobId,
                Name = zone.Name,
                Description = zone.Description,
                ZoneTypeId = zone.ZoneTypeId,
                WaterClassId = zone.WaterClassId,
                WaterCategoryId = zone.WaterCategoryId,
                WaterClassOverridden = zone.WaterClassOverridden,
                SketchMediaContentId = zone.SketchMediaContentId,
                AchievedDehuCapacity = zone.AchievedDehuCapacity,
                AirMoverCalculationTypeId = zone.AirMoverCalculationTypeId,
                CanValidateDehuCapacity = zone.CanValidateDehuCapacity,
                RequiredDehuCapacity = zone.RequiredDehuCapacity
            };

            private TaskCreatedEvent.TaskCreatedDto MapTaskCreatedDto(Models.Drybook.Task task) =>
                new TaskCreatedEvent.TaskCreatedDto
                {
                    Id = task.Id,
                    JobId = task.JobId,
                    Body = task.Body,
                    CancellationDate = task.CancellationDate,
                    CompletionDate = task.CompletionDate,
                    DueDate = task.DueDate,
                    EstimateId = task.EstimateId,
                    FranchiseSetId = task.FranchiseSetId,
                    IsSystem = task.IsSystem,
                    IsTemplate = task.IsTemplate,
                    StartDate = task.StartDate,
                    Subject = task.Subject,
                    ZoneId = task.ZoneId,
                    WorkOrderId = task.WorkOrderId,
                    JobTriStateQuestionId = task.JobTriStateQuestionId,
                    JobVisitId = task.JobVisitId,
                    PercentComplete = task.PercentComplete,
                    ReminderDate = task.ReminderDate,
                    TaskPriorityId = task.TaskPriorityId,
                    ZoneReadingId = task.ZoneReadingId,
                    TaskStatusId = task.TaskStatusId,
                    TaskTypeId = task.TaskTypeId
                };

            private JournalNoteCreatedEvent.JournalNoteDto MapJournalNoteDto(JournalNote journalNote) =>
                new JournalNoteCreatedEvent.JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedById = _userInfo.GetUserInfo().Id,
                    CreatedBy = _userInfo.GetUserInfo().Username
                };
        }
    }
}
