﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.JobCompletion
{
    public class GetActivePlacedEquipment
    {
        
        #region Query
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
        }
        #endregion

        #region DTO
        public class Dto
        {
            public Dto(
                Guid equipmentPlacementId,
                string roomName,
                string assetId,
                string equipmentType,
                string serialNumber,
                int? cubicFeetPerMinute,
                int? pintsPerDay,
                decimal amps,
                DateTime dateAdded,
                DateTime? dateRemoved)
            {
                EquipmentPlacementId = equipmentPlacementId;
                RoomName = roomName;
                AssetId = assetId;
                EquipmentType = equipmentType;
                SerialNumber = serialNumber;
                CubicFeetPerMinute = cubicFeetPerMinute;
                PintsPerDay = pintsPerDay;
                Amps = amps;
                DateAdded = dateAdded;
                DateRemoved = dateRemoved;
            }

            public Guid EquipmentPlacementId { get; }
            public string RoomName { get; }
            public string AssetId { get; }
            public string EquipmentType { get; }
            public string SerialNumber { get; }
            public int? CubicFeetPerMinute { get; }
            public int? PintsPerDay { get; }
            public decimal Amps { get; }
            public DateTime DateAdded { get; }
            public DateTime? DateRemoved { get; }
        }
        #endregion

        #region Handler
        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            public readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.JobAreas)
                        .ThenInclude(x => x.EquipmentPlacements)
                        .ThenInclude(x => x.Equipment)
                        .ThenInclude(x => x.EquipmentModel)
                        .ThenInclude(x => x.EquipmentType)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                var activeEquipmentPlacements = job.JobAreas
                    .SelectMany(x => x.EquipmentPlacements)
                    .Where(x => !x.EndDate.HasValue)
                    .Select(x => new Dto(
                        x.Id,
                        x.JobArea.RoomId.HasValue ? x.JobArea.Name : null,
                        x.Equipment.AssetNumber,
                        x.Equipment.EquipmentModel.EquipmentType.Name,
                        x.Equipment.SerialNumber,
                        x.Equipment.CubicFeetPerMinute,
                        x.Equipment.PintsPerDay,
                        x.Equipment.EquipmentModel.Amps,
                        x.BeginDate,
                        x.EndDate));
                return activeEquipmentPlacements;
            }
        }
        #endregion
    }
}
