﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class SaveCallDisposition
    {
        public class Command : IRequest<Unit>
        {
            public List<UpdatedCall> Calls { get; set; } = new List<UpdatedCall>();

        }

        public class UpdatedCall
        {
            public Guid? JobId { get; set; }
            public Guid Disposition { get; set; }
            public Guid CallId { get; set; }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<SaveCallDisposition> _logger;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(ILogger<SaveCallDisposition> logger,
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfoAccessor,
                JobDataContext db,
                ISessionIdAccessor sessionIdAccessor)
            {
                _logger = logger;
                _lookupServiceClient = lookupServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _db = db;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Saving Invoca Call Disposition");
                var user = _userInfoAccessor.GetUserInfo();
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                foreach (var updCall in request.Calls)
                {
                    var disposition = lookups != null ? lookups.CallDispositionTypes.FirstOrDefault(k => k.Id == updCall.Disposition) : null;
                    if (disposition == null && updCall.Disposition != Guid.Empty)
                    {
                        _logger.LogInformation("Call Disposition: {disposition}, not found", updCall.Disposition);
                        throw new ResourceNotFoundException();
                    }
                    await UpdateCallsAndJobs(updCall, user, disposition, lookups, cancellationToken);
                }

                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            public async Task UpdateCallsAndJobs(UpdatedCall request,
                UserInfo user,
                CallDispositionTypeDto disposition,
                GetLookups.Dto lookups,
                CancellationToken cancellationToken)
            {
                var call = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => x.Id == request.CallId, cancellationToken);
                if (call == null)
                {
                    _logger.LogInformation("Call : {call}, not found", request.CallId);
                    throw new ResourceNotFoundException();
                }

                call.ModifiedDate = DateTime.UtcNow;
                call.ModifiedBy = user.Username;

                if (call.Disposition != Guid.Empty && request.Disposition == Guid.Empty)
                    call.DispositionRevokedTime = DateTime.UtcNow;

                if (call.DispositionRevokedTime.HasValue && request.Disposition != Guid.Empty)
                    call.DispositionRevokedTime = null;

                call.Disposition = disposition == null ? Guid.Empty : disposition.Id;
                call.DispositionSelectionUserName = disposition == null ? null : user.Username;

                if (disposition == null)
                    call.DispositionSelectionDateTime = null;
                else
                    call.DispositionSelectionDateTime = DateTime.UtcNow;

                if (call.AssociatedJobId.HasValue)
                {
                    if (request.JobId == Guid.Empty
                        || request.JobId != call.AssociatedJobId)
                    {
                        var linkedJob = await _db.Jobs.FirstOrDefaultAsync(k => k.Id == call.AssociatedJobId, cancellationToken);
                        if (linkedJob != null)
                        {
                            linkedJob.CallRecordingId = null;
                            linkedJob.CallDisposition = null;
                        }
                        call.AssociatedJobId = null;
                        call.AssociatedJobLastUpdatedDateTime = null;
                    }
                }

                if (request.JobId != Guid.Empty)
                {
                    var job = await _db.Jobs.FirstOrDefaultAsync(k => k.Id == request.JobId, cancellationToken);
                    if (job == null)
                    {
                        _logger.LogInformation("Job : {call}, not found", request.JobId);
                        throw new ResourceNotFoundException();
                    }

                    job.CallDisposition = disposition.Id;
                    job.CallRecordingId = call.CallRecordingId; ;

                    var ReferredBy = lookups?.LeadIdentificationMethods.FirstOrDefault(k => k.Name == "Internet/Web Search");
                    if (ReferredBy != null)
                    {
                        job.ReferredById = ReferredBy.Id;
                    }
                    else
                    {
                        _logger.LogWarning("LeadIdentificationMethod with name 'Internet/Web Search' not found in lookup values.");
                        job.ReferredById = Guid.Parse("00000014-009b-5365-7276-70726f496e63");
                    }

                    call.AssociatedJobId = job.Id;
                    call.AssociatedJobLastUpdatedDateTime = DateTime.UtcNow;

                    await GenerateReferredByUpdatedEvent(job, user, cancellationToken);
                }
            }

            private async Task GenerateReferredByUpdatedEvent(Job job, UserInfo userInfo, CancellationToken cancellationToken)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var eventDto = new ReferredByUpdatedEvent.ReferredByUpdatedDto(job.Id, job.ReferredById.Value, userInfo.Username, DateTime.UtcNow);
                var newEvent = new ReferredByUpdatedEvent(eventDto, correlationId);
                var outboxMessage = new OutboxMessage(newEvent.ToJson(), nameof(ReferredByUpdatedEvent),
                         correlationId, userInfo.Username);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
            }
        }
    }
}
