upstream PLACEHOLDER_BACKEND_NAME {
  # The web app.
  server PLACEH<PERSON>DER_BACKEND_NAME:PLACEHOLDER_BACKEND_PORT;
}

# Redirect 'www' addresses to the non-www version, and also take care of
# redirects to HTTPS at the same time.
server {
  listen 80;
  server_name www.PLACEHOLDER_VHOST;
  return 301 https://PLACEHOLDER_VHOST$request_uri;
}

server {
  # "deferred" reduces the number of formalities between the server and client.
  listen 80 default deferred;
  server_name PLACEHOLDER_VHOST;

  # Static asset path, which is read from the PLACEHOLDER_BACKEND_NAME
  # container's VOLUME.
  root /PLACEHOLDER_BACKEND_NAME/public;

  # Serve static assets.
  #
  # gzip_static is enabled because the assets are already gzipped to a maximum
  # level due to Rail's asset pipeline.
  #
  # Add headers to set the maximum amount of cache time.
  #
  # We can do this because the Ruby on Rails asset pipeline md5 hashes all of
  # the file names for us. When a file changes, its md5 will change, and the
  # cache will be automatically busted. Other frameworks can do this as well.
  location ~ ^/assets/ {
    gzip_static on;

    # Set a maximum cache time period and null out a few headers to address
    # certain browsers from occasionally requesting cached content.
    expires max;
    add_header Cache-Control public;
    add_header Last-Modified "";
    add_header ETag "";
  }

  # Ensure timeouts are equal across browsers.
  keepalive_timeout 60;

  # Disallow access to hidden files and directories.
  location ~ /\. {
    return 404;
    access_log off;
    log_not_found off;
  }

  # Allow optionally writing an index.html file to take precedence over the upstream.
  try_files $uri $uri/index.html $uri.html @PLACEHOLDER_BACKEND_NAME;

  # Attempt to load the favicon or fall back to status code 204.
  location = /favicon.ico {
    try_files /favicon.ico = 204;
    access_log off;
    log_not_found off;
  }

  # Force SSL connections on agents (browsers) who support this header.
  add_header Strict-Transport-Security "max-age=31536000; includeSubDomains;";

  # Load the web app back end with proper headers.
  location @PLACEHOLDER_BACKEND_NAME {
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_redirect off;

    if ($http_x_forwarded_proto = "http") {
      return 301 https://$host$request_uri;
    }

    proxy_pass http://PLACEHOLDER_BACKEND_NAME;
  }
}
