﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.DocuSign
{
    [Route("api/jobs/{jobId}/")]
    public class DocuSignController : Controller
    {
        private readonly IMediator _mediator;
        public readonly ILogger<DocuSignController> _logger;

        public DocuSignController(IMediator mediator,
            ILogger<DocuSignController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("queries/get-document-data-for-docusign")]
        public async Task<ActionResult> GetDocumentsForDocuSign(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var documents = await _mediator.Send(new GetDocumentDataForDocuSign.Query(jobId));
            return Ok(documents);
        }


        [HttpPost("commands/send-documents-to-sign")]
        public async Task<ActionResult> SendDocumentsToSign(Guid jobId, [FromBody] SendDocumentsToSign.Command command)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            command.JobId = jobId;

            var documents = await _mediator.Send(command);
            return Ok(documents);
        }

        [HttpPost("commands/rescind-documents")]
        public async Task<ActionResult<bool>> RescindDocsAsync(Guid jobId, [FromBody] RescindDocument.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

    }
}
