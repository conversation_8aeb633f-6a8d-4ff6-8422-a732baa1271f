# ARG IMAGE_VERSION=debug

FROM mcr.microsoft.com/dotnet/aspnet:8.0-bookworm-slim AS base
RUN apt-get update \
    && apt-get install -y --no-install-recommends libgconf-2-4 gnupg git wget curl cabextract xfonts-utils passwd \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y lsb-release google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxtst6 libxss1 gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libnss3 lsb-release xdg-utils \
      --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

RUN wget http://ftp.debian.org/debian/pool/contrib/m/msttcorefonts/ttf-mscorefonts-installer_3.8.1_all.deb
RUN dpkg -i ttf-mscorefonts-installer_3.8.1_all.deb

RUN apt-get remove -y wget

WORKDIR /app

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
ENV chrome_launchOptions_executablePath google-chrome-stable
ENV chrome_launchOptions_args --no-sandbox,--headless
#NOTE: use the chrome args --disable-dev-shm-usage only if docker volume --volume=/dev/shm:/dev/shm IS TOO SMALL, ISSUE: FATAL:memory.cc(22)] Out of memory.

FROM mcr.microsoft.com/dotnet/sdk:8.0-bookworm-slim AS build
WORKDIR /src
COPY ["src/Servpro.Franchise.JobService/Servpro.Franchise.JobService.csproj", "src/Servpro.Franchise.JobService/"]
RUN dotnet restore "src/Servpro.Franchise.JobService/Servpro.Franchise.JobService.csproj" -s https://nuget.servpronet.io/v3/index.json -s https://api.nuget.org/v3/index.json
COPY . .
WORKDIR "/src/src/Servpro.Franchise.JobService"
RUN dotnet build "Servpro.Franchise.JobService.csproj" -c Release -o /app

FROM build AS publish
ARG IMAGE_VERSION
RUN dotnet publish "Servpro.Franchise.JobService.csproj" -c Release -o /app
RUN echo "$IMAGE_VERSION" >> /app/version.txt

FROM base AS final
ARG IMAGE_VERSION
LABEL IMAGE_VERSION=$IMAGE_VERSION
WORKDIR /app
COPY --from=publish /app .
RUN chmod -R 0755 /app; \
    chown -R app:app /app
USER 1654
EXPOSE 8080
ENV ASPNETCORE_HTTP_PORTS=8080
ENTRYPOINT ["dotnet", "Servpro.Franchise.JobService.dll"]
