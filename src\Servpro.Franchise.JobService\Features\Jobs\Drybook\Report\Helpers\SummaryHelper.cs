﻿using Servpro.Franchise.JobService.Models;
using System;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public class SummaryHelper
    {
        public static string GetPhoneNoStringWithExtension(Phone personPhoneNumber)
        {
            if (personPhoneNumber?.PhoneNumber == null) return string.Empty;

            var numberString = personPhoneNumber.PhoneNumber;
            var ext = personPhoneNumber.PhoneExtension;
            var useStandardPhoneFormat = numberString.Length == 10 && numberString.All(char.IsDigit);
            const string stdPhoneFormat = "{0:(###) ###-####}";
            var finalPhoneNo = useStandardPhoneFormat
                ? string.Format(stdPhoneFormat, Convert.ToInt64(numberString))
                : numberString;
            var finalExt = string.IsNullOrWhiteSpace(ext) ? string.Empty : " ext." + ext;
            var finalPhoneNoWithExtension = finalPhoneNo + finalExt;
            return finalPhoneNoWithExtension;
        }
    }
}
