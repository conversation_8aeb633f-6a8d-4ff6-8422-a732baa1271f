﻿using System;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models.Drybook;
using System.Threading;
using System.Linq;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{

    public class SaveHvacZone
    {
        public class Command : IRequest<Guid>
        {
            public Guid JobId { get; set; }
            public string Description { get; set; }
        }


        public class Handler : IRequestHandler<Command, Guid>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfo;

            public Handler(JobDataContext context,
                IUserInfoAccessor userInfo)
            {
                _context = context;
                _userInfo = userInfo;
            }

            public async Task<Guid> Handle(Command request, CancellationToken cancellationToken)
            {
                var user = _userInfo.GetUserInfo();
                var job = await _context.Jobs
                    .Include(j => j.Zones)
                    .FirstOrDefaultAsync(j => j.Id == request.JobId 
                    && j.FranchiseSetId == user.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException("Job does not exist");

                job.JobLocks = await _context.JobLock.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);
                job.JobVisits = await _context.JobVisit.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, user.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var hvacCount = job.Zones
                    .Count(x => x.ZoneTypeId == ZoneTypes.HVAC);
                var defaultHvacName = "HVAC";

                var zone = new Zone
                {
                    Id = Guid.NewGuid(),
                    JobId = request.JobId,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = user.Username,
                    // if same name, then append count
                    Name = hvacCount > 0
                        ? $"{defaultHvacName} {hvacCount + 1}"
                        : defaultHvacName,
                    Description = request.Description,
                    AchievedDehuCapacity = null,
                    RequiredDehuCapacity = null,
                    WaterClassId = WaterClasses.NoWaterDamage,
                    WaterCategoryId = WaterCategories.Sanitary,
                    AirMoverCalculationTypeId = AirMoverCalculationTypes.LinearFeet,
                    ZoneTypeId = ZoneTypes.HVAC,
                    WaterClassOverridden = false,
                    ZoneReadings = job.JobVisits
                        .Select(x => new ZoneReading
                        {
                            JobVisit = x,
                            CreatedBy = user.Username,
                            CreatedDate = DateTime.UtcNow,
                        })
                        .ToList()
                };

                job.Zones.Add(zone);
                await _context.SaveChangesAsync(cancellationToken);
                return zone.Id;
            }
        }
    }
}
