﻿using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    [Route("api/photos")]
    public class PhotosController : ControllerBase
    {
        private readonly IMediator _mediator;

        public PhotosController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet("/api/jobs/{jobId}/photos", Name = "GetPhotos")]
        public async Task<ActionResult<ICollection<GetPhoto.Dto>>> GetPhotosAsync(Guid jobId)
        {

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetPhoto.Query(jobId, User.FranchiseSetId().Value));
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/delete-photos", Name = "DeletePhotos")]
        public async Task<ActionResult<bool>> DeleteAsync(Guid jobId, [FromBody] DeletePhoto.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/save-photo-metadata", Name = "SavePhotoMetadata")]
        public async Task<ActionResult<ICollection<SavePhotoMetadata.Dto>>> PostAsync(Guid jobId, [FromBody] SavePhotoMetadata.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/download-photos", Name = "DownloadPhotos")]
        public async Task<ActionResult<ICollection<SavePhotoMetadata.Dto>>> DownloadPhotosAsync(Guid jobId, [FromBody] DownloadPhoto.Query query)
        {
            if (jobId == Guid.Empty)
                return BadRequest();

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            query.FranchiseSetId = User.FranchiseSetId().Value;

            query.JobId = jobId;
            var response = await _mediator.Send(query);
            return Ok(response);
        }

        [HttpPost("/api/jobs/commands/generate-photos-url", Name = "GeneratePhotoUrl")]
        public async Task<ActionResult<ICollection<SavePhotoMetadata.Dto>>> PostAsync([FromBody] GeneratePhotoUrl.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/save-photos", Name = "SavePhoto")]
        public async Task<ActionResult<ICollection<Guid>>> PostAsync(Guid jobId, [FromBody] SavePhoto.Command command)
        {
            var userFranchiseSetId = User.FranchiseSetId();
            var payloadFranchiseSetId = command.FranchiseSetId;

            if (userFranchiseSetId.HasValue && payloadFranchiseSetId != Guid.Empty)
            {
                if (payloadFranchiseSetId != userFranchiseSetId.Value)
                    return StatusCode(StatusCodes.Status403Forbidden, "FranchiseSetId mismatch between payload and user context/header.");
            }

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var commandJobId = command.JobId;

            if (commandJobId != Guid.Empty && commandJobId != jobId)
            {
                return BadRequest("The jobId in the request body must match the jobId in the URL.");
            }

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/save-readings-photos", Name = "SaveReadingsPhoto")]
        public async Task<ActionResult<ICollection<Guid>>> PostAsync(Guid jobId, [FromBody] SaveReadingsPhoto.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpGet("/api/jobs/{jobId}/queries/get-attachments", Name = "GetAttachments")]
        public async Task<ActionResult<ICollection<GetAttachments.Dto>>> GetAttachmentsAsync(Guid jobId)
        {
            Guid? franchiseSetId = User.FranchiseSetId();
            if (!franchiseSetId.HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetAttachments.Query(jobId, franchiseSetId.Value));
            return Ok(response);
        }

        [HttpGet("/api/jobs/{jobId}/queries/image-types", Name = "GetImageTypes")]
        public async Task<ActionResult<ICollection<GetImageTypes.Dto>>> GetImageTypesAsync(Guid jobId)
        {
            Guid? franchiseSetId = User.FranchiseSetId();
            if (!franchiseSetId.HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetImageTypes.Query(jobId, franchiseSetId.Value));
            return Ok(response);
        }

        [HttpPut("/api/jobs/{jobId}/commands/update-photo", Name = "UpdatePhoto")]
        public async Task<ActionResult<bool>> UpdateAsync(Guid jobId, [FromBody] UpdatePhoto.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPut("/api/jobs/{jobId}/commands/set-isForUpload-photos", Name = "SetIsForUploadPhotos")]
        public async Task<ActionResult<bool>> SetIsForUploadPhotosAsync(Guid jobId, [FromBody] SetIsForUploadPhotos.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }
    }
}