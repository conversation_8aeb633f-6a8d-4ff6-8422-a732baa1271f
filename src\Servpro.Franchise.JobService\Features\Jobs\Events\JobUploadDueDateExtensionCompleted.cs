﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobUploadDueDateExtensionCompleted
    {
        public class Event : JobUploadDueDateExtensionCompletedEvent, IRequest
        {
            public Event(JobUploadDueDateExtensionCompletedEvent.DueDateExtensionCompletedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobUploadDueDateExtensionCompleted> _logger;

            public Handler(JobDataContext db, ILogger<JobUploadDueDateExtensionCompleted> logger)
            {
                _db = db;
                _logger = logger;
            }

            private Guid MapJobUploadType(int jobUploadType)
            {
                switch (jobUploadType)
                {
                    case (int) UploadType.Initial:
                        return JobUploadTypes.InitialExtension;
                    case (int) UploadType.Daily:
                        return JobUploadTypes.DailyExtension;
                    case (int) UploadType.Final:
                        return JobUploadTypes.FinalExtension;
                    default:
                        _logger.LogWarning("Unknown upload type: {uploadType}", jobUploadType);
                        throw new ValidationException($"Unknown upload type: {jobUploadType}");
                }
            }

            public enum UploadType
            {
                Initial = 1,
                Daily = 3,
                Final = 2
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                var eventDto = request.DueDateExtensionCompleted;
                var uploadType = MapJobUploadType(eventDto.JobUploadTypeId);
                var job = await _db.Jobs
                    .FirstOrDefaultAsync(j => j.Id == request.DueDateExtensionCompleted.JobId, cancellationToken: cancellationToken);

                if (job == null)
                {
                    _logger.LogWarning("Job not found for {request}", request);
                    return Unit.Value;
                }
                switch ((UploadType)request.DueDateExtensionCompleted.JobUploadTypeId)
                {
                    case UploadType.Initial:
                        job.InitialExtensionCount += 1;
                        break;
                    case UploadType.Final:
                        job.FinalExtensionCount += 1;
                        break;
                    case UploadType.Daily:
                        // we do not record
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                var jobUploadLock = await _db.JobUploadLocks
                    .FirstOrDefaultAsync(x => x.JobId == eventDto.JobId && x.JobUploadTypeId == uploadType, cancellationToken: cancellationToken);

                if (jobUploadLock != null)
                {
                    _db.JobUploadLocks.Remove(jobUploadLock);
                }
                
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}