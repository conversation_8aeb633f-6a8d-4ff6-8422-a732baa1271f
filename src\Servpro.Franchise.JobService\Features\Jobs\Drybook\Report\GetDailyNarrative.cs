﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Features.Jobs.Xact.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Xact.Narratives;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetDailyNarrative
    {
        public class Query : IRequest<DailyNarrativeDto>
        {
            public Query(Guid jobId, TimeZoneDto franchiseTimeZone = null)
            {
                JobId = jobId;
                FranchiseTimeZone = franchiseTimeZone;
            }

            public Guid JobId { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }

        public class Handler : IRequestHandler<Query, DailyNarrativeDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IXactServiceClient _xactServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;
            private readonly INarrativePreviewCommandFactory _narrativePreviewCommandFactory;

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                IXactServiceClient xactServiceClient,
                ILookupServiceClient lookupServiceClient,
                ILogger<GenerateDryingReport> logger,
                INarrativePreviewCommandFactory narrativePreviewCommandFactory)
            {
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
                _xactServiceClient = xactServiceClient;
                _narrativePreviewCommandFactory = narrativePreviewCommandFactory;
            }

            public async Task<DailyNarrativeDto> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogDebug("DryingReportLog - starting GetDailyNarratives for jobId: {JobId}", request.JobId);

                var job = await _context.Jobs.AsNoTracking()
                                .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found jobId: {request.JobId}");

                var dailyNarrativeRequest =
                    await _narrativePreviewCommandFactory.GetNarrativeRequest(request.JobId, cancellationToken);

                _logger.LogDebug("[GetDailyNarrative.Handle] Daily narrative request job {jobId}: {@dailyNarrativeRequest}", request.JobId, dailyNarrativeRequest);

                _logger.LogDebug("[GetDailyNarrative.Handle] Sending request for job {jobId} to Xact Service: {request}", request.JobId, dailyNarrativeRequest);
                GetNarrativePreviewResponseDto narrativePreviewResponse = await _xactServiceClient.GetNarrativePreview(dailyNarrativeRequest, cancellationToken);
                _logger.LogDebug("[GetDailyNarrative.Handle] Received response for job {jobId} from Xact Service: {response}", request.JobId, narrativePreviewResponse);

                var dailyNarratives = new List<DailyNarrative>
                {
                    new DailyNarrative { JobId = Guid.NewGuid(), NarrativeText = narrativePreviewResponse.NarrativeText, CreatedBy = narrativePreviewResponse.CreatedBy, CreatedDate = narrativePreviewResponse.CreatedUtc}
                };

                //Assign to the DailyNarrative the Timezone and Adjust the created date.
                foreach (var dn in dailyNarratives)
                {
                    dn.Timezone = await DryingReportHelper.GetTimeZoneString(request.FranchiseTimeZone);
                    dn.CreatedDate = dn.CreatedDate.GetLocalFranchiseDateTime(request.FranchiseTimeZone);
                }
                _logger.LogDebug("DryingReportLog - GetDailyNarratives completed for jobId: {JobId}", request.JobId);
                return new DailyNarrativeDto(dailyNarratives);
            }
        }
    }
}