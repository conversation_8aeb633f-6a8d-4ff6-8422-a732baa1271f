﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.CorporateService;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Audit;

namespace Servpro.Franchise.JobService.Features.Jobs.Audit
{
    public class AuditMasterFileUpdated
    {
        public class Event : AuditMasterFileUpdatedEvent, IRequest
        {
            public Event(string updatedBy, DateTime updateDate, Guid correlationId) : base(updatedBy, updateDate, correlationId)
            {

            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ICorporateServiceClient _corporateServiceClient;
            private readonly ILogger<AuditMasterFileUpdated> _logger;

            public Handler(JobDataContext db,
                ICorporateServiceClient corporateServiceClient,
                ILogger<AuditMasterFileUpdated> logger)
            {
                _db = db;
                _corporateServiceClient = corporateServiceClient;
                _logger = logger;
            }
            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} receieved: {@request}", nameof(AuditMasterFileUpdatedEvent), request);
                var auditMasterFileRecords = await _corporateServiceClient.GetAuditMasterFileRecords(cancellationToken);

                _logger.LogInformation("Start processing auditMasterFileRecords: {count}", auditMasterFileRecords.Count());

                var areThereRecordsToSave = false;
                foreach (var auditMasterFileRecord in auditMasterFileRecords)
                {
                    if (auditMasterFileRecord.JobId.HasValue && !string.IsNullOrEmpty(auditMasterFileRecord.AuditStage))
                    {
                        var wipRecord = await _db.WipRecords
                            .FirstOrDefaultAsync(q => q.Id == auditMasterFileRecord.JobId.Value, cancellationToken: cancellationToken);

                        if (wipRecord != null)
                        {
                            wipRecord.AuditStage = auditMasterFileRecord.AuditStage;
                            wipRecord.JobAuditMasterId = auditMasterFileRecord.Id;
                            areThereRecordsToSave = true;
                        }
                    }
                }

                if(areThereRecordsToSave)
                    await _db.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("{Event} saved: {count} records received",
                    nameof(AuditMasterFileUpdatedEvent), auditMasterFileRecords.Count());

                return Unit.Value;
            }
        }
    }
}
