@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.AtmosphericReadingsDto
@using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
@using System.Drawing;

@*--------- Create the report model using the Helper class ---------*@
@{ var reportModel = AtmosphericReadingsModelGeneratorHelper.Process(Model);
    var zoneTypeSortOrder = new Dictionary<int, string> { { 0, "Outside" }, { 1, "Unaffected" }, { 2, "HVAC" }, { 3, "Drying" } };
    const int atmosReadingsPerPageWidth = 4;
    const int dehuReadingsPerPageWidth = 3; 
}

@if (reportModel.Count > 0)
{
<div class="floatLeft">
    @*TODO : We commented this out for testing if bookmarks cause issues with styling - this needs to be resolved
        <div class="monitoring-section-heading">{atmosphericDehumidifierReadings}Atmospheric & Dehumidifier Readings{/atmosphericDehumidifierReadings}</div>*@
    <div class="monitoring-section-heading">Atmospheric & Dehumidifier Readings</div>

    @foreach (var zoneReading in reportModel)
    {
        var listOfVisits = zoneReading.TopRow
                            .OrderBy(x => x.VisitTimestamp)
                            .Select(x => new { x.VisitTimestamp, x.Technician })
                            .Distinct()
                            .ToList();

        <p class="monitoring-section-heading2">@zoneReading.ZoneName</p>
        <!--TODO: We removed this bookmark becuase it is causing issues with the styling - the header will wrap if its too long
          This will need to be addressed.-->
        @*<p class="monitoring-section-heading2">{atmosphericDehumZoneName}@zoneReading.ZoneName{/atmosphericDehumZoneName}</p>*@

        <!-- #region Print Atmospheric Readings -->
        <div class="monitoring-data-content">
            <table class="monitoring-table" summary="Monitoring Data">
                @*--------- Printing Atmospheric header ---------*@
                <thead>
                    @{ var atmosphericHeaders = zoneReading.TopRow
                                     .Select(x => new { x.ZoneNameOrEquipModel, x.ZoneDescOrEquipAssetNo })
                                     .Distinct()
                                     .ToList(); 

                        // Ensure that expected zone headers (Outside, Unaffected and HVAC) are present in the atmosphericHeaders list, according to the given sort order.
                        if (atmosphericHeaders != null && atmosphericHeaders.Count > 0)
                        {
                            foreach (var zoneType in zoneTypeSortOrder)
                            {
                                if (zoneType.Value == "Drying")
                                    continue;

                                var index = atmosphericHeaders.FindIndex(x => x.ZoneNameOrEquipModel.Equals(zoneType.Value));
                                if (index < 0)
                                {
                                    atmosphericHeaders.Insert(zoneType.Key, new { ZoneNameOrEquipModel = zoneType.Value, ZoneDescOrEquipAssetNo = "" });
                                }
                            }
                        }
                    }

                    <tr>
                        <th scope="col" style="width: 22%">
                            Atmospheric <br />
                            Readings
                        </th>
                        @foreach (var atmosphericHeader in atmosphericHeaders)
                        {
                            <th scope="col" colspan="3" style="width: 19.5%">
                                @atmosphericHeader.ZoneNameOrEquipModel <br />
                                @atmosphericHeader.ZoneDescOrEquipAssetNo
                            </th>
                        }
                    </tr>
                    <tr>
                        <th scope="col">Timestamp</th>
                        @for (int i = 0; i < atmosReadingsPerPageWidth; i++)
                        {
                            <th scope="col" style="width: 6.5%">Temp</th>
                            <th scope="col" style="width: 6.5%">RH%</th>
                            <th scope="col" style="width: 6.5%">GPP</th>
                        }
                    </tr>
                </thead>

                @*--------- Printing the Atmospherics table content ---------*@
                <tbody>
                    @foreach (var visitDate in listOfVisits)
                    {
                        <tr>
                            <td>
                                @visitDate.VisitTimestamp.ToString("MM/dd/yyyy hh:mm tt") <br />
                                Tech: @visitDate.Technician
                            </td>

                            @for (int i = 0; i < zoneTypeSortOrder.Count; i++)
                            {
                                var zoneTypeValues = zoneReading.TopRow
                                    .FirstOrDefault(x => x.VisitTimestamp == visitDate.VisitTimestamp && x.ZoneType == zoneTypeSortOrder[i]);
                                decimal? temp = null, rh = null, gpp = null;
                                string tempStyle = "", rhStyle = "", gppStyle = "";
                                if (zoneTypeValues != null)
                                {
                                    temp = zoneTypeValues.Temp;
                                    rh = zoneTypeValues.RH;
                                    gpp = zoneTypeValues.GPP;
                                    if (zoneTypeValues.TempCellColor != Color.Empty)
                                    {
                                        tempStyle = $"background-color: {zoneTypeValues.TempCellColor.ToKnownColor().ToString()}";
                                    }
                                    if (zoneTypeValues.RhCellColor != Color.Empty)
                                    {
                                        rhStyle = $"background-color: {zoneTypeValues.RhCellColor.ToKnownColor().ToString()}";
                                    }
                                    if (zoneTypeValues.GppCellColor != Color.Empty)
                                    {
                                        gppStyle = $"background-color: {zoneTypeValues.GppCellColor.ToKnownColor().ToString()}";
                                    }
                                }
                                <td style="@tempStyle">@String.Format("{0:0.00}", temp)</td>
                                <td style="@rhStyle">@String.Format("{0:0.00}", rh)</td>
                                <td style="@gppStyle">@gpp</td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <!-- #endregion Print Atmospheric Readings-->

        <!-- #region Print Dehumidifier Readings -->
        @foreach (var dehuReadings in zoneReading.LoloDehuReadings)
        {
            var dehuHeaders = dehuReadings
                                    .Select(x => new { x.ZoneNameOrEquipModel, x.ZoneDescOrEquipAssetNo })
                                    .Distinct()
                                    .ToList();
            var tableWidth = "width: 100%";
            if (dehuReadingsPerPageWidth > dehuHeaders.Count)
            {
                tableWidth = $"width: {(dehuHeaders.Count * 100 / dehuReadingsPerPageWidth) + 19}%";
            }
            <div class="monitoring-data-content">
                <table class="monitoring-table" style="@tableWidth" summary="Monitoring">
                    <thead>
                        @*--------- Printing Dehumidifier header ---------*@
                        @{ }
                        <tr>
                            <th scope="col" style="width: 19%">
                                Dehumidifier <br />
                                Readings
                            </th>
                            @foreach (var dehumidifierHeader in dehuHeaders)
                            {
                                <th scope="col" colspan="5" style="width: 27%">
                                    @dehumidifierHeader.ZoneNameOrEquipModel <br />
                                    @dehumidifierHeader.ZoneDescOrEquipAssetNo
                                </th>
                            }
                        </tr>
                        <tr>
                            <th scope="col">Timestamp</th>
                            @for (int i = 0; i < dehuHeaders.Count; i++)
                            {
                                <th scope="col" style="width: 5.4%">Temp</th>
                                <th scope="col" style="width: 5.4%">RH%</th>
                                <th scope="col" style="width: 5.4%">GPP</th>
                                <th scope="col" style="width: 5.4%">GDEP</th>
                                <th scope="col" style="width: 5.4%">Hrs</th>
                            }
                        </tr>
                    </thead>

                    @*--------- Printing the table content ---------*@
                    <tbody>
                        @{listOfVisits = dehuReadings
                                                .OrderBy(x => x.VisitTimestamp)
                                                .Select(x => new { x.VisitTimestamp, x.Technician })
                                                .Distinct()
                                                .ToList();

                        }
                        @foreach (var visitDate in listOfVisits)
                        {
                            <tr>
                                <td>
                                    @visitDate.VisitTimestamp.ToString("MM/dd/yyyy hh:mm tt") <br />
                                    Tech: @visitDate.Technician
                                </td>

                                @for (var i = 0; i < dehuHeaders.Count; i++)
                                {

                                    var zoneTypeRow = dehuReadings.FirstOrDefault(x => x.VisitTimestamp == visitDate.VisitTimestamp &&
                                                                                       x.ZoneDescOrEquipAssetNo == dehuHeaders[i].ZoneDescOrEquipAssetNo &&
                                                                                       x.ZoneNameOrEquipModel == dehuHeaders[i].ZoneNameOrEquipModel);
                                    decimal? temp = null, rh = null, gpp = null, gdep = null, hrs = null;
                                    string tempStyle = "", rhStyle = "", gppStyle = "", gdepStyle = "";
                                    if (zoneTypeRow != null)
                                    {
                                        temp = zoneTypeRow.Temp;
                                        rh = zoneTypeRow.RH;
                                        gpp = zoneTypeRow.GPP;
                                        gdep = zoneTypeRow.GDep;
                                        hrs = zoneTypeRow.Hours;

                                        if (zoneTypeRow.TempCellColor != Color.Empty)
                                        {
                                            tempStyle = $"background-color: {zoneTypeRow.TempCellColor.ToKnownColor().ToString()}";
                                        }
                                        if (zoneTypeRow.RhCellColor != Color.Empty)
                                        {
                                            rhStyle = $"background-color: {zoneTypeRow.RhCellColor.ToKnownColor().ToString()}";
                                        }
                                        if (zoneTypeRow.GppCellColor != Color.Empty)
                                        {
                                            gppStyle = $"background-color: {zoneTypeRow.GppCellColor.ToKnownColor().ToString()}";
                                        }
                                        if (zoneTypeRow.RhCellColor != Color.Empty)
                                        {
                                            gdepStyle = $"background-color: {zoneTypeRow.GDepCellColor.ToKnownColor().ToString()}";
                                        }
                                    }
                                    <td style="@tempStyle">@String.Format("{0:0.00}", temp)</td>
                                    <td style="@rhStyle">@String.Format("{0:0.00}", rh)</td>
                                    <td style="@gppStyle">@gpp</td>
                                    <td style="@gdepStyle">@gdep</td>
                                    <td>@hrs</td>
                                }
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        <!-- #endregion Print Dehumidifier Readings-->
    }
</div>
}