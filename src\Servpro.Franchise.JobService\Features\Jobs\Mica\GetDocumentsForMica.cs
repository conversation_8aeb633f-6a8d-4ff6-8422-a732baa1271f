﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.Franchise.JobService.Infrastructure.MicaService.GetSentEntities;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetDocumentsForMica
    {
        public class Query : IRequest<List<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public DateTime CreatedDate { get; set; }
            public bool HasBeenSentToMica { get; set; }
        }

        public class Handler : IRequestHandler<Query, List<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetDocumentsForMica> _logger;
            private readonly IMicaServiceClient _micaServiceClient;
            private List<string> _allowedExtensions = new List<string>{ "pdf" };

            public Handler(IMicaServiceClient micaServiceClient, 
                ILogger<GetDocumentsForMica> logger, 
                JobReadOnlyDataContext context)
            {
                _micaServiceClient = micaServiceClient;
                _logger = logger;
                _context = context;
            }

            public async Task<List<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Documents to Submit to Mica");

                var sentEntities = await _micaServiceClient.GetSentEntitiesAsync(
                    new GetSentEntities.Command(request.JobId, GetSentEntities.EntityType.Document), cancellationToken);

                var documents = await _context.MediaMetadata
                    .Where(x => x.JobId == request.JobId &&
                                x.MediaTypeId == MediaTypes.Document &&
                                !x.FormTemplateId.HasValue &&
                                x.UploadedSuccessfully &&
                                !x.IsDeleted)
                    .ToListAsync(cancellationToken);

                documents = documents.Where(x => _allowedExtensions.Contains(x.GetFileExtension())).ToList();

                return documents.Select(x => Map(x, sentEntities)).ToList();
            }

            private Dto Map(MediaMetadata media, GetSentEntitiesDto entities)
            {
                return new Dto
                {
                    Id = media.Id,
                    Name = media.Name,
                    ArtifactTypeId = media.ArtifactTypeId,
                    CreatedDate = media.CreatedDate,
                    HasBeenSentToMica = entities.SentEntities?.Any(x => x.Id == media.Id) ?? false
                };
            }
        }
    }
}
