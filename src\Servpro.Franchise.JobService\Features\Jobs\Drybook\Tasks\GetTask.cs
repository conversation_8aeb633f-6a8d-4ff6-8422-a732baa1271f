﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using ServproTask = Servpro.Franchise.JobService.Models.Drybook.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Tasks
{
    public class GetTask
    {
        public class Query : IRequest<Dto>
        {
            public Guid TaskId { get; set; }
            public Guid JobId { get; set; }
        }

        public class Dto
        {
            public Dto(
                Guid id,
                Guid? jobId,
                Guid taskPriorityId,
                Guid taskStatusId,
                Guid taskTypeId,
                string subject,
                DateTime date,
                Guid? jobTriStateQuestionId,
                IEnumerable<JournalNoteDto> journalNotes)
            {
                Id = id;
                JobId = jobId;
                TaskPriorityId = taskPriorityId;
                TaskStatusId = taskStatusId;
                TaskTypeId = taskTypeId;
                Subject = subject;
                Date = date;
                JobTriStateQuestionId = jobTriStateQuestionId;
                JournalNotes = journalNotes;
            }
            public Guid Id { get; }
            public Guid? JobId { get; }
            public Guid TaskPriorityId { get; }
            public Guid TaskStatusId { get; }
            public Guid TaskTypeId { get; }
            public string Subject { get; }
            public DateTime Date { get; }
            public Guid? JobTriStateQuestionId { get; }
            public IEnumerable<JournalNoteDto> JournalNotes { get; }

            public class JournalNoteDto
            {
                public JournalNoteDto(
                    Guid id,
                    string author,
                    string subject,
                    string note,
                    DateTime? actionDate,
                    Guid categoryId,
                    Guid typeId,
                    Guid visibilityId)
                {
                    Id = id;
                    Author = author;
                    Subject = subject;
                    Note = note;
                    ActionDate = actionDate;
                    CategoryId = categoryId;
                    TypeId = typeId;
                    VisibilityId = visibilityId;
                }
                public Guid Id { get; }
                public string Author { get; }
                public string Subject { get; }
                public string Note { get; }
                public DateTime? ActionDate { get; }
                public Guid CategoryId { get; }
                public Guid TypeId { get; }
                public Guid VisibilityId { get; }
            }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
                => _context = context;

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var task = await _context.Tasks
                    .Include(x => x.JournalNotes)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.TaskId && x.JobId == request.JobId, cancellationToken);

                if (task is null)                    
                        throw new ResourceNotFoundException($"Task not found (Id: {request.TaskId}");

                return Map(task);
            }

            private Dto Map(ServproTask task)
                => new Dto(
                    task.Id,
                    task.JobId,
                    task.TaskPriorityId,
                    task.TaskStatusId,
                    task.TaskTypeId,
                    task.Subject,
                    task.CreatedDate,
                    task.JobTriStateQuestionId,                    
                    task.JournalNotes.Select(Map));

            private Dto.JournalNoteDto Map(JournalNote note)
                => new Dto.JournalNoteDto(
                    note.Id,
                    note.Author,
                    note.Subject,
                    note.Note,
                    note.ActionDate,
                    note.CategoryId,
                    note.TypeId,
                    note.VisibilityId);
        }

    }
}