﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities.Calculators;
using Servpro.Franchise.LookupService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetJobDryingDataForMica
    {
        public class Query : IRequest<List<Zone>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }

        public class Zone
        {
            public Guid Id { get; set; }
            public Guid ZoneTypeId { get; set; }
            public string Name { get; set; }
            public int WaterClass { get; set; }
            public int WaterCategory { get; set; }
            public bool IsDeleted { get; set; }
            //Job Area has a zoneId Select * from JobAreas where ZoneId = zoneId
            public List<JobArea> JobAreas { get; set; }
            //This can be derived from jobAreas, JobAreas.Select(x => x.Id) Select * from EquipmentPlacements ep where JobAreas contains ep.JobAreaId
            public List<EquipmentPlacement> EquipmentPlacements { get; set; }
            public List<ZoneReading> Readings { get; set; }
        }

        public class ZoneReading
        {
            public Guid Id { get; set; }
            public decimal? RelativeHumidity { get; set; }
            public decimal? Temperature { get; set; }
            public bool? IsInUse { get; set; }
            public DateTime JobVisitDate { get; set; }
        }

        public class EquipmentPlacement
        {
            public Guid Id { get; set; }
            public DateTime BeginDate { get; set; }
            public DateTime? EndDate { get; set; }
            public Guid RoomId { get; set; }
            public Equipment Equipment { get; set; }
            public List<EquipmentPlacementReading> EquipmentPlacementReadings { get; set; }
        }

        public class Equipment
        {
            public Guid Id { get; set; }
            public EquipmentModel EquipmentModel { get; set; }
            public bool IsDehu { get; set; }
        }

        public class EquipmentModel
        {
            public string Name { get; set; }
            public int CubicFeetPerMinute { get; set; }
            public int PintsPerDay { get; set; }
            public EquipmentType EquipmentType { get; set; }
        }

        public class EquipmentType
        {
            public string Name { get; set; }
        }

        public class JobArea
        {
            public string Name { get; set; }
            public Room Room { get; set; }
            //This can be derived from jobArea: Select * from EquipmentPlacements where JobAreaId = jobAreaId
            public List<EquipmentPlacement> EquipmentPlacements { get; set; }
            public List<JobAreaMaterial> JobAreaMaterials { get; set; }
        }

        public class JobAreaMaterial
        {
            public Guid Id { get; set; }
            public Guid? RemovedOnJobVisitId { get; set; }
            public JobMaterial JobMaterial { get; set; }
            public DateTime? RemovedOnJobVisitDate { get; set; }
            public List<JobAreaMaterialReading> JobAreaMaterialReadings { get; set; }
        }

        public class JobAreaMaterialReading
        {
            public Guid Id { get; set; }
            public int? Value { get; set; }
            public DateTime JobVisitDate { get; set; }
            //Comes from JobMaterial associated with JobAreaMaterial
            public string JobMaterialName { get; set; }
            //Comes from JobAreaMaterial
            public DateTime? RemovedOnJobVisitDate { get; set; }
        }

        public class JobMaterial
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public int Goal { get; set; }
            public Guid ObjectId { get; set; }
        }

        public class Room
        {
            public Guid Id { get; set; }
            public int Width1TotalInches { get; set; }
            public int Length1TotalInches { get; set; }
            public int Height1TotalInches { get; set; }
            public decimal AffectedCeilingAreaSquareFeet { get; set; }
            public decimal AffectedFloorAreaSquareFeet { get; set; }
            public decimal RoomVolumeCubicFeet { get; set; }
            public decimal AffectedWallAreaAbove2FeetSquareFeet { get; set; }
            public decimal AffectedWallAreaBelow2FeetSquareFeet { get; set; }
            public bool IsDeleted { get; set; }
            public int OffsetSpaceQuantity { get; set; }
            public string FlooringType { get; set; }
            public decimal PerimeterLinearFeet { get; set; }
            public decimal AffectedPerimeterLinearFeet { get; set; }
            public decimal CeilingAreaSquareFeet { get; set; }
            public decimal FloorAreaSquareFeet { get; set; }
            public decimal WallAreaSquareFeet { get; set; }
        }

        public class EquipmentPlacementReading
        {
            public Guid Id { get; set; }
            public decimal? RelativeHumidity { get; set; }
            public decimal? Temperature { get; set; }
            public DateTime JobVisitDate { get; set; }
        }

        public class Handler : IRequestHandler<Query, List<Zone>>
        {
            public Dictionary<Guid, int> _waterClasses { get; set; }
            public Dictionary<Guid, int> _waterCategories { get; set; }
            public Dictionary<Guid, string> _floorTypes { get; set; }

            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupClient;
            private readonly IEquipmentServiceClient _equipmentServiceClient;
            private readonly ILogger<Handler> _logger;
            private readonly IRoomCalculator _roomCalculator;

            public Handler(
                  JobReadOnlyDataContext context,
                  ILookupServiceClient lookupClient,
                  IEquipmentServiceClient equipmentServiceClient,
                  ILogger<Handler> logger,
                  IRoomCalculator roomCalculator)
            {
                _context = context;
                _logger = logger;
                _lookupClient = lookupClient;
                _equipmentServiceClient = equipmentServiceClient;
                _roomCalculator = roomCalculator;
            }

            public async Task<List<Zone>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);

                _logger.LogInformation("Getting Drying Data info for Mica.");

                var lookups = await _lookupClient.GetLookupsAsync(cancellationToken);
                _waterCategories = lookups.WaterCategories.ToDictionary(wc => wc.Id, wc => wc.Ordinal ?? 0);
                _waterClasses = lookups.WaterClasses.ToDictionary(wc => wc.Id, wc => wc.Severity);
                _floorTypes = lookups.FloorTypes.ToDictionary(wc => wc.Id, wc => wc.Name);

                var retDto = new List<Zone>();

                var zones = await FindZonesData(request.JobId, cancellationToken);

                if (zones.Any())
                {
                    var jobAreas = await FindJobAreasData(zones, cancellationToken);
                    var equipPlacement = await FindEquipPlacement(jobAreas, cancellationToken);
                    foreach (var zone in zones)
                    {
                        retDto.Add(await MapAsync(zone, jobAreas, equipPlacement, cancellationToken));
                    }
                }
                return retDto;
            }

            private async Task<List<Models.Drybook.Zone>> FindZonesData(
                                        Guid jobId,
                                        CancellationToken cancellationToken)
            {
                List<Models.Drybook.Zone> zones = new List<Models.Drybook.Zone>();
                zones = await _context.Zones
                                        .Include(x => x.ZoneReadings)
                                            .ThenInclude(k => k.JobVisit)
                                        .Include(x => x.JobAreas)
                                        .Where(x => x.JobId == jobId).ToListAsync(cancellationToken);

                if (zones.Count > 0)
                    _logger.LogInformation("Drying Data (Zones) found: {0}", zones.Count);

                return zones;
            }

            private async Task<List<Models.Drybook.JobArea>> FindJobAreasData(
                                        List<Models.Drybook.Zone> zones,
                                        CancellationToken cancellationToken)
            {
                var jobAreaIds = zones.Select(z => z.JobAreas?.Select(ja => ja.Id)).SelectMany(x => x).ToList();

                List<Models.Drybook.JobArea> jobAreas = new List<Models.Drybook.JobArea>();

                jobAreas = await _context.JobAreas
                                            .Include(x => x.JobAreaMaterials)
                                                .ThenInclude(k => k.JobAreaMaterialReadings)
                                                    .ThenInclude(l => l.JobVisit)
                                            .Include(x => x.JobAreaMaterials)
                                                .ThenInclude(k => k.JobMaterial)
                                            .Include(x => x.Room)
                                                .ThenInclude(x => x.RoomFlooringTypesAffected)
                                            .Include(x => x.EquipmentPlacements)
                                            .Where(x => jobAreaIds.Contains(x.Id))
                                            .ToListAsync(cancellationToken);

                if (jobAreas.Count > 0)
                    _logger.LogInformation("Drying Data (JobAreas) found: {0}", jobAreas.Count);
                return jobAreas;
            }


            private async Task<List<Models.Drybook.EquipmentPlacement>> FindEquipPlacement(
                                        List<Models.Drybook.JobArea> jobAreas,
                                        CancellationToken cancellationToken)
            {
                var equipPlamntIds = jobAreas.Select(ja => ja.EquipmentPlacements?.Select(ep => ep.Id)).SelectMany(x => x).ToList();

                List<Models.Drybook.EquipmentPlacement> equipPlamnt = new List<Models.Drybook.EquipmentPlacement>();

                equipPlamnt = await _context.EquipmentPlacements
                                                .Include(x => x.Equipment)
                                                    .ThenInclude(x => x.EquipmentModel)
                                                        .ThenInclude(k => k.EquipmentType)
                                                .Include(x => x.EquipmentPlacementReadings)
                                                    .ThenInclude(y => y.JobVisit)
                                                .Include(x => x.JobArea)
                                                .Where(x => equipPlamntIds.Contains(x.Id))
                                                .ToListAsync(cancellationToken);
                if (equipPlamnt.Count > 0)
                    _logger.LogInformation("Drying Data (EquipmentPlacement) found: {0}", equipPlamnt.Count);

                return equipPlamnt;
            }

            public async Task<Zone> MapAsync(Models.Drybook.Zone zone,
                                   List<Models.Drybook.JobArea> jobAreas,
                                   List<Models.Drybook.EquipmentPlacement> eqPlacements,
                                   CancellationToken cancellationToken)
            {
                var jobAreasId = jobAreas.Where(x => x.ZoneId == zone.Id).Select(k => k.Id).ToList();
                
                var newJobAreas = MapJobAreas(jobAreas, jobAreasId);

                var newEquipmentPlacement = new List<EquipmentPlacement>();
                var equipmentPlacementsWithJobArea = eqPlacements.Where(x => jobAreasId.Contains(x.JobAreaId));
                var equipmentIds = eqPlacements.Select(x => x.EquipmentId);
                //Get Equipment from source of truth, some model Ids don't exist from job-service -> equipment-service
                var equipment = await _equipmentServiceClient.GetEquipmentByListAsync(null, equipmentIds, false, cancellationToken);
                foreach (var ep in equipmentPlacementsWithJobArea)
                {
                    var equipmentPlaced = equipment.Equipments.FirstOrDefault(x => x.Id == ep.EquipmentId);
                    var eqPlacement = await MapEquipmentPlacementAsync(ep, equipmentPlaced, cancellationToken);
                    var jaName = jobAreas.FirstOrDefault(ja => ja.Id == ep.JobAreaId).Name;
                    var obj = newJobAreas.FirstOrDefault(k => k.Name == jaName);
                    obj.EquipmentPlacements.Add(eqPlacement);

                    newEquipmentPlacement.Add(eqPlacement);
                }
                
                var newZoneReading = MapZoneReadings(zone.ZoneReadings);

                return new Zone()
                {
                    Id = zone.Id,
                    Name = zone.Name,
                    ZoneTypeId = zone.ZoneTypeId,
                    WaterClass = _waterClasses.GetValueOrDefault(zone.WaterClassId),
                    WaterCategory = _waterCategories.GetValueOrDefault(zone.WaterCategoryId),
                    IsDeleted = zone.IsDeleted,
                    JobAreas = newJobAreas,
                    EquipmentPlacements = newEquipmentPlacement,
                    Readings = newZoneReading
                };
            }

            public List<ZoneReading> MapZoneReadings(ICollection<Models.Drybook.ZoneReading> zoneReadings)
            {
                var list = new List<ZoneReading>();

                if (zoneReadings is null) return list;

                foreach(var zoneReading in zoneReadings)
                {
                    list.Add(new ZoneReading
                    {
                        Id = zoneReading.Id,
                        JobVisitDate = zoneReading.JobVisit.Date,
                        RelativeHumidity = zoneReading.RelativeHumidity,
                        Temperature = zoneReading.Temperature,
                        IsInUse = zoneReading.IsInUse
                    });
                }

                return list;
            }

            public List<EquipmentPlacementReading> MapEquipmentPlacementReadings(ICollection<Models.Drybook.EquipmentPlacementReading> eqPlacementsReadings)
            {
                var list = new List<EquipmentPlacementReading>();

                if (eqPlacementsReadings is null) return list;

                foreach (var reading in eqPlacementsReadings)
                {
                    list.Add(new EquipmentPlacementReading()
                    {
                        Id = reading.Id,
                        RelativeHumidity = reading.RelativeHumidity,
                        Temperature = reading.Temperature,
                        JobVisitDate = reading.JobVisit.Date
                    });
                }
                return list;
            }

            public async Task<EquipmentPlacement> MapEquipmentPlacementAsync(Models.Drybook.EquipmentPlacement eqPlacement, 
                GetEquipmentByListDto.EquipmentDto equipmentPlaced, 
                CancellationToken cancellationToken)
            {
                if (eqPlacement is null) return null;

                var equipmentModel = equipmentPlaced?.EquipmentModel;
                string equipmentTypeName = equipmentModel?.EquipmentType?.Name;
                bool isDehu = IsDehumidifier(equipmentModel?.EquipmentType?.BaseEquipmentTypeId);

                if (isDehu)
                {
                    equipmentTypeName = IsDesiccantDehu(equipmentTypeName)
                        ? MicaDehuType.Desiccant
                        : MicaDehuType.LGR;
                }


                return new EquipmentPlacement()
                {
                    Id = eqPlacement.Id,
                    BeginDate = eqPlacement.BeginDate,
                    EndDate = eqPlacement.EndDate,
                    RoomId = (Guid)eqPlacement.JobArea?.RoomId,
                    Equipment = new Equipment()
                    {
                        Id = eqPlacement.Equipment.Id,
                        EquipmentModel = new EquipmentModel()
                        {
                            Name = equipmentModel?.Name,
                            PintsPerDay = equipmentModel?.PintsPerDay ?? 0,
                            CubicFeetPerMinute = equipmentModel?.CubicFeetPerMinute ?? 0,
                            EquipmentType = new EquipmentType()
                            {
                                Name = equipmentTypeName
                            }
                        },
                        IsDehu = isDehu
                    },
                    EquipmentPlacementReadings = MapEquipmentPlacementReadings(eqPlacement.EquipmentPlacementReadings)
                };
            }

            public bool IsDehumidifier(Guid? baseEquipmentTypeId)
                => baseEquipmentTypeId.HasValue && baseEquipmentTypeId == BaseEquipmentTypes.Dehumidifier;

            public bool IsDesiccantDehu(string equipmentTypeName)
                => !string.IsNullOrWhiteSpace(equipmentTypeName) 
                    && equipmentTypeName.Contains(MicaDehuType.Desiccant, System.StringComparison.CurrentCultureIgnoreCase);

            public Room MapRoom(Models.Room room)
            {
                if (room is null) return null;

                return new Room()
                {
                    Id = room.Id,
                    Width1TotalInches = room.Width1TotalInches,
                    Length1TotalInches = room.Length1TotalInches,
                    Height1TotalInches = room.Height1TotalInches,
                    AffectedCeilingAreaSquareFeet = _roomCalculator.CalculateAffectedCeilingAreaSquareFeet(room),
                    AffectedFloorAreaSquareFeet = _roomCalculator.CalculateAffectedFloorAreaSquareFeet(room),
                    RoomVolumeCubicFeet = _roomCalculator.CalculateTotalRoomVolumeCubicFeet(room),
                    AffectedWallAreaAbove2FeetSquareFeet = _roomCalculator.CalculateAffectedWallAreaAbove2SquareFeet(room),
                    AffectedWallAreaBelow2FeetSquareFeet = _roomCalculator.CalculateAffectedWallAreaBelow2SquareFeet(room),
                    IsDeleted = room.IsDeleted,
                    OffsetSpaceQuantity = room.OffsetSpaces?.Count() ?? 0,
                    FlooringType = _floorTypes.GetValueOrDefault(room.FloorTypeId),
                    AffectedPerimeterLinearFeet = _roomCalculator.CalculateAffectedFloorLinearFeet(room),
                    PerimeterLinearFeet = _roomCalculator.CalculateFloorLinearFeet(room),
                    WallAreaSquareFeet = _roomCalculator.CalculateWallAreaSquareFeet(room),
                    CeilingAreaSquareFeet = _roomCalculator.CalculateCeilingAreaSquareFeet(room),
                    FloorAreaSquareFeet = _roomCalculator.CalculateFloorAreaSquareFeet(room),
                };
            }

            public JobMaterial MapJobMaterial(Models.Drybook.JobMaterial jobMaterial)
            {
                if (jobMaterial is null) return null;

                return new JobMaterial()
                {
                    Id = jobMaterial.Id,
                    Name = jobMaterial.Name,
                    Goal = jobMaterial.Goal,
                    ObjectId = jobMaterial.ObjectId
                };
            }

            public List<JobAreaMaterialReading> MapJobAreaMaterialReading(
                ICollection<Models.Drybook.JobAreaMaterialReading> materialReadings, 
                string jobMaterialName,
                DateTime? removedDate)
            {
                var list = new List<JobAreaMaterialReading>();

                if (materialReadings is null) return list;

                foreach(var materialReading in materialReadings)
                {
                    list.Add(new JobAreaMaterialReading()
                    {
                        Id= materialReading.Id,
                        JobMaterialName = jobMaterialName,
                        JobVisitDate = materialReading.JobVisit.Date,
                        RemovedOnJobVisitDate = removedDate,
                        Value = materialReading.Value
                    });
                }

                return list;
            }

            public List<JobAreaMaterial> MapJobAreaMaterials(ICollection<Models.Drybook.JobAreaMaterial> jobAreaMaterials)
            {
                var list = new List<JobAreaMaterial>();

                if (jobAreaMaterials is null) return list;

                foreach (var jobAreaMaterial in jobAreaMaterials)
                {
                    list.Add(new JobAreaMaterial()
                    {
                        Id = jobAreaMaterial.Id,
                        RemovedOnJobVisitId = jobAreaMaterial.RemovedOnJobVisitId,
                        RemovedOnJobVisitDate = jobAreaMaterial.RemovedOnJobVisit?.Date,
                        JobMaterial = MapJobMaterial(jobAreaMaterial.JobMaterial),
                        JobAreaMaterialReadings = MapJobAreaMaterialReading(jobAreaMaterial.JobAreaMaterialReadings,
                            jobAreaMaterial.JobMaterial?.Name, jobAreaMaterial.RemovedOnJobVisit?.Date),
                    });
                }

                return list;
            }

            public List<JobArea> MapJobAreas(List<Models.Drybook.JobArea> jobAreas, List<Guid> jobAreaIds)
            {
                var list = new List<JobArea>();

                if (jobAreas is null) return list;

                foreach (var jobArea in jobAreas.Where(x => jobAreaIds.Contains(x.Id)))
                {
                    list.Add(new JobArea()
                    {
                        Name = jobArea.Name,
                        Room = MapRoom(jobArea.Room),
                        EquipmentPlacements = new List<EquipmentPlacement>(), //TODO?
                        JobAreaMaterials = MapJobAreaMaterials(jobArea.JobAreaMaterials)
                    });
                }
                return list;
            }
        }

    }
}
