﻿using Nager.Date;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.LookupService.Features.LookUps.Lead.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.WipBoards.ScheduleService
{
    public class ScheduleService : IScheduleService
    {
        public ILookupServiceClient _lookupServiceClient { get; set; }
        private readonly IDateTimeProvider _dateTimeProvider;
        public ScheduleService(ILookupServiceClient lookupServiceClient ,IDateTimeProvider dateTimeProvider)
        {
            _lookupServiceClient = lookupServiceClient;
            _dateTimeProvider = dateTimeProvider;
        }

        private readonly TimeSpan CloseOfBusinessDayTimespan = new TimeSpan(17, 0, 0);
        private readonly TimeSpan EndOfDayTimespan = new TimeSpan(23, 59, 59);

        public async Task<DateTime> LastCloseOfNormalDay(TimeZoneInfo info, int inDays = 0, IEnumerable<HolidayDto> holidays = null)
            => await LastCloseOfDay(info, _dateTimeProvider.GetUtcNow(), EndOfDayTimespan, inDays, holidays:holidays);
        public async Task<DateTime> LastCloseOfNormalDay(TimeZoneInfo info, DateTime startDate, int inDays = 0, IEnumerable<HolidayDto> holidays = null) => 
            await LastCloseOfDay(info, startDate, EndOfDayTimespan, inDays);
        public async Task<DateTime> LastCloseOfNormalDay(TimeZoneInfo info, DateTime startDate, int
            inDays = 0, bool skipHoliday = false, IEnumerable<HolidayDto> holidays = null) 
            => await LastCloseOfDay(info, startDate, EndOfDayTimespan, inDays, skipHoliday, holidays);

        public async Task<DateTime> LastCloseOfBusinessDay(TimeZoneInfo info, int inDays = 0, IEnumerable<HolidayDto> holidays = null)
            => await LastCloseOfDay(info, _dateTimeProvider.GetUtcNow(), CloseOfBusinessDayTimespan, inDays, holidays:holidays);
        
        public async Task<DateTime> LastCloseOfBusinessDay(TimeZoneInfo info, DateTime startDate, int inDays = 0, IEnumerable<HolidayDto> holidays = null) => 
            await LastCloseOfDay(info, startDate, CloseOfBusinessDayTimespan, inDays, holidays:holidays);
        
        public async Task<DateTime> LastCloseOfDay(TimeZoneInfo info, DateTime startDate, 
            TimeSpan endOfDayTimespan, int inDays = 0, bool skipHoliday = false, IEnumerable<HolidayDto> holidays = null)
        {
            if (info == null)
                throw new ArgumentNullException(nameof(info));
            if (startDate == DateTime.MinValue || startDate == DateTime.MaxValue)
                throw new ArgumentOutOfRangeException(nameof(startDate));
            if (holidays == null)
                holidays = (await _lookupServiceClient.GetLookupsAsync())?.Holidays;
            
            var franchiseTime = TimeZoneInfo.ConvertTimeFromUtc(startDate, info);
            franchiseTime = franchiseTime.Date + endOfDayTimespan;
            franchiseTime = franchiseTime.AddBusinessDays(inDays);
            var result = CloseOfBusinessDayRec(franchiseTime.AddDays(-1), info, holidays);
            return TimeZoneInfo.ConvertTimeToUtc(result, info);

            DateTime CloseOfBusinessDayRec(DateTime date, TimeZoneInfo timeZone, IEnumerable<HolidayDto> holidays)
            {
                if (skipHoliday == false)
                {
                    if (IsOnHoliday(date, timeZone, holidays))
                    {
                        return CloseOfBusinessDayRec(date.AddDays(-1), timeZone, holidays);
                    }
                    if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
                    {
                        return CloseOfBusinessDayRec(date.AddDays(-1), timeZone, holidays);
                    }
                }
                return date;
            }
        }

        public async Task<DateTime> NextCloseNormalDayInDays(TimeZoneInfo info, int inDays, IEnumerable<HolidayDto> holidays = null)
            => await NextCloseDayInDays(info, inDays, _dateTimeProvider.GetUtcNow(), EndOfDayTimespan, holidays);
        public async Task<DateTime> NextCloseNormalDayInDays(TimeZoneInfo info, int inDays, DateTime date, IEnumerable<HolidayDto> holidays = null)
            => await NextCloseDayInDays(info, inDays, date, EndOfDayTimespan, holidays);

        public async Task<DateTime> NextCloseBusinessDayInDays(TimeZoneInfo info, int inDays, IEnumerable<HolidayDto> holidays = null)
            => await NextCloseDayInDays(info, inDays, _dateTimeProvider.GetUtcNow(), CloseOfBusinessDayTimespan, holidays);

        public async Task<DateTime> NextCloseBusinessDayInDays(TimeZoneInfo info, int inDays, DateTime date, IEnumerable<HolidayDto> holidays = null) => 
            await NextCloseDayInDays(info, inDays, date, CloseOfBusinessDayTimespan, holidays);

        public async Task<DateTime> NextCloseDayInDays(TimeZoneInfo info, int inDays, DateTime date, TimeSpan endOfDayTimespan, IEnumerable<HolidayDto> holidays = null)
        {
            if (info == null)
                throw new ArgumentNullException(nameof(info));
            if (date == DateTime.MinValue || date == DateTime.MaxValue)
                throw new ArgumentOutOfRangeException(nameof(date));
            if (holidays == null)
                holidays = (await _lookupServiceClient.GetLookupsAsync())?.Holidays;

            var franchiseTime = TimeZoneInfo.ConvertTimeFromUtc(date, info);
            franchiseTime = franchiseTime.Date + endOfDayTimespan;
            franchiseTime = franchiseTime.AddBusinessDays(inDays);
            var result = CloseOfNextBusinessDayRec(franchiseTime, info, holidays);
            return TimeZoneInfo.ConvertTimeToUtc(result, info);

            DateTime CloseOfNextBusinessDayRec(DateTime date, TimeZoneInfo timeZone, IEnumerable<HolidayDto> holidays)
            {
                if (IsOnHoliday(date, timeZone, holidays))
                {
                    return CloseOfNextBusinessDayRec(date.AddDays(1), timeZone, holidays);
                }
                if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
                {
                    return CloseOfNextBusinessDayRec(date.AddDays(1), timeZone, holidays);
                }
                return date;
            }

        }

        private bool IsOnHoliday(DateTime date, TimeZoneInfo info, IEnumerable<HolidayDto> holidays)
        {
            var dateUtc = TimeZoneInfo.ConvertTimeToUtc(date, info);
            return holidays != null && 
                   holidays.Any(holiday => dateUtc >= holiday.HolidayDateUtc && dateUtc < holiday.HolidayDateUtc.AddDays(1));
        }
    }
}
