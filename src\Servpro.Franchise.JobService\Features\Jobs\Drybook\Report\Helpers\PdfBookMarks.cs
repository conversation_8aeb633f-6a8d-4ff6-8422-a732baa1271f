﻿using Aspose.Pdf;
using Aspose.Pdf.Annotations;
using Aspose.Pdf.Text;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using iText;
using Document = Aspose.Pdf.Document;
using System.Threading;
using Aspose.Pdf.Facades;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser.Listener;
using iText.Kernel.Pdf.Canvas.Parser;
using System.Drawing;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public class PdfBookMarks
    {
        public List<PdfBookMark> BookMarks { get; set; } = new List<PdfBookMark>();
        public PdfBookMarks(List<PdfBookMark> bookmarks)
        {
            BookMarks = bookmarks;
        }
        public Aspose.Pdf.Document CreateBookMarks(Stream stream)
        {
            Stream backstream = new System.IO.MemoryStream();
            List<PdfBookMark> updBkmk = new List<PdfBookMark>();
            using (var newMemoryStream = new MemoryStream())
            {
                stream.CopyTo(newMemoryStream);
                newMemoryStream.Position = 0;
                PdfReader pdfReader = new PdfReader(newMemoryStream);
                PdfDocument docToOperate = new PdfDocument(pdfReader);                
                updBkmk = GetBookmarkPage(BookMarks, docToOperate);
            }
            Document pdfDocument = new Document(stream);
            pdfDocument.Optimize();

            List<OutlineItemCollection> outlines = new List<OutlineItemCollection>();

            foreach (var pdfBookMark in updBkmk)
            {
                OutlineItemCollection pdfOutline = new OutlineItemCollection(pdfDocument.Outlines);
                pdfOutline.Title = pdfBookMark.BookMarkOutlineText;
                pdfOutline.Italic = true;
                pdfOutline.Bold = true;
                //This is how it know the page associated with the bookmark
                pdfOutline.Action = new GoToAction(pdfDocument.Pages[pdfBookMark.Page]);
                AddChildrenBookMarks(pdfBookMark.Children, pdfDocument, pdfOutline);
                pdfDocument.Outlines.Add(pdfOutline);
            }

            Stream st = new MemoryStream();
            // create PdfContentEditor object to edit text
            PdfContentEditor editor = new PdfContentEditor();
           
            editor.BindPdf(pdfDocument);
            foreach(var pdfBookMark in updBkmk)
            {
                // change text                 
                editor.ReplaceText("{" + pdfBookMark.BookMarkTagText + "}", pdfBookMark.Page, "");
                editor.ReplaceText("{/" + pdfBookMark.BookMarkTagText + "}", pdfBookMark.Page, "");
                if (pdfBookMark.Children.Count > 0)
                {
                    ChangeTextDoc(editor, pdfBookMark.Children);
                }
            }
            // save document
            pdfDocument.Save(st);
            Document doc = new Document(st);
            return doc;
        }

        private void AddChildrenBookMarks(List<PdfBookMark> children, Document pdfDocument, OutlineItemCollection parentOutline)
        {
            foreach (var childBookMark in children)
            {

                OutlineItemCollection pdfChildOutline = new OutlineItemCollection(pdfDocument.Outlines);
                pdfChildOutline.Title = childBookMark.BookMarkOutlineText;
                pdfChildOutline.Italic = true;
                pdfChildOutline.Bold = true;
                //This is how it know the page associated with the bookmark
                pdfChildOutline.Action = new GoToAction(pdfDocument.Pages[childBookMark.Page]);
                parentOutline.Add(pdfChildOutline);

                if (childBookMark.Children.Any())
                {
                    AddChildrenBookMarks(childBookMark.Children, pdfDocument, pdfChildOutline);
                }                
            }
        }
        private void ChangeTextDoc(PdfContentEditor editor, List<PdfBookMark> childBookmarks)
        {
            foreach (var pdfBookMark in childBookmarks)
            {
                TextState ts = new TextState();
                
                var newcolorC = new ColorConverter();
                ts.BackgroundColor = Aspose.Pdf.Color.FromRgb((System.Drawing.Color)newcolorC.ConvertFromString("#e0e0e0"));
                var tabs = new string(' ', 180 -pdfBookMark.BookMarkOutlineText.Length);
                editor.ReplaceText("{" + pdfBookMark.BookMarkTagText + "}" + pdfBookMark.BookMarkOutlineText + "{/" + pdfBookMark.BookMarkTagText + "}", pdfBookMark.Page, pdfBookMark.BookMarkOutlineText + tabs,ts);
                if (pdfBookMark.Children.Count > 0)
                {
                    ChangeTextDoc(editor, pdfBookMark.Children);
                }
            }

        }

        private List<PdfBookMark> GetBookmarkPage(List<PdfBookMark> bookmarks, PdfDocument pdfDocument)
        {
            List<PdfBookMark> resVal = new List<PdfBookMark>();
            int lastPage = 1;
            foreach (var bookmark in bookmarks)
            { 
                var updatedBmklist = GetChildBookmarkPages(bookmark, pdfDocument, ref lastPage);
                resVal.AddRange(updatedBmklist);
            }

            return resVal;
        }

        private List<PdfBookMark> GetChildBookmarkPages(PdfBookMark bookmark, PdfDocument pdfDocument, ref int lastPage)
        {
            List<PdfBookMark> resultList = new List<PdfBookMark>();
            bool findNextPage = false;
            bool alreadyFound = false;
            for (int page = lastPage; page <= pdfDocument.GetNumberOfPages(); page++)
            {
                var pdfPage = pdfDocument.GetPage(page);
                var strategy = new LocationTextExtractionStrategy();
                string currentText = PdfTextExtractor.GetTextFromPage(pdfPage, strategy);
                if (Regex.IsMatch(currentText, bookmark.BookMarkRegEx.ToString()))
                {
                    findNextPage = true;
                    alreadyFound = true;
                    lastPage = page;
                    bool found = true;
                    Match match = bookmark.BookMarkRegEx.Match(currentText);
                    while (found)
                    {
                        if (match.Value != "")
                        {
                            var usingBmk = new PdfBookMark()
                            {
                                Bookmark = bookmark.Bookmark,
                                BookMarkOutlineText = bookmark.BookMarkOutlineText,
                                BookMarkRegEx = bookmark.BookMarkRegEx,
                                BookMarkTagText = bookmark.BookMarkTagText,
                                Children = bookmark.Children,
                                Page = bookmark.Page
                            };
                            var text = match.Value.Replace("\r\n", " ")
                                                           .Replace("{" + usingBmk.BookMarkTagText + "}", "")
                                                           .Replace("{/" + usingBmk.BookMarkTagText + "}", "");
                            usingBmk.BookMarkOutlineText = text;
                            usingBmk.Page = page;

                            if (usingBmk.Children.Count > 0)
                            {
                                var chList = new List<PdfBookMark>();
                                foreach (var child in usingBmk.Children)
                                {
                                    var resChild = GetChildBookmarkPages(child, pdfDocument, ref lastPage);
                                    if (resChild != null)
                                    {
                                        chList.AddRange(resChild);
                                    }
                                }
                                usingBmk.Children = chList;
                            }
                            resultList.Add(usingBmk);
                            match = match.NextMatch();
                        }
                        else
                        {
                            found = false;
                        }
                    }
                }
                else
                {
                    findNextPage = false;
                }
                if ((alreadyFound && findNextPage) || alreadyFound == false)
                {
                    continue;
                }
                else
                {
                    break;
                }
            }
            return resultList;
        }
    }

    public class PdfBookMark
    {
        //Empty ctor is to help with json deserialization - otherwise objects who need this deserialized
        // have a difficult time.
        public PdfBookMark() { }
        public PdfBookMark(string bookmark, List<PdfBookMark> children = null)
        {
            BookMarkTagText = bookmark;
            BookMarkRegEx = new Regex("{" + BookMarkTagText + "}.+\\s*.+\\s*{/" + BookMarkTagText + "}");
            Bookmark = new TextFragmentAbsorber(BookMarkRegEx);
            Children = children ?? new List<PdfBookMark>();
            Page = 1;
            BookMarkOutlineText = "";
        }
        public Regex BookMarkRegEx { get; set; }
        public string BookMarkTagText { get; set; }
        public string BookMarkOutlineText { get; set; }

        public TextFragmentAbsorber Bookmark { get; set; }

        public List<PdfBookMark> Children { get; set; } = new List<PdfBookMark>();
        public int Page { get; set; }

    }
}
