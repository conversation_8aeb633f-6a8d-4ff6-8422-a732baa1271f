﻿using System;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;

namespace Servpro.Franchise.JobService.Features.Artifacts
{
    [Route("api/artifacts")]
    public class ArtifactsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ArtifactsController> _logger;

        public ArtifactsController(
            IMediator mediator,
            ILogger<ArtifactsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<GetArtifact>> GetFormAsync(Guid id)
        {
            if (id == Guid.Empty)
                return BadRequest();

            _logger.LogDebug("Getting job artifact information");
            var artifact = await _mediator.Send(new GetArtifact.Query { Id = id });
            return Ok(artifact);
        }

        [HttpPost("/api/mobile/leads/commands/sync-artifacts")]
        public async Task<ActionResult<MobileLeadArtifactSync.ResponseDto>> SyncMobileLeadsArtifactsAsync(
            [FromBody] MobileLeadArtifactSync.Command command,
            [FromHeader(Name = "X-DeviceId")] string deviceId)
        {
            if (string.IsNullOrWhiteSpace(deviceId))
                return BadRequest("DeviceId is missing");
            command.DeviceId = deviceId;
            var franchiseSetId = User.FranchiseSetId();
            command.User = User.UserInfo();
            if (!franchiseSetId.HasValue)
            {
                _logger.LogWarning(LoggingEvents.BadRequest,
                    "SyncMobileLeadsArtifacts - BadRequest: FranchiseSetId for {username} not found.", User.Identity.Name);
                return BadRequest("The requested franchise-set information was not found for the requested user.");
            }

            command.FranchiseSetId = franchiseSetId.GetValueOrDefault();

            var response = await _mediator.Send(command);
            return Ok(response);
        }
    }
}
