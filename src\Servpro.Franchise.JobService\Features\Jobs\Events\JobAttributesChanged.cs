﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NLog;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobAttributesChanged
    {
        public class Event : JobAttributesChangedEvent, IRequest
        {
            public Event(Guid jobId, Dictionary<string, string> customAttributes, DateTime createdDate,
                Guid correlationId) : base(jobId, customAttributes, createdDate, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobDateUpdated> _logger;

            public Handler(JobDataContext db, ILogger<JobDateUpdated> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var jobIdScope = MappedDiagnosticsLogicalContext.SetScoped("jobId", request.JobId.ToString());
                _logger.LogInformation("Begin handler with: {@request}", request);

                if(request.JobId == Guid.Empty)
                {
                    _logger.LogWarning("The Job Id was not found on the request.");
                }
                else
                {
                    foreach (var attribute in request.CustomAttributes)
                    {
                        await UpsertJobCustomAttribute(attribute, request.JobId, request.CreatedDate, cancellationToken);
                    }
                }
                
                return Unit.Value;
            }

            public async Task UpsertJobCustomAttribute(KeyValuePair<string, string> attribute, Guid jobId, DateTime createdDate, CancellationToken cancellationToken)
            {
                var existingAttribute = await _db.JobCustomAttributes
                    .FirstOrDefaultAsync(a => a.JobId == jobId && a.Key == attribute.Key, cancellationToken);
                if (existingAttribute == null)
                {
                    // Insert new record
                    var newAttribute = new Models.JobCustomAttribute
                    {
                        Id = Guid.NewGuid(),
                        JobId = jobId,
                        Key = attribute.Key,
                        Value = attribute.Value,
                        CreatedDate = createdDate,
                        ModifiedDate = createdDate,
                        CreatedBy = "JobAttributesChangedEvent",
                        ModifiedBy = "JobAttributesChangedEvent"
                    };

                    await _db.JobCustomAttributes.AddAsync(newAttribute, cancellationToken);
                }
                else
                {
                    // Update existing record
                    existingAttribute.Value = attribute.Value;
                    existingAttribute.ModifiedDate = createdDate;
                    existingAttribute.ModifiedBy = "JobAttributesChangedEvent";
                }
                await _db.SaveChangesAsync(cancellationToken);
            }
        }
    }
}

