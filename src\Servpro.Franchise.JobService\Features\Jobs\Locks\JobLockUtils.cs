﻿using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Models;

using System;

namespace Servpro.Franchise.JobService.Common.Utils
{
    public static class JobLockUtils
    {
        public static bool HasLockConflict(JobLock jobLock, Guid userId)
        {
            return jobLock is null || !jobLock.IsLocked || jobLock.LockedByUserId != userId;
        }

        public static GetJobLock.Dto Map(JobLock jobLock)
        {
            return new GetJobLock.Dto
            {
                Id = jobLock.Id,
                IsLocked = jobLock.IsLocked,
                LockedByUserId = jobLock.LockedByUserId,
                LockedByUserFullName = jobLock.LockedByUserFullName,
                LockedTimestamp = jobLock.LockedTime,
                LockedByDevice = jobLock.LockedByDeviceId,
                LockedByApplicationId = jobLock.LockedByApplicationId,
                LockedByApplicationName = jobLock.LockedByApplicationName,
                UnlockedByUserId = jobLock.UnlockedByUserId,
                UnlockedTimestamp = jobLock.UnlockedTime,
                UnlockedByDevice = jobLock.UnlockedByDeviceId
            };
        }
    }
}