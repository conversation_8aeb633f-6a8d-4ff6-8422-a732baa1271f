﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.DryLink
{
    [Route("api/jobs/{jobId}/")]
    public class DryLinkController : Controller
    {
        private readonly IMediator _mediator;
        public readonly ILogger<DryLinkController> _logger;

        public DryLinkController(IMediator mediator,
            ILogger<DryLinkController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpGet("queries/get-job-for-drylink")]
        public async Task<ActionResult> GetJobForDryLink(Guid jobId)
        {
            if (jobId == Guid.Empty)
                return BadRequest(nameof(jobId));

            var jobData = await _mediator.Send(new GetJobDataForDryLink.Query(jobId));
            return Ok(jobData);
        }

    }
}
