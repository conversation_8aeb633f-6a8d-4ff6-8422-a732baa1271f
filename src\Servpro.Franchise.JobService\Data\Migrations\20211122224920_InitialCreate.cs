﻿using System;
using System.IO;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Business",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(64)", maxLength: 64, nullable: false, collation: "utf8mb4_general_ci"),
                    Address = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    PhoneNumber = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    IsSystem = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    BusinessTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    HasErnetContract = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsOther = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RecordSourceId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    PreferredName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    EmailAddress = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Business", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "EmployeeWipColumnCustomization",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CustomizationTypeId = table.Column<int>(type: "int", nullable: false),
                    EmployeeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Data = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeeWipColumnCustomization", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "EquipmentType",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    BaseEquipmentTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentType", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "FormTemplates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, collation: "utf8mb4_general_ci"),
                    Description = table.Column<string>(type: "varchar(250)", maxLength: 250, nullable: false, collation: "utf8mb4_general_ci"),
                    LinkedPage = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RelatedForm = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RelatedForm2 = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RelatedForm3 = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsRequiredForAllLossTypes = table.Column<byte>(type: "tinyint(3) unsigned", nullable: true),
                    WaterFormRequired = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    MoldFormRequired = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    FireFormRequired = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    IsRequiredForResidentialJob = table.Column<byte>(type: "tinyint(3) unsigned", nullable: true),
                    IsRequiredForCommercialJob = table.Column<byte>(type: "tinyint(3) unsigned", nullable: true),
                    WaterForm = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    MoldForm = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    FireForm = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    InsuranceClient = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: true, collation: "utf8mb4_general_ci"),
                    CommercialClient = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    IsAvailableInFirstNotice = table.Column<byte>(type: "tinyint(3) unsigned", nullable: true),
                    Approved = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    State = table.Column<string>(type: "varchar(2)", maxLength: 2, nullable: true, collation: "utf8mb4_general_ci"),
                    Country = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: true, collation: "utf8mb4_general_ci"),
                    FormalLanguage = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: true, collation: "utf8mb4_general_ci"),
                    IsActive = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false),
                    FormVersion = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    FileType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    SyncDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MediaPath = table.Column<string>(type: "varchar(1024)", maxLength: 1024, nullable: true, collation: "utf8mb4_general_ci"),
                    IsAuthorizationForm = table.Column<byte>(type: "tinyint(3) unsigned", nullable: false, defaultValue: (byte)0),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PRIMARY", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "FranchiseSetWipColumnCustomization",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CustomizationTypeId = table.Column<int>(type: "int", nullable: false),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Data = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FranchiseSetWipColumnCustomization", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "InboxMessages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CorrelationId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    MessageId = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: false, collation: "utf8mb4_general_ci"),
                    MessageType = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: false, collation: "utf8mb4_general_ci"),
                    Message = table.Column<string>(type: "mediumtext", maxLength: 16000000, nullable: true, collation: "utf8mb4_general_ci"),
                    ProcessedAt = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: false, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Version = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InboxMessages", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "InsuranceClients",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    InsuranceNumber = table.Column<int>(type: "int", nullable: false),
                    ParentInsuranceNumber = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsLocalAuditRequired = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsAuditRequired = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InsuranceClients", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobUploadLocks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobUploadTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobUploadLocks", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "LeadRollbackError",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CorrelationId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ProjectNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    ExceptionMessage = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LeadRollbackError", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "MaintenanceAlerts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Note = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    MaintenanceTypeId = table.Column<int>(type: "int", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MaintenanceAlerts", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "MenuItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Order = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Url = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Roles = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    FeatureSets = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    Items = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MenuItem", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "MergeCandidates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RecordSourceId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LossTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobProgress = table.Column<int>(type: "int", nullable: false),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Address1 = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "MobileData",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    EstablishmentCompanyId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    FireExtinguishedMethodId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobDispatchPendingReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobDispatchReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    MarketingCampaignId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    NotToExceedOverrideCurrencyId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReferralTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ServiceTimeZoneId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    TemperatureScaleId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ThirdPartyAdminCompanyId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IacInvoiceAmount = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    LossSeverityId = table.Column<int>(type: "int", nullable: false),
                    NotToExceedOverrideAmount = table.Column<int>(type: "int", nullable: false),
                    UnitsAffected = table.Column<int>(type: "int", nullable: false),
                    LeadStatusReasonCodeId = table.Column<int>(type: "int", nullable: true),
                    MachineLearningId = table.Column<int>(type: "int", nullable: true),
                    NumberOfBuildings = table.Column<int>(type: "int", nullable: true),
                    NumberOfElevators = table.Column<int>(type: "int", nullable: true),
                    PercentAffected = table.Column<int>(type: "int", nullable: true),
                    ProjectRangeId = table.Column<int>(type: "int", nullable: true),
                    SoaSynchronizationPassIdentifier = table.Column<int>(type: "int", nullable: true),
                    NumberOfFloors = table.Column<int>(type: "int", nullable: true),
                    OtherJobCause = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    OtherStructureType = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    Access = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    AnyInjuriesNotes = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    DeductibleNotes = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    ImmediateNeeds = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    LeadIdentifiedBy = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    LeadStatusReasonNote = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    ManagementOwner = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    OtherCompany = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    OtherNeeds = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    OtherQuestions = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    OtherTypeOfLoss = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    QuickBooksJobListId = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    ReferralName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    Situation = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    LossAddressDirections = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, collation: "utf8mb4_general_ci"),
                    ContentsAffectedSeverityId = table.Column<int>(type: "int", nullable: true, defaultValue: 1),
                    CommercialPropertyTypeId = table.Column<int>(type: "int", nullable: true),
                    AffectedArea = table.Column<int>(type: "int", nullable: true),
                    AffectedFloors = table.Column<int>(type: "int", nullable: true),
                    BuildOutDensityId = table.Column<int>(type: "int", nullable: true),
                    OccupantsId = table.Column<int>(type: "int", nullable: true),
                    WetDocumentsId = table.Column<int>(type: "int", nullable: true),
                    Conditions = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    ResultOfFirstContact = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    WhatIsTheSituation = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, collation: "utf8mb4_general_ci"),
                    HowCanIHelpYouNow = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileData", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "MobileLog",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Username = table.Column<string>(type: "varchar(60)", maxLength: 60, nullable: true, collation: "utf8mb4_general_ci"),
                    Type = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: true, collation: "utf8mb4_general_ci"),
                    Name = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    MediaPath = table.Column<string>(type: "varchar(1024)", maxLength: 1024, nullable: true, collation: "utf8mb4_general_ci"),
                    BucketName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Timestamp = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    Filesize = table.Column<long>(type: "bigint", nullable: false),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileLog", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "OutboxMessages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CorrelationId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    MessageType = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: false, collation: "utf8mb4_general_ci"),
                    Message = table.Column<string>(type: "mediumtext", maxLength: 16000000, nullable: false, collation: "utf8mb4_general_ci"),
                    Dispatched = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    DispatchedAt = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    LockedAt = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: false, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Version = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn),
                    DispatchAttempts = table.Column<int>(type: "int", nullable: false),
                    IsDead = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OutboxMessages", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "ServproWipColumnCustomization",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CustomizationTypeId = table.Column<int>(type: "int", nullable: false),
                    Data = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServproWipColumnCustomization", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Storm",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    StormEventId = table.Column<int>(type: "int", nullable: false),
                    StormName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, collation: "utf8mb4_general_ci"),
                    HostFranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    HostFranchiseId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    HostFranchiseName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    HostFranchiseNumber = table.Column<int>(type: "int", nullable: false),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsClosed = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Storm", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "StormEvent",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, collation: "utf8mb4_general_ci"),
                    StormEventId = table.Column<int>(type: "int", nullable: false),
                    StormEventName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, collation: "utf8mb4_general_ci"),
                    StormId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StormEvent", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "UserSession",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    UserId = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, collation: "utf8mb4_general_ci"),
                    SessionId = table.Column<string>(type: "varchar(30)", maxLength: 30, nullable: false, collation: "utf8mb4_general_ci"),
                    Username = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    ClientId = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    IsExpired = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserSession", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "VersionData",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    VersionNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    VersionAcknowledge = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ReleaseNotesUrl = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VersionData", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "WipRecord",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ProjectNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    JobRowId = table.Column<long>(type: "bigint", nullable: false),
                    IsChaseLead = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CorporateJobNumber = table.Column<long>(type: "bigint", nullable: true),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    DateReceived = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DispatchedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    JobProgress = table.Column<int>(type: "int", nullable: false),
                    StatusNotes = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    PropertyTypeName = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    StructureTypeName = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobTypeName = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    PriorityResponderName = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CallerFirstName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CallerLastName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CallerFullName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CallerPhoneNumbers = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerFirstName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerLastName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerFullName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerPhoneNumbers = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerPhoneExtesion = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerCreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    LossBusinessName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossBusinessPhoneNumber = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    DateOfLoss = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    LossAddress1 = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossAddress2 = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossCity = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossPostalCode = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossCountryId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LossCountryName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossCountyName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LossStateId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LossStateName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    ReferralName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    ReferralBusiness = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    StormName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    InsuranceCompanyName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    LossStateAbbreviation = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LeadSource = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CustomerEmailAddresses = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    InsuranceClaimNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    JobFileCoordinatorName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ProductionManagerName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ProjectManagerName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CrewChiefName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RecpDispatcherName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    GeneralManagerName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    OfficeManagerName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReconSuperintendent = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReferredByName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReportedByName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JournalNoteCount = table.Column<int>(type: "int", nullable: false),
                    DocumentCount = table.Column<int>(type: "int", nullable: false),
                    PhotoCount = table.Column<int>(type: "int", nullable: false),
                    IsLocked = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    LockedByUser = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LockedTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    LockedByDevice = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    LockedByApplication = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Adjusters = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    ActiveAlertCount = table.Column<int>(type: "int", nullable: false),
                    CompletedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsWarmLead = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    HasEstimate = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    LeadEstimate = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    MarketingRepName = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    AmountDue = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    DurationOpen = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    HasInvoice = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsDispatched = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    LockedByUserId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    AirMoverCount = table.Column<int>(type: "int", nullable: false),
                    DehuCount = table.Column<int>(type: "int", nullable: false),
                    FacilityTypeName = table.Column<int>(type: "int", nullable: true),
                    NotSoldOrCancelledDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    NotSoldOrCancelledBy = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    NotSoldOrCancelledDetermination = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    DurationAtCurrentProgress = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    AirScrubberCount = table.Column<int>(type: "int", nullable: false),
                    IsRedFlagged = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RedFlagNotes = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    InitialUploadDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DailyUploadDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    FinalUploadDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MostRecentInitialUploadCompleted = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MostRecentDailyUploadCompleted = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MostRecentFinalUploadCompleted = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    InitialExtensionCount = table.Column<int>(type: "int", nullable: false),
                    DailyExtensionCount = table.Column<int>(type: "int", nullable: false),
                    FinalExtensionCount = table.Column<int>(type: "int", nullable: false),
                    InitialExtensionReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DailyExtensionReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    FinalExtensionReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IgnoreMergeCandidates = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    MergeTarget = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    MergeSource = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    MentorId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DefaultMentorId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    PriorityResponderFullName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    FranchiseName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    FranchiseState = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CallerId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CallerPhoneNumberExtension = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    CustomerBusinessName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    CallerBusinessName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    PreliminaryEstimate = table.Column<double>(type: "double", nullable: false),
                    Confidence = table.Column<double>(type: "double", nullable: false),
                    IsAuditRequired = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    NumberOfFinalReturns = table.Column<int>(type: "int", nullable: false),
                    SelfAuditDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    SelfAuditReturnedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CorporateAuditDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CorporateAuditDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CorporateAuditReturnedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsAuditComplete = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsNonStandardJob = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsArchived = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    TargetCompletionDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MergeCandidates = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WipRecord", x => x.Id);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Contact",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FirstName = table.Column<string>(type: "varchar(64)", maxLength: 64, nullable: true, collation: "utf8mb4_general_ci"),
                    LastName = table.Column<string>(type: "varchar(64)", maxLength: 64, nullable: true, collation: "utf8mb4_general_ci"),
                    EmailAddress = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Address = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    TitleId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ContactViaTextMessage = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    SmsNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    PhoneNumbers = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    BusinessId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsMarketingContact = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    MarketingRepId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Contact", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Contact_Business_BusinessId",
                        column: x => x.BusinessId,
                        principalTable: "Business",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "EquipmentModel",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ManufacturerName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    IsSymbol = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsValidModel = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Description = table.Column<string>(type: "varchar(250)", maxLength: 250, nullable: true, collation: "utf8mb4_general_ci"),
                    ManufacturerModelNumber = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    Notes = table.Column<string>(type: "varchar(250)", maxLength: 250, nullable: true, collation: "utf8mb4_general_ci"),
                    EquipmentTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CubicFeetPerMinute = table.Column<int>(type: "int", nullable: false),
                    PintsPerDay = table.Column<int>(type: "int", nullable: false),
                    Amps = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    IsPenetratingMeter = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsNotPenetratingMeter = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsCurrent = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsSystem = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentModel", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EquipmentModel_EquipmentType_EquipmentTypeId",
                        column: x => x.EquipmentTypeId,
                        principalTable: "EquipmentType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Job",
                columns: table => new
                {
                    ReferenceNumber = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ProjectNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: false, collation: "utf8mb4_general_ci"),
                    JobProgress = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    PropertyTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsChase = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CallerId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedByFullName = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, collation: "utf8mb4_general_ci"),
                    Confidence = table.Column<double>(type: "double", nullable: false),
                    PreliminaryEstimate = table.Column<double>(type: "double", nullable: false),
                    SourceOfOpportunity = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    ReferredById = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReportedById = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CustomerId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    LossAddress = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    DateOfLoss = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    SiteAppointmentById = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    LossTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CauseOfLossId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    StructureTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    LossSeverityId = table.Column<int>(type: "int", nullable: true),
                    FacilityTypeId = table.Column<int>(type: "int", nullable: true),
                    YearStructureBuilt = table.Column<int>(type: "int", nullable: true),
                    IsWaterAvailable = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsElectricAvailable = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsThereStandingWater = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    HasSourceBeenTurnedOff = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsCeilingAffected = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsWallAffected = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsContentAffected = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    LevelsAffected = table.Column<int>(type: "int", nullable: false),
                    RoomsAffected = table.Column<int>(type: "int", nullable: false),
                    LossOccurredOnLevel = table.Column<int>(type: "int", nullable: false),
                    SquareFeetAffected = table.Column<int>(type: "int", nullable: true),
                    SquareFeet = table.Column<int>(type: "int", nullable: true),
                    IsMultiUnitStructure = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    LossNote = table.Column<string>(type: "varchar(5000)", maxLength: 5000, nullable: true, collation: "utf8mb4_general_ci"),
                    StatusNotes = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, collation: "utf8mb4_general_ci"),
                    InsuranceCarrierId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    InsuranceClaimNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    InsurancePolicyNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    WorkOrderNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    PurchaseOrderNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    SiteReferenceNumber = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    IsBidRequested = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    NteAmount = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    DeductibleAmount = table.Column<double>(type: "double", nullable: false),
                    CollectDeductible = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    PriorityResponderId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    PriorityResponderFullName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    FranchiseState = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    StormId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsDispatched = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    JobFileCoordinatorId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ProjectManagerId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ProductionManagerId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CrewChiefId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RecpDispatcherId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    GeneralManagerId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    OfficeManagerId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReconSupportId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    BusinessName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    IsWarmLead = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    LeadEstimate = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    MarketingRepId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DeductibleAmountDue = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    JobCancelReasonId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    OnHoldReasonId = table.Column<int>(type: "int", nullable: false),
                    HasInvoice = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    JobEstimator = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    IsRedFlagged = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RedFlagNotes = table.Column<string>(type: "varchar(300)", maxLength: 300, nullable: true, collation: "utf8mb4_general_ci"),
                    MostRecentInitialUploadCompleted = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MostRecentDailyUploadCompleted = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    MostRecentFinalUploadCompleted = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    InitialUploadDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    InitialUploadDueDateModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DailyUploadDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DailyUploadDueDateModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    FinalUploadDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    FinalUploadDueDateModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    InitialExtensionCount = table.Column<int>(type: "int", nullable: false),
                    DailyExtensionCount = table.Column<int>(type: "int", nullable: false),
                    FinalExtensionCount = table.Column<int>(type: "int", nullable: false),
                    InitialExtensionReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DailyExtensionReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    FinalExtensionReasonId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobDispatchTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    AssociatedErpId = table.Column<int>(type: "int", nullable: true),
                    ProjectRangeId = table.Column<int>(type: "int", nullable: true),
                    CorporateJobSourceId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    TotalRevenue = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    FlooringTypesAffected = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    JobDates = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    WorkCenterJobNumber = table.Column<long>(type: "bigint", nullable: true),
                    CorporateJobNumber = table.Column<int>(type: "int", nullable: true),
                    IsNonStandardJob = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsArchived = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    DropDownQuestionResponses = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    MentorId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DefaultMentorId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RecordSourceId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobDispatchStatusId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ClientConditions = table.Column<string>(type: "longtext", maxLength: 65000, nullable: true, collation: "utf8mb4_general_ci"),
                    MergeTarget = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    MergeSource = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ClientRequirementsCount = table.Column<int>(type: "int", nullable: true),
                    TotalAmountDue = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    BidStatusId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("00000000-0002-5365-7276-70726f496e63"), collation: "latin1_swedish_ci"),
                    CollectDeductibleDecisionId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("10319f7f-6520-473e-a776-7b6a77ccddf0"), collation: "latin1_swedish_ci"),
                    CoverageTypeId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("00000000-004e-5365-7276-70726f496e63"), collation: "latin1_swedish_ci"),
                    ReceivedContactMethodId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("00000001-009a-5365-7276-70726f496e63"), collation: "latin1_swedish_ci"),
                    ProgramTypeId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("7968ce8e-fc1e-4b77-8384-bbdf3f2a91e8"), collation: "latin1_swedish_ci"),
                    JobSourceId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("00000001-004f-5365-7276-70726f496e63"), collation: "latin1_swedish_ci"),
                    CustomerTypeId = table.Column<Guid>(type: "char(36)", nullable: false, defaultValue: new Guid("6872a501-5486-4c79-a683-935037323d69"), collation: "latin1_swedish_ci"),
                    IsStreamlineAudit = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    MobileDataId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    TransferredToJobId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    SelfAuditDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    AuditRejectionDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    AuditRejectRole = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    RejectedFinalAuditsCount = table.Column<int>(type: "int", nullable: true),
                    CorrectionDueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsAuditComplete = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    TargetCompletionDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Job", x => x.ReferenceNumber);
                    table.UniqueConstraint("AK_Job_Id", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Job_Contact_CallerId",
                        column: x => x.CallerId,
                        principalTable: "Contact",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Job_Contact_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Contact",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Job_MobileData_MobileDataId",
                        column: x => x.MobileDataId,
                        principalTable: "MobileData",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Equipment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    AssetNumber = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true, collation: "utf8mb4_general_ci"),
                    SerialNumber = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Notes = table.Column<string>(type: "varchar(1000)", maxLength: 1000, nullable: true, collation: "utf8mb4_general_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    EquipmentModelId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    VolumeRate = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Equipment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Equipment_EquipmentModel_EquipmentModelId",
                        column: x => x.EquipmentModelId,
                        principalTable: "EquipmentModel",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "FirstNoticeActivity",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ActivityTypeId = table.Column<int>(type: "int", nullable: false),
                    OriginalValue = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    RevisedValue = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    RecordSourceId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FirstNoticeActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FirstNoticeActivity_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "FirstNoticeUserActivityTracker",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "utf8mb4_general_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    UserId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "utf8mb4_general_ci"),
                    Username = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, collation: "utf8mb4_general_ci"),
                    LastChecked = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FirstNoticeUserActivityTracker", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FirstNoticeUserActivityTracker_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobActionLocation",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobActionTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RecordSourceId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    DeviceKey = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    Username = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    ReferenceId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ReferenceDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ReferenceDescription = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true, collation: "utf8mb4_general_ci"),
                    ActionDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    Latitude = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    Longitude = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    HorizontalAccuracy = table.Column<float>(type: "float", nullable: true),
                    VerticalAccuracy = table.Column<float>(type: "float", nullable: true),
                    Altitude = table.Column<float>(type: "float", nullable: true),
                    LocationDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    LocationsEnabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobActionLocation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobActionLocation_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobBusinessMaps",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    BusinessId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsPrimary = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    JobBusinessTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobBusinessMaps", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobBusinessMaps_Business_BusinessId",
                        column: x => x.BusinessId,
                        principalTable: "Business",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobBusinessMaps_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobContactMap",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ContactId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobContactTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    TitleId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsBusinessContact = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobContactMap", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobContactMap_Contact_ContactId",
                        column: x => x.ContactId,
                        principalTable: "Contact",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobContactMap_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobLock",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsLocked = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LockedByUserId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LockedByUserFullName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, collation: "utf8mb4_general_ci"),
                    LockedByDeviceId = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, collation: "utf8mb4_general_ci"),
                    LockedTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UnlockedByUserId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    UnlockedByDeviceId = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    UnlockedTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    LockedByApplicationId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LockedByApplicationName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobLock", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobLock_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobMaterial",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, collation: "utf8mb4_general_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    MaterialReadingTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Goal = table.Column<int>(type: "int", nullable: false),
                    MeterTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ObjectId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    OtherText = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    XactNarrativeId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobMaterial", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobMaterial_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobProgressHistory",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobProgress = table.Column<int>(type: "int", nullable: false),
                    ChangedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ChangedBy = table.Column<string>(type: "varchar(150)", maxLength: 150, nullable: true, collation: "utf8mb4_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobProgressHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobProgressHistory_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobTriStateAnswer",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobTriStateQuestionId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Answer = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobTriStateAnswer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobTriStateAnswer_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobVisit",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Date = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    EmployeeInitials = table.Column<string>(type: "varchar(3)", maxLength: 3, nullable: true, collation: "utf8mb4_general_ci"),
                    DepartureDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DefaultPenetratingMeterEquipmentId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DefaultNonPenetratingMeterEquipmentId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DefaultThermoHygrometerEquipmentId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsMissingVisit = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobVisit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobVisit_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Zones",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ZoneTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Description = table.Column<string>(type: "varchar(250)", maxLength: 250, nullable: true, collation: "utf8mb4_general_ci"),
                    AirMoverCalculationTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    WaterClassId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    WaterCategoryId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    SketchMediaContentId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    WaterClassOverridden = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CanValidateDehuCapacity = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    RequiredDehuCapacity = table.Column<int>(type: "int", nullable: true),
                    AchievedDehuCapacity = table.Column<int>(type: "int", nullable: true),
                    ZoneHazordousMaterialTypes = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Zones", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Zones_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobVisitTriStateAnswer",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobTriStateQuestionId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Answer = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobVisitTriStateAnswer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobVisitTriStateAnswer_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Task",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsTemplate = table.Column<bool>(type: "tinyint(1)", nullable: false, defaultValue: false),
                    IsSystem = table.Column<bool>(type: "tinyint(1)", nullable: false, defaultValue: false),
                    TaskPriorityId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    TaskStatusId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    TaskTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Subject = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true, collation: "utf8mb4_general_ci"),
                    Body = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    StartDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DueDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CompletionDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CancellationDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    PercentComplete = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    ReminderDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    EstimateId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    WorkOrderId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobTriStateQuestionId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ZoneId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ZoneReadingId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Task", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Task_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Task_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Task_Zones_ZoneId",
                        column: x => x.ZoneId,
                        principalTable: "Zones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "EquipmentPlacements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    EquipmentId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobAreaId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    BeginDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsUsedInValidation = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentPlacements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EquipmentPlacements_Equipment_EquipmentId",
                        column: x => x.EquipmentId,
                        principalTable: "Equipment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "EquipmentPlacementReading",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    EquipmentPlacementId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ZoneId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RelativeHumidity = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    Temperature = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    HourCount = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentPlacementReading", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EquipmentPlacementReading_EquipmentPlacements_EquipmentPlace~",
                        column: x => x.EquipmentPlacementId,
                        principalTable: "EquipmentPlacements",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentPlacementReading_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentPlacementReading_Zones_ZoneId",
                        column: x => x.ZoneId,
                        principalTable: "Zones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobAreas",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobAreaTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RoomId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, collation: "utf8mb4_general_ci"),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsUsedInValidation = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ZoneId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    BeginJobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    EndJobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobAreas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobAreas_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobAreas_JobVisit_BeginJobVisitId",
                        column: x => x.BeginJobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobAreas_JobVisit_EndJobVisitId",
                        column: x => x.EndJobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobAreas_Zones_ZoneId",
                        column: x => x.ZoneId,
                        principalTable: "Zones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobAreaMaterial",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobMaterialId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobAreaId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RemovedOnJobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    GoalMetOnJobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    BeginJobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobAreaMaterial", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterial_JobAreas_JobAreaId",
                        column: x => x.JobAreaId,
                        principalTable: "JobAreas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterial_JobMaterial_JobMaterialId",
                        column: x => x.JobMaterialId,
                        principalTable: "JobMaterial",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterial_JobVisit_BeginJobVisitId",
                        column: x => x.BeginJobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterial_JobVisit_GoalMetOnJobVisitId",
                        column: x => x.GoalMetOnJobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterial_JobVisit_RemovedOnJobVisitId",
                        column: x => x.RemovedOnJobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "MediaMetadata",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FranchiseSetId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    MediaTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ArtifactTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FormTemplateId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Description = table.Column<string>(type: "varchar(400)", maxLength: 400, nullable: true, collation: "utf8mb4_general_ci"),
                    MediaPath = table.Column<string>(type: "varchar(1024)", maxLength: 1024, nullable: true, collation: "utf8mb4_general_ci"),
                    BucketName = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsForUpload = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    UploadedSuccessfully = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    JobSketchId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobInvoiceId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    SignedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    FormVersion = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    SyncDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ArtifactDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    Comment = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    JobAreaId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ZoneId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobAreaMaterialId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsUploadedToXact = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MediaMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MediaMetadata_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MediaMetadata_JobAreas_JobAreaId",
                        column: x => x.JobAreaId,
                        principalTable: "JobAreas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_MediaMetadata_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobAreaMaterialReading",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Value = table.Column<int>(type: "int", nullable: true),
                    JobAreaMaterialId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    MaterialReadingTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobAreaMaterialReading", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterialReading_JobAreaMaterial_JobAreaMaterialId",
                        column: x => x.JobAreaMaterialId,
                        principalTable: "JobAreaMaterial",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobAreaMaterialReading_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobInvoices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    InvoiceNumber = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, collation: "utf8mb4_general_ci"),
                    Description = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Source = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Amount = table.Column<double>(type: "double", nullable: false),
                    AmountCollected = table.Column<double>(type: "double", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    MediaMetadataId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobInvoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobInvoices_MediaMetadata_MediaMetadataId",
                        column: x => x.MediaMetadataId,
                        principalTable: "MediaMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobSketch",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    Name = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    MediaMetadataId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CanvasJson = table.Column<string>(type: "longtext", maxLength: 30000000, nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobSketch", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobSketch_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobSketch_MediaMetadata_MediaMetadataId",
                        column: x => x.MediaMetadataId,
                        principalTable: "MediaMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JobSketchJobVisit",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobSketchId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobSketchJobVisit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobSketchJobVisit_JobSketch_JobSketchId",
                        column: x => x.JobSketchId,
                        principalTable: "JobSketch",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobSketchJobVisit_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "JournalNote",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    TaskId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    Author = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Subject = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Note = table.Column<string>(type: "longtext", maxLength: 65000, nullable: true, collation: "utf8mb4_general_ci"),
                    CategoryId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    TypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    VisibilityId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    ActionDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    JobAreaId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobAreaMaterialId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    ZoneId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RoomFlooringTypeAffectedId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IncludeInSummaryCoverPage = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    OtherName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RuleIds = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    Rules = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JournalNote", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JournalNote_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JournalNote_Task_TaskId",
                        column: x => x.TaskId,
                        principalTable: "Task",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "Rooms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RoomTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    AreaAffectedPercentage = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    RoomShapeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    OldId = table.Column<int>(type: "int", nullable: true),
                    WaterClassId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RoomOrder = table.Column<int>(type: "int", nullable: false),
                    Width1TotalInches = table.Column<int>(type: "int", nullable: false),
                    Width2TotalInches = table.Column<int>(type: "int", nullable: false),
                    Length1TotalInches = table.Column<int>(type: "int", nullable: false),
                    Length2TotalInches = table.Column<int>(type: "int", nullable: false),
                    Height1TotalInches = table.Column<int>(type: "int", nullable: false),
                    Height2TotalInches = table.Column<int>(type: "int", nullable: false),
                    Height3TotalInches = table.Column<int>(type: "int", nullable: false),
                    MissingCeilingAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    OffsetCeilingAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    CeilingAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    MissingFloorAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    OffsetFloorAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    FloorAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    MissingWallAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    OffsetWallAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    WallAreaSquareInches = table.Column<int>(type: "int", nullable: false),
                    MissingCeilingPerimeterInches = table.Column<int>(type: "int", nullable: false),
                    OffsetCeilingPerimeterInches = table.Column<int>(type: "int", nullable: false),
                    CeilingPerimeterInches = table.Column<int>(type: "int", nullable: false),
                    MissingFloorPerimeterInches = table.Column<int>(type: "int", nullable: false),
                    OffsetFloorPerimeterInches = table.Column<int>(type: "int", nullable: false),
                    FloorPerimeterInches = table.Column<int>(type: "int", nullable: false),
                    MissingRoomVolumeCubicInches = table.Column<long>(type: "bigint", nullable: false),
                    OffsetRoomVolumeCubicInches = table.Column<long>(type: "bigint", nullable: false),
                    RoomVolumeCubicInches = table.Column<long>(type: "bigint", nullable: false),
                    Floor = table.Column<int>(type: "int", nullable: false),
                    IsSlopeOrientedAlongLength = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    WaterCategoryId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    CarpetTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    OtherCarpetType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    PadTypeId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    OtherPadType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    FloorTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsStructureScopeCompleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsContentScopeCompleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    HasDamagedContents = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsPadRestorable = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    FlooringPercentageAffected = table.Column<int>(type: "int", nullable: true),
                    FlooringPercentageSaved = table.Column<int>(type: "int", nullable: true),
                    PreExistingConditions = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    HasSpecialSituation = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    AffectedCeilingAreaSquareInches = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    AffectedWallAreaSquareInches = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    PreExistingConditionsDiaryNoteId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DryOnJobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    WallAreaBelow2FeetSquareInches = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    AffectedWallAreaAbove2FeetSquareInches = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    AffectedWallAreaBelow2FeetSquareInches = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    SketchGroupId = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: true, collation: "utf8mb4_general_ci"),
                    OffsetSpaces = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    MissingSpaces = table.Column<string>(type: "json", nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Rooms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Rooms_JobVisit_DryOnJobVisitId",
                        column: x => x.DryOnJobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Rooms_JournalNote_PreExistingConditionsDiaryNoteId",
                        column: x => x.PreExistingConditionsDiaryNoteId,
                        principalTable: "JournalNote",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "ZoneReading",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RelativeHumidity = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    Temperature = table.Column<decimal>(type: "decimal(19,4)", nullable: true),
                    ZoneId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    IsInUse = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    JournalNoteId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZoneReading", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ZoneReading_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ZoneReading_JournalNote_JournalNoteId",
                        column: x => x.JournalNoteId,
                        principalTable: "JournalNote",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZoneReading_Zones_ZoneId",
                        column: x => x.ZoneId,
                        principalTable: "Zones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "LineItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LineItemId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RoomId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    JobVisitId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    Quantity = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    Percentage = table.Column<decimal>(type: "decimal(19,4)", nullable: false),
                    ActivityCode = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true, collation: "utf8mb4_general_ci"),
                    Code = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Category = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    Description = table.Column<string>(type: "varchar(240)", maxLength: 240, nullable: true, collation: "utf8mb4_general_ci"),
                    UnitOfMeasure = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true, collation: "utf8mb4_general_ci"),
                    ReviewedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    XactUploadTransactionId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    DeletedBy = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: true, collation: "utf8mb4_general_ci"),
                    DeletedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Info = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true, collation: "utf8mb4_general_ci"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LineItem", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LineItem_Job_JobId",
                        column: x => x.JobId,
                        principalTable: "Job",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LineItem_JobVisit_JobVisitId",
                        column: x => x.JobVisitId,
                        principalTable: "JobVisit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_LineItem_Rooms_RoomId",
                        column: x => x.RoomId,
                        principalTable: "Rooms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "RoomFlooringTypesAffected",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    RoomId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    FlooringTypeId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    OtherText = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    IsSalvageable = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsPadRestorable = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    TotalSquareFeet = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    AffectedSquareFeet = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    AffectedPercentage = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    SavedSquareFeet = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    SavedPercentage = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoomFlooringTypesAffected", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoomFlooringTypesAffected_Rooms_RoomId",
                        column: x => x.RoomId,
                        principalTable: "Rooms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.CreateTable(
                name: "LineItemNote",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "latin1_swedish_ci"),
                    LineItemId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    RoomId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "latin1_swedish_ci"),
                    Note = table.Column<string>(type: "longtext", maxLength: 65000, nullable: true, collation: "utf8mb4_general_ci"),
                    IsEditable = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsDeleted = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    CreatedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    ModifiedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, collation: "utf8mb4_general_ci"),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LineItemNote", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LineItemNote_LineItem_LineItemId",
                        column: x => x.LineItemId,
                        principalTable: "LineItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_LineItemNote_Rooms_RoomId",
                        column: x => x.RoomId,
                        principalTable: "Rooms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("Relational:Collation", "utf8mb4_general_ci");

            migrationBuilder.InsertData(
                table: "MenuItem",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "FeatureSets", "Items", "ModifiedBy", "ModifiedDate", "Name", "Order", "Roles", "Url" },
                values: new object[,]
                {
                    { new Guid("ba004c89-db75-4c52-b15e-70b30fa79752"), null, new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc), "[]", "[{\"Order\":1,\"Name\":\"View Contacts\",\"Url\":\"ContactManagement/Contact\",\"Roles\":[\"F_AllFranchiseAccountingClerks\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseAccountingHR\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseMarketingSupportCoordinators\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"a16cecaf-5f0f-4799-b7e5-ef3ef7173010\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":\"2019-12-18T00:00:00Z\",\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Contact Merge\",\"Url\":\"ContactManagement/ContactMerge\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d54c0715-ec0d-4217-8a3d-d4fedad99533\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Contact Diary Notes\",\"Url\":\"ContactManagement/DiaryNotes\",\"Roles\":[\"F_AllFranchiseAccountingClerks\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseAccountingHR\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"5ed1c3e8-267d-41a3-90cb-953641e5ba4c\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":\"2019-12-23T00:00:00Z\",\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]", null, null, "Contact", 1, "[]", null },
                    { new Guid("996a0c51-8f09-4e27-9ac4-61a9a9699b12"), null, new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc), "[]", "[{\"Order\":1,\"Name\":\"View Scheduler\",\"Url\":\"ScheduleArea/Schedule?signin=oidc\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e6c61517-3f7d-4673-906f-9a92d15fb2b7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]", null, null, "Scheduler", 2, "[]", null },
                    { new Guid("f1807962-9ab8-4286-b42c-807bf5e45bd3"), null, new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc), "[]", "[{\"Order\":1,\"Name\":\"View Tasks\",\"Url\":\"Tasks/Task.aspx?signin=oidc\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e7adff31-114d-4c44-98f0-c494259a4a9f\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]", null, null, "Tasks", 3, "[]", null },
                    { new Guid("7479be22-361a-4da3-baef-11e91a990f89"), null, new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc), "[]", "[{\"Order\":1,\"Name\":\"Equipment/Consumables\",\"Url\":\"Equipment/Equipment.aspx?signin=oidc\",\"Roles\":[\"F_AllFranchiseAccountingClerks\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseAccountingHR\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"0eef3ac4-e5c7-403d-9087-c3fadab9c676\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Revenue Dashboard\",\"Url\":\"TableauDashboards/Revenue\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"c082203c-57d7-4cda-8333-c6b80f1085a1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Vendor List\",\"Url\":\"Vendors/Vendor.aspx?signin=oidc\",\"Roles\":[\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"562d6283-8c77-4d61-9122-a2757e748c2f\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"Client KPM Dashboard\",\"Url\":\"TableauDashboards/ClientKPM\",\"Roles\":[\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d0d9f30f-6d1e-4ac9-bd93-be4a96a8a95e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"Financial KPM Dashboard\",\"Url\":\"TableauDashboards/FinancialKPM\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"f0d8295f-5704-43ef-9ded-989f45deb790\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"Completed Jobs Dashboard\",\"Url\":\"TableauDashboards/CompletedJobsDashboard\",\"Roles\":[\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"6efcd318-fb3b-4cd7-afd0-581be16aac66\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]", null, null, "Dashboards", 4, "[]", null },
                    { new Guid("8996855a-f826-4052-8f67-ac2a7e463f95"), null, new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc), "[]", "[{\"Order\":1,\"Name\":\"KPM Dashboard - Owner View\",\"Url\":\"views/KPMDashboard-Owner/KPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9e46d5e2-5349-4f80-ac0a-84388daa44aa\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"KPM Dashboard\",\"Url\":\"views/KPMDashboard/KPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9a152e44-6a00-4d17-bbf4-0a4dfd509f16\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Client KPM Scorecard\",\"Url\":\"views/ClientKPMDashboard/ClientKPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"10196706-1a1a-46f5-ad98-7be85c16daf1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"Financial KPM Scorecard\",\"Url\":\"FinancialKPMDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"e400a392-2bdf-4072-8cfc-36aebb720ec7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":5,\"Name\":\"Office KPM Scorecard\",\"Url\":\"OfficeKPMsDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"202ee198-b0cc-4869-a206-2dff4faf31e1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"Production KPM Scorecard\",\"Url\":\"ProductionKPMsDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"a85b10c5-0986-4961-86a5-1b00e5b21618\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"Drive the 5 Scorecard\",\"Url\":\"DrivetheFiveScorecard\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"efb2a0d0-f627-4bcc-8349-80b5041e9335\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":8,\"Name\":\"Revenue Dashboard\",\"Url\":\"views/RevenueDashboard/VolumeDashboard\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ea8daf13-7612-4e49-ab71-83a364f3e570\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":9,\"Name\":\"Additional Reports\",\"Url\":\"projects\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGeneralManagers\",\"F_AllFranchiseMarketingManagerSMMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"11a2ef5c-3ab7-4487-addb-4f40451dd6f5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]", null, null, "Business Intelligence/Reports", 5, "[]", null },
                    { new Guid("9f431564-1812-4998-b373-9e24b9480394"), null, new DateTime(2019, 12, 2, 17, 39, 38, 83, DateTimeKind.Utc), "[]", "[{\"Order\":1,\"Name\":\"Preferences\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Franchise Preferences\",\"Url\":\"FranchisePreferences\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"587231b2-c63b-4003-bd5e-37d0f23bf040\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"User Management/Alert Subscription\",\"Url\":\"admin/subscription\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"bbc2b295-c725-4913-954f-1484b0546ec4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":5,\"Name\":\"Alert Management\",\"Url\":\"admin/management\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"ca31c9d2-5c69-4283-81b1-09ebeb4d23cb\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"Xact Options\",\"Url\":\"Xactimate\",\"Roles\":[\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseDirectors\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseProductionManagerEstimators\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseGMs\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"84561a8c-0a38-496e-85e6-fe07734b4fd1\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"Training Data Refresh\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9c292ab8-baa7-458a-bf13-dc6ee50bbc1e\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}],\"Id\":\"36025296-81a9-4010-9c85-2045a3a28b25\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Navigation\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Dispatch Status Management\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"581ce55d-591a-484e-ae25-62258957adb4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":2,\"Name\":\"Emergency READY Profile\",\"Url\":\"Account/Login\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9ff4a581-08c0-4633-aeb0-fe64aa0a484b\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Job File Audit\",\"Url\":\"jobfileaudit\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"0176aea4-326c-412e-bccb-ea1399b5d674\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":4,\"Name\":\"Manage Active Claims\",\"Url\":\"franchiseclaimsinfo/ActiveClaims.aspx\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"d084b944-55fe-4144-9147-85c64e780fdd\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":5,\"Name\":\"Royalty Reporting\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseReceptionistDispatchers\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"5d705364-6cb1-48ba-b492-98eeeee04478\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":6,\"Name\":\"ServproNET\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"4a4f5313-5a50-4cf7-a81e-a68b276a5f69\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":7,\"Name\":\"SSO User Management\",\"Url\":\"ARWebAdmin/\",\"Roles\":[\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFinancialRibbonAccess\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"9474842b-d051-441f-8873-67294a04fac2\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":8,\"Name\":\"Disaster Response Manager\",\"Url\":\"StormManager\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseOfficeManagers\",\"F_AllFranchiseOperationsManagers\",\"F_AllFranchiseConstructionOperationsManagers\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseProductionManagers\",\"F_AllFranchiseConstructionEstimators\",\"F_AllFranchiseConstructionManagers\",\"F_AllFranchiseAdministrativeAssistants\",\"F_AllFranchiseReceptionistDispatchers\",\"F_AllFranchiseJobFileCoordinators\",\"F_AllFranchiseAccountingHR\",\"F_AllFinancialRibbonAccess\",\"F_AllFranchiseAuditors\",\"F_AllFranchiseCrewChiefs\",\"F_AllFranchiseConstructionSuperintendents\",\"F_AllFranchiseTechnicians\",\"F_AllFranchiseConstructionTechnicians\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"F_AllFranchiseTrainers\",\"F_AllFranchiseDirectors\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"043b3ac6-155c-452d-87c5-2ea7ae592bc7\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":9,\"Name\":\"Salesforce Marketing\",\"Url\":\"\",\"Roles\":[\"F_AllFranchiseOwners\",\"F_AllFranchiseGMs\",\"F_AllFranchiseMarketingManagers\",\"F_AllFranchiseMarketingSupportCoordinators\",\"F_AllFranchiseMarketingRepresentatives\",\"F_AllFranchiseCommercialMarketingReps\",\"Users - Corporate\"],\"FeatureSets\":[],\"Items\":[],\"Id\":\"8dab9bf2-0e40-11eb-9edc-0242ac120004\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}],\"Id\":\"66401d3e-30a3-4668-a430-b9c97d3403d5\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null},{\"Order\":3,\"Name\":\"Help\",\"Url\":null,\"Roles\":[],\"FeatureSets\":[],\"Items\":[{\"Order\":1,\"Name\":\"Helpdesk\",\"Url\":\"\",\"Roles\":[],\"FeatureSets\":[],\"Items\":[],\"Id\":\"533ad319-386a-4e65-a1a8-c9140d3ce4f4\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}],\"Id\":\"24e3b472-f222-4280-9f7c-93516bd2d6b0\",\"CreatedDate\":\"2019-12-02T17:39:38.083Z\",\"ModifiedDate\":null,\"CreatedBy\":null,\"ModifiedBy\":null,\"RowVersion\":null}]", null, null, "More", 6, "[]", null }
                });

            migrationBuilder.InsertData(
                table: "ServproWipColumnCustomization",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "CustomizationTypeId", "Data", "ModifiedBy", "ModifiedDate" },
                values: new object[] { new Guid("416020c8-1e92-4cdd-847e-ec7fcee7d333"), null, new DateTime(2019, 12, 6, 14, 49, 26, 149, DateTimeKind.Utc), 0, "[{\"ColumnPosition\":3,\"ColumnWidth\":95,\"ColumnName\":\"dateReceived\",\"ColumnText\":\"Date Received\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":4,\"ColumnWidth\":100,\"ColumnName\":\"corporateJobNumber\",\"ColumnText\":\"Corporate Ref #\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":5,\"ColumnWidth\":150,\"ColumnName\":\"projectNumber\",\"ColumnText\":\"Project #\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":6,\"ColumnWidth\":90,\"ColumnName\":\"propertyTypeName\",\"ColumnText\":\"Property Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":7,\"ColumnWidth\":80,\"ColumnName\":\"jobTypeName\",\"ColumnText\":\"Type\",\"IsVisible\":true,\"Type\":\"icon\"},{\"ColumnPosition\":8,\"ColumnWidth\":140,\"ColumnName\":\"progress\",\"ColumnText\":\"Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":9,\"ColumnWidth\":80,\"ColumnName\":\"isRedFlagged\",\"ColumnText\":\"Project Flag\",\"IsVisible\":false,\"Type\":\"boolean\"},{\"ColumnPosition\":10,\"ColumnWidth\":140,\"ColumnName\":\"customerFullName\",\"ColumnText\":\"Customer\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":11,\"ColumnWidth\":120,\"ColumnName\":\"PreliminaryEstimate\",\"ColumnText\":\"Preliminary Estimate $\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":12,\"ColumnWidth\":120,\"ColumnName\":\"Confidence\",\"ColumnText\":\"Confidence %\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":13,\"ColumnWidth\":140,\"ColumnName\":\"priorityResponderName\",\"ColumnText\":\"Priority Responder\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":14,\"ColumnWidth\":140,\"ColumnName\":\"jobFileCoordinatorName\",\"ColumnText\":\"Job File Coordinator\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":15,\"ColumnWidth\":140,\"ColumnName\":\"projectManagerName\",\"ColumnText\":\"Project Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":16,\"ColumnWidth\":140,\"ColumnName\":\"productionManagerName\",\"ColumnText\":\"Production Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":17,\"ColumnWidth\":140,\"ColumnName\":\"marketingRepName\",\"ColumnText\":\"Marketing Rep.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":18,\"ColumnWidth\":140,\"ColumnName\":\"crewChiefName\",\"ColumnText\":\"Crew Chief\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":19,\"ColumnWidth\":140,\"ColumnName\":\"recpDispatcherName\",\"ColumnText\":\"Recp Dispatcher\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":20,\"ColumnWidth\":140,\"ColumnName\":\"generalManagerName\",\"ColumnText\":\"General Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":21,\"ColumnWidth\":140,\"ColumnName\":\"officeManagerName\",\"ColumnText\":\"Office Manager\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":22,\"ColumnWidth\":140,\"ColumnName\":\"reconSuperintendent\",\"ColumnText\":\"Recon Supt\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":23,\"ColumnWidth\":210,\"ColumnName\":\"statusNotes\",\"ColumnText\":\"Status Notes\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":24,\"ColumnWidth\":80,\"ColumnName\":\"lossPostalCode\",\"ColumnText\":\"Loss Zip\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":25,\"ColumnWidth\":130,\"ColumnName\":\"insuranceCompanyName\",\"ColumnText\":\"Insurance Co.\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":26,\"ColumnWidth\":110,\"ColumnName\":\"adjusters\",\"ColumnText\":\"Adjuster\",\"IsVisible\":true,\"Type\":\"array\"},{\"ColumnPosition\":27,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress1\",\"ColumnText\":\"Loss Address 1\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":28,\"ColumnWidth\":140,\"ColumnName\":\"lossAddress2\",\"ColumnText\":\"Loss Address 2\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":29,\"ColumnWidth\":90,\"ColumnName\":\"lossCity\",\"ColumnText\":\"Loss City\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":30,\"ColumnWidth\":80,\"ColumnName\":\"lossStateAbbreviation\",\"ColumnText\":\"Loss State\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":31,\"ColumnWidth\":130,\"ColumnName\":\"leadSource\",\"ColumnText\":\"Lead Source\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":32,\"ColumnWidth\":130,\"ColumnName\":\"reportedByName\",\"ColumnText\":\"Reported By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":33,\"ColumnWidth\":130,\"ColumnName\":\"referredByName\",\"ColumnText\":\"Referred By\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":34,\"ColumnWidth\":110,\"ColumnName\":\"durationOpen\",\"ColumnText\":\"Duration Open\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":35,\"ColumnWidth\":110,\"ColumnName\":\"airMoverCount\",\"ColumnText\":\"Air Mover Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":36,\"ColumnWidth\":110,\"ColumnName\":\"activeAlertCount\",\"ColumnText\":\"DB Alerts\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":37,\"ColumnWidth\":110,\"ColumnName\":\"callerFullName\",\"ColumnText\":\"Caller\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":38,\"ColumnWidth\":110,\"ColumnName\":\"dehuCount\",\"ColumnText\":\"Dehu Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":39,\"ColumnWidth\":120,\"ColumnName\":\"airScrubberCount\",\"ColumnText\":\"Air Scrubber Count\",\"IsVisible\":false,\"Type\":\"numeric\"},{\"ColumnPosition\":40,\"ColumnWidth\":160,\"ColumnName\":\"customerEmailAddresses\",\"ColumnText\":\"Email\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":41,\"ColumnWidth\":160,\"ColumnName\":\"customerPhoneNumbers\",\"ColumnText\":\"Phone Number\",\"IsVisible\":false,\"Type\":\"array\"},{\"ColumnPosition\":42,\"ColumnWidth\":110,\"ColumnName\":\"insuranceClaimNumber\",\"ColumnText\":\"Insurance Claim #\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":43,\"ColumnWidth\":110,\"ColumnName\":\"dateOfLoss\",\"ColumnText\":\"Date of Loss\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":44,\"ColumnWidth\":110,\"ColumnName\":\"referralName\",\"ColumnText\":\"Referral\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":45,\"ColumnWidth\":110,\"ColumnName\":\"referralBusiness\",\"ColumnText\":\"Referral Business\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":46,\"ColumnWidth\":120,\"ColumnName\":\"franchiseName\",\"ColumnText\":\"Franchise\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":47,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDetermination\",\"ColumnText\":\"Not Sold/Cancelled Determination\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":48,\"ColumnWidth\":110,\"ColumnName\":\"completedDate\",\"ColumnText\":\"Job Completed date\",\"IsVisible\":true,\"Type\":\"date\"},{\"ColumnPosition\":49,\"ColumnWidth\":90,\"ColumnName\":\"amountDue\",\"ColumnText\":\"$ Due\",\"IsVisible\":true,\"Type\":\"numeric\"},{\"ColumnPosition\":50,\"ColumnWidth\":80,\"ColumnName\":\"hasEstimate\",\"ColumnText\":\"Est\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":51,\"ColumnWidth\":80,\"ColumnName\":\"hasInvoice\",\"ColumnText\":\"Inv\",\"IsVisible\":true,\"Type\":\"boolean\"},{\"ColumnPosition\":52,\"ColumnWidth\":130,\"ColumnName\":\"stormName\",\"ColumnText\":\"Event Name\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":53,\"ColumnWidth\":120,\"ColumnName\":\"leadType\",\"ColumnText\":\"Lead Type\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":54,\"ColumnWidth\":110,\"ColumnName\":\"notSoldOrCancelledDate\",\"ColumnText\":\"Not Sold/Cancelled Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":55,\"ColumnWidth\":120,\"ColumnName\":\"notSoldOrCancelledBy\",\"ColumnText\":\"Not Sold/Cancelled By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":56,\"ColumnWidth\":120,\"ColumnName\":\"createdDate\",\"ColumnText\":\"Created Date\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":57,\"ColumnWidth\":120,\"ColumnName\":\"createdBy\",\"ColumnText\":\"Created By\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":58,\"ColumnWidth\":120,\"ColumnName\":\"durationAtCurrentProgress\",\"ColumnText\":\"Duration at Current Progress\",\"IsVisible\":true,\"Type\":\"string\"},{\"ColumnPosition\":59,\"ColumnWidth\":120,\"ColumnName\":\"facilityTypeName\",\"ColumnText\":\"Facility Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":60,\"ColumnWidth\":120,\"ColumnName\":\"structureTypeName\",\"ColumnText\":\"Structure Type\",\"IsVisible\":false,\"Type\":\"string\"},{\"ColumnPosition\":61,\"ColumnWidth\":120,\"ColumnName\":\"finalUploadDueDate\",\"ColumnText\":\"Upload Due Dates\",\"IsVisible\":false,\"Type\":\"date\"},{\"ColumnPosition\":62,\"ColumnWidth\":120,\"ColumnName\":\"selfAuditStatus\",\"ColumnText\":\"Audit Details\",\"IsVisible\":false,\"Type\":\"numeric\"}]", null, null });

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("629f2878-075b-4dc9-94d9-aa9044685111"), null, new DateTime(2021, 11, 22, 22, 49, 18, 979, DateTimeKind.Utc).AddTicks(5296), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            migrationBuilder.CreateIndex(
                name: "IX_Contact_BusinessId",
                table: "Contact",
                column: "BusinessId");

            migrationBuilder.CreateIndex(
                name: "IX_Equipment_EquipmentModelId",
                table: "Equipment",
                column: "EquipmentModelId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentModel_EquipmentTypeId",
                table: "EquipmentModel",
                column: "EquipmentTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentPlacementReading_EquipmentPlacementId",
                table: "EquipmentPlacementReading",
                column: "EquipmentPlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentPlacementReading_JobVisitId",
                table: "EquipmentPlacementReading",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentPlacementReading_ZoneId",
                table: "EquipmentPlacementReading",
                column: "ZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentPlacements_EquipmentId",
                table: "EquipmentPlacements",
                column: "EquipmentId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentPlacements_JobAreaId",
                table: "EquipmentPlacements",
                column: "JobAreaId");

            migrationBuilder.CreateIndex(
                name: "IX_FirstNoticeActivity_JobId",
                table: "FirstNoticeActivity",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_FirstNoticeUserActivityTracker_JobId_UserId",
                table: "FirstNoticeUserActivityTracker",
                columns: new[] { "JobId", "UserId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FirstNoticeUserActivityTracker_UserId",
                table: "FirstNoticeUserActivityTracker",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceClients_Id",
                table: "InsuranceClients",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceClients_InsuranceNumber",
                table: "InsuranceClients",
                column: "InsuranceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Job_CallerId",
                table: "Job",
                column: "CallerId");

            migrationBuilder.CreateIndex(
                name: "IX_Job_CustomerId",
                table: "Job",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Job_FranchiseSetId",
                table: "Job",
                column: "FranchiseSetId");

            migrationBuilder.CreateIndex(
                name: "IX_Job_FranchiseSetId_JobProgress_LossTypeId",
                table: "Job",
                columns: new[] { "FranchiseSetId", "JobProgress", "LossTypeId" });

            migrationBuilder.CreateIndex(
                name: "IX_Job_Id",
                table: "Job",
                column: "Id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Job_MobileDataId",
                table: "Job",
                column: "MobileDataId");

            migrationBuilder.CreateIndex(
                name: "IX_Job_ProjectNumber",
                table: "Job",
                column: "ProjectNumber");

            migrationBuilder.CreateIndex(
                name: "IX_JobActionLocation_JobId",
                table: "JobActionLocation",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterial_BeginJobVisitId",
                table: "JobAreaMaterial",
                column: "BeginJobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterial_GoalMetOnJobVisitId",
                table: "JobAreaMaterial",
                column: "GoalMetOnJobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterial_JobAreaId",
                table: "JobAreaMaterial",
                column: "JobAreaId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterial_JobMaterialId",
                table: "JobAreaMaterial",
                column: "JobMaterialId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterial_RemovedOnJobVisitId",
                table: "JobAreaMaterial",
                column: "RemovedOnJobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterialReading_JobAreaMaterialId",
                table: "JobAreaMaterialReading",
                column: "JobAreaMaterialId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreaMaterialReading_JobVisitId",
                table: "JobAreaMaterialReading",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreas_BeginJobVisitId",
                table: "JobAreas",
                column: "BeginJobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreas_EndJobVisitId",
                table: "JobAreas",
                column: "EndJobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreas_JobId",
                table: "JobAreas",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreas_RoomId",
                table: "JobAreas",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_JobAreas_ZoneId",
                table: "JobAreas",
                column: "ZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_JobBusinessMaps_BusinessId",
                table: "JobBusinessMaps",
                column: "BusinessId");

            migrationBuilder.CreateIndex(
                name: "IX_JobBusinessMaps_JobId",
                table: "JobBusinessMaps",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobContactMap_ContactId",
                table: "JobContactMap",
                column: "ContactId");

            migrationBuilder.CreateIndex(
                name: "IX_JobContactMap_JobId",
                table: "JobContactMap",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobInvoices_MediaMetadataId",
                table: "JobInvoices",
                column: "MediaMetadataId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobLock_JobId",
                table: "JobLock",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobMaterial_JobId",
                table: "JobMaterial",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobProgressHistory_ChangedDate",
                table: "JobProgressHistory",
                column: "ChangedDate");

            migrationBuilder.CreateIndex(
                name: "IX_JobProgressHistory_JobId",
                table: "JobProgressHistory",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobSketch_JobId",
                table: "JobSketch",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobSketch_MediaMetadataId",
                table: "JobSketch",
                column: "MediaMetadataId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobSketchJobVisit_JobSketchId",
                table: "JobSketchJobVisit",
                column: "JobSketchId");

            migrationBuilder.CreateIndex(
                name: "IX_JobSketchJobVisit_JobVisitId",
                table: "JobSketchJobVisit",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobTriStateAnswer_JobId",
                table: "JobTriStateAnswer",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobTriStateAnswer_JobId_JobTriStateQuestionId",
                table: "JobTriStateAnswer",
                columns: new[] { "JobId", "JobTriStateQuestionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JobVisit_JobId",
                table: "JobVisit",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JobVisitTriStateAnswer_JobVisitId",
                table: "JobVisitTriStateAnswer",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_JobVisitTriStateAnswer_JobVisitId_JobTriStateQuestionId",
                table: "JobVisitTriStateAnswer",
                columns: new[] { "JobVisitId", "JobTriStateQuestionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_JournalNote_JobId",
                table: "JournalNote",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_JournalNote_RoomFlooringTypeAffectedId",
                table: "JournalNote",
                column: "RoomFlooringTypeAffectedId");

            migrationBuilder.CreateIndex(
                name: "IX_JournalNote_TaskId",
                table: "JournalNote",
                column: "TaskId");

            migrationBuilder.CreateIndex(
                name: "IX_LeadRollbackError_CreatedDate",
                table: "LeadRollbackError",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_LeadRollbackError_JobId",
                table: "LeadRollbackError",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_LeadRollbackError_ProjectNumber",
                table: "LeadRollbackError",
                column: "ProjectNumber");

            migrationBuilder.CreateIndex(
                name: "IX_LineItem_JobId",
                table: "LineItem",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_LineItem_JobVisitId",
                table: "LineItem",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_LineItem_RoomId",
                table: "LineItem",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_LineItemNote_LineItemId",
                table: "LineItemNote",
                column: "LineItemId");

            migrationBuilder.CreateIndex(
                name: "IX_LineItemNote_RoomId",
                table: "LineItemNote",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_JobAreaId",
                table: "MediaMetadata",
                column: "JobAreaId");

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_JobId",
                table: "MediaMetadata",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_JobVisitId",
                table: "MediaMetadata",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_RoomFlooringTypesAffected_RoomId",
                table: "RoomFlooringTypesAffected",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_Rooms_DryOnJobVisitId",
                table: "Rooms",
                column: "DryOnJobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_Rooms_PreExistingConditionsDiaryNoteId",
                table: "Rooms",
                column: "PreExistingConditionsDiaryNoteId");

            migrationBuilder.CreateIndex(
                name: "IX_Storm_Id",
                table: "Storm",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_Storm_StormEventId",
                table: "Storm",
                column: "StormEventId");

            migrationBuilder.CreateIndex(
                name: "IX_StormEvent_FranchiseSetId_FranchiseId_StormEventId",
                table: "StormEvent",
                columns: new[] { "FranchiseSetId", "FranchiseId", "StormEventId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StormEvent_FranchiseSetId_FranchiseId_StormId",
                table: "StormEvent",
                columns: new[] { "FranchiseSetId", "FranchiseId", "StormId" });

            migrationBuilder.CreateIndex(
                name: "IX_StormEvent_FranchiseSetId_StormId",
                table: "StormEvent",
                columns: new[] { "FranchiseSetId", "StormId" });

            migrationBuilder.CreateIndex(
                name: "IX_StormEvent_StormId",
                table: "StormEvent",
                column: "StormId");

            migrationBuilder.CreateIndex(
                name: "IX_Task_JobId",
                table: "Task",
                column: "JobId");

            migrationBuilder.CreateIndex(
                name: "IX_Task_JobVisitId",
                table: "Task",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_Task_ZoneId",
                table: "Task",
                column: "ZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_SessionId",
                table: "UserSession",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_UserId",
                table: "UserSession",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_WipRecord_DateReceived",
                table: "WipRecord",
                column: "DateReceived");

            migrationBuilder.CreateIndex(
                name: "IX_WipRecord_FranchiseSetId",
                table: "WipRecord",
                column: "FranchiseSetId");

            migrationBuilder.CreateIndex(
                name: "IX_WipRecord_FranchiseSetId_JobProgress",
                table: "WipRecord",
                columns: new[] { "FranchiseSetId", "JobProgress" });

            migrationBuilder.CreateIndex(
                name: "IX_WipRecord_JobProgress",
                table: "WipRecord",
                column: "JobProgress");

            migrationBuilder.CreateIndex(
                name: "IX_WipRecord_MentorId_JobProgress",
                table: "WipRecord",
                columns: new[] { "MentorId", "JobProgress" });

            migrationBuilder.CreateIndex(
                name: "IX_ZoneReading_JobVisitId",
                table: "ZoneReading",
                column: "JobVisitId");

            migrationBuilder.CreateIndex(
                name: "IX_ZoneReading_JournalNoteId",
                table: "ZoneReading",
                column: "JournalNoteId");

            migrationBuilder.CreateIndex(
                name: "IX_ZoneReading_ZoneId",
                table: "ZoneReading",
                column: "ZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_Zones_JobId",
                table: "Zones",
                column: "JobId");

            migrationBuilder.AddForeignKey(
                name: "FK_EquipmentPlacements_JobAreas_JobAreaId",
                table: "EquipmentPlacements",
                column: "JobAreaId",
                principalTable: "JobAreas",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_JobAreas_Rooms_RoomId",
                table: "JobAreas",
                column: "RoomId",
                principalTable: "Rooms",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_JournalNote_RoomFlooringTypesAffected_RoomFlooringTypeAffect~",
                table: "JournalNote",
                column: "RoomFlooringTypeAffectedId",
                principalTable: "RoomFlooringTypesAffected",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            //Job Progress Trigger
            var sqlFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data/Triggers/JobProgressHistoryTriggers.mysql");
            migrationBuilder.Sql(File.ReadAllText(sqlFile));

            //FindMergeCandidates and FindAllMergeCandidates sprocs
            var findMergeCandidates = @"
            DROP PROCEDURE IF EXISTS findMergeCandidates;
            CREATE PROCEDURE findMergeCandidates (IN jobId char(36))
            BEGIN
                DECLARE residentialPropertyType CHAR(36) DEFAULT '00000001-0011-5365-7276-70726f496e63';
                DECLARE onHold INT(11) DEFAULT 17;
                DECLARE turnedDown INT(11) DEFAULT 19;
                DECLARE notSoldCancelled INT(11) DEFAULT 16;
                SET @address1 = (SELECT JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) FROM Job WHERE Id = jobId);
                SET @franchiseSetId = (SELECT FranchiseSetId FROM Job WHERE Id = jobId);
                
                SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, MergeTarget, JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) AS Address1
                FROM Job
                WHERE JSON_EXTRACT(LossAddress, '$.Address1') = @address1
                    AND Id <> jobId
                    AND FranchiseSetId = @franchiseSetId
                    AND PropertyTypeId = residentialPropertyType
                    AND JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                    AND MergeTarget IS NULL OR MergeTarget = '';
            END;";
            migrationBuilder.Sql(findMergeCandidates);

            var findAllMergeCandidates = @"
            DROP PROCEDURE IF EXISTS findAllMergeCandidates;
            CREATE PROCEDURE findAllMergeCandidates()
            BEGIN
                DECLARE residentialPropertyType CHAR(36) DEFAULT '00000001-0011-5365-7276-70726f496e63';
                DECLARE onHold INT(11) DEFAULT 17;
                DECLARE turnedDown INT(11) DEFAULT 19;
                DECLARE notSoldCancelled INT(11) DEFAULT 16;

                CREATE TEMPORARY TABLE IF NOT EXISTS duplicateAddresses AS (
                SELECT FranchiseSetId, LossAddress1 AS Address1
                FROM WipRecord
                WHERE PropertyTypeName = residentialPropertyType
                    AND JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                GROUP BY FranchiseSetId, LossAddress1
                HAVING count(*) > 1);

                SELECT w.Id, w.LeadSource as RecordSourceId, w.JobTypeName as LossTypeId, w.FranchiseSetId, w.JobProgress, LOWER(w.LossAddress1) as Address1
                FROM duplicateAddresses da
                JOIN WipRecord w on w.FranchiseSetId = da.FranchiseSetId
                    AND w.LossAddress1 = da.Address1
                WHERE w.PropertyTypeName = residentialPropertyType
                    AND w.JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                    AND w.MergeTarget IS NULL OR w.MergeTarget = '';
            END;";
            migrationBuilder.Sql(findAllMergeCandidates);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contact_Business_BusinessId",
                table: "Contact");

            migrationBuilder.DropForeignKey(
                name: "FK_Rooms_JobVisit_DryOnJobVisitId",
                table: "Rooms");

            migrationBuilder.DropForeignKey(
                name: "FK_Task_JobVisit_JobVisitId",
                table: "Task");

            migrationBuilder.DropForeignKey(
                name: "FK_Task_Zones_ZoneId",
                table: "Task");

            migrationBuilder.DropForeignKey(
                name: "FK_JournalNote_Job_JobId",
                table: "JournalNote");

            migrationBuilder.DropForeignKey(
                name: "FK_Task_Job_JobId",
                table: "Task");

            migrationBuilder.DropForeignKey(
                name: "FK_RoomFlooringTypesAffected_Rooms_RoomId",
                table: "RoomFlooringTypesAffected");

            migrationBuilder.DropTable(
                name: "EmployeeWipColumnCustomization");

            migrationBuilder.DropTable(
                name: "EquipmentPlacementReading");

            migrationBuilder.DropTable(
                name: "FirstNoticeActivity");

            migrationBuilder.DropTable(
                name: "FirstNoticeUserActivityTracker");

            migrationBuilder.DropTable(
                name: "FormTemplates");

            migrationBuilder.DropTable(
                name: "FranchiseSetWipColumnCustomization");

            migrationBuilder.DropTable(
                name: "InboxMessages");

            migrationBuilder.DropTable(
                name: "InsuranceClients");

            migrationBuilder.DropTable(
                name: "JobActionLocation");

            migrationBuilder.DropTable(
                name: "JobAreaMaterialReading");

            migrationBuilder.DropTable(
                name: "JobBusinessMaps");

            migrationBuilder.DropTable(
                name: "JobContactMap");

            migrationBuilder.DropTable(
                name: "JobInvoices");

            migrationBuilder.DropTable(
                name: "JobLock");

            migrationBuilder.DropTable(
                name: "JobProgressHistory");

            migrationBuilder.DropTable(
                name: "JobSketchJobVisit");

            migrationBuilder.DropTable(
                name: "JobTriStateAnswer");

            migrationBuilder.DropTable(
                name: "JobUploadLocks");

            migrationBuilder.DropTable(
                name: "JobVisitTriStateAnswer");

            migrationBuilder.DropTable(
                name: "LeadRollbackError");

            migrationBuilder.DropTable(
                name: "LineItemNote");

            migrationBuilder.DropTable(
                name: "MaintenanceAlerts");

            migrationBuilder.DropTable(
                name: "MenuItem");

            migrationBuilder.DropTable(
                name: "MergeCandidates");

            migrationBuilder.DropTable(
                name: "MobileLog");

            migrationBuilder.DropTable(
                name: "OutboxMessages");

            migrationBuilder.DropTable(
                name: "ServproWipColumnCustomization");

            migrationBuilder.DropTable(
                name: "Storm");

            migrationBuilder.DropTable(
                name: "StormEvent");

            migrationBuilder.DropTable(
                name: "UserSession");

            migrationBuilder.DropTable(
                name: "VersionData");

            migrationBuilder.DropTable(
                name: "WipRecord");

            migrationBuilder.DropTable(
                name: "ZoneReading");

            migrationBuilder.DropTable(
                name: "EquipmentPlacements");

            migrationBuilder.DropTable(
                name: "JobAreaMaterial");

            migrationBuilder.DropTable(
                name: "JobSketch");

            migrationBuilder.DropTable(
                name: "LineItem");

            migrationBuilder.DropTable(
                name: "Equipment");

            migrationBuilder.DropTable(
                name: "JobMaterial");

            migrationBuilder.DropTable(
                name: "MediaMetadata");

            migrationBuilder.DropTable(
                name: "EquipmentModel");

            migrationBuilder.DropTable(
                name: "JobAreas");

            migrationBuilder.DropTable(
                name: "EquipmentType");

            migrationBuilder.DropTable(
                name: "Business");

            migrationBuilder.DropTable(
                name: "JobVisit");

            migrationBuilder.DropTable(
                name: "Zones");

            migrationBuilder.DropTable(
                name: "Job");

            migrationBuilder.DropTable(
                name: "Contact");

            migrationBuilder.DropTable(
                name: "MobileData");

            migrationBuilder.DropTable(
                name: "Rooms");

            migrationBuilder.DropTable(
                name: "JournalNote");

            migrationBuilder.DropTable(
                name: "RoomFlooringTypesAffected");

            migrationBuilder.DropTable(
                name: "Task");

            var sql = @"
                DROP TRIGGER IF EXISTS JobProgressHistoryInsert;
                DROP TRIGGER IF EXISTS JobProgressHistoryUpdate;
                DROP PROCEDURE IF EXISTS findAllMergeCandidates;
                DROP PROCEDURE IF EXISTS findMergeCandidates;";
            migrationBuilder.Sql(sql);
        }
    }
}
