﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Models.Drybook;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale
{
    public class GetAssociatedEquipment
    {
        public class Command : IRequest<Dto>
        {
            public Guid FranchiseSetId { get; set; }
            public Guid FranchiseId { get; set; }
        }

        public class Dto
        {
            public List<EquipmentDto> Equipment { get; set; }
        }

        public class EquipmentDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<GetAssociatedEquipment> _logger;

            public Handler(JobReadOnlyDataContext context, ILogger<GetAssociatedEquipment> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{franchiseSetId}{franchiseId}", request.FranchiseSetId, request.FranchiseId);

                _logger.LogInformation("Begin query with: {@request}", request);

                var equipment = await (
                    from j in _context.Jobs
                    join ja in _context.JobAreas on j.Id equals ja.JobId
                    join ep in _context.EquipmentPlacements on ja.Id equals ep.JobAreaId
                    join e in _context.Equipments on ep.EquipmentId equals e.Id
                    where j.FranchiseSetId == request.FranchiseSetId && j.FranchiseId == request.FranchiseId
                    select new EquipmentDto { Id = e.Id, Name = e.AssetNumber }
                    ).ToListAsync(cancellationToken: cancellationToken);

                if (!equipment.Any())
                {
                    _logger.LogWarning("No Equipment found for FranchiseSetId {franchiseSetId} and FranchiseId: {franchiseId}", request.FranchiseSetId, request.FranchiseId);

                }

                var dto = new Dto
                {
                    Equipment = equipment.GroupBy(x => x.Id).Select(x => x.First()).ToList()
                };
                _logger.LogInformation(" Equipment found for FranchiseSetId {franchiseSetId} and FranchiseId: {franchiseId} are {Equipment}", request.FranchiseSetId, request.FranchiseId, dto.Equipment.Count);

                return dto;
            }
        }
    }
}

