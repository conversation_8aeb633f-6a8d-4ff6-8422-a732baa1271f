﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Common.Utils
{
    public static class IntakeUtils
    {
        public static string ClearPhoneMask(string phoneNumber) => phoneNumber?.Replace("-", "");
        public static string ClearZipCodeMask(string zipCode)
        {
            var formattedZip = zipCode?.Replace("_", "") ?? "";
            return formattedZip.EndsWith("-") ? formattedZip?.Replace("-", "") : formattedZip;
        }
        public static string ClearCanadianZipCodeMask(string zipCode)
        {
            var formattedZip = zipCode?.Replace("_", "") ?? "";
            //This is incase they manually enter the address since the - remains in the middle of the zipcode
            return formattedZip?.Replace("-", "");
        }
    }
}
