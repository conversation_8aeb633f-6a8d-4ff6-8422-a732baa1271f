﻿using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobUpload
{
    public class UploadEventGenerator
    {
        public static JobUploadDto GetJobUploadForCorporateJobSync(
            Job job,
            InsuranceClient insuranceClient,
            UploadType uploadType,
            string createdBy,
            Guid createdById,
            int franchiseNumber,
            bool uploadJobToXact,
            bool isAutoUpload,
            bool isExtension,
            Guid? jobUploadDueDateExtensionReasonId)
        {
            var jobUploadDto = new JobUploadDto()
            {
                JobId = job.Id,
                JobUploadTypeId = (int)uploadType,
                CreatedBy = createdBy,
                CreatedById = createdById,
                CorporateServiceDto = new CorporateServiceDto()
                {
                    Caller = job.Caller != null ? MapContact(job.Caller, franchiseNumber, JobContactTypes.Caller) : null,
                    CauseOfLossId = job.CauseOfLossId,
                    CollectDeductible = job.CollectDeductible,
                    Contacts = job.JobContacts?.Select(c => MapJobContact(c, franchiseNumber)).ToList(),
                    CreatedBy = createdBy,
                    CreatedById = createdById,
                    CorporateJobNumber = job.CorporateJobNumber,
                    Customers = job.Customer != null ? new List<CorporateServiceDto.PersonDto>()
                    {
                        MapContact(job.Customer, franchiseNumber, JobContactTypes.Customer)
                    } : null,
                    DeductibleAmount = job.DeductibleAmount,
                    DoesJobUploadToXact = uploadJobToXact,
                    FlooringTypesAffected = job.FlooringTypesAffected?.ToList(),
                    FranchiseId = franchiseNumber,
                    FranchiseSetId = job.FranchiseSetId,
                    InsuranceCarrierId = job.InsuranceCarrierId ?? Guid.Empty,
                    InsuranceCarrierLegacyId = insuranceClient?.InsuranceNumber ?? 0,
                    InsuranceClaimNumber = job.InsuranceClaimNumber,
                    InsurancePolicyNumber = job.InsurancePolicyNumber,
                    IsAutoUpload = isAutoUpload,
                    IsExtension = isExtension,
                    JobCancelReasonId = job.JobCancelReasonId,
                    JobDates = job.JobDates?.Select(MapJobDate).ToList(),
                    JobFormMetadata = new List<JobUploadFormMetadata>(),
                    JobId = job.Id,
                    JobProgressId = (int)job.JobProgress,
                    JobRevenue = (decimal?)job.TotalRevenue,
                    JobTriStateAnswers = job.JobTriStateAnswers?.Select(MapJobTriStateAnswer).ToList(),
                    JobUploadExtensionReasonId = jobUploadDueDateExtensionReasonId,
                    JournalNotes = job.JournalNotes?.Select(MapJournalNote).ToList(),
                    LossAddress = job.LossAddress != null ? MapAddressDto(job.LossAddress) : null,
                    LossTypeId = job.LossTypeId,
                    MediaMetadata = new List<JobUploadMediaMetadata>(),
                    NteAmount = job.NteAmount,
                    ProjectNumber = job.ProjectNumber,
                    PropertyTypeId = job.PropertyTypeId,
                    StructureTypeId = job.StructureTypeId ?? Guid.Empty,
                    UploadType = uploadType,
                    WorkCenterJobNumber = job.WorkCenterJobNumber
                }
            };

            if (job.JobDates != null && job.JobDates.Any(d => d.JobDateTypeId != JobDateTypes.LossOccurred))
            {
                jobUploadDto.CorporateServiceDto.JobDates.Add(new CorporateServiceDto.JobDateDto()
                {
                    Date = job.DateOfLoss,
                    JobDateTypeId = JobDateTypes.LossOccurred
                });
            }

            return jobUploadDto;
        }

        public static XactJobUploadStartedEvent.XactJobUploadStartedDto GetXactJobUploadStarted(
            GetLookups.Dto lookups,
            int insuranceNumber,
            Job job,
            XactJobUploadStartedEvent.UploadType uploadType,
            string createdByUsername,
            Guid createdById)
        {
            var xactDto = new XactJobUploadStartedEvent.XactJobUploadStartedDto()
            {
                CorporateJobNumber = job.CorporateJobNumber,
                Arrival = job.JobDates.FirstOrDefault(d => d.JobDateTypeId == JobDateTypes.InitialOnSiteArrival)?.Date,
                CauseOfLossId = job.CauseOfLossId,
                ClaimNumber = job.InsuranceClaimNumber,
                Country = job.LossAddress?.State?.CountryName,
                EstimateType = "Xactimate",
                FranchiseSetId = job.FranchiseSetId,
                FranchiseType = "Unknown",
                InsuranceCarrierId = job.InsuranceCarrierId,
                InsuranceClientId = insuranceNumber,
                JobId = job.Id,
                State = job.LossAddress?.State?.StateAbbreviation,
                IsStormJob = job.StormId.HasValue,
                JobFormMetadata = new List<JobUploadFormMetadata>(),
                UploadType = uploadType,
                LossType = lookups.LossTypes.FirstOrDefault(l => l.Id == job.LossTypeId)?.Name,
                LossTypeId = job.LossTypeId,
                MediaMetadata = new List<JobUploadMediaMetadata>(),
                PropertyTypeId = job.PropertyTypeId,
                StructureType = lookups.StructureTypes.FirstOrDefault(t => t.Id == job.StructureTypeId)?.Name,
                StructureTypeId = job.StructureTypeId ?? Guid.Empty,
                CreatedBy = createdByUsername,
                CreatedUtc = DateTime.UtcNow,
                UserId = createdById
            };
            return xactDto;
        }

        private static CorporateServiceDto.JournalNoteDto MapJournalNote(JournalNote n)
        {
            if (n == null)
            {
                return null;
            }

            return new CorporateServiceDto.JournalNoteDto()
            {
                Id = n.Id,
                Note = n.Note,
                TypeId = n.TypeId
            };
        }

        private static JobTriStateAnswerDto MapJobTriStateAnswer(JobTriStateAnswer jtsa)
        {
            if (jtsa == null)
            {
                return null;
            }

            return new JobTriStateAnswerDto()
            {
                Answer = jtsa.Answer ?? false,
                JobId = jtsa.JobId,
                JobTriStateAnswerId = jtsa.Id,
                JobTriStateQuestionId = jtsa.JobTriStateQuestionId,
                UpdatedBy = jtsa.ModifiedBy,
                UpdatedOn = jtsa.ModifiedDate ?? DateTime.MinValue
            };
        }

        private static CorporateServiceDto.JobDateDto MapJobDate(JobDate d)
        {
            if (d == null)
            {
                return null;
            }

            return new CorporateServiceDto.JobDateDto()
            {
                Date = d.Date,
                JobDateTypeId = d.JobDateTypeId
            };
        }

        private static CorporateServiceDto.PersonDto MapJobContact(JobContactMap jcm, int franchiseId)
        {
            return jcm.Contact != null ? MapContact(jcm.Contact, franchiseId, jcm.JobContactTypeId) : null;
        }

        private static CorporateServiceDto.PersonDto MapContact(Contact c, int franchiseId, Guid jobContactTypeId)
        {
            if (c == null)
            {
                return null;
            }

            return new CorporateServiceDto.PersonDto()
            {
                Address = MapAddressDto(c.Address),
                Company = c.Business?.Name,
                EditDate = c.ModifiedDate ?? DateTime.MinValue,
                Email = c.EmailAddress,
                FirstName = c.FirstName,
                FranchiseId = franchiseId,
                JobContactTypeId = jobContactTypeId,
                LastName = c.LastName,
                PhoneNumbers = c.PhoneNumbers?.Select(MapPhone).ToList()
            };
        }

        private static CorporateServiceDto.PhoneDto MapPhone(Phone p)
        {
            if (p == null)
            {
                return null;
            }

            return new CorporateServiceDto.PhoneDto()
            {
                Id = p.Id,
                PhoneExtension = p.PhoneExtension,
                PhoneNumber = p.PhoneNumber,
                PhoneType = MapPhoneType(p.PhoneType)
            };
        }

        private static CorporateServiceDto.PhoneType MapPhoneType(PhoneType t)
        {
            switch (t)
            {
                case PhoneType.Home:
                    return CorporateServiceDto.PhoneType.Home;
                case PhoneType.Fax:
                    return CorporateServiceDto.PhoneType.OfficeFax;
                case PhoneType.Mobile:
                    return CorporateServiceDto.PhoneType.Mobile;
                case PhoneType.Office:
                    return CorporateServiceDto.PhoneType.Office;
                case PhoneType.AfterHours:
                case PhoneType.Other:
                case PhoneType.Main:
                    return CorporateServiceDto.PhoneType.Other;
                default:
                    return CorporateServiceDto.PhoneType.Other;
            }
        }

        private static CorporateServiceDto.AddressDto MapAddressDto(Address a)
        {
            if (a == null)
            {
                return null;
            }

            return new CorporateServiceDto.AddressDto()
            {
                Address1 = a.Address1,
                Address2 = a.Address2,
                AddressType = MapAddressType(a.AddressType),
                City = a.City,
                PostalCode = a.PostalCode,
                StateAbbreviation = a.State?.StateAbbreviation
            };
        }

        private static CorporateServiceDto.AddressType MapAddressType(AddressType aType)
        {
            return aType switch
            {
                AddressType.Billing => CorporateServiceDto.AddressType.Billing,
                AddressType.Mailing => CorporateServiceDto.AddressType.Mailing,
                _ => CorporateServiceDto.AddressType.Physical
            };
        }
    }
}
