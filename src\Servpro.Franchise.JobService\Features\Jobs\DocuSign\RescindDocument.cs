﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Amazon.SQS;
using Amazon.SQS.Model;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NuGet.Protocol;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.Options.DocuSign;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.DocuSign
{
    public class RescindDocument
    {
        public class Command : IRequest<Unit>
        {
            public Guid JobId { get; set; }
            public List<Guid> DocsToSignId { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty().WithMessage("JobId must not be empty");
                RuleFor(x => x.DocsToSignId).NotEmpty().NotNull().WithMessage("There must be FormTemplateIds to rescind");
            }
        }

        public class MessageBody
        {
            public MessageBody(Guid jobId, Guid franchiseSetId, Guid documentId)
            {
                JobId = jobId;
                FranchiseSetId = franchiseSetId;
                DocumentId = documentId;
            }

            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid DocumentId { get; set; }

        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly ILogger<Handler> _logger;
            private readonly IAmazonSQS _sqsClient;
            private readonly IOptionsMonitor<DocuSignOptions> _docuSignOptions;
            private readonly string _sqsUrl;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IAmazonDynamoDB _dynamoClient;
            private readonly List<SignDocumentStatus> _validToRescindStatus = 
                new List<SignDocumentStatus>() { SignDocumentStatus.Sent, SignDocumentStatus.FailedToRescind };

            public Handler(ILogger<Handler> logger,
                           IAmazonSQS sqsClient,
                           IConfiguration config,
                           ISessionIdAccessor sessionIdAccessor,
                           IAmazonDynamoDB amazonDynamoDB,
                           IOptionsMonitor<DocuSignOptions> docuSignOptions)
            {
                _logger = logger;
                _sqsClient = sqsClient;
                _sessionIdAccessor = sessionIdAccessor;
                _sqsUrl = config["AWS:SqsRescindUrl"];
                _dynamoClient = amazonDynamoDB;
                _docuSignOptions = docuSignOptions;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                using var jobIdScope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Sending Documents to be rescinded for Job. Request: {@request}", request);
                
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var envelopeIdToFormTemplateIds = await GetEnvelopeIdToFormTemplateIdsAsync(request.DocsToSignId, request.JobId, cancellationToken);

                await SendRescindEventToRescindQueue(request.JobId, 
                    request.FranchiseSetId, 
                    envelopeIdToFormTemplateIds, 
                    correlationId, 
                    cancellationToken);

                return Unit.Value;
            }

            /// <summary>
            /// Creates a new EnvelopeId to FormTemplateIds mapping to be utilized in calling the rescindDocumentEvent
            /// </summary>
            private async Task<Dictionary<Guid, HashSet<Guid>>> GetEnvelopeIdToFormTemplateIdsAsync(List<Guid> docsToSignId, 
                Guid jobId, 
                CancellationToken cancellationToken)
            {
                var envelopeIdToFormTemplateIds = new Dictionary<Guid, HashSet<Guid>>();

                foreach (var formTemplateId in docsToSignId)
                {
                    using var formTemplateIdScope = _logger.BeginScope("{formTemplateId}", formTemplateId);
                    var dynamoDocument = await GetDocumentRecordsAsync(jobId, formTemplateId, cancellationToken);

                    if (dynamoDocument == null)
                        continue;

                    var envelopeId = dynamoDocument.EnvelopeId;
                    using var envelopeIdScope = _logger.BeginScope("{envelopeId}", envelopeId);
                    if (!envelopeIdToFormTemplateIds.ContainsKey(envelopeId))
                    {
                        _logger.LogInformation("EnvelopeId not found in the dictionary, creating new entry with formTemplateId");
                        envelopeIdToFormTemplateIds.Add(envelopeId, new HashSet<Guid>{ formTemplateId });
                        continue;
                    }

                    //If the envelope has already been added with one formTemplateId, add the additional to the hashset
                    var formTemplateIds = envelopeIdToFormTemplateIds.GetValueOrDefault(envelopeId);
                    _logger.LogInformation("EnvelopeId exists in dictionary with the following set of ids: {formTemplateIds}", formTemplateIds);
                    formTemplateIds.Add(formTemplateId);
                    envelopeIdToFormTemplateIds[envelopeId] = formTemplateIds;
                }

                return envelopeIdToFormTemplateIds;
            }

            public async Task SendRescindEventToRescindQueue(Guid jobId,
                Guid fransetId,
                Dictionary<Guid, HashSet<Guid>> envelopeIdToFormTemplateIds,
                Guid correlationId,
                CancellationToken cancellationToken)
            {
                if (!envelopeIdToFormTemplateIds.Any())
                {
                    _logger.LogWarning("No documents were identified to be rescinded, will not send event to lambda.");
                    return;
                }

                var rescindDto = new RescindDocumentDto()
                {
                    JobId = jobId,
                    FranchiseSetId = fransetId,
                    EnvelopeIdToFormTemplateIds = envelopeIdToFormTemplateIds
                };
                var rescindEvent = new RescindDocumentEvent(rescindDto, correlationId);

                var messageRequest = new SendMessageRequest()
                {
                    QueueUrl = _sqsUrl,
                    MessageBody = rescindEvent.ToJson(),
                    MessageAttributes = new Dictionary<string, MessageAttributeValue>
                    {
                        { "CorrelationId", new MessageAttributeValue {StringValue = correlationId.ToString(), DataType = nameof(String)} },
                    }
                };
                var response = await _sqsClient.SendMessageAsync(messageRequest, cancellationToken);

                if (response.HttpStatusCode != System.Net.HttpStatusCode.OK)
                {
                    _logger.LogError("An Error occured when sending Rescind Document message to SQS");
                    return;
                }

                _logger.LogInformation("The Rescind Document message was sent to SQS.");
            }

            private async Task<SignDocumentRecord> GetDocumentRecordsAsync(Guid jobId,
                Guid formTemplateId,
                CancellationToken cancellationToken)
            {
                _logger.LogInformation("Attempting to get SignDocumentRecord from DynamoDB");
                var query = GetDocumentRecordsByJobIdQueryRequest(jobId);
                var response = await _dynamoClient.QueryAsync(query, cancellationToken);

                if (response == null || !response.Items.Any())
                {
                    _logger.LogError("Documents for Job were not found in dynamo");
                    return null;
                }

                var signDocumentRecordsForJob = response.Items.Select(MapSignDocumentRecord).ToList();
                _logger.LogDebug("Fetched signDocumentRecords from dynamo for the job: {@signDocumentRecordsForJob}", signDocumentRecordsForJob);

                var signDocumentRecord = signDocumentRecordsForJob
                    .Find(x => x.FormTemplateId == formTemplateId && _validToRescindStatus.Contains(x.Status));

                if (signDocumentRecord == null)
                    _logger.LogInformation("The specified FormTemplateId cannot be found in dynamo or is not in the correct status");
                    
                return signDocumentRecord;
            }

            private QueryRequest GetDocumentRecordsByJobIdQueryRequest(Guid jobId)
                => new QueryRequest
                {
                    TableName = _docuSignOptions.CurrentValue.TableName,
                    IndexName = SignDocumentTable.Indexes.JobIdIndexName,
                    ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                    {
                        { ":jobId", new AttributeValue { S = jobId.ToString() } }
                    },
                        ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#jobId", nameof(SignDocumentRecord.JobId) }
                    },
                    KeyConditionExpression = "#jobId = :jobId"
                };

            private static SignDocumentRecord MapSignDocumentRecord(Dictionary<string, AttributeValue> item)
                => new SignDocumentRecord(item);
        }
    }
}
