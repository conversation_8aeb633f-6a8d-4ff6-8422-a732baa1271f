﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models.Drybook;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class GetZonesSummary
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public Guid ZoneId { get; set; }
            public string ZoneName { get; set; }
            public IEnumerable<RoomDto> Rooms { get; set; }
            public Guid WaterClassId { get; set; }
            public Guid WaterCategoryId { get; set; }
            public Guid AirCalculationTypeId { get; set; }
            public Guid ZoneTypeId { get; set; }
            public Guid WaterClassRecommendationId { get; set; }
            public decimal TotalSquareFeet { get; set; }
            public decimal AffectedSquareFeet { get; set; }
            public decimal AffectedPercentage { get; set; }
            public bool WaterClassOverriden { get; set; }
        }

        public class RoomDto
        {
            public Guid JobId { get; set; }
            public Guid JobAreaId { get; set; }
            public Guid RoomId { get; set; }
            public string RoomName { get; set; }
            public Guid ZoneId { get; set; }
            public string ZoneName { get; set; }
            public DateTime VisitDate { get; set; }
            public decimal? TotalSquareFeet { get; set; }
            public decimal? AffectedSquareFeet { get; set; }
            public decimal AffectedPercentage { get; set; }
            public bool HasMissingFlooring { get; set; }
            public bool HasMissingDimensions { get; set; }
            public bool HasEquipmentPlacements { get; set; }
            public int EquipmentPlacementCount { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;

            public Handler(JobReadOnlyDataContext context)
            {
                _context = context;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .Include(x => x.Zones)
                        .ThenInclude(x => x.JobAreas)
                            .ThenInclude(x => x.Room)
                                .ThenInclude(x => x.RoomFlooringTypesAffected)
                    .Include(x => x.Zones)
                        .ThenInclude(x => x.JobAreas)
                            .ThenInclude(x => x.BeginJobVisit)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId, cancellationToken);
                return job.Zones
                    .Where(x => x.ZoneTypeId == ZoneTypes.Drying && !x.IsDeleted)
                    .Select(Map);
            }

            private Dto Map(Zone zone)
            {
                return new Dto
                {
                    JobId = zone.JobId,
                    ZoneId = zone.Id,
                    ZoneName = zone.Name,
                    Rooms = zone.JobAreas.Where(x => x.Room != null).Select(x => Map(x, zone)),
                    WaterClassId = zone.WaterClassId,
                    WaterCategoryId = zone.WaterCategoryId,
                    AirCalculationTypeId = zone.AirMoverCalculationTypeId,
                    ZoneTypeId = zone.ZoneTypeId,
                    WaterClassRecommendationId = zone.WaterClassId,
                    WaterClassOverriden = zone.WaterClassOverridden
                };
            }

            private RoomDto Map(
                JobArea jobArea,
                Zone zone)
            {
                var equipmentPlacementCount = jobArea.EquipmentPlacements.Count;
                var roomData = RoomUtils.CalculateRoomData(jobArea.Room, equipmentPlacementCount);
                return new RoomDto
                {
                    JobId = zone.JobId,
                    JobAreaId = jobArea.Id,
                    RoomId = jobArea.RoomId.Value,
                    RoomName = jobArea.Name,
                    ZoneId = zone.Id,
                    ZoneName = zone.Name,
                    VisitDate = jobArea.BeginJobVisit?.Date ?? DateTime.UtcNow,
                    TotalSquareFeet = roomData.TotalSqFt,
                    AffectedSquareFeet = roomData.AffectedSqFeet,
                    AffectedPercentage = roomData.AffectedPercentage,
                    HasMissingFlooring = roomData.HasMissingFlooring,
                    HasMissingDimensions = roomData.HasMissingDimensions,
                    HasEquipmentPlacements = roomData.HasEquipmentPlacements,
                    EquipmentPlacementCount = equipmentPlacementCount
                };
            }
        }
    }
}