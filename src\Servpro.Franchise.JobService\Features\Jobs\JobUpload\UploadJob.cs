﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.JobUpload;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Task = System.Threading.Tasks.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models.MicaAutomation;

namespace Servpro.Franchise.JobService.Features.Jobs
{
    public class UploadJob
    {
        public enum UploadType
        {
            Initial = 1,
            Daily = 3,
            Final = 2
        }

        public class Command : IRequest<Guid>
        {
            public Guid JobId { get; set; }
            public int FranchiseNumber { get; set; }
            public UploadType UploadType { get; set; }
            public Guid? InitialUploadDueDateExtensionReasonId { get; set; }
            public Guid? FinalUploadDueDateExtensionReasonId { get; set; }
            public bool? ShouldGenerateDryingReport { get; set; }
            public string CreatedBy { get; set; }
            public Guid CreatedById { get; set; }
            public bool IsAutoUpload { get; set; }
        }

        public class Handler : IRequestHandler<Command, Guid>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<UploadJob> _logger;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILookupServiceClient _lookupService;
            private readonly IMediator _mediator;
            private readonly IFranchiseServiceClient _franchiseService;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMicaAutomationUtility _micaAutomationUtility;

            public Handler(JobDataContext db, ILogger<UploadJob> logger, 
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupService,
                IMediator mediator,
                IFranchiseServiceClient franchiseService,
                ISessionIdAccessor sessionIdAccessor,
                IMicaAutomationUtility micaAutomationUtility)
            {
                _db = db;
                _logger = logger;
                _userInfoAccessor = userInfoAccessor;
                _lookupService = lookupService;
                _mediator = mediator;
                _franchiseService = franchiseService;
                _sessionIdAccessor = sessionIdAccessor;
                _micaAutomationUtility = micaAutomationUtility;
            }

            private static Guid MapUploadType(UploadType uploadType, bool isXact)
            {
                return uploadType switch
                {
                    UploadType.Daily => isXact ? JobUploadTypes.XactJobDaily : JobUploadTypes.JobDaily,
                    UploadType.Initial => isXact ? JobUploadTypes.XactJobInitial : JobUploadTypes.JobInitial,
                    UploadType.Final => isXact ? JobUploadTypes.XactJobFinal : JobUploadTypes.JobFinal,
                    _ => throw new ValidationException("Invalid UploadType")
                };
            }

            public async Task<Guid> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing upload request: {@request}", request);
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = GetCorrelationId();

                if (!string.IsNullOrEmpty(userInfo.Username))
                {
                    request.CreatedBy = userInfo.Username;
                    request.CreatedById = userInfo.Id;
                }

                var job = await _db.Jobs
                    .AsNoTracking()
                    .Include(x => x.JobCustomAttributes)
                    .Include(j => j.Caller)
                    .Include(j => j.Customer)
                        .ThenInclude(c => c.Business)
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job == null)
                {
                    throw new ResourceNotFoundException($"JobId not found: {request.JobId}");
                }

                job.JobContacts = await _db.JobContactMap
                   .Include(x => x.Contact)
                   .ThenInclude(y => y.Business)
                   .Where(x => x.JobId == request.JobId)
                   .ToListAsync(cancellationToken);

                job.MediaMetadata = await _db.MediaMetadata
                    .Include(x => x.JobInvoice)
                    .Where(x => x.JobId == request.JobId)
                    .ToListAsync(cancellationToken);

                job.JournalNotes = await _db.JournalNote
                    .Where(x => x.JobId == request.JobId)
                    .ToListAsync(cancellationToken);

                var uploadsToXact = JobUploadsToXact(job);

                // check for any upload locks
                var uploadTypeId = MapUploadType(request.UploadType, uploadsToXact);
                var uploadLock = await _db.JobUploadLocks
                    .AsNoTracking()
                    .Where(l => l.JobId == request.JobId && l.JobUploadTypeId == uploadTypeId)
                    .FirstOrDefaultAsync(cancellationToken: cancellationToken);

                if (uploadLock != null)
                    throw new ValidationException($"Requested upload is already in-progress.  Requested by: {uploadLock.CreatedBy} at {uploadLock.CreatedDate}");

                var isExtension = request.InitialUploadDueDateExtensionReasonId.HasValue ||
                                  request.FinalUploadDueDateExtensionReasonId.HasValue;

                if (uploadsToXact && !isExtension)
                    await RaiseXactUploadEvent(job, (int)request.UploadType, userInfo, correlationId, cancellationToken);
                else
                    await RaiseCorpSyncEvent(request, job, correlationId, cancellationToken);

                var micaAutomationTriggeredBy = GetTriggeringAction(request.UploadType);
                // Handle both here, normally only one or the other, but with jobs that need to trigger complete from final in Mitigate
                // the ShellMicaClaim check is included here and handled as well.
                await _micaAutomationUtility.HandleMicaAutomationConditionallyAsync(job, micaAutomationTriggeredBy, correlationId, true, cancellationToken);
                await _micaAutomationUtility.HandleShellMicaClaimAutomationConditionallyAsync(job, micaAutomationTriggeredBy, correlationId, cancellationToken);

                return job.Id;
            }

            private MicaAutomationTrigger GetTriggeringAction(UploadType uploadType)
            {
                return uploadType switch
                {
                    UploadType.Initial => MicaAutomationTrigger.InitialUpload,
                    UploadType.Final => MicaAutomationTrigger.FinalUpload,
                    UploadType.Daily => MicaAutomationTrigger.DailyUpload,
                    //Base case shouldn't ever be hit, but as a fallback, assign to Initial
                    _ => MicaAutomationTrigger.InitialUpload
                };
            }

            private bool JobUploadsToXact(Job job)
            {
                return job.CorporateJobNumber.HasValue && job.JobDispatchTypeId.HasValue &&
                       job.JobDispatchTypeId.Value == DispatchTypes.NonScanEr;
            }

            private async Task RaiseXactUploadEvent(Job job, int uploadTypeId, UserInfo userInfo, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising {eventName}", nameof(XactJobUploadStartedEvent));
                var lookups = await _lookupService.GetLookupsAsync(cancellationToken);
                var insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(ic => ic.Id == job.InsuranceCarrierId, cancellationToken);

                //TODO: Determine where the "CustomerCalled" date value comes from
                var xactDto = UploadEventGenerator.GetXactJobUploadStarted(
                    lookups,
                    insuranceClient?.InsuranceNumber ?? 0,
                    job,
                    MapUploadType(uploadTypeId),
                    userInfo.Username,
                    userInfo.Id
                    );

                var xactUploadEvent = new XactJobUploadStartedEvent(xactDto, correlationId);

                await GenerateOutboxMessage(xactUploadEvent.ToJson(), nameof(XactJobUploadStartedEvent),
                    xactUploadEvent.CorrelationId, userInfo.Username, cancellationToken);
            }

            private XactJobUploadStartedEvent.UploadType MapUploadType(int uploadTypeId)
            {
                return uploadTypeId switch
                {
                    1 => XactJobUploadStartedEvent.UploadType.Initial,
                    2 => XactJobUploadStartedEvent.UploadType.Final,
                    _ => XactJobUploadStartedEvent.UploadType.Daily
                };
            }

            private async Task RaiseCorporateJobSyncFailedEvent(Command request, string error, Guid correlationId, CancellationToken cancellationToken)
            {
                var dto = new CorporateJobSyncFailedEvent.JobSyncFailedDto()
                {
                    JobId = request.JobId,
                    CreatedBy = request.CreatedBy,
                    CreatedUtc = DateTime.UtcNow,
                    Error = error,
                    UploadType = Map((int)request.UploadType),
                    WasAutoUpload = request.IsAutoUpload
                };

                var newEvent = new OutboxMessage(new CorporateJobSyncFailedEvent(dto, correlationId).ToJson(),
                    nameof(CorporateJobSyncFailedEvent),
                    correlationId,
                    request.CreatedBy);
                await _db.OutboxMessages.AddAsync(newEvent, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);
            }

            private static CorporateJobSyncFailedEvent.UploadType Map(int typeId)
            {
                return typeId switch
                {
                    1 => CorporateJobSyncFailedEvent.UploadType.Initial,
                    2 => CorporateJobSyncFailedEvent.UploadType.Final,
                    _ => CorporateJobSyncFailedEvent.UploadType.Daily
                };
            }

            private async Task RaiseCorpSyncEvent(Command request, Job job, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising {eventName}", nameof(CorporateJobSyncStartedEvent));

                var franchise = await _franchiseService.GetFranchiseAsync(job.FranchiseId, job.FranchiseSetId, false, cancellationToken);

                if (franchise == null)
                {
                    const string error = "Franchise Service was unavailable. Unable to determine FranchiseNumber.";
                    _logger.LogWarning(error);
                    await RaiseCorporateJobSyncFailedEvent(request, error, correlationId, cancellationToken);
                    return;
                }
                if (franchise.FranchiseType == "Storm Team")
                {
                    const string error = "Unable to upload job. Storm Teams are not allowed to upload jobs.";
                    _logger.LogWarning(error);
                    await RaiseCorporateJobSyncFailedEvent(request, error, correlationId, cancellationToken);
                    return;
                }

                var franchiseNumber = (int)franchise.FranchiseNumber; // not sure why someone made this a long instead of an int

                InsuranceClient insuranceClient = null;
                if (job.InsuranceCarrierId.HasValue)
                {
                    insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(c => c.Id == job.InsuranceCarrierId.Value, cancellationToken);
                }

                var jobUploadDto = UploadEventGenerator.GetJobUploadForCorporateJobSync(
                    job,
                    insuranceClient,
                    MapCorpSyncUploadType(request.UploadType),
                    request.CreatedBy,
                    request.CreatedById,
                    franchiseNumber,
                    JobUploadsToXact(job),
                    request.IsAutoUpload,
                    request.InitialUploadDueDateExtensionReasonId.HasValue || request.FinalUploadDueDateExtensionReasonId.HasValue,
                    request.InitialUploadDueDateExtensionReasonId ?? request.FinalUploadDueDateExtensionReasonId
                    );

                // this should NOT happen, but putting it in here anyway
                if (jobUploadDto.CorporateServiceDto.DoesJobUploadToXact && !jobUploadDto.CorporateServiceDto.IsExtension)
                {
                    var eventDto = new XactJobUploadFailedEvent.XactJobUploadFailedDto()
                    {
                        ProjectNumber = job.ProjectNumber,
                        CreatedUtc = DateTime.UtcNow,
                        Error = "XactAnalysis jobs cannot use Corporate Sync",
                        JobId = job.Id,
                        Username = request.CreatedBy
                    };
                    var outgoingEvent = new XactJobUploadFailedEvent(eventDto, correlationId);
                    await GenerateOutboxMessage(outgoingEvent.ToJson(), nameof(XactJobUploadFailedEvent),
                        outgoingEvent.CorrelationId, request.CreatedBy, cancellationToken);
                    return;
                }

                var corpSyncEvent = new CorporateJobSyncStartedEvent(jobUploadDto, correlationId);

                await GenerateOutboxMessage(corpSyncEvent.ToJson(), nameof(CorporateJobSyncStartedEvent),
                    corpSyncEvent.CorrelationId, request.CreatedBy, cancellationToken);
            }

            private Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload.UploadType MapCorpSyncUploadType(UploadType t)
            {
                return t switch
                {
                    UploadType.Initial => FranchiseSystems.Framework.Messaging.Events.Jobs.Upload.UploadType.Initial,
                    UploadType.Final => FranchiseSystems.Framework.Messaging.Events.Jobs.Upload.UploadType.Final,
                    _ => FranchiseSystems.Framework.Messaging.Events.Jobs.Upload.UploadType.Daily
                };
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername, CancellationToken cancellationToken)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _db.OutboxMessages.AddAsync(newEvent, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);
            }

            private Guid GetCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out var correlationId))
                    correlationId = Guid.NewGuid();
                return correlationId;
            }
        }
    }
}