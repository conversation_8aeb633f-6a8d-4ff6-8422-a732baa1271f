@model List<Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.EquipmentPlacementDetail>

@{  var listOfEquipmentTypes = Model.Select(x => x.EquipmentType).Distinct().ToList();
    var finalTotalRowSumOfDays = Model.Sum(x => x.Days);
    var finalTotalRowSumOfHours = Model.Sum(x => x.Hours);
    var printedEquipmentTypes = new List<string>();
    var printedRooms = new List<string>();
}

<div class="floatLeft">
    <div class="monitoring-section-heading">{usageSummary}Usage Summary{/usageSummary}</div>

    <table class="usage-summary-table" summary="Usage summary table">
        <thead>
            <tr>
                <th scope="col" class="cellLeftText">Equipment Type</th>
                <th scope="col" class="cellLeftText">Room</th>
                <th scope="col">Equipment Model</th>
                <th scope="col">Asset Number</th>
                <th scope="col">Placed</th>
                <th scope="col">Removed</th>
                <th scope="col">Total Days*</th>
                <th scope="col">Total Hours*</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var equipmentType in listOfEquipmentTypes)
            {
                printedRooms.Clear();
                var equipmentTypeRows = Model.Where(x => x.EquipmentType == equipmentType).ToList();
                var listOfRooms = equipmentTypeRows.Select(x => x.Room).Distinct().ToList();
                var rowSpanEquipmentType = equipmentTypeRows.Count + listOfRooms.Count + 1;
                var equipmentTotalHours = equipmentTypeRows.Sum(x => x.Hours);
                var equipmentTotalDays = equipmentTypeRows.Sum(x => x.Days);
                foreach (var room in listOfRooms)
                {
                    var roomRows = equipmentTypeRows.Where(x => x.Room == room).ToList();
                    var rowSpanRoom = roomRows.Count + 1;
                    var roomAssetsCount = roomRows.Count;
                    var roomTotalDays = roomRows.Sum(x => x.Days);
                    var roomTotalHours = roomRows.Sum(x => x.Hours);
                    foreach (var roomRow in roomRows)
                    {
                        string placedTimestring = roomRow.Placed != null ? roomRow.Placed.Value.ToString("MM/d/yyyy\r\nh:mm tt") : "";
                        string removedTimestring = roomRow.Removed != null ? roomRow.Removed.Value.ToString("MM/d/yyyy\r\nh:mm tt") : "";
                        <tr>
                            @if (!printedEquipmentTypes.Contains(equipmentType))
                            {
                                printedEquipmentTypes.Add(equipmentType);
                                <td class="usage-summary-darkcolor cellLeftText wrapTextEquipmentType" rowspan="@rowSpanEquipmentType">@equipmentType</td>
                            }
                            @if (!printedRooms.Contains(room))
                            {
                                printedRooms.Add(room);
                                <td class="cellLeftText" rowspan="@rowSpanRoom">@room</td>
                            }
                            <td class="cellLeftText">@roomRow.EquipmentModel</td>
                            <td class="cellLeftText">@roomRow.AssetNumber</td>
                            <td class="cellAlignMiddle">@placedTimestring</td>
                            <td class="cellAlignMiddle">@removedTimestring</td>
                            <td>@roomRow.Days</td>
                            <td>@roomRow.Hours</td>
                        </tr>
                    }
                    @*--------- Totals for the Rooms ---------*@
                    <tr>
                        <td></td>
                        <td class="bold cellLeftText cellAlignMiddle">@roomAssetsCount</td>
                        <td></td>
                        <td></td>
                        <td class="bold cellAlignMiddle">@roomTotalDays</td>
                        <td class="bold cellAlignMiddle">@roomTotalHours</td>
                    </tr>
                }
                <tr class="usage-summary-darkcolor">
                    <td class="bold cellLeftText cellAlignMiddle" colspan="5">Total</td>
                    <td class="bold cellAlignMiddle">@equipmentTotalDays</td>
                    <td class="bold cellAlignMiddle">@equipmentTotalHours</td>
                </tr>
            }
            <tr class="silver-color">
                <td class="bold cellLeftText cellAlignMiddle" colspan="6">Total</td>
                <td class="bold cellAlignMiddle">@finalTotalRowSumOfDays</td>
                <td class="bold cellAlignMiddle">@finalTotalRowSumOfHours</td>
            </tr>
        </tbody>
    </table>
</div>