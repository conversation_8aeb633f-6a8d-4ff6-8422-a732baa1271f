﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ClientRequirementCountChanged
    {
        public class Event : ClientRequirementCountChangedEvent, IRequest
        {
            public Event(ClientRequirementCountChangedEvent.ClientRequirementCountChangedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db, ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogDebug("Handler begin with: {@request}", request);

                var job = await _db.Jobs
                    .FirstOrDefaultAsync(j => j.Id == request.ClientRequirementCountChanged.JobId, cancellationToken: cancellationToken);

                if (job == null)
                {
                    _logger.LogWarning("Job not found for request: {@request}", request);
                    return Unit.Value;
                }

                job.ClientRequirementsCount = request.ClientRequirementCountChanged.RequirementCount;
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}