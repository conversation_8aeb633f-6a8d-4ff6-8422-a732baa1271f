﻿{
  "estimaticsRequirements": [
    {
      "name": "Discount Not Allowed",
      "description": "A credit has been applied to the estimate, but this insurance client does not require a discount. This credit must be removed.",
      "isMet": false,
      "allowBypass": false,
      "allowAcknowledgement": false,
      "status": 0,
      "originalValidationStatus": 0,
      "validationStatus": 0,
      "id": 5,
      "ruleId": 103,
      "askedPerVisit": false,
      "ruleParameters": [],
      "requiredForInitialUpload": false
    }
  ],
  "photoRequirements": [
    {
      "ArtifactTypeId": "cc420b00-2a15-4ccf-9b25-5810f934932b",
      "ValidationEffect": "Require",
      "RequiredPerVisit": false,
      "RequiredPerRoom": false,
      "RequiredLastVisit": false,
      "BypassAllowed": false,
      "Photos": [
        {
          "JobArtifactId": "c4127627-ad69-47db-b105-42a099c26fc3",
          "Valid": true
        }
      ],
      "RuleParameters": [],
      "Name": "Front of Structure",
      "RuleId": 5,
      "Description": "A photo of the front of structure is required. This should be the front of the risk location taken from the street, driveway, or parking area.",
      "Notification": "Front of Structure photo is required.",
      "ValidationStatus": 1,
      "FailMessage": "A Front of Structure photo has not been taken.",
      "PassMessage": "A Front of Structure photo has been taken.",
      "RequiredForInitialUpload": false
    },
    {
      "artifactTypeId": "e53d84d8-637c-42fb-b074-7255280f4d6f",
      "validationEffect": "Require",
      "requiredPerVisit": false,
      "requiredPerRoom": true,
      "requiredLastVisit": false,
      "bypassAllowed": true,
      "photos": [],
      "ruleParameters": [
        {
          "Key": "PhotosPerRoom",
          "Value": "1"
        }
      ],
      "name": "Pre Mitigation Photo(s) of Each Affected Room",
      "ruleId": 10,
      "description": "A pre-mitigation photo is required for each room. This should show the room contents and any pre-existing damage.",
      "notification": "A room has been created. Take a pre-mitigation photo.",
      "validationStatus": 0,
      "doNothingMessage": "No rooms have been added to this job, no action required.",
      "failMessage": "A Pre Mitigation photo has not been taken of every affected room. No exception note has been found.",
      "passMessage": "A Pre Mitigation photo has been taken of every affected room, or an exception note has been found.",
      "requiredForInitialUpload": false
    }
  ],
  "forms": [
    {
      "name": "GuideOneATP",
      "type": "Other",
      "formTemplateId": "87f43f9b-baf6-491a-a2fe-f540d311b77d",
      "validationStatus": 0,
      "description": "GuideOneATP.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28500 - California Customer Information Form - Fire Damage",
      "type": "Other",
      "formTemplateId": "4e8484de-cdd3-447e-a671-71a04a47fe0a",
      "validationStatus": 0,
      "description": "28500 - California Customer Information Form - Fire Damage.pdf",
      "relatedForm": "30C3C0D9-1259-491C-BCE1-081321A16EDA",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ACA Home Insurance Corp_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "637c08f3-1dc5-496a-80f3-3bc27ddd4c11",
      "validationStatus": 0,
      "description": "ACA Home Insurance Corp_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Capital Assurance Corp_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "d07c7a18-d466-46fb-860c-4a1c87a64dd5",
      "validationStatus": 0,
      "description": "American Capital Assurance Corp_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Lloyds_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "1903fdb4-2d4b-46a1-abd5-dfbcef0aa24e",
      "validationStatus": 0,
      "description": "ASI Lloyds_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Preferred Insurance Corp_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "bfa38531-0ad8-4c2e-ae07-bc3ec957e978",
      "validationStatus": 0,
      "description": "ASI Preferred Insurance Corp_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Select Insurance Corp_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "fc1b884a-0ccc-4725-bf7b-d9fcc37c6e53",
      "validationStatus": 0,
      "description": "ASI Select Insurance Corp_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Strategic Insurance Corp_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "6bdf431d-fbf4-455e-9528-9f493a4cd8d3",
      "validationStatus": 0,
      "description": "American Strategic Insurance Corp_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Ark Royal Insurance_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "64b7b8f6-1fea-4ae6-b4ce-eaeef0b33708",
      "validationStatus": 0,
      "description": "Ark Royal Insurance_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Assurance Corp_Initial Mitigation Report",
      "type": "Other",
      "formTemplateId": "d9a3617c-ba0d-4ae6-8fe1-1ee70add9b78",
      "validationStatus": 0,
      "description": "ASI Assurance Corp_Initial Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Capital Assurance Corp_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "32c372dc-9c9e-430c-bb27-ae9c129e53c8",
      "validationStatus": 0,
      "description": "American Capital Assurance Corp_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ACA Home Insurance Corp_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "0588c87d-01df-4154-89cd-4b6c314bc578",
      "validationStatus": 0,
      "description": "ACA Home Insurance Corp_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Strategic Insurance Corp_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "e27bd1f1-4ec0-4d3d-bdc6-11554d41dd1d",
      "validationStatus": 0,
      "description": "American Strategic Insurance Corp_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Ark Royal Insurance_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "400ef2c3-3899-4c99-9768-41d92e4e74bc",
      "validationStatus": 0,
      "description": "Ark Royal Insurance_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Assurance Corp_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "c4559dc4-7194-4b90-b53f-6ab49826ab0f",
      "validationStatus": 0,
      "description": "ASI Assurance Corp_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Lloyds_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "e27705ec-92df-4df0-aac8-7ed3a0969aa8",
      "validationStatus": 0,
      "description": "ASI Lloyds_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Preferred Insurance Corp_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "339170d7-4fc7-4a14-a050-d572d2396485",
      "validationStatus": 0,
      "description": "ASI Preferred Insurance Corp_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Select Insurance Corp_Final Mitigation Report",
      "type": "Other",
      "formTemplateId": "27f6d6ee-da04-46d6-94bd-d5d7c110287d",
      "validationStatus": 0,
      "description": "ASI Select Insurance Corp_Final Mitigation Report.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Capital Assurance Corp_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "46a45e79-04f7-4750-b470-d12f1bd3c93a",
      "validationStatus": 0,
      "description": "American Capital Assurance Corp_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ACA Home Insurance Corp_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "a4ca6c71-45a6-4ee3-97a6-6f59b831511f",
      "validationStatus": 0,
      "description": "ACA Home Insurance Corp_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Assurance Corp_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "a58029e5-7819-45ae-a4a8-e9624b0b8c39",
      "validationStatus": 0,
      "description": "ASI Assurance Corp_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Ark Royal Insurance_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "c3f7fb9e-ac04-4eb8-a79f-282e0c67f372",
      "validationStatus": 0,
      "description": "Ark Royal Insurance_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Strategic Insurance Corp_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "d4e2c73e-7773-4157-b117-456e667e74e1",
      "validationStatus": 0,
      "description": "American Strategic Insurance Corp_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Lloyds_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "f1b9e63d-419c-4140-a00b-91be3dfe5d7a",
      "validationStatus": 0,
      "description": "ASI Lloyds_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Select Insurance Corp_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "ea07d8c8-e4f2-4d96-8d6d-ac65a423e79e",
      "validationStatus": 0,
      "description": "ASI Select Insurance Corp_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ASI Preferred Insurance Corp_TheChoiceIsYours",
      "type": "Other",
      "formTemplateId": "3adb814d-673d-4a4e-8458-e0a1bef9d385",
      "validationStatus": 0,
      "description": "ASI Preferred Insurance Corp_TheChoiceIsYours.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28500 - Customer Information Form - Fire Damage",
      "type": "Other",
      "formTemplateId": "30c3c0d9-1259-491c-bce1-081321a16eda",
      "validationStatus": 0,
      "description": "28500 - Customer Information Form - Fire Damage.pdf",
      "relatedForm": "4E8484DE-CDD3-447E-A671-71A04A47FE0A",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "AIG - Work Authorization Direction of Payment",
      "type": "Other",
      "formTemplateId": "6f182bb8-7f84-42c1-bade-2e8d1965f96d",
      "validationStatus": 0,
      "description": "AIG - Work Authorization Direction of Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "AIGM- Work Authorization Direction of Payment",
      "type": "Other",
      "formTemplateId": "7e547d11-ab79-4b56-9c17-833ae3614863",
      "validationStatus": 0,
      "description": "AIGM- Work Authorization Direction of Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Guarantee and Liability Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "7598bc00-8ff5-4ff9-b621-8390750f5e2c",
      "validationStatus": 0,
      "description": "American Guarantee and Liability Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Guarantee and Liability COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "f430f3f7-edb2-476e-b105-5c96580812d2",
      "validationStatus": 0,
      "description": "American Guarantee and Liability COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Zurich Ins. Co.  Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "48145983-9d19-4541-b58a-73ca0bc9ddc3",
      "validationStatus": 0,
      "description": "American Zurich Ins. Co.  Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Zurich Ins. Co. COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "0fd9d6ff-79ea-4aa0-8df4-7ec2b62f33ba",
      "validationStatus": 0,
      "description": "American Zurich Ins. Co. COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Assurance Co. of America Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "3b1ca848-d25e-4cb2-a423-8ef9fb57c65d",
      "validationStatus": 0,
      "description": "Assurance Co. of America Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Assurance Co. of America COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "e28c8f31-a222-430d-a506-6b37af2328a4",
      "validationStatus": 0,
      "description": "Assurance Co. of America COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Colonial American Cas. and Surety Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "9d2a435d-83ed-47d0-9c87-f626eef43330",
      "validationStatus": 0,
      "description": "Colonial American Cas. and Surety Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Colonial American Cas. and Surety COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "3a5aebee-1423-464f-ad8c-16dd638819d0",
      "validationStatus": 0,
      "description": "Colonial American Cas. and Surety COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Empire Indemnity Insurance Company Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "7dd1972f-6211-4719-8b0b-bae1200bf604",
      "validationStatus": 0,
      "description": "Empire Indemnity Insurance Company Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Empire Indemnity Insurance Company COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "5fd6c5ac-27b6-4084-9527-b219b68f3655",
      "validationStatus": 0,
      "description": "Empire Indemnity Insurance Company COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Fidelity and Deposit Ins. Co of MD Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "4b233727-2f3a-4502-8f6a-9d61ec4e03d2",
      "validationStatus": 0,
      "description": "Fidelity and Deposit Ins. Co of MD Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Fidelity and Deposit Ins. Co. of MD COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "64ebc19a-0840-4190-8a5d-160fb7338607",
      "validationStatus": 0,
      "description": "Fidelity and Deposit Ins. Co. of MD COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Maryland Casualty Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "626c346d-ed73-4ddf-b2ad-fa1d0cb71a4e",
      "validationStatus": 0,
      "description": "Maryland Casualty Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Maryland Casualty COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "d3dd46be-6864-4957-885a-6455d4e51fcb",
      "validationStatus": 0,
      "description": "Maryland Casualty COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Northern Ins. Co. of New York Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "0ccff439-56b9-4729-b663-0b87755ac743",
      "validationStatus": 0,
      "description": "Northern Ins. Co. of New York Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Northern Ins. Co. of New York COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "4cc186cf-51f5-458a-a3a5-343489034e86",
      "validationStatus": 0,
      "description": "Northern Ins. Co. of New York COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Steadfast Insurance Co. Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "85dce1ef-a296-4a9d-be1f-03c42ec0c533",
      "validationStatus": 0,
      "description": "Steadfast Insurance Co. Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Steadfast Insurance Company COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "2e6c507b-f4ae-424a-9d82-4cd55bde76be",
      "validationStatus": 0,
      "description": "Steadfast Insurance Company COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Universal Underwriters Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "e01cfcf7-b50b-4588-9bcc-10e303707a32",
      "validationStatus": 0,
      "description": "Universal Underwriters Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Universal Underwriters COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "cba592a9-4875-4862-b3ed-c600e58a18e1",
      "validationStatus": 0,
      "description": "Universal Underwriters COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Zurich Insurance Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "3d399222-2e9d-488b-99ba-42ce80a451af",
      "validationStatus": 0,
      "description": "Zurich Insurance Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Zurich Insurance COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "eba9f139-1753-4c5a-a2dc-7aa279e9f0ca",
      "validationStatus": 0,
      "description": "Zurich Insurance COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Empire Fire and Marine Insurance Authorization for Repairs and Payment",
      "type": "Other",
      "formTemplateId": "7570ef29-153e-40c6-af29-44b9c9f047e7",
      "validationStatus": 0,
      "description": "Empire Fire and Marine Insurance Authorization for Repairs and Payment.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Empire Fire and Marine Insurance COS - Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "d1dca32c-c6b6-42e1-93d5-66e219b9ae9a",
      "validationStatus": 0,
      "description": "Empire Fire and Marine Insurance COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "AllState - LSC Monitoring Services - 28554",
      "type": "Other",
      "formTemplateId": "d107f96b-2b08-4676-93c1-739fa9cec7fc",
      "validationStatus": 0,
      "description": "AllState - LSC Monitoring Services - 28554.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Liberty Mutual  Initial Call Template",
      "type": "Other",
      "formTemplateId": "4ac274b6-605b-44c4-a1d4-9558728c9561",
      "validationStatus": 0,
      "description": "Liberty Mutual  Initial Call Template.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Safeco Initial Call Template",
      "type": "Other",
      "formTemplateId": "66d828d6-2fb9-464d-a99e-412bc55708a4",
      "validationStatus": 0,
      "description": "Safeco Initial Call Template.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Safeco - Water Initial Call Template",
      "type": "Other",
      "formTemplateId": "eacbd329-3b34-4839-b432-0786c47ae98e",
      "validationStatus": 0,
      "description": "Safeco - Water Initial Call Template.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28502 - Customer Information Form - Move-Outs - 09-2012-DryBook",
      "type": "Other",
      "formTemplateId": "f404c1ad-d7c2-471f-b4cb-166f20719489",
      "validationStatus": 0,
      "description": "28502 - Customer Information Form - Move-Outs - 09-2012-DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Assurant FEMA_Form_086-0-9_PROOF OF LOSS",
      "type": "Other",
      "formTemplateId": "e3ac02df-27d6-4198-8c85-cfee9e8df1f1",
      "validationStatus": 0,
      "description": "Assurant FEMA_Form_086-0-9_PROOF OF LOSS.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28554 - Limitations on Standard Compliance - Monitoring Services-02-2018_Drybook-ACTIVE",
      "type": "Other",
      "formTemplateId": "2f471f59-3308-4949-990c-8e78c3d9d6c7",
      "validationStatus": 0,
      "description": "28554 - Limitations on Standard Compliance - Monitoring Services-02-2018_Drybook-ACTIVE.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28650 - Certificate of Completion",
      "type": "Other",
      "formTemplateId": "271502b1-05b7-4024-9f67-92814539cf6e",
      "validationStatus": 0,
      "description": "28650 - Certificate of Completion.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28640 - Commercial Customer Information Form - Water",
      "type": "Other",
      "formTemplateId": "f216e923-9121-4f82-82dc-3819907fdcbd",
      "validationStatus": 0,
      "description": "28640 - Commercial Customer Information Form - Water.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28641 - Commercial Customer Information Form - Fire",
      "type": "Other",
      "formTemplateId": "0c1e73ff-307d-4644-9a1a-07afe56dce69",
      "validationStatus": 0,
      "description": "28641 - Commercial Customer Information Form - Fire.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28642 - Commercial Emergency Work Authorization",
      "type": "Other",
      "formTemplateId": "5729b872-0017-4955-95f9-d78990927429",
      "validationStatus": 0,
      "description": "28642 - Commercial Emergency Work Authorization.pdf",
      "relatedForm": "1137CEB4-017A-4BEA-A58D-BC91A527D604",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28748 - Authorization and Service Contract - Disaster Recovery Team",
      "type": "Other",
      "formTemplateId": "bfa40409-535c-4da9-b976-2baa79d3e939",
      "validationStatus": 0,
      "description": "28748 - Authorization and Service Contract - Disaster Recovery Team.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28749 - Commercial Certificate of Satisfaction",
      "type": "Other",
      "formTemplateId": "a1a98110-8374-43fe-9224-f5bd99bdc7b2",
      "validationStatus": 0,
      "description": "28749 - Commercial Certificate of Satisfaction.pdf",
      "relatedForm": "9424D84F-CC88-4B0F-8064-38F3D1057893",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28541-CANADA - Content Inventory Worksheet - Unsalvageable Items - 05-2011",
      "type": "Other",
      "formTemplateId": "f6bf1636-9795-4614-b65c-2dddae4b760f",
      "validationStatus": 0,
      "description": "28541-CANADA - Content Inventory Worksheet - Unsalvageable Items - 05-2011.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28557 - Limitations on Standard Compliance -  Trauma and Crime Scene Cleanup",
      "type": "Other",
      "formTemplateId": "dd69c0f5-19fd-4825-9213-130b7c0efb2f",
      "validationStatus": 0,
      "description": "28557 - Limitations on Standard Compliance -  Trauma and Crime Scene Cleanup.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28746 Image Usage Release Form",
      "type": "Other",
      "formTemplateId": "05efbb1b-f049-4869-87cb-d3cadcbc644b",
      "validationStatus": 0,
      "description": "28746 Image Usage Release Form.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28745 - Roof Tarp  Board Up Waiver",
      "type": "Other",
      "formTemplateId": "244c2060-7c65-43e3-8b89-be41b8d60422",
      "validationStatus": 0,
      "description": "28745 - Roof Tarp  Board Up Waiver.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28568 - Upholstery and Drapery Condition Report",
      "type": "Other",
      "formTemplateId": "7050f783-30cd-4d65-b0f0-2e682b17f8d6",
      "validationStatus": 0,
      "description": "28568 - Upholstery and Drapery Condition Report.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28569 - Carpet and Rug Condition Report",
      "type": "Other",
      "formTemplateId": "e06fad1f-0e26-43e8-a9db-5700eb77e837",
      "validationStatus": 0,
      "description": "28569 - Carpet and Rug Condition Report.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28514 - Release for Disposal of Belongings - 11-10",
      "type": "Other",
      "formTemplateId": "7d106678-b380-4f88-aa71-b12d009df0ac",
      "validationStatus": 0,
      "description": "28514 - Release for Disposal of Belongings - 11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28540 - Authorization to Remove Drying Equipment - 11-10",
      "type": "Other",
      "formTemplateId": "5f497241-4df7-4e94-94df-1452368f19c8",
      "validationStatus": 0,
      "description": "28540 - Authorization to Remove Drying Equipment - 11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28553 - Limitations on Standard Compliance - Water Damage",
      "type": "Other",
      "formTemplateId": "dbecc1dd-0117-4232-b44d-d7688f54f5fc",
      "validationStatus": 0,
      "description": "28553 - Limitations on Standard Compliance - Water Damage.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28580 - Customer Informed Consent for Sporicidin - Version 05-11",
      "type": "Other",
      "formTemplateId": "a09885ab-a813-413d-9b61-664f544fe0d6",
      "validationStatus": 0,
      "description": "28580 - Customer Informed Consent for Sporicidin - Version 05-11.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28541 - Contents Inventory Worksheet - Unsalvageable Items_11 Page-05-2015_DryBook",
      "type": "Other",
      "formTemplateId": "15e5d1fa-c93d-43ee-a42e-3d30b41ea381",
      "validationStatus": 0,
      "description": "28541 - Contents Inventory Worksheet - Unsalvageable Items_11 Page-05-2015_DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28542 - Contents Inventory Worksheet - Large Items_11 Page-05-2015_DryBook",
      "type": "Other",
      "formTemplateId": "d48efcdc-c828-4a76-8d19-c3a5c8a66f1e",
      "validationStatus": 0,
      "description": "28542 - Contents Inventory Worksheet - Large Items_11 Page-05-2015_DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "WENDY's ATR",
      "type": "Other",
      "formTemplateId": "bb54e2b6-11fb-45e3-8b37-f1a555c2dd00",
      "validationStatus": 0,
      "description": "WENDY's ATR.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28544 - Contents Inventory Worksheet - Personal Items_11_pages-05-2015_DryBook",
      "type": "Other",
      "formTemplateId": "5c5a241c-6683-41d2-8a42-0d69cb01a9ac",
      "validationStatus": 0,
      "description": "28544 - Contents Inventory Worksheet - Personal Items_11_pages-05-2015_DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28565 - Terms of Move-Out- 09-2012",
      "type": "Other",
      "formTemplateId": "0ec476fb-1e3e-491b-bf6b-a3314e6235d9",
      "validationStatus": 0,
      "description": "28565 - Terms of Move-Out- 09-2012.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28543 - Contents Inventory Worksheet - Electronics-11 Pages_ 05-2015_DryBook",
      "type": "Other",
      "formTemplateId": "ceee916e-4b8e-4660-ab2b-b65598ed3423",
      "validationStatus": 0,
      "description": "28543 - Contents Inventory Worksheet - Electronics-11 Pages_ 05-2015_DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28521 - Estimate for Services 01-08",
      "type": "Other",
      "formTemplateId": "df8a17ce-e796-40cc-ad82-5880deb1ed8f",
      "validationStatus": 0,
      "description": "28521 - Estimate for Services 01-08.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28529 - Certificate of Satisfaction - Emergency Response_11-2010",
      "type": "Other",
      "formTemplateId": "9424d84f-cc88-4b0f-8064-38f3d1057893",
      "validationStatus": 0,
      "description": "28529 - Certificate of Satisfaction - Emergency Response_11-2010.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28530 - Certificate of Satisfaction - Restoration Services_11-2010",
      "type": "Other",
      "formTemplateId": "8b66409b-5f12-412f-98ec-4f5fc72d3aa9",
      "validationStatus": 0,
      "description": "28530 - Certificate of Satisfaction - Restoration Services_11-2010.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28625 - Decline Service Form 12-2013",
      "type": "Other",
      "formTemplateId": "7cc868f7-3174-4aa4-b5d0-1d3934357ccf",
      "validationStatus": 0,
      "description": "28625 - Decline Service Form 12-2013.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28516 - Job Diary - 11-10",
      "type": "Other",
      "formTemplateId": "d9a91b16-31a3-418f-b848-d2ea10c62720",
      "validationStatus": 0,
      "description": "28516 - Job Diary - 11-10.docx",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28534 - Drying Zone Monitoring Report - 01-2011 (2)",
      "type": "Other",
      "formTemplateId": "23d32a3a-5d96-42b2-a90b-c1337f0be9d3",
      "validationStatus": 0,
      "description": "28534 - Drying Zone Monitoring Report - 01-2011 (2).docx",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28532 - Daily Equipment Tracking Report - 01-2011",
      "type": "Other",
      "formTemplateId": "0664b2ba-886a-41fd-b406-cd51d2f0939f",
      "validationStatus": 0,
      "description": "28532 - Daily Equipment Tracking Report - 01-2011.docx",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28536 - Sketch Pad - 11-2010",
      "type": "Other",
      "formTemplateId": "a86b4aaa-fa61-42e9-ba14-b5bd6d88e59a",
      "validationStatus": 0,
      "description": "28536 - Sketch Pad - 11-2010.docx",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ABIC FEMA_Form_086-0-9_PROOF OF LOSS",
      "type": "Other",
      "formTemplateId": "20b3970b-1aa5-4f9a-89a0-2554a546f857",
      "validationStatus": 0,
      "description": "ABIC FEMA_Form_086-0-9_PROOF OF LOSS.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28550 - Contents Inventory Worksheet-Boxed Items-11_Pages_ 05-2015_DryBook",
      "type": "Other",
      "formTemplateId": "3e670f73-8e0a-4472-b35c-9ba632b2f379",
      "validationStatus": 0,
      "description": "28550 - Contents Inventory Worksheet-Boxed Items-11_Pages_ 05-2015_DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28535 - Notice of Equipment Removal - 11-10",
      "type": "Other",
      "formTemplateId": "1e3b5e6e-5bf7-43ab-8b41-ceb9778c574d",
      "validationStatus": 0,
      "description": "28535 - Notice of Equipment Removal - 11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28552 - Discovery of Mold Growth.11-2010",
      "type": "Other",
      "formTemplateId": "61cf8d35-8558-4aaf-9526-0ae425aa0c9d",
      "validationStatus": 0,
      "description": "28552 - Discovery of Mold Growth.11-2010.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28545 - Mold Notice to Prospective Customer.11-10",
      "type": "Other",
      "formTemplateId": "44dfad82-9ecd-4738-9d40-3f5f4c7ab711",
      "validationStatus": 0,
      "description": "28545 - Mold Notice to Prospective Customer.11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28547 - Limitations on Standard Compliance - Mold Remediation",
      "type": "Other",
      "formTemplateId": "2eacb033-ca38-4a7b-9c6c-f9873b087506",
      "validationStatus": 0,
      "description": "28547 - Limitations on Standard Compliance - Mold Remediation.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28546 - Customer Agreement - Mold Remediation and Related Services - 11-10",
      "type": "Other",
      "formTemplateId": "b41237f0-043d-4702-99f2-ccb0c2d9574a",
      "validationStatus": 0,
      "description": "28546 - Customer Agreement - Mold Remediation and Related Services - 11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28583 - EPA Renovation Repair and Painting Notice - 02-2011",
      "type": "Other",
      "formTemplateId": "551db2f3-f9c8-4f3e-8a37-3ad50a40e0e9",
      "validationStatus": 0,
      "description": "28583 - EPA Renovation Repair and Painting Notice - 02-2011.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28582 - Lead Paint Questionnaire and Confirmation of Receipt of Lead Pamphlet - 11-10",
      "type": "Other",
      "formTemplateId": "64bea6c0-a926-4490-a6f8-a74f07df1c0e",
      "validationStatus": 0,
      "description": "28582 - Lead Paint Questionnaire and Confirmation of Receipt of Lead Pamphlet - 11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28586 - Report of Lead Test Results - 11-10",
      "type": "Other",
      "formTemplateId": "66b7b40f-da4f-4ec7-8a67-532f4476115b",
      "validationStatus": 0,
      "description": "28586 - Report of Lead Test Results - 11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28567 - Processed Wood Release and Waiver - 07-2011",
      "type": "Other",
      "formTemplateId": "ea4c7653-39a6-43fd-a837-f90e9c8bdeea",
      "validationStatus": 0,
      "description": "28567 - Processed Wood Release and Waiver - 07-2011.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Form Field Name with Tokens",
      "type": "Other",
      "formTemplateId": "75ca1c7c-274b-4128-bd83-ca13040bf6f5",
      "validationStatus": 0,
      "description": "Form Field Name with Tokens.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28577 - Fire Damage Emergency Services Report - 06-2014-DryBook",
      "type": "Other",
      "formTemplateId": "8f96489e-38a1-4feb-91fe-8e9505268ab4",
      "validationStatus": 0,
      "description": "28577 - Fire Damage Emergency Services Report - 06-2014-DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28578 - Water Damage Emergency Services Report - 06-2014-DryBook",
      "type": "Other",
      "formTemplateId": "57b78b9c-d418-40d0-b448-edb18de523fc",
      "validationStatus": 0,
      "description": "28578 - Water Damage Emergency Services Report - 06-2014-DryBook.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ATR State Farm - Authorization to Repair (ATR California)",
      "type": "Required",
      "formTemplateId": "214f4af6-fcee-4209-90e9-8a9eced0ae04",
      "validationStatus": 2,
      "description": "ATR State Farm - Authorization to Repair (ATR California).pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ATP State Farm - California (Auth to Pay)",
      "type": "Required",
      "formTemplateId": "f736aeef-9921-471e-93eb-c5bc381d65d3",
      "validationStatus": 2,
      "description": "ATP State Farm - California (Auth to Pay).pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28501 - California Customer Information Form - Water Damage",
      "type": "Required",
      "formTemplateId": "a81a769c-b63f-4701-956a-dc1fe72e1dc7",
      "validationStatus": 2,
      "description": "28501 - California Customer Information Form - Water Damage.pdf",
      "relatedForm": "21D19102-4C6C-4849-BA88-41CA0A565B9C",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Family SERVPRO  Subrogation Form 7 20 13",
      "type": "Required",
      "formTemplateId": "ae903467-d608-464c-a4c1-0c0e2f8a5269",
      "validationStatus": 2,
      "description": "American Family SERVPRO  Subrogation Form 7 20 13.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Chubb_Direction of Payment - NA-100CH 0808",
      "type": "Required",
      "formTemplateId": "46aa61af-537c-4ec6-8c53-af8cfd0c2791",
      "validationStatus": 2,
      "description": "Chubb_Direction of Payment - NA-100CH 0808.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Encompass COS - Certificate of Satisfaction",
      "type": "Required",
      "formTemplateId": "bef9f4e9-5c8f-4522-8fce-0f3c485dffe4",
      "validationStatus": 2,
      "description": "Encompass COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Allied Insurance - Satisfaction of Repair",
      "type": "Required",
      "formTemplateId": "540597ae-5fe8-48b5-8719-fdc11ed9dbbc",
      "validationStatus": 2,
      "description": "Allied Insurance - Satisfaction of Repair.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Allied Insurance - Authorization to Pay",
      "type": "Required",
      "formTemplateId": "decc2159-c64a-4d30-bc4e-488b2e2bf90a",
      "validationStatus": 2,
      "description": "Allied Insurance - Authorization to Pay.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28501 - Customer Information Form - Water Damage",
      "type": "Required",
      "formTemplateId": "21d19102-4c6c-4849-ba88-41ca0a565b9c",
      "validationStatus": 2,
      "description": "28501 - Customer Information Form - Water Damage.pdf",
      "relatedForm": "A81A769C-B63F-4701-956A-DC1FE72E1DC7",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Family - All States except CO OH WA - Auth to Repair",
      "type": "Required",
      "formTemplateId": "be97999d-fe2f-4a38-9c57-bd1f276cd909",
      "validationStatus": 2,
      "description": "American Family - All States except CO OH WA - Auth to Repair.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Allstate COS - Certificate of Satisfaction",
      "type": "Required",
      "formTemplateId": "e40311b6-d7d1-4993-adf5-81554a6c2161",
      "validationStatus": 2,
      "description": "Allstate COS - Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "American Family - All States except CO OH WA - Auth to Pay",
      "type": "Required",
      "formTemplateId": "7f6c60ce-28c4-420c-9e52-83059a1fddc5",
      "validationStatus": 2,
      "description": "American Family - All States except CO OH WA - Auth to Pay.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Nationwide Agribusiness - Satisfaction of Repair",
      "type": "Required",
      "formTemplateId": "218b0107-c66e-460e-9d48-2858be8b136f",
      "validationStatus": 2,
      "description": "Nationwide Agribusiness - Satisfaction of Repair.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Nationwide Agribusiness - Authorization to Pay",
      "type": "Required",
      "formTemplateId": "0850c271-d03a-4e85-8d97-22cfd5e43162",
      "validationStatus": 2,
      "description": "Nationwide Agribusiness - Authorization to Pay.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Nationwide Satisfaction of Repair and Authorization To Pay - CA",
      "type": "Required",
      "formTemplateId": "e8afb022-792d-47d7-92c7-0ab02ea3a257",
      "validationStatus": 2,
      "description": "Nationwide Satisfaction of Repair and Authorization To Pay - CA.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "State Farm Warranty Form",
      "type": "Required",
      "formTemplateId": "bc2f57d9-487d-4c67-bd74-29eb14342441",
      "validationStatus": 2,
      "description": "State Farm Warranty Form.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28553 - American Bankers Ins. Co. Limitations on Standard Compliance - Water Damage",
      "type": "Required",
      "formTemplateId": "a92312b2-22d9-4ea6-9b23-7e24cd5d2641",
      "validationStatus": 2,
      "description": "28553 - American Bankers Ins. Co. Limitations on Standard Compliance - Water Damage.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28553 -Assurant Limitations on Standard Compliance - Water Damage",
      "type": "Required",
      "formTemplateId": "dce5510a-2451-46e7-9c04-b5234672e5c5",
      "validationStatus": 2,
      "description": "28553 -Assurant Limitations on Standard Compliance - Water Damage.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28000 - Auth to Perform Services and Direction of Payment",
      "type": "Required",
      "formTemplateId": "1137ceb4-017a-4bea-a58d-bc91a527d604",
      "validationStatus": 2,
      "description": "28000 - Auth to Perform Services and Direction of Payment.pdf",
      "relatedForm": "9E7012B0-BDB9-479D-A34D-A44CB83721B2",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28001 - Authorization to Perform Services",
      "type": "Required",
      "formTemplateId": "9e7012b0-bdb9-479d-a34d-a44cb83721b2",
      "validationStatus": 2,
      "description": "28001 - Authorization to Perform Services.pdf",
      "relatedForm": "1137CEB4-017A-4BEA-A58D-BC91A527D604",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28509 - Customer Equipment Responsibility Form-11-10",
      "type": "Required",
      "formTemplateId": "9491a0da-cf3a-4b25-a79c-1d2687cf01c3",
      "validationStatus": 2,
      "description": "28509 - Customer Equipment Responsibility Form-11-10.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "28531 - Certificate of Satisfaction - Job Completion_11-2010",
      "type": "Required",
      "formTemplateId": "368ca1ec-c7f8-4d05-8a90-a530a55c584c",
      "validationStatus": 2,
      "description": "28531 - Certificate of Satisfaction - Job Completion_11-2010.pdf",
      "isClientRequirement": false,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Stillwater Choice",
      "type": "Required",
      "formTemplateId": "182d8d9b-0d92-43a3-a082-5887f37f19c2",
      "validationStatus": 2,
      "description": "Stillwater Choice.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "ABIC National Flood Insurance Program Limitations",
      "type": "Required",
      "formTemplateId": "a30f9291-f797-4f9c-b2a4-cd10ed9ddecf",
      "validationStatus": 2,
      "description": "ABIC National Flood Insurance Program Limitations.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Assurant National Flood Insurance Program Limitations",
      "type": "Required",
      "formTemplateId": "b15f2363-2a75-4db7-bc27-2dfce07c3746",
      "validationStatus": 2,
      "description": "Assurant National Flood Insurance Program Limitations.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "Allstate COS - Commercial Certificate of Satisfaction",
      "type": "Standard",
      "formTemplateId": "e4b8a753-b511-4726-9b65-2e5fcb7234cb",
      "validationStatus": 0,
      "description": "Allstate COS - Commercial Certificate of Satisfaction.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "AmFam Commercial - All States except CO OH WA - Auth to Pay",
      "type": "Standard",
      "formTemplateId": "6175717f-45fb-45f1-a9d4-76d51c277f9f",
      "validationStatus": 0,
      "description": "AmFam Commercial - All States except CO OH WA - Auth to Pay.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "AmFam Commercial - All States except CO OH WA - Auth to Repair",
      "type": "Standard",
      "formTemplateId": "a6e3de9b-de30-4185-87b6-436ed7d26ec4",
      "validationStatus": 0,
      "description": "AmFam Commercial - All States except CO OH WA - Auth to Repair.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    },
    {
      "name": "AmFam Commercial SERVPRO  Subrogation Form 7 20 13",
      "type": "Standard",
      "formTemplateId": "fd192ec9-4441-482c-9bb9-2b501b41bf73",
      "validationStatus": 0,
      "description": "AmFam Commercial SERVPRO  Subrogation Form 7 20 13.pdf",
      "isClientRequirement": true,
      "isAvailableInFirstNotice": false,
      "isActive": true,
      "requiredForInitialUpload": false
    }
  ],
  "clientNotificationRequirements": [
    {
      "ruleId": 45,
      "validationStatus": 0,
      "name": "Drying Days",
      "validationEffect": "Require",
      "description": "Drying time is longer than 3 days based on when the initial drying equipment was placed and readings recorded. The client needs to be informed of this. Document your contact with the client (date, time, who you spoke to, what was discussed, and if any approvals were given).",
      "notification": "Client contact required. Drying time 3 days or over. ",
      "bypassAllowed": true,
      "askedPerVisit": false,
      "requiredForInitialUpload": false,
      "clientNotifications": [],
      "ruleParameters": [
        {
          "Key": "MaxDryingDays",
          "Value": "3"
        }
      ]
    }
  ],
  "miscellaneousRequirements": [
    {
      "ruleId": 126,
      "validationStatus": 1,
      "name": "MICA Upload",
      "validationEffect": "Require",
      "description": "Job and drying data must be transmitted to or entered in MICA.",
      "notification": "Job and drying data must be transmitted to or entered in MICA.",
      "bypassAllowed": true,
      "askedPerVisit": false,
      "requiredForInitialUpload": false,
      "clientNotifications": [],
      "ruleParameters": []
    },
    {
      "ruleId": 120,
      "validationStatus": 1,
      "name": "Xactimate ESX",
      "validationEffect": "Require",
      "description": "An estimate (.esx) must be attached.",
      "notification": "An estimate (.esx) must be attached.",
      "bypassAllowed": false,
      "askedPerVisit": false,
      "requiredForInitialUpload": false,
      "clientNotifications": [],
      "ruleParameters": []
    }
  ],
  "jobInfo": {
    "activeInAudit": false,
    "evergreenJobId": "57aabe12-e264-4bdc-b311-2bc886d69675",
    "country": "US",
    "isStormJob": false,
    "insuranceClientId": 1,
    "validatedOn": "2020-04-15T03:42:57.2992004Z",
    "memoizedOn": "2020-04-15T03:42:56.8386469Z"
  }
}