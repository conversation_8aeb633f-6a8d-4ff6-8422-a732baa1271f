﻿using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using System.Collections.Generic;
using System.Linq;
using Servpro.Franchise.JobService.Common;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.Mapping
{
    public static partial class JobMergeMappers
    {
        public static JobMergeEvent.JobDto MapMarketingJobDto(Job src, JobMergeEvent.JobDto dest)
            => src == null
            ? dest
            : MapJob(src, dest);

        private static JobMergeEvent.JobDto MapJob(Job src, JobMergeEvent.JobDto dest)
        {
            dest.ProjectNumber = src.ProjectNumber;
            dest.JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)src.JobProgress;
            dest.DateOfLoss = src.DateOfLoss;
            dest.Customer = src.Customer != null ? MapContact(src.Customer, src, new JobMergeEvent.ContactDto()) : null;
            dest.PropertyTypeId = src.PropertyTypeId;
            dest.LossTypeId = src.LossTypeId;
            dest.FranchiseSetId = src.FranchiseSetId != Guid.Empty ? src.FranchiseSetId : Guid.Empty;
            var srcReferral = src.JobContacts?.Where(x => x.JobContactTypeId == JobContactTypes.ReferralSource).FirstOrDefault();
            dest.Referral = srcReferral != null && srcReferral.Contact != null ? MapContact(srcReferral, src.FranchiseSetId, new JobMergeEvent.ContactDto()) : null;
            dest.TotalRevenue = src.TotalRevenue;
            dest.TotalCollections = src.TotalAmountDue;
            dest.DateReceived = src.GetDate(JobDateTypes.ReceivedDate);
            dest.CauseOfLossId = src.CauseOfLossId;
            dest.StructureTypeId = src.StructureTypeId;
            dest.FacilityTypeId = src.FacilityTypeId;
            dest.FranchiseId = src.FranchiseId;
            dest.InsuranceCarrierId = src.InsuranceCarrierId;
            dest.InsuranceClaimNumber = src.InsuranceClaimNumber;
            dest.ProjectManagerId = src.ProjectManagerId;
            dest.JobBusinesses = MapJobBusinesses(src.JobBusinesses, src);
            dest.JobContacts = MapJobContacts(src.JobContacts, src);

            return dest;
        }
        private static JobMergeEvent.ContactDto MapContact(Contact src, Job srcJob, JobMergeEvent.ContactDto dest)
        {
            dest.Id = src.Id;
            dest.FirstName = src.FirstName;
            dest.LastName = src.LastName;
            dest.BusinessId = src.BusinessId;
            dest.BusinessName = src.Business?.Name;
            dest.FranchiseSetId = srcJob.FranchiseSetId;

            return dest;
        }

        private static JobMergeEvent.ContactDto MapContact(JobContactMap src, Guid? franchiseSetId, JobMergeEvent.ContactDto dest)
        {
            dest.Id = src.Contact.Id;
            dest.FirstName = src.Contact.FirstName;
            dest.LastName = src.Contact.LastName;
            dest.BusinessName = src.Contact.Business?.Name;
            dest.BusinessId = src.Contact.Business?.Id;
            dest.FranchiseSetId = franchiseSetId != null && franchiseSetId != Guid.Empty ? franchiseSetId : Guid.Empty;

            return dest;
        }

        private static ICollection<JobMergeEvent.JobBusinessDto> MapJobBusinesses(ICollection<JobBusinessMap> jobBusinesses, Job job)
        {
            var jobBusinessesDto = new List<JobMergeEvent.JobBusinessDto>();

            if (jobBusinesses == null || !jobBusinesses.Any())
                return jobBusinessesDto;

            foreach (var jobBusiness in jobBusinesses)
                jobBusinessesDto.Add(MapJobBusiness(jobBusiness, job));

            return jobBusinessesDto;
        }

        private static JobMergeEvent.JobBusinessDto MapJobBusiness(JobBusinessMap jobBusiness, Job job)
        {
            var jobBusinessDto = new JobMergeEvent.JobBusinessDto
            {
                JobId = jobBusiness.JobId,
                BusinessId = jobBusiness.BusinessId,
                IsPrimary = jobBusiness.IsPrimary,
                JobBusinessTypeId = jobBusiness.JobBusinessTypeId,
                Business = MapBusiness(jobBusiness.Business, job),
            };

            return jobBusinessDto;
        }

        private static JobMergeEvent.BusinessDto MapBusiness(Business business, Job job)
        {
            if (business == null)
                return null;

            var businessDto = new JobMergeEvent.BusinessDto
            {
                Address = MapAddress(business.Address),
                BusinessTypeId = business.BusinessTypeId,
                Contacts = MapContacts(business.Contacts, job),
                EmailAddress = MapEmail(business.EmailAddress),
                FranchiseSetId = business.FranchiseSetId,
                HasErnetContract = business.HasErnetContract,
                IsDeleted = business.IsDeleted,
                IsOther = business.IsOther,
                IsSystem = business.IsSystem,
                Name = business.Name,
                PhoneNumber = MapPhone(business.PhoneNumber),
                PreferredName = business.PreferredName,
                RecordSourceId = business.RecordSourceId,
            };

            return businessDto;
        }

        private static JobMergeEvent.AddressDto MapAddress(Address address)
        {
            if (address == null)
                return null;

            var addressDto = new JobMergeEvent.AddressDto
            {
                Address1 = address.Address1,
                Address2 = address.Address2,
                AddressType = (Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums.AddressType)address.AddressType,
                City = address.City,
                Latitude = address.Latitude,
                Logitude = address.Logitude,
                PostalCode = address.PostalCode,
                State = MapState(address.State)
            };

            return addressDto;
        }

        private static JobMergeEvent.StateDto MapState(State state)
        {
            if (state == null)
                return null;

            var stateDto = new JobMergeEvent.StateDto
            {
                CountryId = state.CountryId,
                CountryName = state.CountryName,
                CountyName = state.CountyName,
                StateAbbreviation = state.StateAbbreviation,
                StateId = state.StateId,
                StateName = state.StateName,
            };

            return stateDto;
        }

        private static JobMergeEvent.EmailDto MapEmail(Email email)
        {
            if (email == null)
                return null;

            var emailDto = new JobMergeEvent.EmailDto
            {
                Address = email.Address,
                EmailAddressTypeId = email.EmailAddressTypeId,
                Id = email.Id,
                IsUsedForNotifications = email.IsUsedForNotifications,
                SequenceNumber = email.SequenceNumber,
            };

            return emailDto;
        }

        private static JobMergeEvent.PhoneDto MapPhone(Phone phone)
        {
            if (phone == null)
                return null;

            var phoneDto = new JobMergeEvent.PhoneDto
            {
                Id = phone.Id,
                PhoneNumber = phone.PhoneNumber,
                PhoneExtension = phone.PhoneExtension,
                PhoneType = (Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums.PhoneType)phone.PhoneType,
            };

            return phoneDto;
        }

        private static ICollection<JobMergeEvent.ContactDto> MapContacts(ICollection<Contact> contacts, Job job)
        {
            var contactsDto = new List<JobMergeEvent.ContactDto>();

            if (contacts == null || !contacts.Any())
                return contactsDto;

            foreach (var contact in contacts)
                contactsDto.Add(MapContact(contact, job, new JobMergeEvent.ContactDto()));

            return contactsDto;
        }

        private static ICollection<JobMergeEvent.JobContactDto> MapJobContacts(ICollection<JobContactMap> jobContacts, Job job)
        {
            var jobContactsDto = new List<JobMergeEvent.JobContactDto>();

            if (jobContacts == null || !jobContacts.Any())
                return jobContactsDto;

            foreach (var jobContact in jobContacts)
                jobContactsDto.Add(MapJobContact(jobContact, job));

            return jobContactsDto;
        }

        private static JobMergeEvent.JobContactDto MapJobContact(JobContactMap jobContact, Job job)
        {
            if (jobContact == null)
                return null;

            var jobContactDto = new JobMergeEvent.JobContactDto
            {
                Contact = jobContact.Contact != null ? MapContact(jobContact.Contact, job, new JobMergeEvent.ContactDto()) : null,
                Id = jobContact.Id,
                IsBusinessContact = jobContact.IsBusinessContact,
                JobContactType = jobContact.JobContactTypeId,
                JobId = jobContact.JobId,
                TitleId = jobContact.TitleId,
            };

            return jobContactDto;
        }
    }
}
