using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.JobService.Models.RawSqlModels;
using Servpro.Franchise.JobService.Models.Session;
using Servpro.FranchiseSystems.Framework.Messaging;

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Google.Protobuf.Reflection;
using Microsoft.EntityFrameworkCore.Metadata;
using Servpro.Franchise.JobService.Infrastructure.WipMaterializedView;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Task = Servpro.Franchise.JobService.Models.Drybook.Task;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient.X.XDevAPI.Common;
using Newtonsoft.Json;
using Pomelo.EntityFrameworkCore.MySql.Json.Newtonsoft.Storage.ValueConversion.Internal;
using Servpro.Franchise.JobService.Models.Customization.Dtos;
using RelationalPropertyExtensions = Microsoft.EntityFrameworkCore.RelationalPropertyExtensions;
using Servpro.Franchise.JobService.Common.ExtensionMethods;

namespace Servpro.Franchise.JobService.Data
{
    public class JobDataContext : DbContext, IMessagingDbContext
    {
        public JobDataContext(DbContextOptions<JobDataContext> options)
            : base(options)
        {
        }
        protected JobDataContext(DbContextOptions options)
            : base(options)
        {
        }

        public DbSet<Job> Jobs { get; set; }
        public DbSet<JobLock> JobLock { get; set; }
        public DbSet<JobProgressHistory> JobProgressHistory { get; set; }
        public DbSet<WipRecord> WipRecords { get; set; }
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<OutboxMessage> OutboxMessages { get; set; }
        public DbSet<JobContactMap> JobContactMap { get; set; }
        public DbSet<JournalNote> JournalNote { get; set; }
        public DbSet<InboxMessage> InboxMessages { get; set; }
        public DbSet<MediaMetadata> MediaMetadata { get; set; }
        public DbSet<Task> Tasks { get; set; }
        public DbSet<Room> Rooms { get; set; }
        public DbSet<Zone> Zones { get; set; }
        public DbSet<JobArea> JobAreas { get; set; }
        public DbSet<JobSketch> JobSketch { get; set; }       
        public DbSet<EquipmentPlacement> EquipmentPlacements { get; set; }
        public DbSet<RoomFlooringTypeAffected> RoomFlooringTypesAffected { get; set; }
        public DbSet<Equipment> Equipments { get; set; }
        public DbSet<EquipmentModel> EquipmentModels { get; set; }
        public DbSet<EquipmentType> EquipmentTypes { get; set; }
        public DbSet<MenuItem> MenuItem { get; set; }
        public DbSet<ServproWipColumnCustomization> ServproWipColumnCustomization { get; set; }
        public DbSet<FranchiseSetWipColumnCustomization> FranchiseSetWipColumnCustomization { get; set; }
        public DbSet<EmployeeWipColumnCustomization> EmployeeWipColumnCustomization { get; set; }
        public DbSet<MyCustomViews> MyCustomViews { get; set; }
        public DbSet<Business> Businesses { get; set; }
        public DbSet<JobBusinessMap> JobBusinessMaps { get; set; }
        public DbSet<JobVisit> JobVisit { get; set; }
        public DbSet<JobVisitTriStateAnswer> JobVisitTriStateAnswers { get; set; }
        public DbSet<ZoneReading> ZoneReadings { get; set; }
        public DbSet<EquipmentPlacementReading> EquipmentPlacementReadings { get; set; }
        public DbSet<JobMaterial> JobMaterials { get; set; }
        public DbSet<JobAreaMaterial> JobAreaMaterials { get; set; }
        public DbSet<JobInvoice> JobInvoices { get; set; }
        public DbSet<JobAreaMaterialReading> JobAreaMaterialReadings { get; set; }
        public DbSet<FormTemplate> FormTemplates { get; set; }
        public DbSet<MaintenanceAlerts> MaintenanceAlerts { get; set; }
        public DbSet<InsuranceClient> InsuranceClients { get; set; }
        public DbSet<JobUploadLock> JobUploadLocks { get; set; }
        public DbSet<LineItem> LineItems { get; set; }
        public DbSet<LineItemNote> LineItemNotes { get; set; }
        public DbSet<JobTriStateAnswer> JobTriStateAnswers { get; set; }
        public DbSet<MergeCandidate> MergeCandidates { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<StormEvent> StormEvents { get; set; }
        public DbSet<Storm> Storms { get; set; }
        public DbSet<StormProductionFranchise> StormProductionFranchises { get; set; }
        public DbSet<VersionData> VersionData { get; set; }

        public DbSet<FirstNoticeActivity> FirstNoticeActivity { get; set; }
        public DbSet<FirstNoticeUserActivityTracker> FirstNoticeUserActivityTrackers { get; set; }
        public DbSet<MobileData> MobileData { get; set; }
        public DbSet<JobActionLocation> JobActionLocations { get; set; }
        public DbSet<MobileLog> MobileLog { get; set; }
        public DbSet<LeadRollbackError> LeadRollbackErrors { get; set; }
        public DbSet<JobConfigExtension> JobConfigExtensions { get; set; }
        public DbSet<ExternalMarketingCall> ExternalMarketingCalls { get; set; }
        public DbSet<JobAdditionalInfo> JobAdditionalInfo { get; set; }
        public DbSet<JobMergeHistory> JobMergeHistory { get; set; }
        public DbSet<JobCustomAttribute> JobCustomAttributes { get; set; }

        private readonly ImmutableHashSet<Type> WipViewDependentEntities = new HashSet<Type>
        {
            typeof(Job),
            typeof(JobContactMap),
            typeof(Business),
            typeof(JobBusinessMap),
            typeof(Contact),
            typeof(JournalNote),
            typeof(EquipmentPlacement),
            typeof(MediaMetadata),
            typeof(Task),
            typeof(JobLock),
            typeof(JobArea)
        }.ToImmutableHashSet();

        public bool UpdateWipRecords { get; set; } = true;

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var entries = UpdateWipRecords
                ? GetEntriesRelevantToWip()
                : Enumerable.Empty<WipRecordEntry>().ToList();

            var result =  await base.SaveChangesAsync(cancellationToken);

            if (entries.Any())
                await WipMaterializedViewService.UpdateWipRecordsAsync(entries);

            return result;
        }
        
        /// <summary>
        /// Calls SaveChangesAsync(...) independently of WIPMaterializedService, this should only be used in cases where
        /// the updates that need to be done on WipRecords have already been done before hand.
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<int> SaveChangesIndependentlyAsync(CancellationToken cancellationToken = default)
            => await base.SaveChangesAsync(cancellationToken);

        /// <summary>
        /// This method will get all property changes (only 1 level deep) for any entity T, all names and values.
        /// However, the S object type must have matching property names as entity T.  This is to help with mapping
        /// to a different object other than the entity T.  The reason why this was added was to help ONLY get changes
        /// from a specific object that has changed in EF.  However, that same object can map to some other dto and its
        /// better to only get the exact changes instead of every property.
        /// 
        /// Explicit mappings were also added to allow for property names that didnt match between objects T and S.  
        /// </summary>
        public Dictionary<string, object> GetChanges<T, S>(T entity, Dictionary<string,string> explicitMappings = null, List<string> exceptions = null)
        {
            var entityPropertyChangeList = new Dictionary<string, object>();
            var mappings = new Dictionary<string, string>();

            if (explicitMappings != null)
                foreach (var key in explicitMappings.Keys)
                    mappings.Add(key, explicitMappings[key]);

            var matchingPropertyNames = GetPropertyNames<S>();
            if (matchingPropertyNames != null)
                foreach(var key in matchingPropertyNames)
                    if(!mappings.ContainsKey(key))
                      mappings.Add(key, key);

            foreach (var propertyEntry in this.Entry(entity).Properties)
            {
                var propertyName = propertyEntry.Metadata.Name;
                if (exceptions != null && exceptions.Contains(propertyName)) continue;

                if (mappings.ContainsKey(propertyName))
                {
                    if (propertyEntry.Metadata.ClrType == typeof(DateTime))
                    {
                        if (IsDatePropertyModified(propertyEntry))
                        {
                            var modifiedProperty = mappings[propertyName];
                            entityPropertyChangeList.Add(modifiedProperty, propertyEntry.CurrentValue);
                        }
                    }
                    else if(propertyEntry.IsModified)
                    {
                        var modifiedProperty = mappings[propertyName];
                        entityPropertyChangeList.Add(modifiedProperty, propertyEntry.CurrentValue);
                    }
                }
            }
            return entityPropertyChangeList;
        }

        private bool IsDatePropertyModified(PropertyEntry entry)
        {
            if (entry.Metadata.ClrType == typeof(DateTime)) 
            {
                //Some datetimes can have a different level of percision
                // this ensures that comparisons are only to the third decimal
                var currentValue = ((DateTime)entry.CurrentValue).ToString("MM/dd/yyyy HH:mm:ss.fff");
                var originalValue = ((DateTime)entry.OriginalValue).ToString("MM/dd/yyyy HH:mm:ss.fff");
                if (currentValue != originalValue) return true;
            }
            return false;
        }

        private List<string> GetPropertyNames<T>()
        {
            var propertyNames = typeof(T).GetProperties().Select(x => x.Name).ToList();
            return propertyNames;
        }

        public override int SaveChanges()
        {
            var entries = UpdateWipRecords
                ? GetEntriesRelevantToWip()
                : Enumerable.Empty<WipRecordEntry>().ToList();

            int result = base.SaveChanges();

            if (entries.Any())
                WipMaterializedViewService.UpdateWipRecordsAsync(entries).Wait();

            return result;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

            modelBuilder.ApplyConfiguration(new Room.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Zone.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobArea.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new EquipmentPlacement.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new RoomFlooringTypeAffected.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Job.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new WipRecord.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobLock.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Contact.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobContactMap.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JournalNote.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MediaMetadata.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Task.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobTriStateAnswer.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobVisit.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobVisitTriStateAnswer.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobSketch.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Equipment.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new EquipmentModel.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new EquipmentType.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MenuItem.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new ServproWipColumnCustomization.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new FranchiseSetWipColumnCustomization.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new EmployeeWipColumnCustomization.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MyCustomViews.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Business.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobBusinessMap.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new ZoneReading.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new EquipmentPlacementReading.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobMaterial.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobAreaMaterial.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobAreaMaterialReading.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new FormTemplate.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobProgressHistory.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new InsuranceClient.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobInvoice.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new LineItem.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new LineItemNote.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MergeCandidate.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new UserSession.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new StormEvent.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new Storm.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new VersionData.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new FirstNoticeActivity.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new FirstNoticeUserActivityTracker.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MobileData.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobActionLocation.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MobileLog.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new LeadRollbackError.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobUploadLock.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new MaintenanceAlerts.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobConfigExtension.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new ExternalMarketingCall.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobAdditionalInfo.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobMergeHistory.EntityConfiguration());
            modelBuilder.ApplyConfiguration(new JobCustomAttribute.EntityConfiguration());
            SpecifyUtcKindOnAllDateTimes(modelBuilder);
            SetDefaultStringMaxLength(modelBuilder);
            SetDefaultDecimalPrecision(modelBuilder);

            ApplyInboxMessagesConfiguration(modelBuilder);
            ApplyOutboxMessagesConfiguration(modelBuilder);

            if (bool.TryParse(Environment.GetEnvironmentVariable("IS_UNIT_TESTING"), out bool isUnitTesting))
            {
                if (isUnitTesting)
                {
                    Debug.WriteLine("Json Columns");
                    modelBuilder.Model.GetEntityTypes()
                        .Where(type => type.GetProperties().Any(x => x.GetColumnType() == "json"))
                        .ToList()
                        .ForEach(entityType =>
                        {
                            Debug.WriteLine(entityType.ClrType);
                            entityType.GetProperties().Where(x => x.GetColumnType() == "json").ToList()
                                .ForEach(property =>
                                {
                                    Debug.WriteLine(property.Name);
                                });
                                        
                        });
                    modelBuilder.Entity<Business>().Property(x => x.Address).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<Address>());
                    modelBuilder.Entity<Business>().Property(x => x.EmailAddress).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<Email>());
                    modelBuilder.Entity<Business>().Property(x => x.PhoneNumber).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<Phone>());
                    modelBuilder.Entity<Contact>().Property(x => x.Address).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<Address>());
                    modelBuilder.Entity<Contact>().Property(x => x.PhoneNumbers).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<Phone>>());
                    modelBuilder.Entity<Zone>().Property(x => x.ZoneHazordousMaterialTypes).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<List<HazordousMaterialType>>());
                    modelBuilder.Entity<EmployeeWipColumnCustomization>().Property(x => x.Data).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<GridColumnDto>>());
                    modelBuilder.Entity<MyCustomViews>().Property(x => x.Data).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<MyCustomViewData>());
                    modelBuilder.Entity<FranchiseSetWipColumnCustomization>().Property(x => x.Data).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<GridColumnDto>>());
                    modelBuilder.Entity<ServproWipColumnCustomization>().Property(x => x.Data).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<GridColumnDto>>());
                    modelBuilder.Entity<Job>().Property(x => x.DropDownQuestionResponses).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<Dictionary<Guid,Guid>>());
                    modelBuilder.Entity<Job>().Property(x => x.JobDates).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<JobDate>>());
                    modelBuilder.Entity<Job>().Property(x => x.LossAddress).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<Address>());
                    modelBuilder.Entity<JournalNote>().Property(x => x.Rules).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<List<JournalNote.BreRule>>());
                    modelBuilder.Entity<MenuItem>().Property(x => x.Items).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<MenuItem>>());
                    modelBuilder.Entity<Room>().Property(x => x.MissingSpaces).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<List<MissingSpace>>());
                    modelBuilder.Entity<Room>().Property(x => x.OffsetSpaces).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<List<OffsetSpace>>());
                    modelBuilder.Entity<JobMergeHistory>().Property(x => x.MergeOptions).HasConversion(new MySqlJsonNewtonsoftPocoValueConverter<ICollection<JobMergeOption>>());
                }

            }
            else
            {
                modelBuilder.UseCollation("utf8mb4_general_ci");
                modelBuilder.UseGuidCollation("latin1_swedish_ci");
            }


        }

        private void ApplyInboxMessagesConfiguration(ModelBuilder modelBuilder)
        {

            modelBuilder.Entity<InboxMessage>()
                        .HasKey(j => j.Id).HasName("PRIMARY");

            //This is to prevent EF Core from converting Message column to something
            // other than "mediumtext".  Sometimes it does this.
            modelBuilder.Entity<InboxMessage>()
                        .Property(j => j.Message)
                        .HasColumnType("mediumtext")
                        .HasMaxLength(16000000);

            modelBuilder.Entity<InboxMessage>()
                .HasIndex(j => j.CreatedDate);
            modelBuilder.Entity<InboxMessage>()
                .HasIndex(j => new { j.CorrelationId, j.MessageType });

            // HasPrefixLength is used here because longtext columns
            // cant be indexed unless a prefix length is given.  This means
            // only the first part of the string is actually indexed BUT its better than 
            // not having an index at all.  Also note that 0 is used for MessageType because
            // it indicates the full length should be used (its not a longtext column)
            modelBuilder.Entity<InboxMessage>()
                .HasIndex(j => j.Message)
                .HasPrefixLength(255);
            modelBuilder.Entity<InboxMessage>()
                 .HasIndex(j => new { j.MessageType, j.Message })
                 .HasPrefixLength(0, 255);

            modelBuilder.Entity<InboxMessage>()
                .HasIndex(j => j.MessageId);
        }

        private void ApplyOutboxMessagesConfiguration(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OutboxMessage>()
                .HasKey(j => j.Id).HasName("PRIMARY");

            //This is to prevent EF Core from converting Message column to something
            // other than "medium".  Sometimes it does this.
            modelBuilder.Entity<OutboxMessage>()
               .Property(j => j.Message)
               .HasColumnType("mediumtext")
               .HasMaxLength(16000000);

            modelBuilder.Entity<OutboxMessage>()
                .HasIndex(j => new { j.Id, j.Version });
            modelBuilder.Entity<OutboxMessage>()
                .HasIndex(j => new { j.Dispatched, j.LockedAt, j.DispatchAttempts, j.IsDead });
        }

        private List<WipRecordEntry> GetEntriesRelevantToWip()
        {
            HashSet<EntityEntry> entries = ChangeTracker?
                .Entries()
                .Where(x => x.State != EntityState.Unchanged &&
                          x.State != EntityState.Detached &&
                          WipViewDependentEntities.Contains(x.Entity.GetType()))
                .ToHashSet();

            var entryTuples =
                (entries?.Select(x => new WipRecordEntry { Entity = x.Entity, 
                                                           OriginalValues = x.OriginalValues.Clone(),
                                                           State = x.State,
                                                           Changes = x.Properties.Where(x=>x.IsModified == true).ToList()}))?.ToList()
                 ?? Enumerable.Empty<WipRecordEntry>().ToList();

            return entryTuples;
        }

        protected void SpecifyUtcKindOnAllDateTimes(ModelBuilder modelBuilder)
        {
            var dateTimeConverter = new ValueConverter<DateTime, DateTime>(
            v => v.ToUniversalTime(), v => DateTime.SpecifyKind(v, DateTimeKind.Utc));

            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                        property.SetValueConverter(dateTimeConverter);
                }
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "EF1001:Internal EF Core API usage.", Justification = "This is a concise solution to a simple problem. If broken there will be a new method. We can even manually set max length on all strings.)")]
        protected void SetDefaultStringMaxLength(ModelBuilder modelBuilder)
        {
            foreach (var property in modelBuilder.Model.GetEntityTypes()
                .SelectMany(t => t.GetProperties())
                .Where(p => p.ClrType == typeof(string)
                            && !p.IsKey()
                            && p.GetMaxLength() == null
                            && !p.IsConcurrencyToken))
            {
                //Using Convention so we don't override specified max length.
                //Using 240 because at 256 MySQL Begins using 2 byte prefixes.
                //At 255 length, casing and certain characters can require over 256.
                //When in-memory internal temporary tables are used, fixed-length row format is used.
                //Varchar and varbinary are padded to the max length and char/binary is used.
                //Specify your column lengths.
                property.SetMaxLength(240);
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "EF1001:Internal EF Core API usage.", Justification = "This is a concise solution to a simple problem. If broken there will be a new method. We can even manually column type on all decimals.)")]
        protected void SetDefaultDecimalPrecision(ModelBuilder modelBuilder)
        {
            foreach (var property in modelBuilder.Model.GetEntityTypes()
                .SelectMany(t => t.GetProperties())
                .Where(p => (p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?))
                            && !p.IsKey()
                            && p.GetColumnType() == null
                            && !p.IsConcurrencyToken))
            {
                //Resources:
                //https://rietta.com/blog/best-data-types-for-currencymoney-in/
                //Checking that GetColumnType() is null so we don't override specified decimal precision.
                //Using 19,4 because GAAP compliance requires (13,4) for a max of 999,999,999.9999
                //19,4 gives us more room to accomodate future requirements and is much better than the
                //Pomelo default of (65,30) - MySQL's max allowable range. Which breaks AWS Glue's Spark precision limit of 38.
                property.SetPrecision(19);
                property.SetScale(4);
            }
        }

        public override void Dispose()
        {
            UpdateWipRecords = true;
            base.Dispose();
        }

        public override ValueTask DisposeAsync()
        {
            UpdateWipRecords = true;
            return base.DisposeAsync();
        }
    }
}