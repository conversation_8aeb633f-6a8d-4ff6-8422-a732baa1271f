﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Infrastructure.ClientRequirementsService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{


    public class SatisfyAtpRequirement
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public DateTime Date { get; set; }
            public ICollection<Guid?> FormTemplateIds { get; set; }
            public string ModifiedBy { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(c => c.JobId).NotEmpty();
                RuleFor(c => c.Date).NotNull();
            }
        }

        public class Dto
        {
            public AtpResult Result { get; set; } = 0;
        }

        public enum AtpResult
        {
            Inactive = 0,
            Complete = 1,
            Active = 2
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _context;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IClientRequirementsService _crsService;
            private readonly ILogger<Handler> _logger;

            public Handler(
                JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IClientRequirementsService crsService, 
                ILogger<Handler> logger)
            {
                _context = context;
                _sessionIdAccessor = sessionIdAccessor;
                _crsService = crsService;
                _logger = logger;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("SatisfyAtpRequirement: Validating Satisfy ATP Requirements for Job {jobId}, request {@request}", request.JobId, request);
                var response = new Dto();
                var job = await GetJobAsync(request.JobId, cancellationToken);
                if (job == null)
                {
                    _logger.LogInformation("SatisfyAtpRequirement: The current job: {jobId} is not found on the context", request.JobId);
                    return response;
                }

                response.Result = await DoConditionsApplyAsync(job, request.FormTemplateIds, cancellationToken);

                if (response.Result != AtpResult.Active)
                {
                    _logger.LogInformation("SatisfyAtpRequirement: The current job {jobId} status is {status}", request.JobId, response.Result);
                    return response;
                }
                    
                await PersistChangesAsync(request, job, cancellationToken);

                return response;
            }

            private async Task<AtpResult> DoConditionsApplyAsync(Job job, IEnumerable<Guid?> formTemplateIdentifiers, CancellationToken cancellationToken)
            {
                var workAuthDate = job.GetDate(JobDateTypes.WorkAuthorizationSigned);
                if (workAuthDate.HasValue)
                {
                    _logger.LogInformation("SatisfyAtpRequirement: The current job {jobId} has been already sign work authorize on {date}", job.Id, workAuthDate);
                    return AtpResult.Inactive;
                }

                var formTemplatesLookup = await GetFormTemplatesLookupAsync(formTemplateIdentifiers, cancellationToken);
                if (!formTemplatesLookup.Any())
                {
                    foreach (var item in formTemplatesLookup)
                    {
                        _logger.LogInformation("SatisfyAtpRequirement: The current job {jobId} does not have any valid template {@item}}", job.Id, item);
                    }
                   
                    return AtpResult.Inactive;
                }
                
                var validationResponse = await GetValidationResponse(job, cancellationToken);
                var forms = validationResponse?.Result?.Forms;
                if (forms == null || !forms.Any())
                {
                    _logger
                        .LogInformation("SatisfyAtpRequirement: For job {jobId}, there are no forms on the validation response.", job.Id);
                    return AtpResult.Complete;
                }
                
                var formsToValidate = forms.Where(x => formTemplatesLookup.ContainsKey(x.FormTemplateId)).ToList();
                var isValidForm = formsToValidate.Any(x => x.ValidationStatus == 2);

                if (isValidForm) return AtpResult.Active;
                
                foreach (var item in forms)
                {
                    _logger
                        .LogInformation("SatisfyAtpRequirement: For job {jobId}, the current formTemplate {templateId} validation status is equal to {validationStatus}."
                           , job.Id , item.FormTemplateId, item.ValidationStatus);
                }

                return AtpResult.Complete;

            }

            private async Task<CrsValidationResponse> GetValidationResponse(Job job, CancellationToken cancellationToken)
            {
                var requirementRequest = await _crsService.CreateJobRequirementsRequestDto(job, cancellationToken);
                var validationResponse = await _crsService.GetJobRequirementsAsync(requirementRequest, cancellationToken);
                return validationResponse;
            }

            private async Task<Dictionary<Guid, FormTemplate>> GetFormTemplatesLookupAsync(IEnumerable<Guid?> formTemplateIdentifiers, CancellationToken cancellationToken)
            {
                var formTemplatesIds = formTemplateIdentifiers
                    .Where(x => x.HasValue)
                    .DistinctBy(x => x.Value)
                    .ToArray();

                var formTemplates = await _context.FormTemplates
                    .Where(x => formTemplatesIds.Contains(x.Id) && x.IsActive && x.Approved && x.IsAuthorizationForm)
                    .ToListAsync(cancellationToken: cancellationToken);

                return formTemplates.ToDictionary(x => x.Id);
            }

            private async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .Include(j => j.MediaMetadata)
                    .Include(j => j.JobAreas)
                        .ThenInclude(a => a.JobAreaMaterials)
                    .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken: cancellationToken);
                return job;
            }

            private async Task PersistChangesAsync(Command request, Job job, CancellationToken cancellationToken)
            {
                _logger.LogInformation("SatisfyAtpRequirement: Persisting changes for job {jobId}, request {@request} ", request.JobId, request);
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                //Update Job Progress and date
                var jobProgress = GetUpdatedJobProgress(job.JobProgress);
                _logger.LogInformation("SatisfyAtpRequirement: Updating jobProgress for job {jobId}, from {previousJobProgress} to {newJobProgress} ", job.Id, job.JobProgress, jobProgress);
                job.JobProgress = jobProgress;

                //JobDate to be updated should go here
                job.SetOrUpdateDate(JobDateTypes.WorkAuthorizationSigned, request.Date);

                //Post Event JobDateUpdated
                var outboxJobDateCreatedEvent = GenerateJobDateCreatedEvent(request, correlationId);
                _context.OutboxMessages.Add(outboxJobDateCreatedEvent);

                //Post Event JobUpdateCreated
                var outboxJoUpdateCreatedEvent = GenerateJobProgressUpdatedEvent(request, job.JobProgress, correlationId);
                _context.OutboxMessages.Add(outboxJoUpdateCreatedEvent);
                await _context.SaveChangesAsync(cancellationToken);
            }

           private static JobProgress GetUpdatedJobProgress(JobProgress jobProgress)
            {
                var validJobProgress = new List<JobProgress>
                {
                    JobProgress.IntakeResearch,
                    JobProgress.ContactSchedule,
                    JobProgress.InitialSiteInspection,
                    JobProgress.CustomerConsultation
                };
                return validJobProgress.Any(p=> p == jobProgress) ? JobProgress.InitialServices : jobProgress;
            }

            private OutboxMessage GenerateJobDateCreatedEvent(Command request, Guid correlationId)
            {
                var dateEventDto = new JobDateCreatedEvent.JobDateCreatedDto(
                            request.JobId, JobDateTypes.WorkAuthorizationSigned, request.Date, request.ModifiedBy);
                var dateEvent = new JobDateCreatedEvent(dateEventDto, correlationId);
                _logger.LogInformation("SatisfyAtpRequirement: Generating event {eventName} {@event} {correlationId}", nameof(JobDateCreatedEvent), dateEventDto, correlationId);
                return new OutboxMessage(dateEvent.ToJson(), nameof(JobDateCreatedEvent),
                    correlationId, request.ModifiedBy);
            }

            private OutboxMessage GenerateJobProgressUpdatedEvent(Command request, JobProgress progress, Guid correlationId)
            {
                var dateEventDto = new JobProgressUpdatedEvent.JobProgressUpdatedDto()
                {
                    JobId = request.JobId,
                    ModifiedBy = request.ModifiedBy,
                    JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)progress
                };
                var dateEvent = new JobProgressUpdatedEvent(dateEventDto, correlationId);
                _logger.LogInformation("SatisfyAtpRequirement: Generating event {eventName} {@event} {correlationId}", nameof(JobProgressUpdatedEvent), dateEventDto, correlationId);
                return new OutboxMessage(dateEvent.ToJson(), nameof(JobProgressUpdatedEvent),
                    correlationId, request.ModifiedBy);
            }
        }
    } 
}