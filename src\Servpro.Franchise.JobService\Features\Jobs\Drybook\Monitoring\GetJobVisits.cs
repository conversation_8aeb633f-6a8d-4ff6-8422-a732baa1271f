﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetJobVisits
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Dto
        {
            public Dto(
                Guid jobId,
                Guid jobVisitId,
                DateTime? date,
                DateTime? departureDate,
                string empleyeeInitials
                )
            {
                JobId = jobId;
                JobVisitId = jobVisitId;
                Date = date;
                DepartureDate = departureDate;
                EmployeeInitials = empleyeeInitials;
            }

            public Guid JobId { get; set; }
            public Guid JobVisitId { get; set; }
            public DateTime? Date { get; set; }
            public DateTime? DepartureDate { get; set; }
            public string EmployeeInitials { get; set; }
            public bool? IsAbleToGetReadings { get; set; }
            public Guid? UnableToGetReadingJournalNoteId { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;

            public Handler(JobReadOnlyDataContext context, IUserInfoAccessor userInfoAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(j => j.JobVisits)
                        .ThenInclude(x => x.JobVisitTriStateAnswers)
                    .Include(j => j.JobVisits)
                        .ThenInclude(y => y.Tasks)
                            .ThenInclude(z => z.JournalNotes)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(j => j.Id == request.JobId
                        && j.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");
                return Map(job);
            }

            private IEnumerable<Dto> Map(Job job)
            {
                return job.JobVisits.Select(x => MapVisitDto(x));
            }

            private Dto MapVisitDto(JobVisit visit)
            {
                var visitDto = new Dto(visit.JobId, visit.Id, visit.Date, visit.DepartureDate, visit.EmployeeInitials);
                var answer = visit.JobVisitTriStateAnswers.FirstOrDefault(x => x.JobTriStateQuestionId == DailyDepartureQuestionIds.AbleToGetReadings);
                visitDto.IsAbleToGetReadings = answer?.Answer ?? false;
                visitDto.UnableToGetReadingJournalNoteId = answer != null ? visit.Tasks
                        .FirstOrDefault(t => t.JobTriStateQuestionId == answer.JobTriStateQuestionId && t.JobVisitId == visit.Id)?
                        .JournalNotes.FirstOrDefault()?.Id
                        : null;
                return visitDto;
            }
        }
    }
}
