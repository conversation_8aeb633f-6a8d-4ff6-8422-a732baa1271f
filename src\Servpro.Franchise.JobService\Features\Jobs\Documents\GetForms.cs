﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Models;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.Franchise.JobService.Infrastructure;
using Amazon.DynamoDBv2;
using Servpro.Franchise.JobService.Common;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Infrastructure.Utilities;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GetForms
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Query(Guid id, Guid franchiseSetId)
            {
                JobId = id;
                FranchiseSetId = franchiseSetId;
            }
            public Guid JobId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }


        public class Dto
        {
            public Guid FormTemplateId { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public bool IsActive { get; set; }
            public bool IsApproved { get; set; }
            public string InsuranceClientId { get; set; }
            public string InsuranceClient { get; set; }
            public string State { get; set; }
            public string Country { get; set; }
            public string FormalLanguage { get; set; }
            public int? Copies { get; set; }
            public bool IsWaterForm { get; set; }
            public bool IsFireForm { get; set; }
            public bool IsMoldForm { get; set; }
            public bool IsWaterFormRequired { get; set; }
            public bool IsFireFormRequired { get; set; }
            public bool IsMoldFormRequired { get; set; }
            public string FileType { get; set; }
            public int? DisplayOrder { get; set; }
            public string Version { get; set; }
            public Guid? RelatedForm { get; set; }
            public string LinkedPage { get; set; }
            public DateTime? CreatedDate { get; set; }
            public DateTime? UpdatedDate { get; set; }
            public string CreatedBy { get; set; }
            public string UpdatedBy { get; set; }
            public Guid? RelatedForm2 { get; set; }
            public Guid? RelatedForm3 { get; set; }
            public bool? IsRequiredForCommercialJob { get; set; }
            public bool? IsRequiredForResidentialJob { get; set; }
            public bool? IsRequiredForAllLossTypes { get; set; }
            public bool? IsAvailableInFirstNotice { get; set; }
            public string CommercialClient { get; set; } // Custom Properties. 
            public bool IsDocumentUploaded { get; set; }
            public string FormCategoryDesc { get; set; }
            public int GroupOrder { get; set; }
            public bool IsForUpload { get; set; }
            public bool IsRequired { get; set; }
            public bool IsXactUploaded { get; set; }
            public Guid MediaId { get; set; }
            public DateTime? SignedDate { get; set; }
            public bool UploadedSuccessfully { get; set; }
            public bool IsDocumentOutForSignature { get; set; }
        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IFormsService _formsService;
            private readonly ILogger<GetForms> _logger;
            private readonly IDocuSignUtility _docuSignUtility;

            public Handler(JobReadOnlyDataContext db,
                IFormsService formsService,
                ILogger<GetForms> logger,
                IDocuSignUtility docuSignUtility)
            {
                _db = db;
                _formsService = formsService;
                _logger = logger;
                _docuSignUtility = docuSignUtility;
            }

            public async Task<ICollection<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                try
                {
                    var stopwatch = Stopwatch.StartNew();
                    var jobId = request.JobId;
                    var job = await GetJobAsync(jobId, request.FranchiseSetId, cancellationToken);

                    using var scope = _logger.BeginScope("{jobId}", jobId);
                    _logger.LogDebug("GetJobAsync Completed. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                    _logger.LogDebug("Job: {@job}", job);

                    if (job == null)
                        throw new ResourceNotFoundException("Job not found");

                    var forms = await _formsService.GetFormsAsync(job, cancellationToken);
                    var formTemplatesOutForSignatureMap = await _docuSignUtility.GetFormTemplatesOutForSignatureAsync(jobId, cancellationToken);
                    _logger.LogDebug("Forms: {@forms}", forms);

                    var dtos = MapDtos(forms, job, formTemplatesOutForSignatureMap);

                    _logger.LogDebug("Handler Completed. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);

                    return dtos;
                }
                catch (Exception e)
                {
                    _logger.LogError("Error retrieving forms. {error}", e.Message);
                    throw;
                }
            }

            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(j => j.Id == jobId && j.FranchiseSetId == franchiseSetId, cancellationToken);
                if (job == null) return null;

                job.MediaMetadata = await _db.MediaMetadata
                    .AsNoTracking()
                    .Where(mm => mm.JobId == jobId)
                    .ToListAsync(cancellationToken);
                
                return job;
            }

            private static List<Dto> MapDtos(List<FormTemplateModel> forms, Job job, Dictionary<Guid, bool> formTemplatesOutForSignatureMap)
            {
                return forms.Select(form => new Dto
                {
                    FormTemplateId = form.FormTemplateId,
                    Name = form.Name,
                    LinkedPage = form.LinkedPage,
                    Description = form.Description,
                    RelatedForm = form.RelatedForm,
                    RelatedForm2 = form.RelatedForm2,
                    RelatedForm3 = form.RelatedForm3,
                    IsRequiredForAllLossTypes = form.IsRequiredForAllLossTypes,
                    IsWaterFormRequired = form.IsWaterFormRequired,
                    IsMoldFormRequired = form.IsMoldFormRequired,
                    IsFireFormRequired = form.IsFireFormRequired,
                    IsRequiredForResidentialJob = form.IsRequiredForResidentialJob,
                    IsRequiredForCommercialJob = form.IsRequiredForCommercialJob,
                    IsWaterForm = form.IsWaterForm,
                    IsMoldForm = form.IsMoldForm,
                    IsFireForm = form.IsFireForm,
                    InsuranceClient = form.InsuranceClient,
                    CommercialClient = form.CommercialClient,
                    IsAvailableInFirstNotice = form.IsAvailableInFirstNotice,
                    State = form.State,
                    Country = form.Country,
                    FormalLanguage = form.FormalLanguage,
                    IsActive = form.IsActive,
                    IsApproved = form.IsApproved,
                    IsXactUploaded = form.IsXactUploaded,
                    IsRequired = form.IsRequired,
                    GroupOrder = form.GroupOrder,
                    MediaId = form.MediaId,
                    InsuranceClientId = job.InsuranceCarrierId?.ToString(),
                    FileType = form.FileType,
                    DisplayOrder = form.DisplayOrder,
                    Copies = form.Copies,
                    FormCategoryDesc = form.FormCategoryDesc,
                    IsDocumentUploaded = form.IsDocumentUploaded,
                    IsForUpload = form.IsForUpload,
                    Version = form.Version,
                    SignedDate = form.SignedDate,
                    UploadedSuccessfully = form.UploadedSuccessfully ||
                                           (
                                               !string.IsNullOrEmpty(job.MediaMetadata?.FirstOrDefault(x => x.Id == form.MediaId)?.BucketName) &&
                                               !string.IsNullOrEmpty(job.MediaMetadata?.FirstOrDefault(x => x.Id == form.MediaId)?.MediaPath)
                                           ),
                    IsDocumentOutForSignature = formTemplatesOutForSignatureMap.ContainsKey(form.FormTemplateId)
                }).ToList();
            }
        }
    }
}