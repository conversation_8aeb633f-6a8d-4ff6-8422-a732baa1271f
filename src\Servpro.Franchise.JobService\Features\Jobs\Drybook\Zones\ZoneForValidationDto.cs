﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook
{
    public class ZoneForValidationDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid WaterClassId { get; set; }
        public int AirMoverAmps { get; set; }
        public int DehumidifierAmps { get; set; }
        public int TotalAmps { get; set; }
        public decimal TotalAffectedLinearFeet { get; set; }
        public decimal TotalAffectedSquareFeet { get; set; }
        public decimal TotalAffectedCubicFeet { get; set; }

        //AirMover standards
        public int AirMoversPlaced { get; set; }
        public int AirMoverSquareFeetRequirementsMinimumRequired { get; set; }
        public int AirMoverSquareFeetRequirementsMaximumAllowed { get; set; }
        public int AirMoverLinearFeetRequirementsMinimumRequired { get; set; }
        public int AirMoverLinearFeetRequirementsMaximumAllowed { get; set; }
        public bool CanGetRequirementsLinear { get; set; }
        public AchievementIndicator AirMoverSquareFeetAchievementIndicator { get; set; }
        public AchievementIndicator AirMoverLinearFeetAchievementIndicator { get; set; }
        public AirMoverCalculationType AirMoverUserSelectedCalculationType { get; set; }
        public int OffsetsInsetsCount { get; set; }
        public int SubRoomsCount { get; set; }

        //DEHU standards
        public int DehumidifierRequirementsCFM { get; set; }
        public int DehumidifierRequirementsPPD { get; set; }
        public int DehumidifiersAchievedCFM { get; set; }
        public int DehumidifiersAchievedPPD { get; set; }
        public AchievementIndicator DehuPpdAchievementIndicator { get; set; }
        public AchievementIndicator DehuCfmAchievementIndicator { get; set; }
        public DehumidifierCalculationType DehuCalculationType { get; set; }
        public bool DehuMixedLgrAndConventionalPlaced { get; set; }

        public IEnumerable<TaskDto> Tasks { get; set; }
        public IEnumerable<RoomDto> Rooms { get; set; }

        public class TaskDto
        {
            public Guid Id { get; set; }
            public Guid StatusId { get; set; }
            public Guid PriorityId { get; set; }
            public Guid TypeId { get; set; }
            public string Subject { get; set; }
            public int PercentComplete { get; set; }
            public DateTime? CompletionDate { get; set; }
        }

        public enum AchievementIndicator
        {
            Under = 0,
            Meets = 1,
            Exceeds150 = 2,
            Exceeds200 = 3,
            NA = 4,
            Invalid = 5,
            Exceeds = 6
        }

        public enum AirMoverCalculationType
        {
            Linear = 0,
            SquareFoot = 1
        }

        public class AirMoverSquareFeetRequirements
        {
            public AirMoverSquareFeetRequirements(
                int minimumRequired,
                int maximumAllowed)
            {
                MinimumRequired = minimumRequired;
                MaximumAllowed = maximumAllowed;
            }
            public int MinimumRequired { get; }
            public int MaximumAllowed { get; }
        }

        public enum DehumidifierCalculationType
        {
            NA = 0,
            PPD = 1,
            CFM = 2,
        }

        public class RoomDto
        {
            public Guid JobAreaId { get; set; }
            public Guid RoomId { get; set; }
            public string RoomName { get; set; }
            public DateTime? VisitDate { get; set; }
            public decimal? TotalSquareFeet { get; set; }
            public decimal? AffectedSquareFeet { get; set; }
            public decimal? AffectedPercentage { get; set; }
            public bool HasMissingFlooring { get; set; }
            public bool HasMissingDimensions { get; set; }
            public bool HasEquipmentPlacements { get; set; }
            public int EquipmentPlacementCount { get; set; }
            public decimal TotalAffectedArea { get; set; }
            public int AirMoversPlaced { get; set; }
            public AirMoverSquareFeetRequirements AirMoverRequirements { get; set; }
            public AchievementIndicator AirMoverAchievementIndicator { get; set; }
            public int InsetsOffsetsCount { get; set; }
        }
    }
}
