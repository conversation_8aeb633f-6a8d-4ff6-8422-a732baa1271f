﻿using System;

namespace Servpro.Franchise.JobService.Common
{
    public static class JobTriStateQuestions
    {

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AnySpecialtyContentItems = new Guid("8359A643-AF97-457E-8991-868B8C7B0691");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreAnyServicesNeededContentsPackoutTexttileDryCleaning = new Guid("571B2089-6E32-4CE0-A703-97D87C09F25A");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreThereAnyFactorsDelayingTheDryingProcess = new Guid("DC8390AB-590C-4D0D-9D5C-E4A84A814D1D");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreThereAnyQuestionsAboutCoverage = new Guid("CA872269-D19D-4DD1-A6D4-51D101BED112");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreThereDumpstersOrBaggedDebris = new Guid("322EC9D5-A641-49DC-A3E1-CABD37B25221");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreYouRemovingCabinetsOrCountertops = new Guid("7B7BE9B5-B6F1-4937-B32C-D901C51E036A");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid DidCustomerNoteConcerns = new Guid("358241DB-23F8-4CFA-A767-67FF3523EA26");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid DidCustomerRefuseService = new Guid("2E1EE5A9-5662-4135-8EF4-828B23A6DF63");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid DoesMoldExceedSF = new Guid("FD081FB7-883F-48D6-AA89-667F7DAF7037");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid DoYouAnticipateMitigationEstimateToExceed = new Guid("C27DE0C0-DBC1-452F-AFCB-E5AB1E7BC315");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid HasSafetySecurityIssues = new Guid("61A3147D-4F00-41A6-BF47-80B0682019AF");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsAccessToLossSiteRestricted = new Guid("64837AE3-25EF-42FD-B39E-414C99586DCF");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsAnyPPEBeingUsed = new Guid("AE1788AD-84BE-4AC1-8B44-7946E32B4858");

        public static readonly Guid IsCustomerFinalWalkThroughComplete = new Guid("4784D78E-8281-4138-85C7-209BB2FAD148");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsDemolitionNeeded = new Guid("F8D0231A-55ED-4B1E-A498-F2B9115192C4");

        public static readonly Guid IsElectricityAvailable = new Guid("9A2B65F6-B469-4DAE-BE39-30A11B9FA714");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsProvidingEstimateOnly = new Guid("DB4F5C0F-4DE8-4C52-B4DF-7BC69A0A6539");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsSpecialtyDryingEquipmentNeeded = new Guid("694A2DEE-2BEF-44F2-BC9A-780B21FDC796");

        public static readonly Guid IsStandingWater = new Guid("1F6E5560-97D2-40BC-8CA6-04A7E4809528");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsSubcontractOrReconstructionRequired = new Guid("1914F728-C4B1-4642-A5B4-ADA742A8B6C5");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsSubrogationPotentialIdentified = new Guid("201BE6AB-B3E0-49A9-8B4F-243F884E38A0");

        public static readonly Guid IsWaterAvailable = new Guid("4981F7FC-C605-4C13-BEB0-B0371F2AA361");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid MoldIsPresent = new Guid("7888D560-88BF-414C-B489-659113EB1DF4");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid MoldLessThanSF = new Guid("E838F797-3FFD-49F8-8EBA-3A566B9AF034");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WasAnyDemolitionPerformed = new Guid("86D8BDAA-8E05-40D6-8F1E-527793CBE6D9");

        public static readonly Guid WasCertificateOfSatisfactionSignedAndDatedByCustomer = new Guid("C868BF38-823E-46B5-8EE7-C1F33D7E4923");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WasEquipmentMovedOrTurnedOff = new Guid("997DBFAC-3371-48DB-8EBA-E9C57666035B");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WasInPlaceDryingPerformedOnCat1 = new Guid("4CD42BA9-FC34-4F3D-81D9-4AC37E1162E1");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WereLeadPaintTestPositive = new Guid("9AF410C5-92CA-4937-8A4B-1A737D0D90F7");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WereYouAbleToGetReadingsForThisDate = new Guid("934A038B-EC8D-48E7-80C4-B83EE4F827E1");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WillAsbestosTestingBePerformed = new Guid("D8A370B7-C0BB-408E-A7FC-576B3F049497");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WillDemoExceedHours = new Guid("F15AF34B-981E-4648-8059-1CAA90A9421E");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WillPackOutExceedHours = new Guid("BDFCF2E4-5276-4897-A19F-8C7621A9B38F");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid WillRepairsDelayDrying = new Guid("4127AB51-6F7D-44C1-81A1-97B4FA23C8B6");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsDryingWorkbookCompleted = new Guid("1105C08E-1F43-4FC9-B55B-C48B052698D6");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsJobSelfPay = new Guid("0663C671-2E35-4120-B588-FF54BCA3486C");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid HasDeductible = new Guid("CA97BF1D-13ED-427D-B60F-16EF2251434A");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid HasEmergencyServicesLimit = new Guid("8FCD544C-532C-4CA6-BB02-A9F1D6A9778B");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid HasSourceBeenTurnedOff = new Guid("D74368B1-84F5-4CCB-8799-278E2184A19F");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsCrawlspaceOrAtticAffected = new Guid("39163AA0-E2DC-4FE7-84D7-3E964D97B94F");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreContentsAffected = new Guid("B1608A5D-0788-4560-807F-6C5BD309DCD3");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid AreWallsAffected = new Guid("FFC5BE3A-EA13-4C51-AEE6-0D02E130635F");

        /// <summary>
        /// Guid valid as of 23 Jan 2021
        /// </summary>
        public static readonly Guid IsCeilingAffected = new Guid("74F7F447-A4A4-4CA3-A336-DBEC17D6873A");

    }
}
