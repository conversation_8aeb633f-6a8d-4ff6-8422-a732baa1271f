﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.JournalNotes
{
    public class DeleteJournalNote
    {
        public class Command : IRequest<Unit>
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
        }

        public class Validator : AbstractValidator<EditJournalNote.Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.TypeId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, Unit>
        {
            private readonly JobDataContext _context;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IUserInfoAccessor _userInfoAxcAccessor;

            public Handler(JobDataContext context,
                ISessionIdAccessor sessionIdAccessor,
                IUserInfoAccessor userInfoAccessor)
            {
                _sessionIdAccessor = sessionIdAccessor;
                _context = context;
                _userInfoAxcAccessor = userInfoAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var journalNote = await _context.JournalNote
                    .FirstOrDefaultAsync(q => q.Id == request.Id && q.JobId == request.JobId, cancellationToken: cancellationToken);

                if (journalNote == null)
                    throw new ResourceNotFoundException($"JournalNote not found: {request.Id}");

                var userInfo = _userInfoAxcAccessor.GetUserInfo();
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                journalNote.IsDeleted = true;
                journalNote.ModifiedDate = DateTime.UtcNow;
                journalNote.ModifiedBy = userInfo.Name;

                var outboxMessage = GenerateJournalNoteDeletedEvent(request, correlationId, userInfo);

                await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private OutboxMessage GenerateJournalNoteDeletedEvent(Command request, Guid correlationId,
                UserInfo userInfo)
            {
                var deletionDto = new JournalNoteDeletedEvent.JournalNoteDto()
                {
                    Id = request.Id,
                    JobId = request.JobId,
                    DeletedById = userInfo.Id,
                    DeletedDate = DateTime.UtcNow
                };
                var deletionEvent = new JournalNoteDeletedEvent(deletionDto, correlationId);
                return new OutboxMessage(deletionEvent.ToJson(), nameof(JournalNoteDeletedEvent), correlationId, userInfo.Username);
            }
        }
    }
}