﻿using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;
using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Models
{
    public class ProcessEntityResult
    {
        public ProcessEntityResult(HashSet<Guid> failedEntities, List<ErrorInfo> errors, string entity)
        {
            FailedEntities = failedEntities;
            Errors = errors;
            Entity = entity;
        }

        public HashSet<Guid> FailedEntities { get; set; }
        public IEnumerable<ErrorInfo> Errors { get; set; }
        public string Entity { get; set; }
    }
}
