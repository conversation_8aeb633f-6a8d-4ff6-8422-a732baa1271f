﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class InsuranceCarrierChanged
    {
        public class Event : InsuranceCarrierChangedEvent, IRequest
        {
            public Event(JobDto dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _dbContext;
            private readonly ILogger<InsuranceCarrierChanged> _logger;

            public Handler(JobDataContext context, 
                ILogger<InsuranceCarrierChanged> logger)
            {
                _dbContext = context;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.Job.JobId);
                _logger.LogInformation("Processing event: {@payload}", request);

                var job = await _dbContext.Jobs.FirstOrDefaultAsync(x => x.Id == request.Job.JobId && 
                                x.CorporateJobNumber == request.Job.CorporateJobNumber, cancellationToken);

                if(job != null)
                {
                    job.InsuranceCarrierId = request.Job.InsuranceCarrierId;
                    job.JobDispatchTypeId = request.Job.JobDispatchTypeId;
                    job.ModifiedBy = request.Job.CreatedBy;
                    job.ModifiedDate = DateTime.UtcNow;

                    await _dbContext.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    _logger.LogWarning("Job With Id: {id} not found.", request.Job.JobId);
                }

                return Unit.Value;
            }
        }
    }
}
