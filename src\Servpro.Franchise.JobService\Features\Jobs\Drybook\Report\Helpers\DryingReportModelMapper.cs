﻿using Servpro.Franchise.JobService.Models.Drybook.DryingReportParser;
using DryingReport = Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class DryingReportModelMapper
    {
        public static DryingReportModel Map(GenerateDryingReport.Dto dto)
            => new DryingReportModel()
            {
                SummaryModel = Map(dto.SummaryModel),
                DailyEquipmentCountModel = Map(dto.DailyEquipmentCountModel),
                ZoneCompositionModel = Map(dto.ZoneCompositionModel),
                MonitoringModel = Map(dto.MonitoringModel),
                JobId = dto.JobId,
                UploadId = dto.UploadId,
                FranchiseName = dto.FranchiseName,
                CustomerName = dto.CustomerName,
                ClaimNumber = dto.ClaimNumber,
                IsWaterJob = dto.IsWaterJob,
                IsDailyNarrativeEnabled = dto.IsDailyNarrativeEnabled
            };
        public static SummaryModel Map(DryingReport.SummaryDto dto)
         => new SummaryModel()
            {
                FranchiseSetTimeZone = dto.FranchiseSetTimeZone,
                CustomerName = dto.CustomerName,
                ClaimPoNumber = dto.ClaimPoNumber,
                JobAddress1 = dto.JobAddress1,
                JobAddress2 = dto.JobAddress2,
                CityStateZip = dto.CityStateZip,
                InsuranceClient = dto.InsuranceClient,
                PolicyWoNumber = dto.PolicyWoNumber,
                CustomerPhone = dto.CustomerPhone,
                CustomerEmail = dto.CustomerEmail,
                LossReceived = dto.LossReceived,
                DateOfLoss = dto.DateOfLoss,
                DryingComplete = dto.DryingComplete,
                CustomerCalled = dto.CustomerCalled,
                ArrivalOnSite = dto.ArrivalOnSite,
                TypeOfLoss = dto.TypeOfLoss,
                StructureType = dto.StructureType,
                CountOfRoomsAffected = dto.CountOfRoomsAffected,
                ElectricityAvailable = dto.ElectricityAvailable,
                YearStructureBuilt = dto.YearStructureBuilt,
                CauseOfLoss = dto.CauseOfLoss,
                PropertyType = dto.PropertyType,
                CountOfFloorsAffected = dto.CountOfFloorsAffected,
                FranchiseName = dto.FranchiseName,
                FranchiseAddress1 = dto.FranchiseAddress1,
                FranchiseAddress2 = dto.FranchiseAddress2,
                FranchiseCityStateZip = dto.FranchiseCityStateZip,
                FranchisePhone = dto.FranchisePhone,
                CategoryOfWater = dto.CategoryOfWater,
                ClassOfWater = dto.ClassOfWater,
                DaysToAchieveDryStandard = dto.DaysToAchieveDryStandard,
                RoomsAffected = dto.RoomsAffected,
                DryingZones = dto.DryingZones,
                TotalSquareFeet = dto.TotalSquareFeet,
                IsPhase2 = dto.IsPhase2
            };

        private static DailyEquipmentCountModel Map(DryingReport.DailyEquipmentCountDto dto)
        {
            return new DailyEquipmentCountModel()
            {
                FirstJobVisitDate = dto.FirstJobVisitDate,
                LastJobVisitDate = dto.LastJobVisitDate,
                EquipmentPlacementCountByDay = dto.EquipmentPlacementCountByDay.Select(x => Map(x)).ToList(),
                EquipmentSummary = dto.EquipmentSummary.Select(x => Map(x)).ToList()
            };
        }

        private static EquipmentCountDetail Map(DryingReport.EquipmentCountDetail dto)
            => new EquipmentCountDetail()
            {
                EquipmentType = dto.EquipmentType,
                Count = dto.Count,
                Day = dto.Day
            };

        private static EquipmentPlacementDetail Map(DryingReport.EquipmentPlacementDetail dto)
            => new EquipmentPlacementDetail()
            {
                EquipmentType = dto.EquipmentType,
                Room = dto.Room,
                EquipmentModel = dto.EquipmentModel,
                AssetNumber = dto.AssetNumber,
                Placed = dto.Placed,
                Removed = dto.Removed,
                Days = dto.Days,
                Hours = dto.Hours
            };

        private static ZoneCompositionModel Map(DryingReport.ZoneCompositionDto dto)
            => new ZoneCompositionModel()
            {
                ZoneCompositions = dto.ZoneCompositions.Select(x => Map(x)).ToList()
            };

        private static ZoneComposition Map(DryingReport.ZoneComposition dto)
            => new ZoneComposition()
            {
                ZoneName = dto.ZoneName,
                WaterCategory = dto.WaterCategory,
                WaterClass = dto.WaterClass,
                IsWaterClassOverridden = dto.IsWaterClassOverridden,
                IsConfirmed = dto.IsConfirmed,
                StabilizationBeginDate = dto.StabilizationBeginDate,
                StabilizationEndDate = dto.StabilizationEndDate,
                DehuValidationType = Map(dto.DehuValidationType),
                MinDehuPpdsRequired = dto.MinDehuPpdsRequired,
                MinDehuCfmsRequired = dto.MinDehuCfmsRequired,
                DehuPpdsPlaced = dto.DehuPpdsPlaced,
                DehuCfmsPlaced = dto.DehuCfmsPlaced,
                DehusRequirementsProximity = Map(dto.DehusRequirementsProximity),
                CountDehusPlaced = dto.CountDehusPlaced,
                RestorativeDryingBeginDate = dto.RestorativeDryingBeginDate,
                RestorativeDryingEndDate = dto.RestorativeDryingEndDate,
                AirMoverValidationMethod = Map(dto.AirMoverValidationMethod),
                MinAirMoversRequired = dto.MinAirMoversRequired,
                MaxAirMoversRequired = dto.MaxAirMoversRequired,
                CountAirMoversPlaced = dto.CountAirMoversPlaced,
                AirMoversRequirementsProximity = Map(dto.AirMoversRequirementsProximity),
                RoomScopes = dto.RoomScopes.Select(x => Map(x)).ToList(),
                ZonePercentAffected = dto.ZonePercentAffected
            };

        private static DehuValidationType Map(DryingReport.DehuValidationType dto)
        {
            switch (dto)
            {
                case DryingReport.DehuValidationType.Ppd:
                    return DehuValidationType.Ppd;

                case DryingReport.DehuValidationType.Cfm:
                    return DehuValidationType.Cfm;

                case DryingReport.DehuValidationType.Mixed:
                    return DehuValidationType.Mixed;

                default:
                    return DehuValidationType.None;
            }
        }

        private static EquipmentPlacementRequirementsProximity Map(DryingReport.EquipmentPlacementRequirementsProximity dto)
        {
            switch (dto) 
            {
                case DryingReport.EquipmentPlacementRequirementsProximity.BelowMinimum:
                    return EquipmentPlacementRequirementsProximity.BelowMinimum;

                case DryingReport.EquipmentPlacementRequirementsProximity.Acceptable:
                    return EquipmentPlacementRequirementsProximity.Acceptable;

                case DryingReport.EquipmentPlacementRequirementsProximity.HighAcceptable:
                    return EquipmentPlacementRequirementsProximity.HighAcceptable;

                case DryingReport.EquipmentPlacementRequirementsProximity.AboveMaximum:
                    return EquipmentPlacementRequirementsProximity.AboveMaximum;

                default:
                    return EquipmentPlacementRequirementsProximity.None;
            }
        }

        private static AirMoverValidationMethod Map(DryingReport.AirMoverValidationMethod dto)
        {
            switch (dto)
            {
                case DryingReport.AirMoverValidationMethod.LinearFeet:
                    return AirMoverValidationMethod.LinearFeet;

                case DryingReport.AirMoverValidationMethod.SquareFeet:
                    return AirMoverValidationMethod.SquareFeet;

                default:
                    return AirMoverValidationMethod.SquareFeet;
            }
        }

        private static RoomScope Map(DryingReport.RoomScope dto)
            => new RoomScope()
            {
                RoomName = dto.RoomName,
                RoomVolumeTotal = dto.RoomVolumeTotal,
                RoomVolumeOffsetsInsets = dto.RoomVolumeOffsetsInsets,
                FloorAreaAffected = dto.FloorAreaAffected,
                FloorArea = dto.FloorArea,
                FloorAreaOffsetsInsets = dto.FloorAreaOffsetsInsets,
                FloorAreaMissingSpaces = dto.FloorAreaMissingSpaces,
                FloorAreaPercentAffected = dto.FloorAreaPercentAffected,
                FloorLinearAffected = dto.FloorLinearAffected,
                FloorLinearOffsetsInsets = dto.FloorLinearOffsetsInsets,
                FloorLinearMissingSpaces = dto.FloorLinearMissingSpaces,
                FloorAreaSaved = dto.FloorAreaSaved,
                TotalAffectedSquareFootage = dto.TotalAffectedSquareFootage,
                TotalSquareFeet = dto.TotalSquareFeet,
                TotalOffsetsInsetsSquareFootage = dto.TotalOffsetsInsetsSquareFootage,
                TotalMissingSpacesSquareFootage = dto.TotalMissingSpacesSquareFootage,
                TotalAffectedPercentage = dto.TotalAffectedPercentage,
                TotalFloorPercentage = dto.TotalFloorPercentage,
                TotalWallPercentage = dto.TotalWallPercentage,
                TotalCeilingPercentage = dto.TotalCeilingPercentage,
                TotalNumberOfAirMovers = dto.TotalNumberOfAirMovers,
                TotalNumberOfDehus = dto.TotalNumberOfDehus,
                IsConfirmed = dto.IsConfirmed,
                MaxAirMoversRequired = dto.MaxAirMoversRequired,
                MinAirMoversRequired = dto.MinAirMoversRequired,
                AirMoversRequirementsProximity = Map(dto.AirMoversRequirementsProximity),
                WallAreaAffected = dto.WallAreaAffected,
                WallAreaAffectedAbove2Feet = dto.WallAreaAffectedAbove2Feet,
                WallSquareFeet = dto.WallSquareFeet,
                WallAreaOffsetsInsets = dto.WallAreaOffsetsInsets,
                WallAreaMissingSpaces = dto.WallAreaMissingSpaces,
                CeilingAreaAffected = dto.CeilingAreaAffected,
                CeilingSquareFeet = dto.CeilingSquareFeet,
                CeilingAreaOffsetsInsets = dto.CeilingAreaOffsetsInsets,
                CeilingAreaMissingSpaces = dto.CeilingAreaMissingSpaces,
                OffsetsInsetsCount = dto.OffsetsInsetsCount,
                SubRoomsCount = dto.SubRoomsCount,
                FlooringType = dto.FlooringType
            };

        private static MonitoringModel Map(DryingReport.MonitoringModelDto dto)
            => new MonitoringModel()
            {
                AtmosphericReadingsModel = Map(dto.AtmosphericReadingsModel)
            };

        private static AtmosphericReadingsModel Map(DryingReport.AtmosphericReadingsDto dto)
            => new AtmosphericReadingsModel()
            {
                ZoneReadingSets = dto.ZoneReadingSets.Select(x => Map(x)).ToList()
            };

        private static ZoneReadingSet Map(DryingReport.AtmosphericReadingsDto.ZoneReadingSet dto)
            => new ZoneReadingSet()
            {
                ZoneType = dto.ZoneType,
                ZoneName = dto.ZoneName,
                ZoneDesc = dto.ZoneDesc,
                VisitReadings = dto.VisitReadings.Select(x => Map(x)).ToList()
            };

        private static VisitReadingSet Map(DryingReport.AtmosphericReadingsDto.VisitReadingSet dto)
            => new VisitReadingSet()
            {
                Temp = dto.Temp,
                GppTooHigh = dto.GppTooHigh,
                TempOutsideOptimalRangeForEquip = dto.TempOutsideOptimalRangeForEquip,
                PreviousGPP = dto.PreviousGPP,
                PreviousRH = dto.PreviousRH,
                PreviousTemp = dto.PreviousTemp,
                DehuReadings = dto.DehuReadings.Select(x => Map(x)).ToList(),
                GPP = dto.GPP,
                RH = dto.RH,
                RhTooHigh = dto.RhTooHigh,
                IsMissingVisit = dto.IsMissingVisit,
                Technician = dto.Technician,
                VisitTimestamp = dto.VisitTimestamp,
                GppNotDecreasing = dto.GppNotDecreasing
            };

        private static DehuReading Map(DryingReport.AtmosphericReadingsDto.DehuReading dto)
            => new DehuReading()
            {
                AssetNo = dto.AssetNo,
                PreviousGPP = dto.PreviousGPP,
                PreviousRH = dto.PreviousRH,
                PreviousTemp = dto.PreviousTemp,
                Hours = dto.Hours,
                GDep = dto.GDep,
                GPP = dto.GPP,
                RH = dto.RH,
                Temp = dto.Temp,
                GDepTooLow = dto.GDepTooLow,
                EquipmentModel = dto.EquipmentModel,
                GDepNegative = dto.GDepNegative
            };
    }
}
