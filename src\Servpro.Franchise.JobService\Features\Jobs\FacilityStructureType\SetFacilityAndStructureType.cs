﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.FacilityStructureType;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.FacilityStructureType
{
    public class SetFacilityAndStructureType
    {
        public class Command : IRequest<Unit>
        {
            public Guid JobId { get; set; }
            public int FacilityTypeId { get; set; }
            public Guid StructureTypeId { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
                RuleFor(x => x.FacilityTypeId).NotEmpty();
                RuleFor(x => x.StructureTypeId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;

            public Handler(JobDataContext context, IUserInfoAccessor userInfoAccessor, ISessionIdAccessor sessionIdAccessor)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs.FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken: cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException();

                job.FacilityTypeId = request.FacilityTypeId;
                job.StructureTypeId = request.StructureTypeId;

                await RaiseEvents(request);

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task RaiseEvents(Command request)
            {
                var user = _userInfoAccessor.GetUserInfo();

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var eventDto = new JobFacilityStructureTypeUpdatedEvent.JobFacilityStructureTypeUpdatedDto(
                    request.JobId, request.FacilityTypeId, request.StructureTypeId, user.Username);
                var newEvent = new JobFacilityStructureTypeUpdatedEvent(eventDto, correlationId);
                await GenerateOutboxMessage(newEvent.ToJson(), nameof(JobFacilityStructureTypeUpdatedEvent),
                    newEvent.CorrelationId, eventDto.ModifiedBy);

            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _context.OutboxMessages.AddAsync(newEvent);
            }
        }
    }
}
