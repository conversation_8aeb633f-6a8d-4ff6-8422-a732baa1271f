﻿using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.IO;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class UpdateFindMergeCandidates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("1d352589-e7d9-4ae0-8752-1a16e1a2b79f"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("a227c390-e569-48aa-b5dd-49d5a36f55ba"), null, new DateTime(2023, 5, 2, 17, 51, 53, 215, DateTimeKind.Utc).AddTicks(3034), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            UpdateFindMergeCandidatesSproc(migrationBuilder);
        }

        private void UpdateFindMergeCandidatesSproc(MigrationBuilder migrationBuilder)
        {
            if (MigrationHelper.IsPipeline())
                migrationBuilder.Sql(MigrationHelper.ChangeDelimiterSql);

            var findMergeCandidates = @"
                DROP PROCEDURE IF EXISTS findMergeCandidates;

                CREATE PROCEDURE findMergeCandidates (IN jobId char(36))
                BEGIN
                    DECLARE residentialPropertyType CHAR(36) DEFAULT '00000001-0011-5365-7276-70726f496e63';
                    DECLARE onHold INT(11) DEFAULT 17;
                    DECLARE turnedDown INT(11) DEFAULT 19;
                    DECLARE notSoldCancelled INT(11) DEFAULT 16;
    
                    SET @address1 = (SELECT JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) FROM Job WHERE Id = CONVERT(jobId USING latin1));
                    SET @franchiseSetId = (SELECT FranchiseSetId FROM Job WHERE Id = CONVERT(jobId USING latin1));
    
                    SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, MergeTarget, Address1 
                    FROM
                    (
                        SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, MergeTarget, JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) AS Address1
                        FROM Job
                        WHERE JSON_EXTRACT(LossAddress, '$.Address1') = CONVERT(@address1 USING latin1)
                            AND Id <> CONVERT(jobId USING latin1)
                            AND FranchiseSetId = CONVERT(@franchiseSetId USING latin1)
                            AND PropertyTypeId = residentialPropertyType
                            AND JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                    ) as MergeTable
                    WHERE MergeTarget IS NULL OR MergeTarget = '';
                END;
            ";
            migrationBuilder.Sql(findMergeCandidates);

            if (MigrationHelper.IsPipeline())
            {
                migrationBuilder.Sql(MigrationHelper.DelimiterSql);
                migrationBuilder.Sql(MigrationHelper.ResetDelimiterSql);
            }
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("a227c390-e569-48aa-b5dd-49d5a36f55ba"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("1d352589-e7d9-4ae0-8752-1a16e1a2b79f"), null, new DateTime(2023, 4, 25, 20, 45, 13, 374, DateTimeKind.Utc).AddTicks(4833), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
