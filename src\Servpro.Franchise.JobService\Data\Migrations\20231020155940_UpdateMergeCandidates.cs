﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class UpdateMergeCandidates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("d9b1c713-4fc5-4b55-8b51-13db523a8a7a"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AddColumn<Guid>(
                name: "FranchiseId",
                table: "MergeCandidates",
                type: "char(36)",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                collation: "latin1_swedish_ci");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("f1d49ab1-1e9a-4c65-8a89-42cce31d342b"), null, new DateTime(2023, 10, 20, 15, 59, 39, 740, DateTimeKind.Utc).AddTicks(7613), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            if (MigrationHelper.IsPipeline())
                migrationBuilder.Sql(MigrationHelper.ChangeDelimiterSql);

            //FindMergeCandidates and FindAllMergeCandidates sprocs
            var findMergeCandidates = @"
            DROP PROCEDURE IF EXISTS findMergeCandidates;
            CREATE PROCEDURE findMergeCandidates (IN jobId char(36))
            BEGIN
                DECLARE residentialPropertyType CHAR(36) DEFAULT '00000001-0011-5365-7276-70726f496e63';
                DECLARE onHold INT(11) DEFAULT 17;
                DECLARE turnedDown INT(11) DEFAULT 19;
                DECLARE notSoldCancelled INT(11) DEFAULT 16;
    
                SET @address1 = (SELECT JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) FROM Job WHERE Id = CONVERT(jobId USING latin1));
                SET @franchiseSetId = (SELECT FranchiseSetId FROM Job WHERE Id = CONVERT(jobId USING latin1));
                SET @franchiseId = (SELECT FranchiseId FROM Job WHERE Id = CONVERT(jobId USING latin1));
    
                SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, FranchiseId, MergeTarget, Address1 
                FROM
                (
                    SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, FranchiseId, MergeTarget, JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) AS Address1
                    FROM Job
                    WHERE JSON_EXTRACT(LossAddress, '$.Address1') = CONVERT(@address1 USING latin1)
                        AND Id <> CONVERT(jobId USING latin1)
                        AND FranchiseSetId = CONVERT(@franchiseSetId USING latin1)
                        AND FranchiseId = CONVERT(@franchiseId USING latin1)
                        AND PropertyTypeId = residentialPropertyType
                        AND JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                ) as MergeTable
                WHERE MergeTarget IS NULL OR MergeTarget = '';
            END;";
            migrationBuilder.Sql(findMergeCandidates);

            if (MigrationHelper.IsPipeline())
            {
                migrationBuilder.Sql(MigrationHelper.DelimiterSql);
            }

            var findAllMergeCandidates = @"
            DROP PROCEDURE IF EXISTS findAllMergeCandidates;
            CREATE PROCEDURE findAllMergeCandidates()
            BEGIN
                DECLARE residentialPropertyType CHAR(36) DEFAULT '00000001-0011-5365-7276-70726f496e63';
                DECLARE onHold INT(11) DEFAULT 17;
                DECLARE turnedDown INT(11) DEFAULT 19;
                DECLARE notSoldCancelled INT(11) DEFAULT 16;

                CREATE TEMPORARY TABLE IF NOT EXISTS duplicateAddresses AS (
                SELECT FranchiseSetId, FranchiseId, LossAddress1 AS Address1
                FROM WipRecord
                WHERE PropertyTypeName = residentialPropertyType
                    AND JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                GROUP BY FranchiseSetId, FranchiseId, LossAddress1
                HAVING count(*) > 1);

                SELECT w.Id, w.LeadSource as RecordSourceId, w.JobTypeName as LossTypeId, w.FranchiseSetId, w.FranchiseId, w.JobProgress, LOWER(w.LossAddress1) as Address1
                FROM duplicateAddresses da
                JOIN WipRecord w on w.FranchiseSetId = da.FranchiseSetId
					AND w.FranchiseId = da.FranchiseId
                    AND w.LossAddress1 = da.Address1
                WHERE w.PropertyTypeName = residentialPropertyType
                    AND w.JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                    AND w.MergeTarget IS NULL OR w.MergeTarget = '';
            END;";
            migrationBuilder.Sql(findAllMergeCandidates);


            if (MigrationHelper.IsPipeline())
            {
                migrationBuilder.Sql(MigrationHelper.DelimiterSql);
                migrationBuilder.Sql(MigrationHelper.ResetDelimiterSql);
            }
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("f1d49ab1-1e9a-4c65-8a89-42cce31d342b"));

            migrationBuilder.DropColumn(
                name: "FranchiseId",
                table: "MergeCandidates");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("d9b1c713-4fc5-4b55-8b51-13db523a8a7a"), null, new DateTime(2023, 9, 20, 22, 15, 45, 571, DateTimeKind.Utc).AddTicks(7196), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
