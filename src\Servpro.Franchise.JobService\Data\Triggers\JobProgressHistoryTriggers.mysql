﻿DROP TRIGGER IF EXISTS JobProgressHistoryInsert;
DROP TRIGGER IF EXISTS JobProgressHistoryUpdate;

CREATE TRIGGER JobProgressHistoryInsert 
AFTER INSERT ON Job FOR EACH ROW
BEGIN
	INSERT INTO JobProgressHistory 
	SELECT UUID(), j.Id, j.<PERSON>, utc_timestamp(), j.<PERSON>By 
	FROM Job AS j WHERE j.Id = NEW.Id;
END;

CREATE TRIGGER JobProgressHistoryUpdate 
AFTER UPDATE ON Job FOR EACH ROW
BEGIN
	IF !(NEW.JobProgress <=> OLD.JobProgress) THEN
        SELECT UUID(), j.Id, j.<PERSON>,  utc_timestamp(), j.<PERSON>  
        INTO @jobHistoryId, @jobid, @jobProgress, @changedDate, @modifiedBy 
        FROM Job AS j WHERE j.Id = NEW.Id;
            
		INSERT INTO JobProgressHistory 
		SELECT @jobHistoryId, @jobid, @jobProgress, @changedDate, @modifiedBy;

		UPDATE WipRecord
		SET JobProgress = (SELECT @jobProgress), 
            DurationAtCurrentProgress = (SELECT @changedDate)
		WHERE Id = NEW.Id;
	END IF;
END;