﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class EquipmentPlaced
    {
        public class Event : EquipmentPlacedEvent, IRequest
        {
            public Event(EquipmentPlacedDto equipmentPlaced, Guid correlationId) : base(equipmentPlaced, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<Handler> _logger;
            private readonly IEquipmentServiceClient _equipmentServiceClient;

            public Handler(ILogger<Handler> logger, JobDataContext context, IEquipmentServiceClient equipmentServiceClient)
            {
                _logger = logger;
                _context = context;
                _equipmentServiceClient = equipmentServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} Handler Activated", nameof(EquipmentPlacedEvent));
                var dto = request.EquipmentPlaced;

                var job = await _context.Jobs
                    .Include(j=> j.JobAreas)
                    .FirstOrDefaultAsync(q => q.Id == dto.JobId, cancellationToken: cancellationToken);
                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {dto.JobId}");

                await JobPlaceEquipments(job, dto, cancellationToken);

                _logger.LogInformation("{event} Handler Completed Successfully", nameof(EquipmentPlacedEvent));
                return Unit.Value;
            }

            private async Task JobPlaceEquipments(Job job, EquipmentPlacedDto dto, CancellationToken cancellationToken)
            {
                var equipments = dto.Placements.ToList();

                var equipmentToAddIds = equipments.Select(x => x.EquipmentId).ToList();
                var existingEquipment = (await _context.Equipments
                    .Include(x => x.EquipmentModel)
                    .ThenInclude(x => x.EquipmentType)
                    .Where(x => equipmentToAddIds.Contains(x.Id))
                    .ToListAsync(cancellationToken));

                var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == dto.RoomId);

                if (jobArea is null)
                {
                    //verify default jobArea
                    jobArea = job.JobAreas.FirstOrDefault(q => q.JobAreaTypeId == JobAreaTypes.Job);

                    if (jobArea == null)
                    {
                        jobArea = new Models.Drybook.JobArea
                        {
                            Id = Guid.NewGuid(),
                            CreatedBy = dto.Username,
                            CreatedDate = DateTime.UtcNow,
                            JobId = dto.JobId,
                            Name = "Default Job Room",
                            JobAreaTypeId = JobAreaTypes.Job
                        };

                        job.JobAreas.Add(jobArea);
                    }
                }

                var equipmentToInsertIds = equipmentToAddIds
                    .Where(x => !existingEquipment.Select(x => x.Id).ToList().Contains(x))
                    .ToList();
                _logger.LogInformation("{event} - {EquipmentsToInsert} equipments to be inserted to job-service db", nameof(EquipmentPlacedEvent), equipmentToInsertIds.Count);

                if(equipmentToInsertIds.Count > 0)
                {
                    var equipmentToInsertEquipmentService = (await _equipmentServiceClient.GetEquipmentByListAsync(job.FranchiseSetId, equipmentToInsertIds, true, cancellationToken));
                    _logger.LogInformation("{event} - {EquipmentsFound} equipments found in EquipmentService", nameof(EquipmentPlacedEvent), equipmentToInsertEquipmentService.Equipments.Count());

                    //Before inserting new Equipments, insert the rest of Equipment information
                    var existingEquipmentTypes = await _context.EquipmentTypes
                        .ToDictionaryAsync(x => x.Id, cancellationToken: cancellationToken);
                    var existingEquipmentModel = await _context.EquipmentModels
                        .ToDictionaryAsync(x => x.Id, cancellationToken: cancellationToken);

                    var newEquipmentTypes = equipmentToInsertEquipmentService.Equipments
                        .Where(x => !existingEquipmentTypes.ContainsKey(x.EquipmentModel.EquipmentTypeId))
                        .GroupBy(x => x.EquipmentModel.EquipmentTypeId)
                        .Select(x => new Models.Drybook.EquipmentType
                        {
                            Id = x.First().EquipmentModel.EquipmentTypeId,
                            BaseEquipmentTypeId = x.First().EquipmentModel.EquipmentType.BaseEquipmentTypeId,
                            CreatedBy = nameof(EquipmentPlacedEvent),
                            CreatedDate = DateTime.UtcNow,
                            FranchiseSetId = x.First().EquipmentModel.EquipmentType.FranchiseSetId,
                            IsDeleted = x.First().EquipmentModel.EquipmentType.IsDeleted,
                            Name = x.First().EquipmentModel.EquipmentType.Name
                        });

                    var newEquipmentModels = equipmentToInsertEquipmentService.Equipments
                        .Where(x => !existingEquipmentModel.ContainsKey(x.EquipmentModelId))
                        .GroupBy(x => x.EquipmentModelId)
                        .Select(x => new Models.Drybook.EquipmentModel
                        {
                            Id = x.First().EquipmentModelId,
                            Amps = x.First().EquipmentModel.Amps,
                            CreatedBy = nameof(EquipmentPlacedEvent),
                            CreatedDate = DateTime.UtcNow,
                            EquipmentTypeId = x.First().EquipmentModel.EquipmentTypeId,
                            FranchiseSetId = x.First().EquipmentModel.FranchiseSetId,
                            IsDeleted = x.First().EquipmentModel.IsDeleted,
                            ManufacturerName = x.First().EquipmentModel.ManufacturerName,
                            Description = x.First().EquipmentModel.Description,
                            CubicFeetPerMinute = x.First().EquipmentModel.CubicFeetPerMinute,
                            IsCurrent = x.First().EquipmentModel.IsCurrent,
                            IsNotPenetratingMeter = x.First().EquipmentModel.IsNotPenetratingMeter,
                            IsPenetratingMeter = x.First().EquipmentModel.IsPenetratingMeter,
                            IsSymbol = x.First().EquipmentModel.IsSymbol,
                            IsValidModel = x.First().EquipmentModel.IsValidModel,
                            IsSystem = x.First().EquipmentModel.IsValidModel,
                            ManufacturerModelNumber = x.First().EquipmentModel.ManufacturerModelNumber,
                            Name = x.First().EquipmentModel.Name,
                            Notes = x.First().EquipmentModel.Notes,
                            PintsPerDay = x.First().EquipmentModel.PintsPerDay
                        });

                    _context.EquipmentTypes.AddRange(newEquipmentTypes);
                    _context.EquipmentModels.AddRange(newEquipmentModels);

                    //Insert new Equipments
                    var equipmentsToInsert = equipmentToInsertEquipmentService.Equipments.Select(Map);
                    _context.Equipments.AddRange(equipmentsToInsert);

                    existingEquipment.AddRange(equipmentsToInsert);
                }

                foreach (var item in existingEquipment)
                {
                    jobArea.EquipmentPlacements ??= new List<EquipmentPlacement>();

                    var itemInfo = equipments.FirstOrDefault(q=> q.EquipmentId == item.Id);

                    jobArea.EquipmentPlacements.Add(new EquipmentPlacement
                    {
                        CreatedBy = dto.Username,
                        CreatedDate = DateTime.UtcNow,
                        BeginDate = DateTime.UtcNow,
                        EquipmentId = item.Id,
                        IsUsedInValidation = false
                    });
                }

                //Lastly, log any Equipment that wasn't able to be inserted
                var equipmentsNotInserted = equipmentToAddIds
                    .Where(x => !existingEquipment.Select(x => x.Id).Contains(x))
                    .ToList();

                foreach (var equipmentId in equipmentsNotInserted)
                    _logger.LogInformation("Equipment with Id {id} was not inserted. It wasn't found in equipment-service", equipmentId);

                await _context.SaveChangesAsync(cancellationToken);
            }

            Models.Drybook.Equipment Map(GetEquipmentByListDto.EquipmentDto dto)
            {
                return new Models.Drybook.Equipment
                {
                    Id = dto.Id,
                    AssetNumber = dto.AssetNumber,
                    FranchiseSetId = dto.FranchiseSetId,
                    CreatedBy = nameof(EquipmentPlacedEvent),
                    SerialNumber = dto.SerialNumber,
                    CreatedDate = DateTime.UtcNow,
                    IsDeleted = dto.IsDeleted,
                    Notes = dto.Notes,
                    EquipmentModelId = dto.EquipmentModelId
                };
            }
        }
    }
}
