﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Xact.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Xact.Narratives;
using Servpro.Franchise.JobService.Infrastructure.XactService;

using System;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetNarrativePreview
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; set; }
        }

        public class Dto
        {
            public string NarrativeText { get; set; }
            public DateTime CreatedDate { get; set; }
            public string CreatedBy { get; set; }
            public bool AllowManualTrx { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IXactServiceClient _xactServiceClient;
            private readonly INarrativePreviewCommandFactory _narrativePreviewCommandFactory;

            public Handler(JobReadOnlyDataContext db,
                           ILogger<Handler> logger,
                           IXactServiceClient xactServiceClient,
                           INarrativePreviewCommandFactory narrativePreviewCommandFactory)
            {
                _db = db;
                _logger = logger;
                _xactServiceClient = xactServiceClient;
                _narrativePreviewCommandFactory = narrativePreviewCommandFactory;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.AnyAsync(j => j.Id == request.JobId, cancellationToken);

                if (!job)
                {
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");
                }

                _logger.LogDebug("[GetNarrativePreview.Handle] Found job {jobId}: {@job}", request.JobId, job);

                var narrativePreviewRequest =
                    await _narrativePreviewCommandFactory.GetNarrativeRequest(request.JobId, cancellationToken);

                _logger.LogDebug("[GetNarrativePreview.Handle] Narrative preview request job {jobId}: {@narrativePreviewRequest}", request.JobId, narrativePreviewRequest);

                _logger.LogDebug("[GetNarrativePreview.Handle] Sending request for job {jobId} to Xact Service: {@request}", request.JobId, narrativePreviewRequest);
                var narrativePreviewResponse = await _xactServiceClient.GetNarrativePreview(narrativePreviewRequest, cancellationToken);
                _logger.LogDebug("[GetNarrativePreview.Handle] Received response for job {jobId} from Xact Service: {@response}", request.JobId, narrativePreviewResponse);

                return MapResponse(narrativePreviewResponse);
            }

            private static Dto MapResponse(GetNarrativePreviewResponseDto responseDto)
            => new Dto
            {
                CreatedBy = responseDto.CreatedBy,
                CreatedDate = responseDto.CreatedUtc,
                NarrativeText = responseDto.NarrativeText,
                AllowManualTrx = responseDto.AllowSend
            };
        }
    }
}