using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Xact;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Xact;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.JobUpload
{
    public class UpdateMediaUploadedToCorporate
    {
        #region Command

        public class Command : IRequest<Guid>
        {
            public Command(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }
        #endregion Command

        #region Handler
        public class Handler : IRequestHandler<Command, Guid>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IMediator _mediator;

            public Handler(JobDataContext db,
                ILogger<Handler> logger, IMediator mediator)
            {
                _db = db;
                _logger = logger;
                _mediator = mediator;
            }

            public async Task<Guid> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{@command} received.", request);

                var resultArtifacts = await _mediator.Send(new GetJobArtifactsForXactUpload.Query(request.JobId), cancellationToken);
                List<Guid> updateElements = new List<Guid>();
                if (resultArtifacts != null)
                {
                    resultArtifacts.MediaMetadata?.ToList().ForEach(media =>
                    {
                        if (media != null && media.IsForUpload)
                        {
                            updateElements.Add(media.Id);
                        }
                        
                    });
                    resultArtifacts.JobFormMetadata?.ToList().ForEach(media =>
                    {
                        if (media != null && media.IsForUpload)
                        {
                            updateElements.Add(media.Id);
                        }
                    });
                }
                var uploadedMedia = await _db.MediaMetadata
                    .Where(mmdt => mmdt.JobId == request.JobId && updateElements.Contains(mmdt.Id) && (mmdt.UploadedSuccessfully|| mmdt.JobSketchId!=null))
                    .ToListAsync(cancellationToken);

                uploadedMedia.ForEach(f => { f.IsUploadedToXact = true; });

                await _db.SaveChangesAsync(cancellationToken);

                return request.JobId;
            }
        }
        #endregion Handler
    }
}
