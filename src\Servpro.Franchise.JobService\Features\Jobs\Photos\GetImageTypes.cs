﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class GetImageTypes
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Query(Guid id, Guid franchiseSetId)
            {
                JobId = id;
                FranchiseSetId = franchiseSetId;
            }
            public Guid JobId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string Name { get; set; }

        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;
            private readonly IConfiguration _config;
            private readonly ILookupServiceClient _lookupServiceClient;

            public Handler(JobReadOnlyDataContext db, IConfiguration config,
                IAmazonS3 clientS3, ILookupServiceClient lookupServiceClient)
            {
                _db = db;
                _config = config;
                _lookupServiceClient = lookupServiceClient;
            }

            public async Task<ICollection<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var mediaMetaData = await _db.MediaMetadata
                    .Where(x => x.JobId == request.JobId &&
                        x.FranchiseSetId == request.FranchiseSetId &&
                        !x.IsDeleted &&
                        (x.MediaTypeId == MediaTypes.Photo ||
                        x.MediaTypeId == MediaTypes.Sketch)).ToListAsync(cancellationToken);

                var medias = mediaMetaData.DistinctBy(x => x.ArtifactTypeId);
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                return medias.Select(x => Map(x, lookups))
                    .Where(x => x != null).ToList();
            }

            private Dto Map(MediaMetadata media, GetLookups.Dto lookups)
            {
                var artifactType = lookups.ArtifactTypes.FirstOrDefault(x => x.Id == media.ArtifactTypeId);
                if (artifactType == null)
                    return null;
                return new Dto
                {
                    Id = artifactType.Id,
                    Name = artifactType.Name
                };
            }
        }
    }
}
