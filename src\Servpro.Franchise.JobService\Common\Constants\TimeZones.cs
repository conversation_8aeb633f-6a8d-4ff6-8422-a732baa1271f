﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Common.Constants
{
    public static class TimeZones
    {
        public static readonly Guid AlaskanStandardTime = new Guid("459CEDE4-086A-4A5D-8229-64916244902D");
        public static readonly Guid AtlanticStandardTimeCanada = new Guid("C1B3ABA7-699A-419F-BB9F-9A0DA3251619");
        public static readonly Guid CanadaCentralStandardTime = new Guid("5D3DD1BF-5B6C-460A-A9FC-8D3856A0AF19");
        public static readonly Guid CentralStandardTime = new Guid("7465263F-C141-4785-A473-D9D998A3D057");
        public static readonly Guid EasternStandardTime = new Guid("E8B0C182-FC64-4DDB-AC26-793BD456B2F7");
        public static readonly Guid HawaiianStandardTime = new Guid("DF1639B6-1C0C-4583-A2CC-89576640FDCD");
        public static readonly Guid MountainStandardTime = new Guid("A5358F44-508B-4358-8222-655C4E5C1694");
        public static readonly Guid NewfoundlandStandardTime = new Guid("19A84479-7280-4DE0-BD88-94F8309F7A0D");
        public static readonly Guid PacificStandardTime = new Guid("CD08DC96-9B00-43D7-BF1E-325AC6E38175");
        public static readonly Guid PacificStandardTimeMexico = new Guid("94C8D4F4-137B-4D45-957A-C8F81ECA7655");
        public static readonly Guid SAWesternStandardTime = new Guid("81BCBE06-9A03-4260-8D60-8345C34B9FE5");
        public static readonly Guid USEasternStandardTimeCanada = new Guid("B757AFA8-23A6-463A-80D4-DE62C46EC15E");
        public static readonly Guid USEasternStandardTime = new Guid("D7A6D917-A949-41E3-9370-2DFAB8600310");
        public static readonly Guid USMountainStandardTime = new Guid("6A4A8D0C-B0E8-461F-A452-2FA674C53D9B");
        public static readonly Guid UTC = new Guid("97E86D72-33F0-419F-9324-BC85EEE2E944");
    }

    public static class TimeZoneHelper
    {
        public static string GetWindowsTimeZoneIdentifier(Guid timeZoneId)
        {
            if (timeZoneId == TimeZones.AlaskanStandardTime) return "Alaskan Standard Time";
            if (timeZoneId == TimeZones.AtlanticStandardTimeCanada) return "Atlantic Standard Time";
            if (timeZoneId == TimeZones.CanadaCentralStandardTime) return "Canada Central Standard Time";
            if (timeZoneId == TimeZones.CentralStandardTime) return "Central Standard Time";
            if (timeZoneId == TimeZones.EasternStandardTime) return "Eastern Standard Time";
            if (timeZoneId == TimeZones.HawaiianStandardTime) return "Hawaiian Standard Time";
            if (timeZoneId == TimeZones.MountainStandardTime) return "Mountain Standard Time";
            if (timeZoneId == TimeZones.NewfoundlandStandardTime) return "Newfoundland Standard Time";
            if (timeZoneId == TimeZones.PacificStandardTime) return "Pacific Standard Time";
            if (timeZoneId == TimeZones.PacificStandardTimeMexico) return "Pacific Standard Time (Mexico)";
            if (timeZoneId == TimeZones.SAWesternStandardTime) return "SA Western Standard Time";
            if (timeZoneId == TimeZones.USEasternStandardTimeCanada) return "US Eastern Standard Time";
            if (timeZoneId == TimeZones.USEasternStandardTime) return "US Eastern Standard Time";
            if (timeZoneId == TimeZones.USMountainStandardTime) return "US Mountain Standard Time";
            return "UTC";
        }
    }
}