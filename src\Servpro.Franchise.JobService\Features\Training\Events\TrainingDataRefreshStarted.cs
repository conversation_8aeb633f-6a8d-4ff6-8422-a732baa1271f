﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Training;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using TdrFranchiseSetStatus = Servpro.FranchiseSystems.Framework.Messaging.Events.Training.ServiceTrainingDataRefreshedEvent.FranchiseSetStatus;

namespace Servpro.Franchise.JobService.Features.Training.Events
{
    public class TrainingDataRefreshStarted
    {
        public class Event : TrainingDataRefreshStartedEvent, IRequest
        {
            public Event(Guid templateFranchiseSetId, List<Guid> trainingFranchiseSetIds, string createdBy,
                DateTime createdDate, Guid correlationId) : base(templateFranchiseSetId, trainingFranchiseSetIds,
                createdBy, createdDate, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<TrainingDataRefreshStarted.Event>
        {
            private readonly ILogger<TrainingDataRefreshStarted> _logger;
            private readonly JobDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IConfiguration _config;

            public Handler(ILogger<TrainingDataRefreshStarted> logger, JobDataContext context, IFranchiseServiceClient franchiseServiceClient, IConfiguration config)
            {
                _logger = logger;
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _config = config;
            }

            public async Task<Unit> Handle(TrainingDataRefreshStarted.Event incomingEvent,
                CancellationToken cancellationToken)
            {
                var trainingFranchiseSetIds = incomingEvent.TrainingFranchiseSetIds;
                var templateFranchiseSetId = incomingEvent.TemplateFranchiseSetId;

                string environment = _config.GetValue<string>("ENVIRONMENT");
                bool unitTesting = _config.GetValue("IntegrationTesting", false);
                if (environment != "train" && !unitTesting)
                {
                    _logger.LogWarning("{eventName} tried to run in an environment other than training, the environment trying to run: {env}", nameof(TrainingDataRefreshStartedEvent), environment);
                    return Unit.Value;
                }
                if (templateFranchiseSetId.IsNullOrEmpty() || !trainingFranchiseSetIds.Any())
                {
                    _logger.LogWarning("Required data missing on event, event received: {data}", @incomingEvent);
                    return Unit.Value;
                }
                if (trainingFranchiseSetIds.Contains(templateFranchiseSetId))
                {
                    _logger.LogWarning("Training franchise sets to refresh cannot contain the template franchise set. Event received: {data}", @incomingEvent);
                    return Unit.Value;
                }

                var tdrResultEvent = GenerateTrainingDataRefreshed(trainingFranchiseSetIds, incomingEvent.RequesterEmail, incomingEvent.CorrelationId);

                try
                {
                    _logger.LogInformation("Removing old data for training franchise sets");
                    await RemoveOldData(trainingFranchiseSetIds, templateFranchiseSetId, cancellationToken);

                    _logger.LogInformation("Copying data from template franchise set to training franchise sets");
                    await Refresh(tdrResultEvent.FranchiseSets, templateFranchiseSetId, cancellationToken);
                }
                catch (DbUpdateException duex)
                {
                    _logger.LogError(duex, "Database update exception occurred during the Training Data Refresh");
                    // If we run into a DbUpdateException, stop tracking the entities causing the issue so that
                    // we can still publish the event (holds up the database otherwise)
                    var entries = duex.Entries;
                    foreach (var entry in entries)
                    {
                        entry.State = EntityState.Detached;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during the Training Data Refresh");
                }

                _logger.LogInformation("Adding TDR Result event to OutboxMessages.");
                await _context.OutboxMessages.AddAsync(GenerateOutboxMessage(tdrResultEvent, incomingEvent.CreatedBy), cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                await SetIsLeadFullySavedOnAllRefreshedWipRecords(trainingFranchiseSetIds, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task RemoveOldData(List<Guid> trainingFranchiseSetIds, Guid templateFranchiseSetId, CancellationToken cancellationToken)
            {
                try
                {
                    var trainingFranchiseSetStrIds = trainingFranchiseSetIds.Select(x => x.ToString().ToLower()).ToList();

                    var oldJobs = await _context.Jobs
                            .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                            .ToListAsync(cancellationToken);
                    var oldJobIds = oldJobs.Select(x => x.Id).ToList();
                    var oldMobileDataIds = oldJobs.Select(x => x.MobileDataId).ToList();

                    var oldMobileData = await _context.MobileData
                        .Where(x => oldMobileDataIds.Contains(x.Id))
                        .ToListAsync(cancellationToken);

                    var oldMarketingCalls = await _context.ExternalMarketingCalls
                        .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                        .ToListAsync(cancellationToken);

                    var oldBusinesses = await _context.Businesses
                        .Where(x => (x.FranchiseSetId.HasValue && trainingFranchiseSetIds.Contains(x.FranchiseSetId.Value)))
                        .ToListAsync(cancellationToken);

                    var orphanedBusinesses = await GetOrphanedBusinessesAsync(trainingFranchiseSetIds, templateFranchiseSetId, cancellationToken);
                    oldBusinesses.AddRange(orphanedBusinesses);

                    var orphanedMobileData = await GetOrphanedMobileDataAsync(trainingFranchiseSetIds, oldMobileDataIds, cancellationToken);
                    oldMobileData.AddRange(orphanedMobileData);

                    var oldBusinessIds = oldBusinesses.Select(x => x.Id).ToList();
                    var oldWipCustom = await _context.FranchiseSetWipColumnCustomization
                        .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                        .ToListAsync(cancellationToken);
                    var oldStorms = await _context.Storms
                        .Where(x => trainingFranchiseSetIds.Contains(x.HostFranchiseSetId))
                        .ToListAsync(cancellationToken);
                    var oldStormEvents = await _context.StormEvents
                        .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                        .ToListAsync(cancellationToken);
                    var oldWipRecord = await _context.WipRecords
                        .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                        .ToListAsync(cancellationToken);
                    var oldEquipmentTypes = await _context.EquipmentTypes
                        .Where(x => x.FranchiseSetId.HasValue && trainingFranchiseSetIds.Contains(x.FranchiseSetId.Value))
                        .ToListAsync(cancellationToken);

                    var oldContacts = await _context.Contacts
                        .Where(x => x.BusinessId.HasValue && oldBusinessIds.Contains(x.BusinessId.Value))
                        .ToListAsync(cancellationToken);
                    var oldContactIds = oldContacts.Select(x => x.Id).ToList();

                    var oldEquipmentModels = await _context.EquipmentModels
                        .Where(x => x.FranchiseSetId.HasValue && trainingFranchiseSetIds.Contains(x.FranchiseSetId.Value))
                        .ToListAsync(cancellationToken);

                    var oldEquipment = await _context.Equipments
                        .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                        .ToListAsync(cancellationToken);

                    var oldJobLocks = await _context.JobLock
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldJobProgressHistory = await _context.JobProgressHistory
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldJobContactMap = await _context.JobContactMap
                        .Where(x => oldContactIds.Contains(x.ContactId))
                        .ToListAsync(cancellationToken);
                    var oldJobBusinessMap = await _context.JobBusinessMaps
                        .Where(x => oldContactIds.Contains(x.BusinessId))
                        .ToListAsync(cancellationToken);
                    var oldZones = await _context.Zones
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldZoneIds = oldZones.Select(x => x.Id).ToList();
                    var oldJobVisits = await _context.JobVisit
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldJobVisitIds = oldJobVisits.Select(x => x.Id).ToList();
                    var oldJobMaterials = await _context.JobMaterials
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldJobTriStateAnswers = await _context.JobTriStateAnswers
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldJobActionLocations = await _context.JobActionLocations
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);

                    var oldTasks = await _context.Tasks
                        .Where(x => (x.JobVisitId.HasValue && oldJobVisitIds.Contains(x.JobVisitId.Value)) || (x.JobId.HasValue && oldJobIds.Contains(x.JobId.Value)))
                        .ToListAsync(cancellationToken);
                    var oldJobVisitTriStateAnswers = await _context.JobVisitTriStateAnswers
                        .Where(x => oldJobVisitIds.Contains(x.JobVisitId))
                        .ToListAsync(cancellationToken);

                    var oldJournalNotes = await _context.JournalNote
                        .Where(x => x.JobId.HasValue && oldJobIds.Contains(x.JobId.Value))
                        .ToListAsync(cancellationToken);
                    var oldJournalNoteIds = oldJournalNotes.Select(x => x.Id).ToList();

                    var oldJobAreas = await _context.JobAreas
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldJobAreaIds = oldJobAreas.Select(x => x.Id).ToList();
                    var oldJobAreaRoomIds = oldJobAreas.Select(x => x.RoomId).ToList();

                    var oldRooms = await _context.Rooms
                        .Where(x => oldJobAreaRoomIds.Contains(x.Id))
                        .ToListAsync(cancellationToken);

                    var oldRoomIds = oldRooms.Select(x => x.Id).ToList();
                    var oldZoneReadings = await _context.ZoneReadings
                        .Where(x => oldZoneIds.Contains(x.ZoneId))
                        .ToListAsync(cancellationToken);

                    var oldRoomFlooringTypesAffected = await _context.RoomFlooringTypesAffected
                        .Where(x => oldRoomIds.Contains(x.RoomId))
                        .ToListAsync(cancellationToken);

                    var oldLineItems = await _context.LineItems
                        .Where(x => oldJobIds.Contains(x.JobId))
                        .ToListAsync(cancellationToken);
                    var oldLineItemIds = oldLineItems.Select(x => x.Id).ToList();

                    var oldLineItemsNotes = await _context.LineItemNotes
                        .Where(x => x.LineItemId.HasValue && oldLineItemIds.Contains(x.LineItemId.Value))
                        .ToListAsync(cancellationToken);
                    var oldMediaMetadata = await _context.MediaMetadata
                        .Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId))
                        .ToListAsync(cancellationToken);
                    var oldMediaMetaDataIds = oldMediaMetadata.Select(x => x.Id).ToList();
                    var oldEquipmentPlacements = await _context.EquipmentPlacements
                        .Where(x => oldJobAreaIds.Contains(x.JobAreaId))
                        .ToListAsync(cancellationToken);
                    var oldEquipmentPlacementIds = oldEquipmentPlacements.Select(x => x.Id).ToList();
                    var oldJobAreaMaterials = await _context.JobAreaMaterials
                        .Where(x => oldJobAreaIds.Contains(x.JobAreaId))
                        .ToListAsync(cancellationToken);
                    var oldJobAreaMaterialIds = oldJobAreaMaterials.Select(x => x.Id).ToList();

                    var oldJobSketches = await _context.JobSketch
                        .Where(x => oldMediaMetaDataIds.Contains(x.MediaMetadataId))
                        .ToListAsync(cancellationToken);
                    var oldJobInvoices = await _context.JobInvoices
                        .Where(x => oldMediaMetaDataIds.Contains(x.MediaMetadataId))
                        .ToListAsync(cancellationToken);
                    var oldEquipmentPlacementReadings = await _context.EquipmentPlacementReadings
                        .Where(x => oldEquipmentPlacementIds.Contains(x.EquipmentPlacementId))
                        .ToListAsync(cancellationToken);
                    var oldJobAreaMaterialReadings = await _context.JobAreaMaterialReadings
                        .Where(x => oldJobAreaMaterialIds.Contains(x.JobAreaMaterialId))
                        .ToListAsync(cancellationToken);

                    // Save in this specific order to prevent FK constraints
                    // This is broke into multiple saves because calling RemoveRange in the correct order is not enough
                    _context.WipRecords.RemoveRange(oldWipRecord);
                    _context.ExternalMarketingCalls.RemoveRange(oldMarketingCalls);
                    await _context.SaveChangesAsync(cancellationToken);
                    _context.JobSketch.RemoveRange(oldJobSketches);
                    _context.JobInvoices.RemoveRange(oldJobInvoices);
                    _context.EquipmentPlacementReadings.RemoveRange(oldEquipmentPlacementReadings);
                    _context.JobAreaMaterialReadings.RemoveRange(oldJobAreaMaterialReadings);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.LineItemNotes.RemoveRange(oldLineItemsNotes);
                    _context.MediaMetadata.RemoveRange(oldMediaMetadata);
                    _context.EquipmentPlacements.RemoveRange(oldEquipmentPlacements);
                    _context.JobAreaMaterials.RemoveRange(oldJobAreaMaterials);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.RoomFlooringTypesAffected.RemoveRange(oldRoomFlooringTypesAffected);
                    _context.JobAreas.RemoveRange(oldJobAreas);
                    _context.LineItems.RemoveRange(oldLineItems);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.ZoneReadings.RemoveRange(oldZoneReadings);
                    _context.Rooms.RemoveRange(oldRooms);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.JournalNote.RemoveRange(oldJournalNotes);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.Tasks.RemoveRange(oldTasks);
                    _context.JobVisitTriStateAnswers.RemoveRange(oldJobVisitTriStateAnswers);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.JobLock.RemoveRange(oldJobLocks);
                    _context.JobProgressHistory.RemoveRange(oldJobProgressHistory);
                    _context.JobContactMap.RemoveRange(oldJobContactMap);
                    _context.Zones.RemoveRange(oldZones);
                    _context.JobBusinessMaps.RemoveRange(oldJobBusinessMap);
                    _context.JobVisit.RemoveRange(oldJobVisits);
                    _context.JobMaterials.RemoveRange(oldJobMaterials);
                    _context.JobTriStateAnswers.RemoveRange(oldJobTriStateAnswers);
                    _context.JobActionLocations.RemoveRange(oldJobActionLocations);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.Equipments.RemoveRange(oldEquipment);
                    _context.Jobs.RemoveRange(oldJobs);
                    await _context.SaveChangesAsync(cancellationToken);

                    await RemoveContactsDirectlyOnJobs(oldContacts, cancellationToken);

                    _context.Contacts.RemoveRange(oldContacts);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.EquipmentModels.RemoveRange(oldEquipmentModels);
                    await _context.SaveChangesAsync(cancellationToken);

                    _context.FranchiseSetWipColumnCustomization.RemoveRange(oldWipCustom);
                    _context.Businesses.RemoveRange(oldBusinesses);
                    _context.MobileData.RemoveRange(oldMobileData);
                    _context.EquipmentTypes.RemoveRange(oldEquipmentTypes);
                    _context.Storms.RemoveRange(oldStorms);
                    _context.StormEvents.RemoveRange(oldStormEvents);
                    await _context.SaveChangesAsync(cancellationToken);
                }
                catch(Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during Removal of old data");
                    throw;
                }
            }

            private async Task RemoveContactsDirectlyOnJobs(List<Contact> contacts, CancellationToken cancellationToken)
            {
                var contactIds = contacts.Select(c => c.Id).ToList();
                var callerJobs = await _context.Jobs.Where(x => x.CallerId.HasValue && contactIds.Contains(x.CallerId.Value)).ToListAsync(cancellationToken);
                foreach (var callerJob in callerJobs)
                {
                    callerJob.Caller = null;
                    callerJob.CallerId = null;
                }
                var customerJobs = await _context.Jobs.Where(x => x.CustomerId.HasValue && contactIds.Contains(x.CustomerId.Value)).ToListAsync(cancellationToken);
                foreach (var customerJob in customerJobs)
                {
                    customerJob.Customer = null;
                    customerJob.CustomerId = null;
                }
                await _context.SaveChangesAsync(cancellationToken);
            }


            private async Task<List<Business>> GetOrphanedBusinessesAsync(List<Guid> trainingFranchiseSetIds, Guid templateFranchiseSetId, CancellationToken cancellationToken)
            {
                // We are considering orphaned businesses as businesses that have a null franchiseSetId AND were created by a franchise (first 8 chars of 
                // the franchiseSetId are the same as the training franchise set).  This seems to happen in certain cases when createing a Contact with a business
                // In order to clean these up, we are having to select them based on that criteria. Unfortunately, string comparisons arent
                // easily done with Guids (it has to be converted to a string in comparison for the 8 char comparison). EF Core doesnt have a good way to convert this
                // thus we are simply using a Raw query that selects them individually.

                // we should always use parameters with FromSqlRaw to avoid SQL Injection attacks.  Sast will complain about this.
                // even if it looks like there is no way to inject sql. Thus {0} and {1} in the queries.
                var orphanedBusinesses = new List<Business>();
                foreach (var trainingFranchiseSetId in trainingFranchiseSetIds)
                {
                    var franchiseSetKey = trainingFranchiseSetId.ToString().ToLower().Substring(0, 8) + "%";
                    var query = "SELECT * FROM Business WHERE (FranchiseSetId is null or FranchiseSetId like {0}) and Id like {1}";
                    var businesses = await _context.Businesses.FromSqlRaw(query, templateFranchiseSetId, franchiseSetKey).ToListAsync(cancellationToken);
                    orphanedBusinesses.AddRange(businesses);
                }

                return orphanedBusinesses;
            }

            /// <summary>
            /// Get Mobile Data entities that are not currently associated to a job
            /// </summary>
            /// <param name="trainingFranchiseSetIds"></param>
            /// <param name="jobAssociatedIds"></param>
            /// <param name="cancellationToken"></param>
            /// <returns></returns>
            private async Task<List<MobileData>> GetOrphanedMobileDataAsync(List<Guid> trainingFranchiseSetIds, List<Guid?> jobAssociatedIds, CancellationToken cancellationToken)
            {
                var orphanedMobileData = new List<MobileData>();
                foreach (var trainingFranchiseSetId in trainingFranchiseSetIds)
                {
                    // We are considering orphaned MobileData as entities that begin with the franchiseSetKey that is being refreshed
                    // but is not associated to a job

                    // we should always use parameters with FromSqlRaw to avoid SQL Injection attacks.  Sast will complain about this.
                    // even if it looks like there is no way to inject sql. Thus {0} in the queries.
                    var franchiseSetKey = trainingFranchiseSetId.ToString().ToLower().Substring(0, 8) + "%";
                    var query = "SELECT * FROM MobileData WHERE Id like {0}";
                    var mobileData = await _context.MobileData.FromSqlRaw(query, franchiseSetKey).ToListAsync(cancellationToken);
                    var orphans = mobileData.Where(x => !jobAssociatedIds.Contains(x.Id));
                    orphanedMobileData.AddRange(orphans);
                }

                return orphanedMobileData;
            }

            private async Task Refresh(List<TdrFranchiseSetStatus> franchiseStatuses, Guid templateFranchiseSetId, CancellationToken cancellationToken)
            {
                var templateEquipment = await _context.Equipments
                    .Where(x => x.FranchiseSetId == templateFranchiseSetId)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
                var templateEquipmentModels = await _context.EquipmentModels
                    .Where(x => x.FranchiseSetId == templateFranchiseSetId)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
                var templateWipCustom = await _context.FranchiseSetWipColumnCustomization
                    .Where(x => x.FranchiseSetId == templateFranchiseSetId)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
                var templateStormEvents = await _context.StormEvents
                    .Where(x => x.FranchiseSetId == templateFranchiseSetId)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
                var templateExternalMarketingCalls = await _context.ExternalMarketingCalls
                    .Where(x => x.FranchiseSetId == templateFranchiseSetId)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
                var templateJobIds = await _context.Jobs
                    .Where(x => x.FranchiseSetId == templateFranchiseSetId)
                    .AsNoTracking()
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                foreach (var trainingFranchiseSetStatus in franchiseStatuses)
                {
                    var trainingFranchiseSetId = trainingFranchiseSetStatus.FranchiseSetId;
                    using var _ = _logger.BeginScope("{trainingFranchiseSetId}", trainingFranchiseSetId);
                    try
                    {
                        if (templateEquipment != null && templateEquipment.Count > 0)
                        {
                            templateEquipment = templateEquipment.Select(x =>
                            {
                                x.Id = GuidTransformHelpers.TransformToManipulatedGuid(x.Id, trainingFranchiseSetId).Value;
                                x.FranchiseSetId = trainingFranchiseSetId;
                                return x;
                            }).ToList();
                            await _context.Equipments.AddRangeAsync(templateEquipment, cancellationToken);
                        };

                        if (templateEquipmentModels != null && templateEquipmentModels?.Count > 0)
                        {
                            templateEquipmentModels = templateEquipmentModels.Select(x =>
                            {
                                x.Id = GuidTransformHelpers.TransformToManipulatedGuid(x.Id, trainingFranchiseSetId).Value;
                                x.FranchiseSetId = trainingFranchiseSetId;
                                return x;
                            }).ToList();
                            await _context.EquipmentModels.AddRangeAsync(templateEquipmentModels, cancellationToken);
                        };

                        if (templateWipCustom != null && templateWipCustom?.Count > 0)
                        {
                            templateWipCustom = templateWipCustom.Select(x =>
                            {
                                x.Id = GuidTransformHelpers.TransformToManipulatedGuid(x.Id, trainingFranchiseSetId).Value;
                                x.FranchiseSetId = trainingFranchiseSetId;
                                return x;
                            }).ToList();
                            await _context.FranchiseSetWipColumnCustomization.AddRangeAsync(templateWipCustom, cancellationToken);
                        };

                        if (templateStormEvents != null && templateStormEvents?.Count > 0)
                        {
                            templateStormEvents = templateStormEvents.Select(x =>
                            {
                                x.Id = GuidTransformHelpers.TransformToManipulatedGuid(x.Id, trainingFranchiseSetId).Value;
                                x.FranchiseSetId = trainingFranchiseSetId;
                                return x;
                            }).ToList();
                            await _context.StormEvents.AddRangeAsync(templateStormEvents, cancellationToken);
                        };

                        var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(trainingFranchiseSetId, cancellationToken);

                        if (templateExternalMarketingCalls != null && templateExternalMarketingCalls?.Count > 0)
                        {
                            templateExternalMarketingCalls = templateExternalMarketingCalls.Select(x =>
                            {
                                x.Id = GuidTransformHelpers.TransformToManipulatedGuid(x.Id, trainingFranchiseSetId).Value;
                                x.FranchiseSetId = trainingFranchiseSetId;
                                x.FranchiseId = franchiseSet.Franchises.First().Id;
                                x.CallReceivedDateTime = DateTime.UtcNow;
                                return x;
                            }).ToList();
                            await _context.ExternalMarketingCalls.AddRangeAsync(templateExternalMarketingCalls, cancellationToken);
                        }


                        var businessIdsCache = new HashSet<Guid>();

                        foreach (var templateJobId in templateJobIds)
                        {
                            using var scope = _logger.BeginScope("{templateJobId}", templateJobId);
                            var templateJob = await GetJob(templateJobId, cancellationToken);

                            var jobJson = templateJob.ToJson();
                            templateJob = jobJson.FromJson<Job>();

                            var cloneResult = new DeepCloner(templateJob, new List<DeepCloner.IdMapEntry>(), new List<string>(), trainingFranchiseSetId)
                                .Clone<Job>();

                            cloneResult.Clone.CreatedBy = nameof(TrainingDataRefreshStarted);
                            cloneResult.Clone.CreatedDate = DateTime.UtcNow;

                            cloneResult.Clone = UpdateIds(cloneResult.Clone, cloneResult.IdMaps, trainingFranchiseSetId);
                            cloneResult.Clone.FranchiseId = franchiseSet.Franchises.First().Id;
                            cloneResult.Clone.FranchiseSetId = trainingFranchiseSetId;
                            cloneResult.Clone.FranchiseName = franchiseSet.Franchises.First().Name;
                            cloneResult.Clone.FranchiseState = franchiseSet.Franchises.First().PrimaryAddress?.State;

                            var newJob = cloneResult.Clone;
                            FixDuplicateBusinessesOnAdd(newJob, businessIdsCache);
                            await _context.Jobs.AddAsync(newJob, cancellationToken);
                        }

                        await _context.SaveChangesAsync(cancellationToken);

                        trainingFranchiseSetStatus.IsSuccessful = true;
                        trainingFranchiseSetStatus.ExceptionMessage = null;
                    }
                    catch (DbUpdateException duex)
                    {
                        _logger.LogError(duex, "Database update exception occurred during the Refresh of franchiseSet {trainingFranchiseSet}", trainingFranchiseSetId);
                        // If we run into a DbUpdateException, stop tracking the entities causing the issue so that
                        // we can still publish the event and do other operations (holds up the database otherwise)
                        var entries = duex.Entries;
                        foreach (var entry in entries)
                        {
                            entry.State = EntityState.Detached;
                        }
                        trainingFranchiseSetStatus.IsSuccessful = false;
                        trainingFranchiseSetStatus.ExceptionMessage = duex?.InnerException?.ToString();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "An exception occurred during the Refresh. Exception");
                        trainingFranchiseSetStatus.IsSuccessful = false;
                        trainingFranchiseSetStatus.ExceptionMessage = ex.ToString();
                    }
                }
            }

            private void FixDuplicateBusinessesOnAdd(Job newJob, HashSet<Guid> businessIdsCache)
            {
                foreach (var businessMap in newJob.JobBusinesses)
                {
                    if (!businessIdsCache.Contains(businessMap.BusinessId))
                    {
                        businessIdsCache.Add(businessMap.BusinessId);
                    }
                    else
                    {
                        businessMap.Business = null;
                    }
                }
            }
            public ServiceTrainingDataRefreshedEvent GenerateTrainingDataRefreshed(List<Guid> trainingFranchiseSetIds, string requesterEmail, Guid correlationId)
            {
                var refreshEvent = new ServiceTrainingDataRefreshedEvent(nameof(JobService), correlationId)
                {
                    RequesterEmail = requesterEmail
                };

                // We pre-populate the franchise sets for the this event so that if a top level exception occurs
                //  before processing the franchise set TDR, then the error message will indicate that those have not been run.
                foreach (var franchiseSetId in trainingFranchiseSetIds)
                {
                    refreshEvent.FranchiseSets.Add(new ServiceTrainingDataRefreshedEvent.FranchiseSetStatus
                    {
                        FranchiseSetId = franchiseSetId,
                        ExceptionMessage = "The franchise set was not refreshed",
                        IsSuccessful = false
                    });
                }

                return refreshEvent;
            }

            public OutboxMessage GenerateOutboxMessage(ServiceTrainingDataRefreshedEvent tdrResultEvent, string createdBy) => 
                new OutboxMessage(tdrResultEvent.ToJson(), nameof(ServiceTrainingDataRefreshedEvent), tdrResultEvent.CorrelationId, createdBy);

            private static Job UpdateIds(Job job, IEnumerable<DeepCloner.IdMapEntry> idMapEntries, Guid targetFranchiseSetId)
            {
                job.ReferenceNumber = 0;

                var dictionaryIds = idMapEntries.Where(x => x.FromId != Guid.Empty)
                    .ToDictionary(item => item.FromId, item => item.ToId);

                job.MobileDataId = GetId(dictionaryIds, job.MobileDataId);

                foreach (var note in job.JournalNotes)
                {
                    note.TaskId = GetId(dictionaryIds, note.TaskId);
                    note.JobAreaId = GetId(dictionaryIds, note.JobAreaId);
                    note.JobVisitId = GetId(dictionaryIds, note.JobVisitId);
                    note.JobAreaMaterialId = GetId(dictionaryIds, note.JobAreaMaterialId);
                    note.ZoneId = GetId(dictionaryIds, note.ZoneId);
                    note.RoomFlooringTypeAffectedId = GetId(dictionaryIds, note.RoomFlooringTypeAffectedId);
                }

                foreach (var jobVisit in job.JobVisits)
                {
                    foreach (var answer in jobVisit.JobVisitTriStateAnswers)
                    {
                        answer.JobVisitId = jobVisit.Id;
                    }
                }

                foreach (var metadata in job.MediaMetadata)
                {
                    metadata.FranchiseSetId = targetFranchiseSetId;
                    metadata.JobSketchId = GetId(dictionaryIds, metadata.JobSketchId);
                    metadata.JobInvoiceId = GetId(dictionaryIds, metadata.JobInvoiceId);
                    metadata.JobAreaId = GetId(dictionaryIds, metadata.JobAreaId);
                    metadata.JobVisitId = GetId(dictionaryIds, metadata.JobVisitId);
                    metadata.ZoneId = GetId(dictionaryIds, metadata.ZoneId);
                    metadata.JobAreaMaterialId = GetId(dictionaryIds, metadata.JobAreaMaterialId);
                }

                foreach (var jobSketch in job.JobSketches)
                {
                    jobSketch.MediaMetadataId = GetId(dictionaryIds, jobSketch.MediaMetadataId);
                    foreach (var sketchJobVisit in jobSketch.JobSketchJobVisits)
                    {
                        sketchJobVisit.JobSketchId = GetId(dictionaryIds, sketchJobVisit.JobSketchId);
                        sketchJobVisit.JobVisitId = GetId(dictionaryIds, sketchJobVisit.JobVisitId);
                    }
                }

                foreach (var business in job.JobBusinesses)
                {
                    business.BusinessId = GetId(dictionaryIds, business.BusinessId);
                    business.Business.FranchiseSetId = targetFranchiseSetId;
                }

                foreach (var task in job.Tasks)
                {
                    task.ZoneId = GetId(dictionaryIds, task.ZoneId);
                    task.ZoneReadingId = GetId(dictionaryIds, task.ZoneReadingId);
                    task.JobVisitId = GetId(dictionaryIds, task.JobVisitId);
                    task.FranchiseSetId = targetFranchiseSetId;
                }

                foreach (var jobArea in job.JobAreas)
                {
                    jobArea.RoomId = GetId(dictionaryIds, jobArea.RoomId);
                    jobArea.ZoneId = GetId(dictionaryIds, jobArea.ZoneId);
                    jobArea.BeginJobVisitId = GetId(dictionaryIds, jobArea.BeginJobVisitId);
                    jobArea.EndJobVisitId = GetId(dictionaryIds, jobArea.EndJobVisitId);

                    if (jobArea.Room != null)
                    {
                        jobArea.Room.DryOnJobVisitId = GetId(dictionaryIds, jobArea.Room.DryOnJobVisitId);
                        jobArea.Room.PreExistingConditionsDiaryNoteId = GetId(dictionaryIds, jobArea.Room.PreExistingConditionsDiaryNoteId);
                        foreach (var flooring in jobArea.Room.RoomFlooringTypesAffected)
                        {
                            flooring.RoomId = GetId(dictionaryIds, flooring.RoomId);
                        }
                    }
                }

                foreach (var zone in job.Zones)
                {
                    foreach (var reading in zone.ZoneReadings)
                    {
                        reading.JournalNoteId = GetId(dictionaryIds, reading.JournalNoteId);
                        reading.JobVisitId = GetId(dictionaryIds, reading.JobVisitId);
                        reading.ZoneId = GetId(dictionaryIds, reading.ZoneId);
                    }
                }

                foreach (var lineItem in job.LineItems)
                {
                    lineItem.RoomId = GetId(dictionaryIds, lineItem.RoomId);
                    lineItem.JobVisitId = GetId(dictionaryIds, lineItem.JobVisitId);

                    foreach (var note in lineItem.Notes)
                    {
                        note.LineItemId = GetId(dictionaryIds, note.LineItemId);
                        note.RoomId = GetId(dictionaryIds, note.RoomId);
                    }
                }

                foreach (var jobMaterial in job.JobMaterials)
                {
                    foreach (var jam in jobMaterial.JobAreaMaterials)
                    {
                        jam.JobMaterialId = GetId(dictionaryIds, jam.JobMaterialId);
                        jam.JobAreaId = GetId(dictionaryIds, jam.JobAreaId);
                        jam.RemovedOnJobVisitId = GetId(dictionaryIds, jam.RemovedOnJobVisitId);
                        jam.GoalMetOnJobVisitId = GetId(dictionaryIds, jam.GoalMetOnJobVisitId);
                        jam.BeginJobVisitId = GetId(dictionaryIds, jam.BeginJobVisitId);

                        foreach (var materialReading in jam.JobAreaMaterialReadings)
                        {
                            materialReading.JobVisitId = GetId(dictionaryIds, materialReading.JobVisitId);
                            materialReading.JobAreaMaterialId = GetId(dictionaryIds, materialReading.JobAreaMaterialId);
                        }
                    }
                }

                return job;
            }

            private static Guid? GetId(IReadOnlyDictionary<Guid, Guid> dict, Guid? sourceId)
            {
                if (!sourceId.IsNullOrEmpty() && dict.TryGetValue(sourceId.Value, out var value))
                    return value;
                return sourceId.ValueOrNullIfEmpty();
            }

            private static Guid GetId(IReadOnlyDictionary<Guid, Guid> dict, Guid sourceId)
            {
                if (!sourceId.IsNullOrEmpty() && dict.TryGetValue(sourceId, out var value))
                    return value;
                return sourceId;
            }

            private async Task<Job> GetJob(Guid jobId, CancellationToken cancellationToken)
            {
                var currentJob = await _context.Jobs
                        .AsNoTracking()
                        .Include(j => j.MobileData)
                        .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);

                if (currentJob is null)
                    return null;

                currentJob.JobAreas = await _context.JobAreas
                    .AsNoTracking()
                    .Include(x => x.Room)
                    .ThenInclude(r => r.RoomFlooringTypesAffected)
                    .Where(j => j.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.Zones = await _context.Zones
                    .AsNoTracking()
                    .Include(x => x.ZoneReadings)
                    .Where(z => z.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobBusinesses = await _context.JobBusinessMaps
                    .AsNoTracking()
                    .Include(x => x.Business)
                    .Where(j => j.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.LineItems = await _context.LineItems
                    .AsNoTracking()
                    .Include(x => x.Notes)
                    .Where(j => j.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.MediaMetadata = await _context.MediaMetadata.
                    AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobSketches = await _context.JobSketch
                    .AsNoTracking()
                    .Include(x => x.JobSketchJobVisits)
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobTriStateAnswers = await _context.JobTriStateAnswers
                    .AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JournalNotes = await _context.JournalNote
                    .AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.Tasks = await _context.Tasks
                    .AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobVisits = await _context.JobVisit
                    .AsNoTracking()
                    .Include(x => x.JobVisitTriStateAnswers)
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobMaterials = await _context.JobMaterials
                    .AsNoTracking()
                    .Include(x => x.JobAreaMaterials)
                    .ThenInclude(x => x.JobAreaMaterialReadings)
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobLocks = await _context.JobLock.
                    AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobContacts = await _context.JobContactMap
                    .AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);
                currentJob.JobActionLocations = await _context.JobActionLocations
                    .AsNoTracking()
                    .Where(x => x.JobId == jobId)
                    .ToListAsync(cancellationToken);


                return currentJob;
            }


            private async Task SetIsLeadFullySavedOnAllRefreshedWipRecords(List<Guid> trainingFranchiseSetIds, CancellationToken cancellationToken)
            {
                //We have to set all of the newly refreshed training franchises set's wip records
                // to have IsLeadFullySaved = true BECAUSE we dont use integrations in this process
                // so a reponse back from RM would never set this flag - because we would never get a response.
                var wipRecords = await _context.WipRecords.Where(x => trainingFranchiseSetIds.Contains(x.FranchiseSetId)).ToListAsync(cancellationToken);
                foreach (var wipRecord in wipRecords)
                {
                    wipRecord.IsLeadFullySaved = true;
                }
            }

        }
    }
}