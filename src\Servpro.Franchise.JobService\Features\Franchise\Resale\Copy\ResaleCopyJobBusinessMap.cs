﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobBusinessMap
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult BusinessResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobBusinessMap>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobBusinessMap> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobBusinessMap> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobBusinessMap));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var jobBusinessMapTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedBusinessMapIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(jobBusinessMapTargetIds, 
                    GetJobBusinessMapIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobBusinessMap, ResaleJobBusinessMap>(
                    request.ResaleId,
                    businessMap =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(businessMap.JobId))
                            failedDependencies.Add((nameof(Job), businessMap.JobId));
                        if (request.BusinessResult.FailedEntities.Contains(businessMap.BusinessId))
                            failedDependencies.Add((nameof(Business), businessMap.BusinessId));

                        return failedDependencies;
                    },
                    alreadyCopiedBusinessMapIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobBusinessMaps.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobBusinessMap>> GetSourceEntitiesAsync(List<Guid> jobIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobBusinessMaps = await _context.JobBusinessMaps
                    .Where(jbm => jobIds.Contains(jbm.JobId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobBusinessMaps.Count);
                return jobBusinessMaps;
            }

            private async Task<List<Guid>> GetJobBusinessMapIdsAsync(List<Guid?> jobBusinessMapTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobBusinessMaps
                    .Where(x => jobBusinessMapTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
