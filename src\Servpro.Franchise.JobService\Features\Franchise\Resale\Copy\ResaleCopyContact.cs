﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyContact
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult BusinessResult { get; set; }
            public HashSet<Guid> MarketingBusinessIds { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> ContactIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyContact>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyContact> _logger;
            private readonly IMapper _mapper;
            private readonly JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyContact> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _context = context;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(Contact));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.ContactIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var contactTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedContactIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(contactTargetIds, 
                    GetContactIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<Contact, ResaleContact>(
                    request.ResaleId,
                    contact =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (contact.BusinessId.HasValue && request.BusinessResult.FailedEntities.Contains(contact.BusinessId.Value))
                            failedDependencies.Add((nameof(Business), contact.BusinessId.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedContactIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    sourceEntity =>
                    {
                        // if the contact is associated to a Marketing Business, we need to remove the marketing flag
                        if (sourceEntity.BusinessId.HasValue && request.MarketingBusinessIds.Contains(sourceEntity.BusinessId.Value))
                        {
                            sourceEntity.IsMarketingContact = false;
                        }
                    },
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.Contacts.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<Contact>> GetSourceEntitiesAsync(List<Guid> contactIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var contacts = await _context.Contacts
                    .Where(b => contactIds.Contains(b.Id))
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", contacts.Count);
                return contacts;
            }

            private async Task<List<Guid>> GetContactIdsAsync(List<Guid?> contactTargetIds, CancellationToken cancellationToken)
            {
                return await _context.Contacts
                    .Where(x => contactTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
