﻿using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;
using System;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
{
    public static class ZoneCompositionHelper
    {
        public static readonly string ColorLightYellow = "lightyellow";
        public static readonly string ColorYellow = "yellow";
        public static readonly string ColorLightPink = "lightpink";
        public static readonly string ColorLightGreen = "lightgreen";
        public static readonly string ColorWhite = "white";

        public static string GetDehuValidationText(this EquipmentPlacementRequirementsProximity proximity, DehuValidationType dehuValidationType)
        {
            if (dehuValidationType == DehuValidationType.Mixed) return "Desiccant Dehus cannot be validated with LGR & Conventional";

            switch (proximity)
            {
                case EquipmentPlacementRequirementsProximity.None:
                case EquipmentPlacementRequirementsProximity.BelowMinimum:
                    return "See Job Diary Notes";
                case EquipmentPlacementRequirementsProximity.Acceptable:
                    return "Dehumidifier Placements are in the Acceptable Range";
                case EquipmentPlacementRequirementsProximity.HighAcceptable:
                    return "Equipment Charge is Reduced and/or Note Provided. See Job Diary Notes.";
                case EquipmentPlacementRequirementsProximity.AboveMaximum:
                    return "See Job Diary Notes";
                default: return String.Empty;
            }
        }

        public static string GetAirMoverValidationTextV2(this EquipmentPlacementRequirementsProximity proximity)
        {
            const string outOfRangeStr = "One or more rooms is not in range.  See diary note for explanation and see detail in the section below.";

            switch (proximity)
            {
                case EquipmentPlacementRequirementsProximity.BelowMinimum:
                    return outOfRangeStr;
                case EquipmentPlacementRequirementsProximity.HighAcceptable: // Never used with AirMovers
                case EquipmentPlacementRequirementsProximity.Acceptable:
                    return "Air Mover Placements are in the Acceptable Range";
                case EquipmentPlacementRequirementsProximity.AboveMaximum:
                    return outOfRangeStr;
                //case EquipmentPlacementRequirementsProximity.None: // Never used with AirMovers
                default: return String.Empty;
            }
        }

        public static string GetAirMoverValidationColor(this EquipmentPlacementRequirementsProximity proximity, bool zoneIsConfirmed)
        {
            switch (proximity)
            {
                case EquipmentPlacementRequirementsProximity.Acceptable:
                    return ColorLightGreen;
                case EquipmentPlacementRequirementsProximity.BelowMinimum:
                case EquipmentPlacementRequirementsProximity.AboveMaximum:
                    return ColorYellow;
                case EquipmentPlacementRequirementsProximity.HighAcceptable:
                case EquipmentPlacementRequirementsProximity.None:
                    return ColorLightYellow;
                default: return ColorWhite;
            }
        }
        
        public static string GetAirMoverByRoomValidationColor(this EquipmentPlacementRequirementsProximity proximity, bool zoneIsConfirmed)
        {
            switch (proximity)
            {
                case EquipmentPlacementRequirementsProximity.BelowMinimum:
                case EquipmentPlacementRequirementsProximity.AboveMaximum:
                    return ColorYellow;
                default: return ColorWhite;
            }
        }

        public static string GetColor(this EquipmentPlacementRequirementsProximity proximity, bool zoneIsConfirmed)
        {
            switch (proximity)
            {
                case EquipmentPlacementRequirementsProximity.Acceptable:
                    return ColorLightGreen;
                case EquipmentPlacementRequirementsProximity.BelowMinimum:
                case EquipmentPlacementRequirementsProximity.AboveMaximum:
                    return ColorLightPink;
                case EquipmentPlacementRequirementsProximity.HighAcceptable:
                case EquipmentPlacementRequirementsProximity.None:
                    return ColorLightYellow;
                default: return ColorWhite;
            }
        }
        public static string GetDehuActualCapacityPlaced(DehuValidationType dehuValidationType, int? dehuPpdsPlaced, int? dehuCfmsPlaced)
        {
            if (dehuValidationType == DehuValidationType.Cfm && !dehuCfmsPlaced.HasValue) return String.Empty;
            if (dehuValidationType == DehuValidationType.Ppd && !dehuPpdsPlaced.HasValue) return String.Empty;
            if ((dehuPpdsPlaced ?? 0) + (dehuCfmsPlaced ?? 0) <= 0) return $"{0} PPD & {0} CFM";

            switch (dehuValidationType)
            {
                case DehuValidationType.Ppd:
                    return $"{dehuPpdsPlaced} PPD";
                case DehuValidationType.Cfm:
                    return $"{dehuCfmsPlaced} CFM";
                case DehuValidationType.Mixed:
                    return $"{dehuPpdsPlaced} PPD & {dehuCfmsPlaced} CFM";
                default:
                    return String.Empty;
            }
        }

        public static string GetDehuMinCapacityRequired(DehuValidationType dehuValidationType, int? minDehuPpdsRequired, int? minDehuCfmsRequired)
        {
            if (dehuValidationType == DehuValidationType.Cfm && !minDehuCfmsRequired.HasValue) return String.Empty;
            if (dehuValidationType == DehuValidationType.Ppd && !minDehuPpdsRequired.HasValue) return String.Empty;

            switch (dehuValidationType)
            {
                case DehuValidationType.Ppd:
                    return minDehuPpdsRequired.HasValue ? $"{minDehuPpdsRequired} PPD" : String.Empty;
                case DehuValidationType.Cfm:
                    return minDehuCfmsRequired.HasValue ? $"{minDehuCfmsRequired} CFM" : String.Empty;
                case DehuValidationType.Mixed:
                    return string.Empty;
                case DehuValidationType.None:
                    return minDehuPpdsRequired.HasValue && minDehuCfmsRequired.HasValue
                        ? $"{minDehuPpdsRequired} PPD or {minDehuCfmsRequired} CFM"
                        : string.Empty;
                default:
                    return string.Empty;
            }
        }

        public static string ToProperCaseWithSpaces(this string properOrCamelCaseString)
        {
            // If there are 0 or 1 characters, just return the string.
            if (string.IsNullOrEmpty(properOrCamelCaseString)) return string.Empty;
            if (properOrCamelCaseString.Length < 2) return properOrCamelCaseString.ToUpper();

            // Start with the first character.
            string result = properOrCamelCaseString.Substring(0, 1).ToUpper();

            // Add the remaining characters.
            for (var i = 1; i < properOrCamelCaseString.Length; i++)
            {
                if (char.IsUpper(properOrCamelCaseString[i])) result += " ";
                result += properOrCamelCaseString[i];
            }

            return result;
        }

        public static string FormatDecimalPlaces(this decimal? number)
        {
            if (number.HasValue)
            {
                return number.Value.ToString("0.00");
            }

            return "0.00";
        }
    }
}
