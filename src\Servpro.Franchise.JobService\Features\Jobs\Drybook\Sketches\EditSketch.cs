﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Locks;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Sketches
{
    public class EditSketch
    {
        public class Command : IRequest<ResponseDto>
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public string CanvasJson { get; set; }
            public MediaMetadataDto MediaMetadata { get; set; } = new MediaMetadataDto();
        }

        public class MediaMetadataDto
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public string Name { get; set; }
            public Guid JobArtifactId { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.Id)
                    .NotNull()
                    .NotEmpty();
                RuleFor(m => m.MediaMetadata.Id)
                    .NotNull()
                    .NotEmpty();
                RuleFor(m => m.MediaMetadata.JobId)
                    .NotNull()
                    .NotEmpty();
            }
        }

        public class PresignedUrlDto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
        }

        public class ResponseDto
        {
            public ResponseDto(Guid id, string signedUrl, string name, Guid jobId, Guid mediaMetadataId, Guid jobArtifactId)
            {
                Id = id;
                SignedUrl = signedUrl;
                Name = name;
                JobId = jobId;
                JobArtifactId = jobArtifactId;
                MediaId = mediaMetadataId;
            }

            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public Guid JobId { get; set; }
            public Guid MediaId { get; set; }
            public Guid JobArtifactId { get; set; }

        }

        public class Handler : IRequestHandler<Command, ResponseDto>
        {
            private readonly JobDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(
                JobDataContext db,
                IConfiguration config,
                IAmazonS3 clientS3,
                IUserInfoAccessor userInfoAccessor)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<ResponseDto> Handle(Command request,
                CancellationToken cancellationToken)
            {
                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _db.Jobs
                     .Include(j => j.JobLocks)
                    .FirstOrDefaultAsync(q => q.Id == request.MediaMetadata.JobId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {request.MediaMetadata.JobId}");
                if (JobLockUtils.HasLockConflict(job.CurrentJobLock, userInfo.Id))
                    throw new JobConflictException(job.CurrentJobLock is null ? null : GetJobLock.Handler.Map(job.CurrentJobLock));

                var sketch = await _db.JobSketch
                    .Where(s => s.Id == request.Id)
                    .Include(c => c.MediaMetadata)
                    .FirstAsync(cancellationToken);

                sketch = MapSketch(sketch, request);
                _db.JobSketch.Update(sketch);
                await _db.SaveChangesAsync(cancellationToken);

                var signedUrl = GetPreSignedUrl(sketch);
                return new ResponseDto(sketch.Id, signedUrl.SignedUrl, sketch.MediaMetadata.Name, sketch.MediaMetadata.JobId, sketch.MediaMetadataId, sketch.MediaMetadata.ArtifactTypeId);
            }


            private PresignedUrlDto GetPreSignedUrl(JobSketch sketch)
            {
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = _config[S3MediaBucketNameKey],
                    Key = sketch.MediaMetadata.GetKey(),
                    Verb = HttpVerb.PUT,
                    ContentType = "multipart/form-data",
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };

                var preSignedUrlDto = new PresignedUrlDto
                {
                    Id = sketch.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = sketch.Name
                };

                return preSignedUrlDto;
            }

            private JobSketch MapSketch(JobSketch sketch, Command command)
            {
                sketch.JobId = sketch.MediaMetadata.JobId;
                sketch.Name = command.Name;
                sketch.CanvasJson = command.CanvasJson;
                sketch.MediaMetadata = MapMediaMetadata(sketch.MediaMetadata, command);
                sketch.ModifiedDate = DateTime.UtcNow;

                return sketch;
            }

            private MediaMetadata MapMediaMetadata(MediaMetadata mediaMetadata, Command command)
            {
                mediaMetadata.ModifiedDate = DateTime.UtcNow;
                mediaMetadata.Name = command.Name;
                mediaMetadata.MediaTypeId = MediaTypes.Sketch;
                mediaMetadata.ArtifactTypeId = command.MediaMetadata.JobArtifactId;

                return mediaMetadata;
            }
        }
    }
}