﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Task = System.Threading.Tasks.Task;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Validation
{
    public class GetJobValidation
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public int Count { get; set; }

            public ValidationPhotosDto Photos { get; set; }

            public ValidationFormsDto Forms { get; set; }

            public ValidationClientNotificationsDto ClientNotifications { get; set; }

            public ValidationMiscellaneousDto Miscellaneous { get; set; }

            public ValidationEstimaticsDto Estimatics { get; set; }

            public DateTime Created { get; set; }

            public Guid JobId { get; set; }

            public bool IsStormJob { get; set; }
            public bool IsSelfPayJob { get; set; }
            public bool IsUnknownInsuranceJob { get; set; }

            public int? CorporateJobNumber { get; set; }

            public Guid FranchiseSetId { get; set; }

            public Guid LossTypeId { get; set; }

            public Guid PropertyTypeId { get; set; }

            public bool DoesJobUploadToXact { get; set; }

            /// <summary>
            /// If non-empty, WorkCenter encountered an error
            /// This will be used to notify the UI that the Business Rules Engine is offline
            /// and that the requirements area cannot be displayed
            /// </summary>
            public string ErrorMessage { get; set; }

            public string ClientConditions { get; set; }

            public int? ClientRequirementsCount { get; set; }

            public class ValidationItemDto
            {
                public string Name { get; set; }
                public string Description { get; set; }
                public string Notification { get; set; }
                public bool IsMet { get; set; }
                public bool AllowBypass { get; set; }
                public bool AllowAcknowledgement { get; set; }
                public int? ExceptionNoteId { get; set; }
                public bool IsOutForSignature { get; set; } = false;

                /// <summary>
                /// This is a detailed status which breaks down by Exception also
                /// </summary>
                public ValidationRequestMapper.ValidationItemStatus Status { get; set; }

                public int OriginalValidationStatus { get; set; }

                /// <summary>
                /// Active or Complete; period.
                /// </summary>
                public ValidationRequestMapper.ValidationItemValidationStatus ValidationStatus { get; set; }

                public int Id { get; set; }
                public Guid? ArtifactTypeId { get; set; }
                public Guid? FormTemplateEgId { get; set; }
                public Guid? JobAreaId { get; set; }
                public Guid? ZoneId { get; set; }
                public Guid? JobDiaryEntryId { get; set; }
                public Guid? JobVisitId { get; set; }
                public Guid? JobArtifactId { get; set; }
                public long? JobArtifactWcId { get; set; }
                public DateTime? JobArtifactDate { get; set; }
                public Guid? JobAreaMaterialId { get; set; }
                public Guid? JobFormId { get; set; }
                public Guid? RelatedFormId { get; set; }
                public Guid? RoomId { get; set; }
                public Guid? ScopeLineItemId { get; set; }
                public string ScopeLineItemInfo { get; set; }
                public int? XactLineItemId { get; set; }
                public int RuleId { get; set; }
                public string FormType { get; set; }
                public string ValidationEffect { get; set; }
                public bool AskedPerVisit { get; set; }
                public string GroupName { get; set; }
                public List<KeyValuePair<string, string>> RuleParameters { get; set; }
                public string MaterialType { get; set; }
                public Guid? JobTriStateQuestionId { get; set; }
                public bool? RequiredForInitialUpload { get; set; }

                public bool? IsClientRequirement { get; set; }

                /// <summary>
                /// Id of the item that cleared the rule.  This can be many things: ContactId, RoomId, etc.
                /// </summary>
                public Guid? DtoId { get; set; }
                public string ClaimNumberFormat { get; set; }

                public ValidationItemDto()
                {
                    this.RuleParameters = new List<KeyValuePair<string, string>>();
                }

                public ValidationItemDto(ValidationItemDto item)
                {
                    this.Name = item.Name;
                    this.Description = item.Description;
                    this.Notification = item.Notification;
                    this.IsMet = item.IsMet;
                    this.AllowBypass = item.AllowBypass;
                    this.AllowAcknowledgement = item.AllowAcknowledgement;
                    this.ExceptionNoteId = item.ExceptionNoteId;
                    this.Status = item.Status;
                    this.ValidationStatus = item.ValidationStatus;
                    this.Id = item.Id;
                    this.ArtifactTypeId = item.ArtifactTypeId;
                    this.FormTemplateEgId = item.FormTemplateEgId;
                    this.JobAreaId = item.JobAreaId;
                    this.ZoneId = item.ZoneId;
                    this.JobDiaryEntryId = item.JobDiaryEntryId;
                    this.JobVisitId = item.JobVisitId;
                    this.JobArtifactId = item.JobArtifactId;
                    this.RuleParameters = item.RuleParameters;
                    this.JobArtifactWcId = item.JobArtifactWcId;
                    this.JobArtifactDate = item.JobArtifactDate;
                    this.JobAreaMaterialId = item.JobAreaMaterialId;
                    this.JobFormId = item.JobFormId;
                    this.RelatedFormId = item.RelatedFormId;
                    this.RoomId = item.RoomId;
                    this.ScopeLineItemId = item.ScopeLineItemId;
                    this.ScopeLineItemInfo = item.ScopeLineItemInfo;
                    this.XactLineItemId = item.XactLineItemId;
                    this.RuleId = item.RuleId;
                    this.FormType = item.FormType;
                    this.ValidationEffect = item.ValidationEffect;
                    this.AskedPerVisit = item.AskedPerVisit;
                    this.GroupName = item.GroupName;
                    this.MaterialType = item.MaterialType;
                    this.JobTriStateQuestionId = item.JobTriStateQuestionId;
                    this.RequiredForInitialUpload = item.RequiredForInitialUpload;
                }
            }

            public class ValidationPhotosDto
            {
                public List<ValidationItemDto> Requirements { get; set; }

                public List<ValidationPhotosGroupDto> Groups { get; set; }
            }

            public class ValidationPhotosGroupDto
            {
                public string Name { get; set; }
                public List<ValidationItemDto> Requirements { get; set; }
                public ValidationRequestMapper.ValidationPhotosGroupType GroupType { get; set; }
                public ValidationPhotosGroupDto()
                {
                    this.Requirements = new List<ValidationItemDto>();
                    this.GroupType = ValidationRequestMapper.ValidationPhotosGroupType.None;
                }
            }

            public class ValidationFormsDto
            {
                public List<ValidationItemDto> Requirements { get; set; }
            }

            public class ValidationMiscellaneousDto
            {
                public List<ValidationItemDto> Requirements { get; set; }
            }

            public class ValidationEstimaticsDto
            {
                public List<ValidationItemDto> Requirements { get; set; }
                public string EstimateSource { get; set; }
            }

            public class ValidationClientNotificationsDto
            {
                public List<ValidationItemDto> Requirements { get; set; }
            }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly IClientRequirementsService _clientRequirementsService;
            private readonly ILogger<GetJobValidation> _logger;
            private readonly JobDataContext _jobDataContext;
            private readonly IMapJobValidations _jobValidationMapper;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IDocuSignUtility _docuSignUtility;

            public Handler(IClientRequirementsService clientRequirementsService,
                JobDataContext jobDataContext, 
                ILogger<GetJobValidation> logger,
                IMapJobValidations jobValidationMapper,
                IFranchiseServiceClient franchiseServiceClient,
                ILookupServiceClient lookupServiceClient,
                IDocuSignUtility docuSignUtility)
            {
                _clientRequirementsService = clientRequirementsService;
                _jobDataContext = jobDataContext;
                _logger = logger;
                _jobValidationMapper = jobValidationMapper;
                _franchiseServiceClient = franchiseServiceClient;
                _lookupServiceClient = lookupServiceClient;
                _docuSignUtility = docuSignUtility;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var logScope = _logger.BeginScope("Handler began with: {@request}", request);
                Dto response;
                try
                {
                    // Get job data ...
                    var job = await _clientRequirementsService.GetJobAsync(request.JobId, cancellationToken);

                    // Get data required for generating CRS request dto ...
                    var dtoHelper = await CreateJobRequirementsRequestDtoHelper(job, cancellationToken);

                    // ... using the required data from above, generate the request DTO to send to CRS ...
                    var crsRequest = await _clientRequirementsService.CreateJobRequirementsRequestDto(job, cancellationToken);

                    // ... send the request to CRS in order to get job requirements ...
                    _logger.LogTrace("Calling CRS with crsRequest: {@crsRequest}", crsRequest);
                    _logger.LogDebug("Calling CRS with crsRequest.Job: {@crsRequestJob}", crsRequest.Job);
                    _logger.LogDebug("Calling CRS with crsRequest.Job.JobAreas: {@crsRequestJobJobAreas}", crsRequest.Job.JobAreas);
                    _logger.LogDebug("Calling CRS with crsRequest.Job.JobVisits: {@crsRequestJobJobVisits}", crsRequest.Job.JobVisits);
                    _logger.LogDebug("Calling CRS with crsRequest.Job.Zones: {@crsRequestJobZones}", crsRequest.Job.Zones);

                    var stopwatch = Stopwatch.StartNew();
                    var validationResult = await _clientRequirementsService.GetJobRequirementsAsync(crsRequest, cancellationToken);
                    _logger.LogDebug("Completed {request}. elapsedMilliseconds: {elapsedMilliseconds}",
                        nameof(_clientRequirementsService.GetJobRequirementsAsync), stopwatch.ElapsedMilliseconds);
                    stopwatch.Restart();

                    if (validationResult?.Result == null)
                    {
                        _logger.LogWarning("Could not obtain requirements from CRS. {request}", request);
                        return new Dto()
                        {
                            ErrorMessage = "Requirements are temporarily unavailable. Please try again later."
                        };
                    }

                    _logger.LogTrace("CRS returned validationResult: {@validationResult}", validationResult);
                    _logger.LogDebug("CRS returned Result: {@validationResultResult}", validationResult.Result);
                    _logger.LogDebug("CRS returned MiscellaneousRequirements: {@validationResultResultMiscellaneousRequirements}", validationResult.Result.MiscellaneousRequirements);
                    _logger.LogDebug("CRS returned ClientNotificationRequirements: {@validationResultResultClientNotificationRequirements}", validationResult.Result.ClientNotificationRequirements);

                    // ... map it to our model and return it
                    response = await MapResponse(validationResult, job, dtoHelper, cancellationToken);
                    _logger.LogDebug("CRS returned {RequirementsCount} requirements", response.ClientRequirementsCount);
                    _logger.LogDebug("Mapped response for photo validation: {@photoValidation}", response.Photos);

                    // determine number of unmet requirements and update the field on the job
                    // load simple job.  job up top was a huge object and did not load change tracking for all of 
                    // the related data
                    _logger.LogDebug("Getting simple, change tracked job from db and updating ClientRequirements Count. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                    stopwatch.Restart();
                    job = await _jobDataContext.Jobs
                        .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);
                    job.ClientRequirementsCount = response.ClientRequirementsCount;
                    await _jobDataContext.SaveChangesAsync(cancellationToken);
                    _logger.LogDebug("Saved Job. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed getting job validation.");
                    logScope.Dispose();
                    throw;
                }

                return response;
            }

            private async Task<Dto> MapResponse(CrsValidationResponse crsValidationResponse, Job job, GetJobRequirementsRequestDtoHelper dtoHelper, CancellationToken cancellationToken)
            {
                var stopwatch = Stopwatch.StartNew();

                var selfPayInsuranceId = (await _jobDataContext.InsuranceClients.FirstOrDefaultAsync(ic => ic.Name.ToLower() == "self pay" || ic.Name.ToLower() == "selfpay", cancellationToken))?.Id;
                var unknownInsuranceId = (await _jobDataContext.InsuranceClients.FirstOrDefaultAsync(ic => ic.Name.ToLower() == "unknown", cancellationToken))?.Id;

                var handlerResponse = new Dto()
                {
                    Created = DateTime.UtcNow,
                    JobId = job.Id,
                    FranchiseSetId = job.FranchiseSetId,
                    ClientConditions = job.ClientConditions,
                    IsSelfPayJob = job.InsuranceCarrierId.HasValue && job.InsuranceCarrierId.Value == selfPayInsuranceId.Value,
                    IsUnknownInsuranceJob = job.InsuranceCarrierId.HasValue && job.InsuranceCarrierId.Value == unknownInsuranceId.Value
                };

                var timeZone = TimeZoneHelper.GetWindowsTimeZoneIdentifier(dtoHelper.FranchiseSet.PrimaryTimeZoneId);
                var converted = handlerResponse.Created.ConvertDateFromUtc(timeZone);

                if (converted.HasValue)
                    handlerResponse.Created = converted.Value;

                /* Room/Zone/Visit Lookups */
                var jobAreas = await _jobDataContext.JobAreas
                    .AsNoTracking()
                    .Include(x => x.Zone)
                    .Where(ja => ja.JobId == job.Id && ja.RoomId.HasValue)
                    .ToListAsync(cancellationToken);

                var jobAreaRoomZones = jobAreas?
                    .Select(ja => new { JobAreaId = ja.Id, Name = ja.Name == ja.Zone?.Name || string.IsNullOrEmpty(ja.Zone?.Name) ? ja.Name : $"{ja.Name} - {ja.Zone?.Name}" })
                    .ToList().ToDictionary(j => j.JobAreaId, j => j.Name);
                _logger.LogDebug("Getting Job Areas, rooms, zones db access complete. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);

                _logger.LogDebug("Mapping Drybook Information. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                var jobAreaIdRoomIdDictionary = jobAreas?
                    .Select(t => new { JobAreaId = t.Id, RoomId = t.RoomId.Value })
                    .ToList().ToDictionary(j => j.JobAreaId, j => j.RoomId);

                var jobVisits = new Dictionary<Guid, int>();
                var count = 0;
                job.JobVisits?.OrderBy(j => j.CreatedDate)
                    .Select(j => j.Id).ToList()
                    .ForEach(jv => jobVisits.Add(jv, count++));

                var zones = job.Zones?.Select(z => new { z.Id, z.Name })
                    .ToList().ToDictionary(z => z.Id, z => z.Name);

                var jobAreaMaterials = new List<JobAreaMaterial>();
                job.JobAreas?.ToList().ForEach(a => jobAreaMaterials.AddRange(a.JobAreaMaterials));

                var jobPhotos = job.MediaMetadata?
                    .Where(m => m.FormTemplateId == null)
                    .ToList();

                var jobForms = job.MediaMetadata?.Where(m => m.FormTemplateId.HasValue).ToList();
                /* END Room/Zone/Visit Lookups */

                // overall counter used by the UI for setting html element ids (convenience)
                var idx = 0;

                /* Map Photos */
                handlerResponse.Photos = _jobValidationMapper.MapPhotos(crsValidationResponse, jobAreaMaterials, jobPhotos, jobAreaRoomZones, jobVisits,
                    dtoHelper.Lookups.ArtifactTypes?.ToList(), timeZone, ref idx);

                /* Map Forms */
                var initialRequirementsMustBeClientRequired = InitialRequirementsMustBeClientRequired(job.InsuranceCarrierId);
                var formTemplatesOutForSignatureMap = await _docuSignUtility.GetFormTemplatesOutForSignatureAsync(job.Id, cancellationToken);
                handlerResponse.Forms = _jobValidationMapper.MapForms(crsValidationResponse, jobForms, true, timeZone, ref idx, initialRequirementsMustBeClientRequired, formTemplatesOutForSignatureMap);

                /* Map Client Notifications */
                handlerResponse.ClientNotifications = _jobValidationMapper.MapClientNotifications(crsValidationResponse, jobVisits, zones, ref idx);

                /* Map Miscellaneous */
                handlerResponse.Miscellaneous = _jobValidationMapper.MapMiscellaneous(crsValidationResponse, jobAreaRoomZones, jobAreaIdRoomIdDictionary, ref idx);

                /* Map Estimatics */
                handlerResponse.Estimatics = _jobValidationMapper.MapEstimatics(job.Id, crsValidationResponse, job.LineItems, ref idx);
                handlerResponse.Estimatics.EstimateSource = crsValidationResponse.Result.JobInfo.ActiveInAudit
                    ? "estimate resides in Job File Audit"
                    : "estimate resides in WorkCenter";

                handlerResponse.Count = idx++;
                handlerResponse.CorporateJobNumber = job.CorporateJobNumber;
                handlerResponse.IsStormJob = job.StormId.HasValue;
                handlerResponse.LossTypeId = job.LossTypeId;
                handlerResponse.PropertyTypeId = job.PropertyTypeId;
                handlerResponse.DoesJobUploadToXact = job.JobDispatchTypeId.HasValue &&
                                                      job.JobDispatchTypeId.Value == DispatchTypes.NonScanEr;
                handlerResponse.ClientRequirementsCount = GetNumberOfUnmetRequirements(handlerResponse);

                MapRequirementDtoIds(job, handlerResponse);
                MapClaimNumberRegex(job.PropertyTypeId, handlerResponse);

                _logger.LogDebug("Completed Mapping Drybook Information. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);

                return handlerResponse;
            }

            private static int GetNumberOfUnmetRequirements(Dto response)
            {
                var photoCount = response.Photos?.Requirements?.Count(r => !r.IsMet);
                response.Photos?.Groups?.ToList().ForEach(g =>
                {
                    photoCount += g.Requirements?.Count(r => !r.IsMet);
                });
                var formsCount = response.Forms?.Requirements?.Count(f => !f.IsMet);
                var clientCount = response.ClientNotifications?.Requirements?.Count(c => !c.IsMet);
                var miscCount = response.Miscellaneous?.Requirements?.Count(r => !r.IsMet);
                var estimaticsCount = response.Estimatics?.Requirements?.Count(e => !e.IsMet);
                var total = photoCount + formsCount + clientCount + miscCount + estimaticsCount;
                return total ?? 0;
            }

            private static void MapRequirementDtoIds(Job job, Dto response)
            {
                var adjusterNameRule = response.Miscellaneous.Requirements.FirstOrDefault(r => r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.AdjusterName && r.IsMet);
                if (adjusterNameRule != null)
                {
                    adjusterNameRule.DtoId = job.JobContacts
                        .FirstOrDefault(c => c.JobContactTypeId == JobContactTypes.Adjuster)?.ContactId;
                }

                var adjusterEmailRule = response.Miscellaneous.Requirements.FirstOrDefault(r => r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.AdjusterEmail);
                if (adjusterEmailRule != null)
                {
                    adjusterEmailRule.DtoId = job.JobContacts
                        .FirstOrDefault(c => c.JobContactTypeId == JobContactTypes.Adjuster)?.ContactId;
                }

                var customerCalledTimeRule = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int) ValidationRequestMapper.MiscellaneousRuleIds.CustomerCalledTime && r.IsMet);
                if (customerCalledTimeRule != null)
                {
                    customerCalledTimeRule.DtoId =
                        job.JobDates.FirstOrDefault(jb => jb.JobDateTypeId == JobDateTypes.CustomerCalled)?.JobDateTypeId;
                }

                var siteInspectedTime = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.SiteInspectedTime && r.IsMet);
                if (siteInspectedTime != null)
                {
                    siteInspectedTime.DtoId =
                        job.JobDates.FirstOrDefault(jb => jb.JobDateTypeId == JobDateTypes.InitialOnSiteArrival)?.JobDateTypeId;
                }

                var customerCalledExceptionReasonRule = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.CustomerCalledExceptionReason && r.IsMet);
                if (customerCalledExceptionReasonRule != null)
                {
                    customerCalledExceptionReasonRule.DtoId =
                        job.JobDates?.FirstOrDefault(d => d.JobDateTypeId == JobDateTypes.CustomerCalled)?.ExceptionReasonId;
                    customerCalledExceptionReasonRule.JobDiaryEntryId =
                        job.JournalNotes.FirstOrDefault(jn => jn.RuleIds?.FirstOrDefault() == (int)ValidationRequestMapper.MiscellaneousRuleIds.CustomerCalledExceptionReason)?.Id;
                }

                var dryingReport = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.DryingReport && r.IsMet);
                if (dryingReport != null)
                {
                    dryingReport.JobArtifactId = job.MediaMetadata.FirstOrDefault(mm =>
                        mm.ArtifactTypeId == ArtifactTypes.DryingReport && !mm.IsDeleted && mm.IsForUpload)?.Id;
                }

                var siteInspectedTimeExceptionReasonRule = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.SiteInspectedTimeExceptionReason && r.IsMet);
                if (siteInspectedTimeExceptionReasonRule != null)
                {
                    siteInspectedTimeExceptionReasonRule.DtoId =
                        job.JobDates?.FirstOrDefault(d => d.JobDateTypeId == JobDateTypes.InitialOnSiteArrival)?.ExceptionReasonId;
                    siteInspectedTimeExceptionReasonRule.JobDiaryEntryId =
                        job.JournalNotes
                            .FirstOrDefault(jn => jn.RuleIds?.FirstOrDefault() == (int)ValidationRequestMapper.MiscellaneousRuleIds.SiteInspectedTimeExceptionReason)
                        ?.Id;
                }

                var projectRevenueRule = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.ProjectRevenue);
                var invoiceRule = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.Invoice && r.IsMet);
                if(projectRevenueRule != null && invoiceRule != null)
                {
                    projectRevenueRule.JobArtifactId = invoiceRule.JobArtifactId;
                }

            }

            private static void MapClaimNumberRegex(Guid propertyTypeId, Dto response)
            {
                var claimNumberRule = response.Miscellaneous.Requirements.FirstOrDefault(r =>
                    r.RuleId == (int)ValidationRequestMapper.MiscellaneousRuleIds.ClaimNumber);
                if (claimNumberRule != null)
                {
                    claimNumberRule.DtoId = propertyTypeId;
                    claimNumberRule.ClaimNumberFormat = propertyTypeId == PropertyTypes.Commercial ? 
                        ClaimNumberFormat.Commercial : ClaimNumberFormat.Residential;
                }
            }

            private static bool InitialRequirementsMustBeClientRequired(Guid? insuranceCarrierId)
            {
                // TODO: State Farm only for now; this will eventually be provided by CRS
                return insuranceCarrierId.HasValue && insuranceCarrierId.Value == LookupService.Constants.InsuranceCarrierTypes.StateFarm;
            }

            private async Task<GetJobRequirementsRequestDtoHelper> CreateJobRequirementsRequestDtoHelper(Job job, CancellationToken cancellationToken)
            {
                var stopwatch = Stopwatch.StartNew();

                var lookupsRequest = _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var franchiseSetRequest = _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, cancellationToken);

                _logger.LogDebug("Awaiting Task List {@tasks}. elapsedMilliseconds: {elapsedMilliseconds}",
                    new[] { nameof(lookupsRequest), nameof(franchiseSetRequest) }, stopwatch.ElapsedMilliseconds);
               
                await Task.WhenAll(lookupsRequest, franchiseSetRequest);
                
                _logger.LogDebug("Task List Complete. Retrieved in {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                stopwatch.Restart();

                _logger.LogDebug("Awaiting Task list again. elapsedMilliseconds: {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                var helper = new GetJobRequirementsRequestDtoHelper()
                {
                    Lookups = await lookupsRequest,
                    FranchiseSet = await franchiseSetRequest,
                };
                _logger.LogDebug("Task list complete again. Retrieved in {elapsedMilliseconds}", stopwatch.ElapsedMilliseconds);
                return helper;
            }

            internal class GetJobRequirementsRequestDtoHelper
            {
                public GetLookups.Dto Lookups { get; set; }
                public GetFranchiseSetDto FranchiseSet { get; set; }
            }
        }
    }
}