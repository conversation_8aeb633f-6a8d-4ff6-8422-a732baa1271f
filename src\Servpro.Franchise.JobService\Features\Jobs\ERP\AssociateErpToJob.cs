﻿using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Erp.ErpService;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Erp;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Erp
{
    public class AssociateErpToJob
    {
        public class Command : IRequest
        {
            public Guid JobId { get; set; }
            public int ErpId { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.ErpId).GreaterThan(0);
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly ILogger<Handler> _logger;
            private readonly IErpService _erpService;

            public Handler(ILogger<Handler> logger,
                           IErpService erpService)
            {
                _logger = logger;
                _erpService = erpService;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                await _erpService.AssociateErpToJobAsync(request.JobId, request.ErpId, cancellationToken);
                return Unit.Value;
            }
        }
    }
}