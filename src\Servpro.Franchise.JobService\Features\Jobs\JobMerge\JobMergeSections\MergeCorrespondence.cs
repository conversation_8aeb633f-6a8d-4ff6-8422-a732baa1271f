﻿using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Servpro.Franchise.JobService.Features.Jobs.JobMerge.JobMergeSections
{
    public class MergeCorrespondence : IJobMergeSection
    {
        public void Merge(Job sourceJob, Job targetJob, bool isDryingDataCopied, bool hasCallDispositionSourceJob, bool hasCallDispositionTargetJob)
        {
            var targetJobId = targetJob.Id;

            targetJob.JournalNotes =
                sourceJob.JournalNotes
                    .Select(jn => MapJournalNote(jn, targetJobId))
                    .Concat(targetJob.JournalNotes)
                    .ToList();
            
            if (isDryingDataCopied)
            {
                UpdateSourceJournalNotes(sourceJob.JournalNotes.ToList());
            }
        }

        /// <summary>
        /// Updates all source JournalNotes to remove association to any FK's <br />
        /// These entities are updated later in merge and will cause source journalNotes 
        /// to have association to target job entities if not removed.
        /// </summary>
        /// <param name="journalNotes"></param>
        private void UpdateSourceJournalNotes(List<JournalNote> journalNotes)
        {
            journalNotes.ForEach(journalNote =>
            {
                if (journalNote.TaskId.HasValue)
                {
                    journalNote.Task = null;
                    journalNote.TaskId = null;
                }

                if (journalNote.RoomFlooringTypeAffectedId.HasValue)
                {
                    journalNote.RoomFlooringTypeAffected = null;
                    journalNote.RoomFlooringTypeAffectedId = null;
                }

                if (journalNote.JobAreaId.HasValue)
                    journalNote.JobAreaId = null;

                if (journalNote.JobVisitId.HasValue)
                    journalNote.JobVisitId = null;

                if (journalNote.JobAreaMaterialId.HasValue)
                    journalNote.JobAreaMaterialId = null;

                if (journalNote.ZoneId.HasValue)
                    journalNote.ZoneId = null;

            });
        }

        /// <summary>
        /// Create a copy of the source JournalNotes to the target JobId, assign it a new Id,
        /// and mark as modified by JobMerge
        /// </summary>
        /// <param name="journalNote"></param>
        /// <param name="targetJobId"></param>
        /// <returns></returns>
        private JournalNote MapJournalNote(JournalNote journalNote, Guid targetJobId)
        {
            return new JournalNote
            {
                Id = Guid.NewGuid(),
                JobId = targetJobId,
                TaskId = journalNote.TaskId,
                Author = journalNote.Author,
                Subject = journalNote.Subject,
                Note = journalNote.Note,
                CategoryId = journalNote.CategoryId,
                TypeId = journalNote.TypeId,
                VisibilityId = journalNote.VisibilityId,
                ActionDate = journalNote.ActionDate,
                JobVisitId = journalNote.JobVisitId.ValueOrNullIfEmpty(),
                ZoneId = journalNote.ZoneId.ValueOrNullIfEmpty(),
                JobAreaId = journalNote.JobAreaId.ValueOrNullIfEmpty(),
                JobAreaMaterialId = journalNote.JobAreaMaterialId.ValueOrNullIfEmpty(),
                IncludeInSummaryCoverPage = journalNote.IncludeInSummaryCoverPage,
                RuleIds = journalNote.RuleIds,
                ModifiedBy = nameof(JobMerge),
                ModifiedDate = DateTime.UtcNow
            };
        }
    }
}
