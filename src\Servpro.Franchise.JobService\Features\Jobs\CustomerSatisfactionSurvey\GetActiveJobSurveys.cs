﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Fnol
{
    public class GetActiveJobSurveys
    {
        public class Query : IRequest<IEnumerable<SurveyJobDto>>
        {
            public Query(IEnumerable<Guid> franchiseSetids)
            {
                FranchiseSetIds = franchiseSetids;
            }

            public IEnumerable<Guid> FranchiseSetIds { get; set; }
        }

        public class QueryValidator : AbstractValidator<Query>
        {
            public QueryValidator()
            {
            }
        }

        public class SurveyJobDto
        {
            public string ProjectNumber { get; set; }
            public int JobId { get; set; }
            public Guid JobGuid { get; set; }
            public string Name { get; set; }
            public string Email { get; set; }
            public DateTime InsertionDate { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<SurveyJobDto>>
        {
            private readonly ILogger<GetActiveJobSurveys> _logger;
            private readonly JobReadOnlyDataContext _context;

            public Handler(ILogger<GetActiveJobSurveys> logger, JobReadOnlyDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<IEnumerable<SurveyJobDto>> Handle(Query request, CancellationToken cancellationToken)
            {
                string franchiseSetIds = string.Join(",", request.FranchiseSetIds.Select(g => g.ToString()).ToArray());
                _logger.LogDebug("GetActiveJobSurveys - Getting active job survey data for FranchiseSetIds: {FranchiseSetIds}", franchiseSetIds);
                var jobs = await (from j in _context.Jobs
                                  join jc in _context.JobContactMap on j.Id equals jc.JobId
                                  join c in _context.Contacts on jc.ContactId equals c.Id
                                  where (request.FranchiseSetIds.Contains(j.FranchiseSetId) && 
                                         ActiveJobProgresses.List.Contains(j.JobProgress) &&
                                         WaterLossTypes.List.Contains(j.LossTypeId))
                                  select new
                                  {
                                      JobId = (int)j.ReferenceNumber,
                                      j.ProjectNumber,
                                      JobGuid = j.Id,
                                      PersonName = c.FirstName + " " + c.LastName,
                                      PersonEmail = c.EmailAddress,
                                      JobInsertionDate = j.CreatedDate,
                                      j.FranchiseSetId,
                                      PersonId = c.Id,
                                      ContactType = jc.JobContactTypeId,
                                      ContactModified = jc.ModifiedDate
                                  }).ToListAsync(cancellationToken);

                List<SurveyJobDto> surveys = new List<SurveyJobDto>();
                foreach (var job in jobs.OrderByDescending(j => j.ContactModified))
                {
                    if (!surveys.Any(j => j.JobId == job.JobId) && 
                         job.ContactType == Common.JobContactTypes.Customer &&
                         !string.IsNullOrEmpty(job.PersonEmail))
                    {
                        surveys.Add(new SurveyJobDto
                        {
                            JobId = job.JobId,
                            ProjectNumber = job.ProjectNumber,
                            JobGuid = job.JobGuid,
                            Name = job.PersonName,
                            Email = job.PersonEmail,
                            InsertionDate = job.JobInsertionDate,
                            FranchiseSetId = job.FranchiseSetId
                        });
                    }
                }

                return surveys;
            }

        }
    }
}
