﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class UpdateMiscellaneousDocuments
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public List<MiscellaneousDocument> Documents { get; set; }
            public UpdateDocumentCommandTypes CommandType { get; set; }
        }

        public class MiscellaneousDocument
        {
            public Guid Id { get; set; }
            public bool IsForUpload { get; set; }
        }

        public enum UpdateDocumentCommandTypes
        {
            UpdateIsForUploadOnly
        }

        public class Dto
        {
            public bool UpdatedSuccessfully { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
                RuleFor(m => m.Documents).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _db;

            public Handler(JobDataContext db)
            {
                _db = db;
            }

            public async Task<Dto> Handle(UpdateMiscellaneousDocuments.Command request, CancellationToken cancellationToken)
            {
                var job = await GetJobAsync(request.JobId, request.FranchiseSetId, cancellationToken);
                if (job == null)
                    return new Dto();

                var result = await HandleRequest(request, job, cancellationToken);

                return result;
            }

            private async Task<Job> GetJobAsync(Guid jobId, Guid franchiseSetId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs
                    .Include(j => j.MediaMetadata)
                    .FirstOrDefaultAsync(j => j.Id == jobId && j.FranchiseSetId == franchiseSetId, cancellationToken);
                return job;
            }

            private async Task<Dto> HandleRequest(Command request, Job job, CancellationToken cancellationToken)
            {
                switch (request.CommandType)
                {
                    case UpdateDocumentCommandTypes.UpdateIsForUploadOnly:
                        return await UpdateIsForUploadProperty(request, job, cancellationToken);
                }
                return new Dto();
            }

            private async Task<Dto> UpdateIsForUploadProperty(Command request, Job job, CancellationToken cancellationToken)
            {
                var result = new Dto();

                var documentsToUpdateLookup = request.Documents.ToDictionary(x => x.Id);

                var mediaMetadata = job.MediaMetadata
                    .Where(x => documentsToUpdateLookup.ContainsKey(x.Id))
                    .ToList();

                if (!mediaMetadata.Any())
                    return result;

                mediaMetadata.ForEach(x => x.IsForUpload = documentsToUpdateLookup[x.Id].IsForUpload);

                await _db.SaveChangesAsync(cancellationToken);

                result.UpdatedSuccessfully = true;

                return result;
            }
        }
    }
}