using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Events;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Split;
using Servpro.FranchiseSystems.Framework.Messaging.Offloading;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SplitFranchise = Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Dto.Franchise;

namespace Servpro.Franchise.JobService.Features.Franchise.Split
{
    public class FranchiseSplitStarted
    {
        public class Event : FranchiseSplitStartedEvent, IRequest
        {
            public Event(Guid id,
                int splitAttempt,
                SplitFranchise sourceFranchise,
                SplitFranchise targetFranchise,
                IEnumerable<MappingLocation> mappingLocations,
                bool copyVendors,
                DateTime splitAttemptExpiresOn,
                Guid correlationId)
                : base(id, splitAttempt, sourceFranchise, targetFranchise, mappingLocations, splitAttemptExpiresOn, copyVendors, correlationId)
            {
            }
        }
        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<FranchiseSplitStarted> _logger;
            private readonly JobDataContext _context;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IS3Client _s3Client;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private readonly int MaxErrorsPerEvent;
            private readonly IMapper _mapper;

            public Handler(
                ILogger<FranchiseSplitStarted> logger,
                JobDataContext context,
                IS3Client s3Client,
                IFranchiseServiceClient franchiseServiceClient,
                IConfiguration config,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility)
            {
                _logger = logger;
                _context = context;
                _s3Client = s3Client;
                _franchiseServiceClient = franchiseServiceClient;
                _mapper = mapper;
                MaxErrorsPerEvent = config.GetValue("Resales:MaxErrorPerEvent", 10);
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{@split}",
                    new
                    {
                        id = request.Id,
                        attempt = request.SplitAttempt,
                        franchiseId = request.SourceFranchise.Id,
                        sourceFranchiseSetId = request.SourceFranchise.FranchiseSetId,
                        targetFranchiseSetId = request.TargetFranchise.FranchiseSetId,
                    });
                _logger.LogInformation("Precessing split: {@payload}", request);

                var correlationId = request.CorrelationId;

                // contact mapping
                var contactMapping = await GetMappingAsync<ContactMapping>(
                    MappingType.Contact,
                    request, cancellationToken);
                if (contactMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Contact mappings: {@contactMapping}", contactMapping);

                // employeeMapping
                var employeeMapping = await GetMappingAsync<EmployeeMapping>(
                    MappingType.Employee,
                    request,
                    cancellationToken);
                if (employeeMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Employee mappings: {@employeeMapping}", employeeMapping);

                // equipmentMapping
                var equipmentMapping = await GetMappingAsync<EquipmentMapping>(
                    MappingType.Equipment,
                    request,
                    cancellationToken);
                if (equipmentMapping is null)
                    return Unit.Value;
                _logger.LogTrace("Equipment mappings: {@equipmentMapping}", equipmentMapping);

                // all distinct business that need to copied over to the target franchiseSet
                var marketingBusinessIds = contactMapping.SoldMarketingBusinesses
                    .Select(x => x.Id)
                    .Distinct()
                    .ToList();
                var businessToBeCopied = contactMapping.BusinessesToBeCopied
                    .Select(x => x.Id)
                    .Concat(marketingBusinessIds)
                    .Distinct()
                    .ToList();

                // list of all the equipment that will be copied
                var equipmentToBeCopiedIds = equipmentMapping.EquipmentSold
                    .Select(x => x.Id)
                    .Concat(equipmentMapping.EquipmentToBeCopied.Select(x => x.Id))
                    .Distinct()
                    .ToList();

                var sourceEntities = await GetSplitEntitiesAsync(
                    businessToBeCopied.ToHashSet(),
                    contactMapping.ContactsToBeCopied.Select(x => x.Id).ToHashSet(),
                    marketingBusinessIds.ToHashSet(),
                    request.SourceFranchise.Id,
                    equipmentToBeCopiedIds.ToHashSet(),
                    cancellationToken);

                var errors = await CopyDataAsync(
                    sourceEntities,
                    employeeMapping.EmployeeMap,
                    employeeMapping.Owner,
                    marketingBusinessIds.ToHashSet(),
                    request.Id,
                    request.TargetFranchise,
                    equipmentMapping.ServproOwnedBaseEquipmentTypes,
                    equipmentMapping.EquipmentSold,
                    cancellationToken);

                await _resaleSplitComboUtility.TriggerRescindByRscAsync(request.SourceFranchise.FranchiseSetId, 
                    request.SourceFranchise.Id, 
                    request.CorrelationId, 
                    cancellationToken);

                var resaleCompletedEvent = GenerateSplitCompletedEvent(request.Id,
                    request.SplitAttempt,
                    errors.Any() ? Status.Failed : Status.Succeeded,
                    errors.Any() ? errors.Count() : (int?)null,
                    request.CorrelationId);

                if (errors.Any())
                {
                    var outboxMessages = new List<OutboxMessage>()
                    {
                        GenerateOutboxMessage(resaleCompletedEvent, request.CorrelationId)
                    };
                    foreach (var errorBatch in errors.Batch(MaxErrorsPerEvent))
                    {
                        outboxMessages.Add(GenerateOutboxMessage(
                            GenerateSplitErrorOccuredEvent(request.Id, request.SplitAttempt, errorBatch, request.CorrelationId),
                            request.CorrelationId));
                    }
                    await _context.OutboxMessages.AddRangeAsync(outboxMessages, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    var outboxMessage = GenerateOutboxMessage(resaleCompletedEvent, request.CorrelationId);
                    await _context.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);
                }

                return Unit.Value;

            }

            private Dictionary<string, object> GetMappingOptions(
                Guid resaleId,
                Guid purchasingFranchiseSetId,
                Guid purchasingFranchiseId,
                string purchasingFranchiseName,
                string purchasingFranchiseState,
                ImmutableDictionary<Guid, Guid> employeeMapping,
                EmployeeMapping.Employee owner,
                ImmutableHashSet<Guid> servproOwnedBaseEquipmentIds)
                => new Dictionary<string, object>()
                {
                    { nameof(Job.Id), resaleId },
                    { nameof(Job.FranchiseSetId), purchasingFranchiseSetId },
                    { nameof(Job.FranchiseId), purchasingFranchiseId },
                    { nameof(Job.FranchiseName), purchasingFranchiseName },
                    { nameof(Job.FranchiseState), purchasingFranchiseState },
                    { SplitMappingProfile.EmployeeMappingKey, employeeMapping },
                    { SplitMappingProfile.OwnerKey, owner },
                    { SplitMappingProfile.ServproOwnedBaseEquipmentKey, servproOwnedBaseEquipmentIds }
                };

            private async Task<(HashSet<Guid> FailedEntities, IEnumerable<ErrorInfo> Errors)> ProcessEntitiesAsync<T, K>(
                Guid resaleId,
                Func<T, List<(string Name, Guid Id)>> failedDependencies,
                HashSet<Guid> previouslyCopiedEntityIds,
                HashSet<T> sourceEntities,
                Dictionary<string, object> mappingOptions,
                Action<T> sourceEntityAction,
                Action<K> targetEntityAction,
                Action<K, CancellationToken> trackChangesAsync,
                CancellationToken cancellationToken) where T : Entity<Guid>
            {
                var sourceEntitiesToCopy = sourceEntities.DistinctBy(x => x.Id);
                _logger.LogTrace("Already copied entities: ({copiedCount}/{totalCount}, {@copiedEntityIds})", previouslyCopiedEntityIds.Count, sourceEntities.Count, previouslyCopiedEntityIds);
                var errors = new List<ErrorInfo>();
                var failedEntities = new HashSet<Guid>();
                var entityName = typeof(T).Name;
                foreach (var sourceEntity in sourceEntitiesToCopy)
                {
                    try
                    {
                        var targetId = GuidTransformHelpers.TransformToManipulatedGuid(sourceEntity.Id, resaleId);
                        using var itemScope = _logger.BeginScope("{@entity}",
                            new { SourceId = sourceEntity.Id, TargetId = targetId, type = entityName });
                        _logger.LogInformation("Processing item.");
                        _logger.LogTrace("Source: {@entity}", sourceEntity);
                        if (targetId.HasValue && previouslyCopiedEntityIds.Contains(targetId.Value))
                        {
                            _logger.LogWarning("{type} has already been copied from previous resale attempt.", nameof(T));
                            continue;
                        }

                        // If dependency function was supplied, and has a failed dependency
                        if (failedDependencies != null)
                        {
                            var dependencyErrors = failedDependencies(sourceEntity);
                            if (dependencyErrors.Any())
                            {
                                foreach (var error in dependencyErrors)
                                {
                                    _logger.LogWarning("Unable to copy entity due to failed dependency, name: {dependencyName} id: {dependencyId}",
                                        error.Name, error.Id);
                                    errors.Add(
                                    new ErrorInfo(
                                        sourceEntity.Id.ToString(),
                                        entityName,
                                        $"Unable to copy entity due to failed dependency, name: {error.Name}, id: {error.Id}",
                                        string.Empty));
                                }
                                failedEntities.Add(sourceEntity.Id);
                                continue;
                            }
                        }
                        var targetEntity = _mapper.Map<K>(
                            sourceEntity,
                            opt => opt.Items.AddMany(mappingOptions));

                        if (sourceEntityAction != null)
                        {
                            sourceEntityAction(sourceEntity);
                        }

                        if (targetEntityAction != null)
                        {
                            targetEntityAction(targetEntity);
                        }

                        _logger.LogTrace("Mapped entity: {@targetEntity}", targetEntity);
                        try
                        {
                            if (trackChangesAsync != null)
                            {
                                trackChangesAsync(targetEntity, cancellationToken);
                                await _context.SaveChangesIndependentlyAsync(cancellationToken);
                                _logger.LogInformation("Item copied successfully.");
                            }
                        }
                        catch (Exception ex)
                        {
                            _context.Remove(targetEntity);
                            failedEntities.Add(sourceEntity.Id);
                            errors.Add(new ErrorInfo(
                                sourceEntity.Id.ToString(),
                                entityName,
                                ex?.InnerException?.Message, ex.StackTrace));
                            _logger.LogError(ex, "Database Error processing item.");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedEntities.Add(sourceEntity.Id);
                        errors.Add(new ErrorInfo(
                            sourceEntity.Id.ToString(),
                            entityName,
                            ex.Message, ex.StackTrace));
                        _logger.LogError(ex, "Error processing item.");
                    }
                }
                return (failedEntities, errors);
            }

            private async Task<IEnumerable<ErrorInfo>> CopyDataAsync(
                SplitEntity sourceEntities,
                ImmutableDictionary<Guid, Guid> employeeMapping,
                EmployeeMapping.Employee owner,
                HashSet<Guid> marketingBusinessIds,
                Guid splitId,
                SplitFranchise purchasingFranchise,
                ImmutableHashSet<Guid> servproOwnedBaseEquipmentIds,
                ImmutableList<EquipmentMapping.EquipmentDto> equipmentSold,
                CancellationToken cancellationToken)
            {

                var mappingOptions = GetMappingOptions(splitId,
                    purchasingFranchise.FranchiseSetId,
                    purchasingFranchise.Id,
                    purchasingFranchise.Name,
                    purchasingFranchise.State,
                    employeeMapping,
                    owner,
                    servproOwnedBaseEquipmentIds);

                _logger.LogInformation("Processing {entity}", nameof(Business));
                var businessTargetIds = sourceEntities.Businesses
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, splitId))
                    .ToList();
                var alreadyCopiedBusinessIds = await _context.Businesses
                    .Where(x => businessTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                var businessResult = await ProcessEntitiesAsync<Business, SplitBusiness>(
                    splitId,
                    null,
                    alreadyCopiedBusinessIds.ToHashSet(),
                    sourceEntities.Businesses,
                    mappingOptions,
                    null,
                    null,
                    async (targetEntity, cancellationToken) =>
                    {
                        await _context.Businesses.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);

                _logger.LogInformation("Processing {entity}", nameof(Contact));
                var contactTargetIds = sourceEntities.Contacts
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, splitId))
                    .ToList();
                var alreadyCopiedContactIds = await _context.Contacts
                    .Where(x => contactTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                var contactResult = await ProcessEntitiesAsync<Contact, SplitContact>(
                    splitId,
                    contact =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();
                        if (contact.BusinessId.HasValue && businessResult.FailedEntities.Contains(contact.BusinessId.Value))
                            failedDependencies.Add((nameof(Business), contact.BusinessId.Value));
                        return failedDependencies;
                    },
                    alreadyCopiedContactIds.ToHashSet(),
                    sourceEntities.Contacts,
                    mappingOptions,
                    sourceEntity =>
                    {
                        // if the contact is associated to a Marketing Business, we need to remove the marketing flag
                        if (sourceEntity.BusinessId.HasValue && marketingBusinessIds.Contains(sourceEntity.BusinessId.Value))
                        {
                            sourceEntity.IsMarketingContact = false;
                        }
                    },
                    null,
                    async (targetEntity, cancellationToken) =>
                    {
                        await _context.Contacts.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);

                _logger.LogInformation("Processing {entity}", nameof(JobService.Models.Drybook.EquipmentType));
                var equipmentTypeTargetIds = sourceEntities.EquipmentTypes
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, splitId))
                    .ToList();
                var alreadyCopiedEquipmentTypeIds = await _context.EquipmentTypes
                    .Where(x => equipmentTypeTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                var equipmentTypeResult = await ProcessEntitiesAsync<JobService.Models.Drybook.EquipmentType, SplitEquipmentType>(
                    splitId,
                    null,
                    alreadyCopiedEquipmentTypeIds.ToHashSet(),
                    sourceEntities.EquipmentTypes,
                    mappingOptions,
                    null,
                    null,
                    async (targetEntity, cancellationToken) =>
                    {
                        await _context.EquipmentTypes.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);

                _logger.LogInformation("Processing {entity}", nameof(JobService.Models.Drybook.EquipmentModel));
                var equipmentModelTargetIds = sourceEntities.EquipmentModels
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, splitId))
                    .ToList();
                var alreadyCopiedEquipmentModelIds = await _context.EquipmentModels
                    .Where(x => equipmentModelTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
                var equipmentModelResult = await ProcessEntitiesAsync<JobService.Models.Drybook.EquipmentModel, SplitEquipmentModel>(
                    splitId,
                    equipmentModel =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (equipmentTypeResult.FailedEntities.Contains(equipmentModel.EquipmentTypeId))
                            failedDependencies.Add((nameof(JobService.Models.Drybook.EquipmentType), equipmentModel.EquipmentTypeId));

                        return failedDependencies;
                    },
                    alreadyCopiedEquipmentModelIds.ToHashSet(),
                    sourceEntities.EquipmentModels,
                    mappingOptions,
                    null,
                    null,
                    async (targetEntity, cancellationToken) =>
                    {
                        await _context.EquipmentModels.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);

                _logger.LogInformation("Processing {entity}", nameof(JobService.Models.Drybook.Equipment));
                var equipmentTargetIds = sourceEntities.Equipment
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, splitId))
                    .ToList();
                var alreadyCopiedEquipmentIds = await _context.Equipments
                    .Where(x => equipmentTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                var equipmentSoldIds = equipmentSold.Select(x => x.Id).ToHashSet();
                var equipmentSoldTargetIds = equipmentSold
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, splitId)).ToHashSet();
                var equipmentResult = await ProcessEntitiesAsync<JobService.Models.Drybook.Equipment, SplitEquipment>(
                    splitId,
                    equipment =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (equipmentModelResult.FailedEntities.Contains(equipment.EquipmentModelId))
                            failedDependencies.Add((nameof(JobService.Models.Drybook.EquipmentModel), equipment.EquipmentModelId));

                        return failedDependencies;
                    },
                    alreadyCopiedEquipmentIds.ToHashSet(),
                    sourceEntities.Equipment,
                    mappingOptions,
                    sourceEntity =>
                    {
                        if (equipmentSoldIds.Contains(sourceEntity.Id))
                        {
                            sourceEntity.IsDeleted = true;
                        }
                    },
                    targetEntity =>
                    {
                        if (!equipmentSoldTargetIds.Contains(targetEntity.Id))
                        {
                            targetEntity.IsDeleted = true;
                        }
                    },
                     async (targetEntity, cancellationToken) =>
                     {
                         await _context.Equipments.AddAsync(targetEntity, cancellationToken);
                     },
                    cancellationToken);

                _logger.LogInformation("Processing {entity}", nameof(Job));
                var jobResult = await ProcessJobsAsync(
                    splitId,
                    sourceEntities.Jobs,
                    purchasingFranchise.FranchiseSetId,
                    businessResult.FailedEntities,
                    contactResult.FailedEntities,
                    employeeMapping,
                    owner,
                    cancellationToken);

                var equipmentPlacementResult = await ProcessEquipmentPlacementsAsync(
                    splitId,
                    sourceEntities.EquipmentPlacements,
                    equipmentResult.FailedEntities,
                    cancellationToken);

                return businessResult.Errors
                    .Concat(contactResult.Errors)
                    .Concat(equipmentTypeResult.Errors)
                    .Concat(equipmentModelResult.Errors)
                    .Concat(equipmentResult.Errors)
                    .Concat(jobResult.Errors)
                    .Concat(equipmentPlacementResult.Errors);
            }

            private async Task<(HashSet<Guid> FailedEntities, IEnumerable<ErrorInfo> Errors)> ProcessEquipmentPlacementsAsync(
                Guid splitId,
                HashSet<Models.Drybook.EquipmentPlacement> equipmentPlacements,
                HashSet<Guid> failedEntities,
                CancellationToken cancellationToken)
            {
                var result = (FailedEntities: new HashSet<Guid>(), Errors: new List<ErrorInfo>());
                var equipmentPlacementsToUpdate = equipmentPlacements.DistinctBy(x => x.Id);
                foreach (var ep in equipmentPlacementsToUpdate)
                {
                    using var placementIdScope = _logger.BeginScope("{equipmentPlacementId}", ep.Id);
                    var targetEquipmentId = GuidTransformHelpers.TransformToManipulatedGuid(ep.EquipmentId, splitId).Value;

                    if (ep.EquipmentId == targetEquipmentId)
                    {
                        _logger.LogInformation("Equipment placement already moved");
                        continue;
                    }

                    _logger.LogInformation("Processing equipmentPlacement");
                    if(failedEntities.Contains(ep.EquipmentId))
                    {
                        _logger.LogWarning("Unable to copy entity due to failed dependency, name: {dependencyName} id: {dependencyId}",
                            nameof(Models.Drybook.Equipment), ep.EquipmentId);
                        result.Errors.Add(
                        new ErrorInfo(
                            ep.Id.ToString(),
                            nameof(Models.Drybook.EquipmentPlacement),
                            $"Unable to copy entity due to failed dependency, name: {nameof(Models.Drybook.EquipmentPlacement)}, id: {ep.Id}",
                            string.Empty));
                        continue;
                    }
                    ep.EquipmentId = targetEquipmentId;
                    try
                    {
                        await _context.SaveChangesIndependentlyAsync(cancellationToken);
                        _logger.LogInformation("Updated equipment placement");
                    }
                    catch (Exception ex)
                    {
                        _context.Remove(ep);
                        result.FailedEntities.Add(ep.Id);
                        result.Errors.Add(new ErrorInfo(
                            ep.Id.ToString(),
                            nameof(Models.Drybook.EquipmentPlacement),
                            ex.InnerException.Message, ex.StackTrace));
                        _logger.LogError(ex, "Database Error processing item.");
                    }
                }
                return result;
            }

            private async Task<(HashSet<Guid> FailedEntities, IEnumerable<ErrorInfo> Errors)> ProcessJobsAsync(
                Guid splitId,
                HashSet<Job> jobs,
                Guid franchiseSetId,
                HashSet<Guid> failedBusinesses,
                HashSet<Guid> failedContacts,
                ImmutableDictionary<Guid, Guid> employeeMap,
                EmployeeMapping.Employee owner,
                CancellationToken cancellationToken)
            {
                var result = (FailedEntities: new HashSet<Guid>(), Errors: new List<ErrorInfo>());
                //The franchiseId does not change during a split, instead we need to check if a job was already copied by checking franchiseSetId
                var jobsNotAlreadyMoved = jobs.Where(x => x.FranchiseSetId != franchiseSetId).ToHashSet();
                foreach (var job in jobsNotAlreadyMoved)
                {
                    _logger.LogInformation("Processing job: {jobId}", job.Id);
                    var dependencyErrors = GetJobDependencyErrors(job, failedBusinesses, failedContacts).ToList();
                    if (dependencyErrors.Any())
                    {
                        _logger.LogInformation("Found {dependencyErrorCount} dependency errors", dependencyErrors.Count);
                        foreach (var error in dependencyErrors)
                        {
                            _logger.LogWarning("Unable to copy entity due to failed dependency, name: {dependencyName} id: {dependencyId}",
                                error.Name, error.Id);
                            result.Errors.Add(
                            new ErrorInfo(
                                job.Id.ToString(),
                                nameof(Job),
                                $"Unable to copy entity due to failed dependency, name: {error.Name}, id: {error.Id}",
                                string.Empty));
                        }
                        result.FailedEntities.Add(job.Id);
                        continue;
                    }
                    job.FranchiseSetId = franchiseSetId;
                    if (job.CallerId.HasValue)
                        job.CallerId = GuidTransformHelpers.TransformToManipulatedGuid(job.CallerId.Value, splitId);
                    if (job.CustomerId.HasValue)
                        job.CustomerId = GuidTransformHelpers.TransformToManipulatedGuid(job.CustomerId.Value, splitId);
                    foreach (var jobContact in job.JobContacts)
                    {
                        jobContact.ContactId = GuidTransformHelpers.TransformToManipulatedGuid(jobContact.ContactId, splitId).Value;
                    }
                    foreach (var jobBusinesses in job.JobBusinesses)
                    {
                        jobBusinesses.BusinessId = GuidTransformHelpers.TransformToManipulatedGuid(jobBusinesses.BusinessId, splitId).Value;
                    }
                    foreach(var media in job.MediaMetadata)
                    {
                        media.FranchiseSetId = franchiseSetId;
                    }
                    foreach(var task in job.Tasks)
                    {
                        task.FranchiseSetId = franchiseSetId;
                    }
                    job.SiteAppointmentById = job.SiteAppointmentById.HasValue
                        ? GetNewEmployeeId(job.SiteAppointmentById.Value, employeeMap, owner)
                        : job.SiteAppointmentById;
                    job.PriorityResponderId = GetNewEmployeeId(job.PriorityResponderId, employeeMap, owner) ?? Guid.Empty;
                    job.JobFileCoordinatorId = job.JobFileCoordinatorId.HasValue
                        ? GetNewEmployeeId(job.JobFileCoordinatorId.Value, employeeMap, owner)
                        : job.JobFileCoordinatorId;
                    job.ProjectManagerId = job.ProjectManagerId.HasValue
                        ? GetNewEmployeeId(job.ProjectManagerId.Value, employeeMap, owner)
                        : job.ProjectManagerId;
                    job.ProductionManagerId = job.ProductionManagerId.HasValue
                        ? GetNewEmployeeId(job.ProductionManagerId.Value, employeeMap, owner)
                        : job.ProductionManagerId;
                    job.CrewChiefId = job.CrewChiefId.HasValue
                        ? GetNewEmployeeId(job.CrewChiefId.Value, employeeMap, owner)
                        : job.CrewChiefId;
                    job.GeneralManagerId = job.GeneralManagerId.HasValue
                        ? GetNewEmployeeId(job.GeneralManagerId.Value, employeeMap, owner)
                        : job.GeneralManagerId;
                    job.ReconSupportId = job.ReconSupportId.HasValue
                        ? GetNewEmployeeId(job.ReconSupportId.Value, employeeMap, owner)
                        : job.ReconSupportId;
                    job.MarketingRepId = job.MarketingRepId.HasValue
                        ? GetNewEmployeeId(job.MarketingRepId.Value, employeeMap, owner)
                        : job.MarketingRepId;
                    try
                    {
                        await _context.SaveChangesAsync(cancellationToken);
                        _logger.LogInformation("Updated job");
                    }
                    catch (Exception ex)
                    {
                        _context.Remove(job);
                        result.FailedEntities.Add(job.Id);
                        result.Errors.Add(new ErrorInfo(
                            job.Id.ToString(),
                            nameof(Job),
                            ex.InnerException.Message, ex.StackTrace));
                        _logger.LogError(ex, "Database Error processing item.");
                    }
                }
                return result;
            }

            private static Guid? GetNewEmployeeId(
                Guid oldEmployeeId,
                ImmutableDictionary<Guid, Guid> employeeMapping,
                EmployeeMapping.Employee owner)
            {
                if (oldEmployeeId == Guid.Empty)
                {
                    return null;
                }

                //check if the franchise provided a mapping, if a mapping was not provided, default to the owner
                return employeeMapping.ContainsKey(oldEmployeeId) ? employeeMapping[oldEmployeeId] : owner.Id;
            }

            private IEnumerable<(string Name, Guid Id)> GetJobDependencyErrors(
                Job job,
                HashSet<Guid> failedBusinesses,
                HashSet<Guid> failedContacts)
            {
                if (job.CallerId.HasValue && failedContacts.Contains(job.CallerId.Value))
                {
                    yield return (nameof(Contact), job.CallerId.Value);
                }
                if (job.CustomerId.HasValue && failedContacts.Contains(job.CustomerId.Value))
                {
                    yield return (nameof(Contact), job.CustomerId.Value);
                }
                foreach (var jobContact in job.JobContacts)
                {
                    if (failedContacts.Contains(jobContact.ContactId))
                    {
                        yield return (nameof(Contact), jobContact.ContactId);
                    }
                }
                foreach (var jobBusiness in job.JobBusinesses)
                {
                    if (failedBusinesses.Contains(jobBusiness.BusinessId))
                    {
                        yield return (nameof(Business), jobBusiness.BusinessId);
                    }
                }
            }

            /// <summary>
            /// Attempts to get the Mapping for the specified mappingType from S3
            /// </summary>
            /// <typeparam name="T"></typeparam>
            /// <param name="mappingType"></param>
            /// <param name="request"></param>
            /// <param name="cancellationToken"></param>
            /// <returns></returns>
            private async Task<T> GetMappingAsync<T>(
                MappingType mappingType,
                Event request,
                CancellationToken cancellationToken)
                where T : BaseMapping
            {
                var mappingLocation = request.MappingLocations
                    .FirstOrDefault(x => x.MappingType == mappingType);
                if (mappingLocation is null)
                {
                    _logger.LogError("Mapping configuration for {mappingType} was not provided. Split failed. {@providedMappings}", mappingType, request.MappingLocations);
                    var completedEvent = GenerateSplitCompletedEvent(
                        request.Id,
                        request.SplitAttempt,
                        Status.Failed,
                        1,
                        request.CorrelationId);
                    var errorEvent = GenerateSplitErrorOccuredEvent(
                        request.Id,
                        request.SplitAttempt,
                        new List<ErrorInfo> { new ErrorInfo(null, null, "ValidationError", $"Mapping configuration for {mappingType} was not provided. Split failed.") },
                        request.CorrelationId);
                    await _context.OutboxMessages.AddRangeAsync(
                        GenerateOutboxMessage(completedEvent, request.CorrelationId),
                        GenerateOutboxMessage(errorEvent, request.CorrelationId));
                    await _context.SaveChangesAsync(cancellationToken);
                    return default;
                }

                try
                {
                    var mapping = await _s3Client.GetResaleMappingAsync<T>(
                        mappingLocation.BucketName, mappingLocation.Path, cancellationToken);
                    return mapping;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error reading mapping from S3. {@mappingLocation}", mappingLocation);
                    var completedEvent = GenerateSplitCompletedEvent(
                        request.Id,
                        request.SplitAttempt,
                        Status.Failed,
                        1,
                        request.CorrelationId);
                    var errorEvent = GenerateSplitErrorOccuredEvent(
                        request.Id,
                        request.SplitAttempt,
                        new List<ErrorInfo> { new ErrorInfo(null, ex.GetType().Name, ex.Message, ex.StackTrace) },
                        request.CorrelationId);
                    await _context.OutboxMessages.AddRangeAsync(
                        GenerateOutboxMessage(completedEvent, request.CorrelationId),
                        GenerateOutboxMessage(errorEvent, request.CorrelationId));
                    await _context.SaveChangesAsync(cancellationToken);
                }
                return default;
            }

            private FranchiseSplitCompletedEvent GenerateSplitCompletedEvent(
                Guid splitId,
                int splitAttempt,
                Status status,
                int? errorCount,
                Guid correlationId)
                => new FranchiseSplitCompletedEvent(
                    splitId,
                    splitAttempt,
                    Service.JobService,
                    status,
                    errorCount,
                    correlationId);

            private FranchiseSplitErrorOccurredEvent GenerateSplitErrorOccuredEvent(
                Guid splitId,
                int splitAttempt,
                IEnumerable<ErrorInfo> errors,
                Guid correlationId)
                => new FranchiseSplitErrorOccurredEvent(
                    splitId,
                    splitAttempt,
                    Service.JobService,
                    errors.ToList(),
                    correlationId);

            private OutboxMessage GenerateOutboxMessage<T>(
                T message,
                Guid correlationId,
                string createdBy = null)
            {
                if (createdBy is null)
                    createdBy = typeof(T).Name;
                return new OutboxMessage(message.ToJson(), typeof(T).Name, correlationId, createdBy);
            }

            private async Task<SplitEntity> GetSplitEntitiesAsync(
                HashSet<Guid> businessIds,
                HashSet<Guid> contactIds,
                HashSet<Guid> marketingBusinessIds,
                Guid sellingFranchiseId,
                HashSet<Guid> equipmentIds,
                CancellationToken cancellationToken)
            {
                //jobs
                var jobs = await _context.Jobs
                    .Include(x => x.JobContacts)
                    .Include(x => x.JobBusinesses)
                    .Include(x => x.MediaMetadata)
                    .Include(x => x.Tasks)
                    .Where(j => j.FranchiseId == sellingFranchiseId)
                    .ToListAsync(cancellationToken);

                var jobIds = jobs.Select(j => j.Id).ToHashSet();

                var newJobContactIds = jobs
                    .SelectMany(x => x.JobContacts)
                    .Where(x => !contactIds.Contains(x.ContactId))
                    .Select(x => x.ContactId)
                    .Distinct()
                    .ToHashSet();

                var customerCallerIds = jobs
                    .SelectMany(x =>
                    {
                        var ids = new List<Guid>();
                        if (x.CallerId.HasValue)
                            ids.Add(x.CallerId.Value);
                        if (x.CustomerId.HasValue)
                            ids.Add(x.CustomerId.Value);
                        return ids;
                    })
                    .Where(x => !newJobContactIds.Contains(x))
                    .Distinct();
                newJobContactIds = newJobContactIds
                    .Concat(customerCallerIds)
                    .ToHashSet();

                //contacts/businesses
                var contacts = await _context.Contacts
                    .Where(x => contactIds.Contains(x.Id) || newJobContactIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                //include all the contacts businesses
                //this is incase they weren't selected but were on a job
                var allBusinessIds = contacts
                    .Where(x => x.BusinessId.HasValue)
                    .Select(x => x.BusinessId.Value)
                    .Concat(businessIds)
                    .Distinct()
                    .ToHashSet();

                var businesses = await _context.Businesses
                    .Where(x => allBusinessIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                //need to get all the contacts associated to the marketing businesses
                var marketingContacts = await _context.Contacts
                    .Where(x => x.BusinessId.HasValue && marketingBusinessIds.Contains(x.BusinessId.Value)
                     && !contactIds.Contains(x.Id))
                    .Distinct()
                    .ToListAsync(cancellationToken);

                businesses = businesses
                    .GroupBy(x => x.Id)
                    .Select(x => x.First())
                    .ToList();

                contacts = contacts
                    .Concat(marketingContacts)
                    .ToList();


                //equipment
                var equipment = await _context.Equipments
                    .Include(x => x.EquipmentModel)
                    .Where(x => equipmentIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                var equipmentTypeIds = equipment
                    .Select(x => x.EquipmentModel)
                    .Where(x => x.EquipmentTypeId != Guid.Empty)
                    .Select(x => x.EquipmentTypeId)
                    .Distinct()
                    .ToList();

                var equipmentTypes = await _context.EquipmentTypes
                    .Where(x => equipmentTypeIds.Contains(x.Id))
                    .ToListAsync(cancellationToken);

                var equipmentTypeMap = equipmentTypes.ToDictionary(x => x.Id, x => x);
                foreach (var equip in equipment)
                {
                    equip.EquipmentModel.EquipmentType = equipmentTypeMap[equip.EquipmentModel.EquipmentTypeId];
                }

                equipmentTypes = equipmentTypes.Where(x => x.FranchiseSetId.HasValue).ToList();
                var equipmentModels = equipment.Select(x => x.EquipmentModel)
                    .Where(x => x.FranchiseSetId.HasValue)
                    .GroupBy(x => x.Id)
                    .Select(x => x.First())
                    .ToList();

                //job area
                var jobAreaIds = await _context.JobAreas
                    .AsNoTracking()
                    .Where(ja => jobIds.Contains(ja.JobId))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);

                //equipment placement
                var equipmentPlacements = await _context.EquipmentPlacements
                    .Where(ep => jobAreaIds.Contains(ep.JobAreaId))
                    .ToListAsync(cancellationToken);

                return new SplitEntity(
                    jobs,
                    businesses,
                    contacts,
                    equipment,
                    equipmentModels,
                    equipmentTypes,
                    equipmentPlacements);
            }
        }
    }
}
