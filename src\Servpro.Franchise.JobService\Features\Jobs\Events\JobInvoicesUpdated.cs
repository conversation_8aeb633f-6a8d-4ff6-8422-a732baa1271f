﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Enums;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobInvoicesUpdated
    {
        public class Event : InvoicesUpdatedEvent, IRequest
        {
            public Event(List<InvoiceUpdatedDto> invoices, Guid correlationId) : base(invoices, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<JobInvoicesUpdated> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<JobInvoicesUpdated> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler started for {Count} invoices to update", incomingEvent.Invoices.Count);

                var jobIds = incomingEvent.Invoices
                    .Select(i => i.JobId)
                    .Distinct()
                    .ToList();

                _logger.LogDebug("Distinct JobIds found: {JobCount}", jobIds.Count);

                Dictionary<Guid, Models.Job> jobs;

                try
                {
                    jobs = await _context.Jobs
                        .Where(j => jobIds.Contains(j.Id))
                        .Include(m => m.MediaMetadata)
                        .ThenInclude(j => j.JobInvoice)
                        .ToDictionaryAsync(j => j.Id, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error fetching jobs from DB");
                    throw;
                }

                foreach (var updatedInvoice in incomingEvent.Invoices)
                {
                    _logger.LogDebug("Processing invoice {InvoiceId} for Job {JobId}",
                        updatedInvoice.InvoiceId, updatedInvoice.JobId);

                    if (!jobs.TryGetValue(updatedInvoice.JobId, out var job))
                    {
                        _logger.LogWarning("Job not found for invoice {InvoiceId} - JobId {JobId}",
                            updatedInvoice.InvoiceId, updatedInvoice.JobId);
                        continue;
                    }

                    var invoiceMedia = job.MediaMetadata
                        .FirstOrDefault(q => q.JobId == updatedInvoice.JobId
                                             && q.JobInvoice?.Id == updatedInvoice.InvoiceId);

                    if (invoiceMedia?.JobInvoice != null)
                    {
                        var invoiceInfo = invoiceMedia.JobInvoice;
                        invoiceInfo.Description = updatedInvoice.Description ?? string.Empty;
                        invoiceInfo.InvoiceNumber = updatedInvoice.InvoiceNumber ?? string.Empty;
                        invoiceInfo.Source = updatedInvoice.Source ?? string.Empty;
                        invoiceInfo.Date = updatedInvoice.Date;

                        if (updatedInvoice.EventType == InvoiceEventType.Invoice)
                        {
                            invoiceInfo.Amount = updatedInvoice.Amount;
                        }
                        else if (updatedInvoice.EventType == InvoiceEventType.Collected)
                        {
                            invoiceInfo.AmountCollected += updatedInvoice.Amount;
                        }
                    }
                    else
                    {
                        _logger.LogDebug("Invoice {InvoiceId} not found in job {JobId}, ignoring...",
                            updatedInvoice.InvoiceId, updatedInvoice.JobId);
                    }
                }

                try
                {
                    await _context.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("All invoices updated in DB successfully.");
                }
                catch (DbUpdateException dbEx)
                {
                    _logger.LogError(dbEx, "Failed to update invoices in DB");
                    throw;
                }

                _logger.LogInformation("Handler completed for {Count} invoices updated.", incomingEvent.Invoices.Count);
                return Unit.Value;
            }
        }
    }
}
