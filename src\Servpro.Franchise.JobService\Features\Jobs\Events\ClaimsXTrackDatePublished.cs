﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Corporate;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class ClaimsXTrackDatePublished
    {
        public class Event : ClaimsXTrackDatePublishedEvent, IRequest
        {
            public Event(ClaimsXTrackDatePublishedEvent.PublishedDateDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<ClaimsXTrackDatePublished> _logger;
            private readonly List<Guid> HandledJobDateTypeIds = new List<Guid>()
            {
                JobDateTypes.CustomerCalled,
                JobDateTypes.InitialOnSiteArrival,
                JobDateTypes.WorkStartDate,
                JobDateTypes.Complete,
                JobDateTypes.TargetJobStart,
                JobDateTypes.TargetJobComplete,
                JobDateTypes.Cancellation,
                JobDateTypes.EstimateReturnedFromXact,
                JobDateTypes.DryingStarted,
                JobDateTypes.DryingComplete
            };

            public Handler(JobDataContext db, ILogger<ClaimsXTrackDatePublished> logger)
            {
                _db = db;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event incomingEvent, CancellationToken cancellationToken)
            {

                _logger.LogInformation("Started with: {@incomingEvent}", incomingEvent);

                if (!HandledJobDateTypeIds.Contains(incomingEvent.PublishedDate.JobDateTypeId))
                {
                    _logger.LogInformation("JobDateTypeId not covered by this handler. Exiting.");
                    return Unit.Value;
                }

                var job = await _db.Jobs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(j => j.CorporateJobNumber.HasValue && j.CorporateJobNumber.Value == incomingEvent.PublishedDate.CorporateJobNumber, cancellationToken);
                
                if (job == null)
                {
                    _logger.LogInformation("Job not found. Exiting.");
                    return Unit.Value;
                }

                if (job.JobDates.Any(d => d.JobDateTypeId == incomingEvent.PublishedDate.JobDateTypeId))
                {
                    _logger.LogInformation("JobDateTypeId previously set for this job. Exiting.");
                    return Unit.Value;
                }

                var triggeredEvent = Map(job.Id, incomingEvent);

                var outboxMessage = new OutboxMessage(triggeredEvent.ToJson(), nameof(JobDateCreatedEvent), incomingEvent.CorrelationId, nameof(ClaimsXTrackDatePublishedEvent));
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("JobDateCreatedEvent generated. Exiting.");

                return Unit.Value;
            }

            private JobDateCreatedEvent Map(Guid jobId, ClaimsXTrackDatePublishedEvent incomingEvent)
            {
                var dto = new JobDateCreatedEvent.JobDateCreatedDto(jobId, incomingEvent.PublishedDate.JobDateTypeId, incomingEvent.PublishedDate.JobDate, nameof(ClaimsXTrackDatePublishedEvent));
                return new JobDateCreatedEvent(dto, incomingEvent.CorrelationId);
            }
        }
    }
}