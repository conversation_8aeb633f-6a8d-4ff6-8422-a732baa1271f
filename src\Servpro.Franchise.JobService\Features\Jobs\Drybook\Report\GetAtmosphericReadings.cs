﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.Franchise.LookupService.Features.LookUps.Lead;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GetAtmosphericReadings
    {
        public class Query : IRequest<AtmosphericReadingsDto>
        {
            public Query(Job job, TimeZoneDto franchiseTimeZone = null)
            {
                Job = job;
                FranchiseTimeZone = franchiseTimeZone;
            }
            public Job Job { get; set; }
            public TimeZoneDto FranchiseTimeZone { get; set; }
        }

        public class ZoneDehuPlacement
        {
            public ZoneDehuPlacement(Guid? zoneId, string equipmentModel, string assetNumber, DateTime beginDate, DateTime? endDate)
            {
                ZoneId = zoneId;
                EquipmentModel = equipmentModel;
                AssetNumber = assetNumber;
                BeginDate = beginDate;
                EndDate = endDate;
            }

            public Guid? ZoneId { get; set; }
            public string EquipmentModel { get; set; }
            public string AssetNumber { get; set; }
            public DateTime BeginDate { get; set; }
            public DateTime? EndDate { get; set; }
        }

        public class DehuReading
        {
            public Guid EquipmentPlacementId { get; set; }
            public Guid JobVisitId { get; set; }
            public Guid ZoneId { get; set; }
            public decimal? RelativeHumidity { get; set; }
            public decimal? Temperature { get; set; }
            public int? HourCounter { get; set; }
            public decimal? GrainsPerPound { get; set; }
            public string EquipmentModel { get; set; }
            public string AssetNumber { get; set; }
            public DateTime BeginDate { get; set; }
            public DateTime? EndDate { get; set; }

        }

        public class Handler : IRequestHandler<Query, AtmosphericReadingsDto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly ILogger<GenerateDryingReport> _logger;

            private GetLookups.Dto Lookups { get; set; }

            public Handler(JobReadOnlyDataContext context,
                IFranchiseServiceClient franchiseServiceClient,
                ILookupServiceClient lookupServiceClient,
                ILogger<GenerateDryingReport> logger)
            {
                _context = context;
                _lookupServiceClient = lookupServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
                _logger = logger;
            }

            public async Task<AtmosphericReadingsDto> Handle(Query request, CancellationToken cancellationToken)
            {
                var job = request.Job;

                using var logScope = _logger.BeginScope("{jobId}", job.Id);

                _logger.LogInformation("DryingReportLog - starting GetAtmosphericReadings");

                var dto = new AtmosphericReadingsDto();

                Lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);

                var stopwatch = Stopwatch.StartNew();

                job = await GetJobAsync(job.Id, cancellationToken);

                stopwatch.Stop();
                _logger.LogInformation("Got job {jobId} for atmospheric readings in {elapsedTime}.", job.Id, stopwatch.Elapsed);

                var zoneDehuPlacements = GetZoneDehuPlacements(job);
                _logger.LogDebug("DryingReportLog - starting GetAtmosphericReadings-GetDehuReadings for jobId: {Id}", job.Id);

                var dehuReadings = GetDehuReadings(job, zoneDehuPlacements, request.FranchiseTimeZone);
                _logger.LogDebug("DryingReportLog - starting GetAtmosphericReadings-GetZoneReadingSets for jobId: {Id}", job.Id);

                var zoneReadingSets = GetZoneReadingSets(job, dehuReadings, request.FranchiseTimeZone);

                dto.ZoneReadingSets = zoneReadingSets;

                _logger.LogDebug("DryingReportLog - starting GetAtmosphericReadings-UpdatePreviousReadings/Threshold for jobId: {Id}", job.Id);

                dto.UpdatePreviousReadingsAndThresholdFlags();

                _logger.LogInformation("DryingReportLog - completed GetAtmosphericReadings");
                return dto;
            }

            private async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                    .FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                job.Zones = await _context.Zones.Where(z => z.JobId == job.Id).ToListAsync(cancellationToken);
                var zoneIds = job.Zones.Select(x => x.Id).ToList();

                var zoneReadings = await _context.ZoneReadings.Where(zr => zoneIds.Contains(zr.ZoneId))
                    .ToListAsync(cancellationToken);
                var equipmentPlacementsReadings = await _context.EquipmentPlacementReadings.Where(zr => zoneIds.Contains(zr.ZoneId))
                    .ToListAsync(cancellationToken);

                job.JobAreas = await _context.JobAreas.Where(ja => ja.JobId == job.Id).ToListAsync(cancellationToken);
                var jobAreaIds = job.JobAreas.Select(x => x.Id).ToList();

                var equipmentPlacements = await _context.EquipmentPlacements
                    .Where(ep => jobAreaIds.Contains(ep.JobAreaId))
                    .ToListAsync(cancellationToken);

                var equipmentIds = equipmentPlacements.Select(x => x.EquipmentId).ToList();

                var equipments = await _context.Equipments
                    .Include(x => x.EquipmentModel)
                    .ThenInclude(e => e.EquipmentType)
                    .TagWith(RemoveLastOrderByInterceptor.QueryTag)
                    .Where(e => equipmentIds.Contains(e.Id))
                    .ToListAsync(cancellationToken);

                foreach (var ja in job.JobAreas)
                {
                    ja.EquipmentPlacements = equipmentPlacements.Where(ep => ep.JobAreaId == ja.Id).ToList();

                    foreach (var ep in ja.EquipmentPlacements)
                    {
                        ep.Equipment = equipments.FirstOrDefault(e => e.Id == ep.EquipmentId);
                        ep.EquipmentPlacementReadings = equipmentPlacementsReadings.Where(epr => epr.EquipmentPlacementId == ep.Id).ToList();
                    }
                }

                foreach (var zone in job.Zones)
                {
                    zone.ZoneReadings = zoneReadings.Where(zr => zone.Id == zr.ZoneId).ToList();
                    zone.EquipmentPlacementReadings = equipmentPlacementsReadings.Where(e => e.ZoneId == zone.Id).ToList();
                    zone.JobAreas = job.JobAreas.Where(ja => ja.ZoneId == zone.Id).ToList();
                }

                job.JobVisits = await _context.JobVisit.Where(z => z.JobId == job.Id).ToListAsync(cancellationToken);

                return job;
            }

            private static List<ZoneDehuPlacement> GetZoneDehuPlacements(Job job)
            {
                var zoneDehuPlacements = job.Zones
                    .SelectMany(z => z.JobAreas)
                    .SelectMany(ep => ep.EquipmentPlacements)
                    .Where(
                        ep =>
                            ep.Equipment.EquipmentModel.EquipmentType
                                .BaseEquipmentTypeId == BaseEquipmentTypes.Dehumidifier)
                    .Select(r => new ZoneDehuPlacement(
                        r.JobArea.ZoneId,
                        r.Equipment.EquipmentModel.Name,
                        r.Equipment.AssetNumber,
                        r.BeginDate,
                        r.EndDate
                    ))
                    .ToList();
                return zoneDehuPlacements;
            }

            private IEnumerable<DehuReading> GetDehuReadings(Job job, IReadOnlyCollection<ZoneDehuPlacement> zoneDehuPlacements,
                TimeZoneDto franchiseTimeZone = null)
            {
                var visits = job.JobVisits.Select(x => new
                {
                    x.Id,
                    DateTime = x.Date.GetLocalFranchiseDateTime(franchiseTimeZone),
                    x.EmployeeInitials
                }).ToList();

                var dehuReadings = job.JobAreas
                    .SelectMany(ja => ja.EquipmentPlacements)
                    .SelectMany(ep => ep.EquipmentPlacementReadings)
                    .Select(r => new DehuReading()
                    {
                        ZoneId = r.ZoneId,
                        JobVisitId = r.JobVisitId,
                        EquipmentModel = r.EquipmentPlacement.Equipment.EquipmentModel.Name,
                        AssetNumber = r.EquipmentPlacement.Equipment.AssetNumber,
                        Temperature = r.Temperature,
                        RelativeHumidity = r.RelativeHumidity,
                        GrainsPerPound =
                            AtmosphericReadingsModelGeneratorHelper.CalculateGpp(r.Temperature, r.RelativeHumidity),
                        HourCounter = (int?)r.HourCount,
                        BeginDate = r.EquipmentPlacement.BeginDate,
                        EndDate = r.EquipmentPlacement.EndDate
                    }).ToList();

                var dehusForEachVisit = visits
                        .SelectMany(v => zoneDehuPlacements.Select(zdp => new
                        {
                            zdp.ZoneId,
                            JobVisitId = v.Id,
                            zdp.EquipmentModel,
                            zdp.AssetNumber,
                            zdp.BeginDate,
                            zdp.EndDate
                        }))
                        .ToList();

                var missingDehusPerVisit =
                    dehusForEachVisit.Except(
                        dehuReadings.Select(dr => new
                        {
                            ZoneId = (Guid?)dr.ZoneId,
                            dr.JobVisitId,
                            dr.EquipmentModel,
                            dr.AssetNumber,
                            dr.BeginDate,
                            dr.EndDate
                        }));

                dehuReadings.AddRange(missingDehusPerVisit.Select(d => new DehuReading
                {
                    ZoneId = d.ZoneId ?? Guid.Empty,
                    JobVisitId = d.JobVisitId,
                    EquipmentModel = d.EquipmentModel,
                    AssetNumber = d.AssetNumber,
                    Temperature = (decimal?)null,
                    RelativeHumidity = (decimal?)null,
                    GrainsPerPound = (decimal?)null,
                    HourCounter = (int?)null,
                    BeginDate = d.BeginDate,
                    EndDate = d.EndDate
                }).ToList());

                return dehuReadings;
            }

            private List<AtmosphericReadingsDto.ZoneReadingSet> GetZoneReadingSets(Job job, IEnumerable<DehuReading> dehuReadings,
                TimeZoneDto franchiseTimeZone = null)
            {
                var zones = job.Zones
                    .Select(x => new
                    {
                        x.Id,
                        x.Name,
                        x.Description,
                        x.ZoneTypeId,
                        ZoneTypeName = Lookups.ZoneTypes.First(a => a.Id == x.ZoneTypeId)?.Name
                    }).ToList();

                var zoneListWithReadings = job.Zones
                    .Select(z =>
                        new
                        {
                            z.Id,
                            Readings = z.ZoneReadings
                                .Select(zr => new
                                {
                                    zr.JobVisit.Id,
                                    zr.Temperature,
                                    zr.RelativeHumidity,
                                    GrainsPerPound =
                                        AtmosphericReadingsModelGeneratorHelper.CalculateGpp(zr.Temperature,
                                            zr.RelativeHumidity)
                                })
                        }).ToList();

                var zoneReadingSets = zones
                    .Select(z => new
                    {
                        z.Id,
                        zone = z,
                        readings = job.JobVisits
                            .Select(visit => new
                            {
                                visit,
                                zoneWithReadings = zoneListWithReadings.FirstOrDefault(zr => zr.Id == z.Id)
                            })
                            .Select(v1 => new
                            {
                                v1.visit,
                                readingForVisit =
                                    v1.zoneWithReadings?.Readings.FirstOrDefault(
                                        r => r.Id == v1.visit.Id)
                            })
                            .Select(v2 => new AtmosphericReadingsDto.VisitReadingSet
                            {
                                VisitTimestamp = v2.visit.Date.GetLocalFranchiseDateTime(franchiseTimeZone),
                                Technician = v2.visit.EmployeeInitials,
                                Temp = v2.readingForVisit?.Temperature,
                                RH = v2.readingForVisit?.RelativeHumidity,
                                GPP = v2.readingForVisit?.GrainsPerPound,
                                DehuReadings = dehuReadings
                                    .Where(dr => dr.JobVisitId == v2.visit.Id && dr.ZoneId == z.Id)
                                    .Select(dr =>
                                    {
                                        var beginDate = dr.BeginDate.RemoveSecondsFromDateTime(dr.BeginDate);
                                        var endDate = dr.EndDate.HasValue ? dr.EndDate.Value.RemoveSecondsFromDateTime(dr.EndDate.Value) : dr.EndDate;
                                        var visitDate = v2.visit.Date.RemoveSecondsFromDateTime(v2.visit.Date);

                                        var includeReading = z.ZoneTypeId != ZoneTypes.Drying
                                                             || (beginDate <= visitDate &&
                                                                 (!endDate.HasValue ||
                                                                  visitDate <= endDate.Value));
                                        return new AtmosphericReadingsDto.DehuReading
                                        {
                                            EquipmentModel = dr.EquipmentModel,
                                            AssetNo = dr.AssetNumber,
                                            Temp = includeReading ? dr.Temperature : null,
                                            RH = includeReading ? dr.RelativeHumidity : null,
                                            GPP = includeReading ? dr.GrainsPerPound : null,
                                            GDep = dr.GrainsPerPound == null || v2.readingForVisit == null ||
                                                   v2.readingForVisit.GrainsPerPound == null
                                                ? null
                                                : (includeReading
                                                    ? v2.readingForVisit.GrainsPerPound - dr.GrainsPerPound
                                                    : null),
                                            Hours = includeReading ? dr.HourCounter : null
                                        };
                                    }).ToList()
                            })
                    })
                    .Select(z => new AtmosphericReadingsDto.ZoneReadingSet
                    {
                        ZoneName = z.zone.Name,
                        ZoneDesc = z.zone.Description ?? String.Empty,
                        ZoneType = z.zone.ZoneTypeName,
                        VisitReadings = z.readings.ToList(),
                    })
                    .ToList();
                return zoneReadingSets;
            }
        }
    }
}