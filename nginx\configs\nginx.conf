# Number of workers to run, usually equals number of CPU cores.
worker_processes auto;

# Maximum number of opened files per process.
worker_rlimit_nofile 4096;

events {
  # Maximum number of simultaneous connections that can be opened by a worker process.
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  # ---------------------------------------------------------------------------
  # Security settings from:
  # https://gist.github.com/plentz/6737338

  # Disable nginx version from being displayed on errors.
  server_tokens off;

  # config to don't allow the browser to render the page inside an frame or iframe
  # and avoid clickjacking http://en.wikipedia.org/wiki/Clickjacking
  # if you need to allow [i]frames, you can use SAMEORIGIN or even set an uri with ALLOW-FROM uri
  # https://developer.mozilla.org/en-US/docs/HTTP/X-Frame-Options
  add_header X-Frame-Options SAMEORIGIN;

  # when serving user-supplied content, include a X-Content-Type-Options: nosniff header along with the Content-Type: header,
  # to disable content-type sniffing on some browsers.
  # https://www.owasp.org/index.php/List_of_useful_HTTP_headers
  # currently suppoorted in IE > 8 http://blogs.msdn.com/b/ie/archive/2008/09/02/ie8-security-part-vi-beta-2-update.aspx
  # http://msdn.microsoft.com/en-us/library/ie/gg622941(v=vs.85).aspx
  # 'soon' on Firefox https://bugzilla.mozilla.org/show_bug.cgi?id=471020
  add_header X-Content-Type-Options nosniff;

  # This header enables the Cross-site scripting (XSS) filter built into most recent web browsers.
  # It's usually enabled by default anyway, so the role of this header is to re-enable the filter for
  # this particular website if it was disabled by the user.
  # https://www.owasp.org/index.php/List_of_useful_HTTP_headers
  add_header X-XSS-Protection "1; mode=block";
  # ---------------------------------------------------------------------------

  # Avoid situations where a hostname is too long when dealing with vhosts.
  server_names_hash_bucket_size 64;
  server_names_hash_max_size 512;

  # Performance optimizations.
  sendfile on;
  tcp_nopush on;

  # http://nginx.org/en/docs/hash.html
  types_hash_max_size 2048;

  # Enable gzip for everything but IE6.
  gzip on;
  gzip_disable "msie6";

  # Default config for the app backend.
  include /etc/nginx/conf.d/default.conf;
}
