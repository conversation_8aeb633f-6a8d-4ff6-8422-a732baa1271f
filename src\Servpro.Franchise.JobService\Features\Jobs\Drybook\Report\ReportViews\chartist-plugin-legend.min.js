﻿!function(e,t){"function"==typeof define&&define.amd?define(["chartist"],function(s){return e.returnExportsGlobal=t(s)}):"object"==typeof exports?module.exports=t(require("chartist")):e["Chartist.plugins.legend"]=t(e.Chartist)}(this,function(e){"use strict";var t={className:"",classNames:!1,removeAll:!1,legendNames:!1,clickable:!0,onClick:null,position:"top"};return e.plugins=e.plugins||{},e.plugins.legend=function(s){function i(e,t){return e-t}if(s&&s.position){if(!("top"===s.position||"bottom"===s.position||s.position instanceof HTMLElement))throw Error("The position you entered is not a valid position");if(s.position instanceof HTMLElement){var a=s.position;delete s.position}}return s=e.extend({},t,s),a&&(s.position=a),function(t){var a=t.container.querySelector(".ct-legend");if(a&&a.parentNode.removeChild(a),s.clickable){var n=t.data.series.map(function(s,i){return"object"!=typeof s&&(s={value:s}),s.className=s.className||t.options.classNames.series+"-"+e.alphaNumerate(i),s});t.data.series=n}var o=document.createElement("ul"),l=t instanceof e.Pie;o.className="ct-legend",t instanceof e.Pie&&o.classList.add("ct-legend-inside"),"string"==typeof s.className&&s.className.length>0&&o.classList.add(s.className),t.options.width&&(o.style.cssText="width: "+t.options.width+"px;margin: 0 auto;");var r=[],c=t.data.series.slice(0),d=t.data.series,p=l&&t.data.labels;if(p){var u=t.data.labels.slice(0);d=t.data.labels}d=s.legendNames||d;var f=Array.isArray(s.classNames)&&s.classNames.length===d.length;d.forEach(function(e,t){var i=document.createElement("li");i.className="ct-series-"+t,f&&(i.className+=" "+s.classNames[t]),i.setAttribute("data-legend",t),i.textContent=e.name||e,o.appendChild(i)}),t.on("created",function(e){if(s.position instanceof HTMLElement)s.position.insertBefore(o,null);else switch(s.position){case"top":t.container.insertBefore(o,t.container.childNodes[0]);break;case"bottom":t.container.insertBefore(o,null)}}),s.clickable&&o.addEventListener("click",function(e){var a=e.target;if(a.parentNode===o&&a.hasAttribute("data-legend")){e.preventDefault();var n=parseInt(a.getAttribute("data-legend")),l=r.indexOf(n);if(l>-1)r.splice(l,1),a.classList.remove("inactive");else if(s.removeAll)r.push(n),a.classList.add("inactive");else if(t.data.series.length>1)r.push(n),a.classList.add("inactive");else{r=[];var d=Array.prototype.slice.call(o.childNodes);d.forEach(function(e){e.classList.remove("inactive")})}var f=c.slice(0);if(p)var m=u.slice(0);r.sort(i).reverse(),r.forEach(function(e){f.splice(e,1),p&&m.splice(e,1)}),s.onClick&&s.onClick(t,e),t.data.series=f,p&&(t.data.labels=m),t.update()}})}},e.plugins.legend});
//# sourceMappingURL=chartist-plugin-legend.min.js.map