﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps.Drybook;

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Monitoring
{
    public class GetAtmosphericReadings
    {
        public class Query : IRequest<Dto>
        {
            public Query(Guid jobId)
            {
                JobId = jobId;
            }
            public Guid JobId { get; }
        }

        public class Dto
        {
            public Dto(
                IEnumerable<ZoneInfo> zones,
                IEnumerable<ZoneInfo> hvacs,
                IEnumerable<VisitNote> visitNotes)
            {
                Zones = zones;
                Hvacs = hvacs;
                VisitNotes = visitNotes;
            }

            public IEnumerable<ZoneInfo> Hvacs { get; }
            public IEnumerable<ZoneInfo> Zones { get; }
            public IEnumerable<VisitNote> VisitNotes { get; }

            public class ZoneInfo
            {
                public ZoneInfo(
                    Guid id,
                    string name,
                    string description,
                    int? maxOccurences,
                    int? displayOrder,
                    bool shouldCollectNA,
                    bool isNameRequired,
                    IEnumerable<VisitValuePair<decimal?>> tempsFarenheight,
                    IEnumerable<VisitValuePair<decimal?>> tempsCelcius,
                    IEnumerable<VisitValuePair<decimal?>> rhs,
                    IEnumerable<VisitValuePair<decimal?>> humidityRatios,
                    IEnumerable<VisitValuePair<bool?>> isHvacOn,
                    IEnumerable<VisitReadingValue> notes)
                {
                    Id = id;
                    Name = name;
                    Description = description;
                    MaxOccurences = maxOccurences;
                    DisplayOrder = displayOrder;
                    ShouldCollectNA = shouldCollectNA;
                    IsNameRequired = isNameRequired;
                    TempsFarenheight = tempsFarenheight;
                    TempsCelcius = tempsCelcius;
                    Rhs = rhs;
                    HumidityRatios = humidityRatios;
                    IsHvacOn = isHvacOn;
                    Notes = notes;
                }

                public Guid Id { get; }
                public string Name { get; }
                public string Description { get; }
                public int? MaxOccurences { get; }
                public int? DisplayOrder { get; }
                public bool ShouldCollectNA { get; }
                public bool IsNameRequired { get; }
                public IEnumerable<VisitValuePair<decimal?>> TempsFarenheight { get; }
                public IEnumerable<VisitValuePair<decimal?>> TempsCelcius { get; }
                public IEnumerable<VisitValuePair<decimal?>> Rhs { get; }
                public IEnumerable<VisitValuePair<decimal?>> HumidityRatios { get; }
                public IEnumerable<VisitValuePair<bool?>> IsHvacOn { get; }
                public IEnumerable<VisitReadingValue> Notes { get; }
            }

            public class VisitNote
            {
                public VisitNote(
                    Guid visitId,
                    Guid? journalNoteId,
                    string note)
                {
                    VisitId = visitId;
                    JournalNoteId = journalNoteId;
                    Note = note;
                }

                public Guid VisitId { get; }
                public Guid? JournalNoteId { get; }
                public string Note { get; }
            }

            public class VisitValuePair<T>
            {
                public VisitValuePair(
                    Guid visitId,
                    T value,
                    bool includedInVisit)
                {
                    VisitId = visitId;
                    Value = value;
                    IncludedInVisit = includedInVisit;
                }

                public Guid VisitId { get; }
                public T Value { get; }
                public bool IncludedInVisit { get; }
            }

            public class VisitReadingValue
            {
                public VisitReadingValue(
                    Guid? value,
                    Guid? readingId,
                    Guid visitId,
                    bool includedInVisit)
                {
                    Value = value;
                    ReadingId = readingId;
                    VisitId = visitId;
                    IncludedInVisit = includedInVisit;
                }

                public Guid? Value { get; }
                public Guid? ReadingId { get; }
                public Guid VisitId { get; }
                public bool IncludedInVisit { get; }
            }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly ILogger<GetAtmosphericReadings> _logger;

            private readonly ImmutableHashSet<Guid> HVACZoneTypes = new HashSet<Guid>
            {
                ZoneTypes.Outside,
                ZoneTypes.Unaffected,
                ZoneTypes.HVAC
            }.ToImmutableHashSet();

            public Handler(
                JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor,
                ILookupServiceClient lookupServiceClient,
                ILogger<GetAtmosphericReadings> logger)
            {
                _context = context;
                _userInfoAccessor = userInfoAccessor;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Begin handler with: {@request}", request);

                var userInfo = _userInfoAccessor.GetUserInfo();
                var job = await _context.Jobs
                    .Include(x => x.Zones)
                        .ThenInclude(x => x.JobAreas)
                        .ThenInclude(x => x.BeginJobVisit)
                    .Include(x => x.Zones)
                        .ThenInclude(x => x.ZoneReadings)
                    .TagWith(RemoveLastOrderByInterceptor.QueryTag)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == request.JobId
                        && x.FranchiseSetId == userInfo.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                job.JobVisits = await _context.JobVisit
                    .Include(x => x.Tasks)
                    .ThenInclude(x => x.JournalNotes)
                    .TagWith(RemoveLastOrderByInterceptor.QueryTag)
                    .AsNoTracking()
                    .Where(x => x.JobId == request.JobId)
                    .ToListAsync(cancellationToken);

                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var zoneTypeLookups = lookups.ZoneTypes.ToDictionary(x => x.Id);

                var visits = job.JobVisits
                    .OrderBy(x => x.Date).ToList();

                _logger.LogDebug("JobVisits for Job {jobId}: {@dto}", request.JobId, visits);

                var zones = job.Zones
                    .Select(x => new
                    {
                        x.ZoneTypeId,
                        Zone = MapZone(x, visits, zoneTypeLookups[x.ZoneTypeId]),
                        x.CreatedDate
                    })
                    .ToList();

                _logger.LogDebug("Zones for Job {jobId}: {@dto}", request.JobId, zones);

                var visitNotes = job.JobVisits
                    .Select(x =>
                    {
                        var task = x.Tasks
                            .FirstOrDefault(x => x.JobTriStateQuestionId == DailyDepartureQuestionIds.AbleToGetReadings);
                        var note = task?.JournalNotes.FirstOrDefault();
                        return new Dto.VisitNote(x.Id, note?.Id, note?.Note);
                    });

                _logger.LogDebug("VisitNotes for Job {jobId}: {@dto}", request.JobId, visitNotes);

                var atmosphericReadings = new Dto(
                    zones.Where(x => x.ZoneTypeId == ZoneTypes.Drying)
                         .OrderBy(x => x.CreatedDate)
                         .Select(x => x.Zone),
                    zones.Where(x => HVACZoneTypes.Contains(x.ZoneTypeId))
                         .OrderBy(x => x.Zone.DisplayOrder)
                         .ThenBy(x => x.CreatedDate)
                         .Select(x => x.Zone),
                    visitNotes);

                _logger.LogDebug("Atmospheric Readings for Job {jobId}: {@dto}", request.JobId, atmosphericReadings);

                return atmosphericReadings;
            }

            Dto.ZoneInfo MapZone(
                Zone zone,
                IEnumerable<JobVisit> visits,
                ZoneTypeDto zoneType)
            {
                var isIncludedInVisit = visits.ToDictionary(x => x.Id, x => IsIncludedInVisit(zone, x));
                var readingsLookup = zone.ZoneReadings.ToDictionary(x => x.JobVisitId);
                var tempsFerenheight = visits.Select(x => MapTemp(x, readingsLookup, isIncludedInVisit)).ToList();
                var tempsCelcius = tempsFerenheight.Select(ConvertToCelcius);
                var rhs = visits.Select(x => MapRHS(x, readingsLookup, isIncludedInVisit));
                var humidityRatios = visits.Select(x => MapHumidityRatio(x, readingsLookup, isIncludedInVisit));
                var isHVACOn = visits.Select(x => MapHVAC(x, readingsLookup, isIncludedInVisit));
                var notes = visits.Select(x => MapVisitReadingValue(x, readingsLookup, isIncludedInVisit));
                return new Dto.ZoneInfo(
                    zone.Id,
                    zone.Name,
                    zone.Description,
                    zoneType.MaxOccurrences,
                    zoneType.DisplayOrder,
                    zoneType.ShouldCollectNA,
                    zoneType.IsNameRequired,
                    tempsFerenheight,
                    tempsCelcius,
                    rhs,
                    humidityRatios,
                    isHVACOn,
                    notes);
            }

            private bool IsIncludedInVisit(Zone zone, JobVisit visit)
            {
                var zoneDate = zone.JobAreas
                    .Where(x => !x.EndJobVisitId.HasValue)
                    .Select(x => x.BeginJobVisit?.Date)
                    .Min(x => x);

                zoneDate = zoneDate.HasValue
                   ? zoneDate.Value.RemoveSecondsFromDateTime(zoneDate.Value)
                   : zoneDate;
                return zoneDate.HasValue && zoneDate <= visit.Date;
            }

            private Dto.VisitValuePair<decimal?> ConvertToCelcius(Dto.VisitValuePair<decimal?> value)
                => new Dto.VisitValuePair<decimal?>(value.VisitId, ToCelcius(value.Value), value.IncludedInVisit);

            private decimal? ToCelcius(decimal? temp)
                => temp.HasValue
                ? (decimal?)((temp.Value - 32m) * (5m/9m))
                : null;

            Dto.VisitValuePair<decimal?> MapTemp(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<decimal?>(
                    visit.Id,
                    readingsLookup.GetOrDefault(visit.Id)?.Temperature,
                    isIncluded[visit.Id]);

            Dto.VisitValuePair<decimal?> MapRHS(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<decimal?>(
                    visit.Id,
                    readingsLookup.GetOrDefault(visit.Id)?.RelativeHumidity,
                    isIncluded[visit.Id]);

            Dto.VisitValuePair<decimal?> MapHumidityRatio(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<decimal?>(
                    visit.Id,
                    CalculateGrainsPerPound(
                        readingsLookup.GetOrDefault(visit.Id)?.Temperature,
                        readingsLookup.GetOrDefault(visit.Id)?.RelativeHumidity),
                    isIncluded[visit.Id]);

            Dto.VisitValuePair<bool?> MapHVAC(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitValuePair<bool?>(
                    visit.Id,
                    readingsLookup.ContainsKey(visit.Id),
                    isIncluded[visit.Id]);

            Dto.VisitReadingValue MapVisitReadingValue(
                JobVisit visit,
                IDictionary<Guid, ZoneReading> readingsLookup,
                IDictionary<Guid, bool> isIncluded)
                => new Dto.VisitReadingValue(
                    readingsLookup.GetOrDefault(visit.Id)?.JournalNoteId,
                    readingsLookup.GetOrDefault(visit.Id)?.Id,
                    visit.Id,
                    isIncluded[visit.Id]);

            /// <summary>
            /// Calculates the GPP.
            /// </summary>
            /// <param name="temperature">The temperature.</param>
            /// <param name="relativeHumidity">The relative humidity.</param>
            /// <returns>Grains Per Pound (Humidity Ratio)</returns>
            decimal? CalculateGrainsPerPound(decimal? temperature, decimal? relativeHumidity)
            {
                /*  This is a code snippet condensed from the Access version of the function:
                    This code copied directly from the drying workbook formulas.
                    It seems to be using some regression formula to emulate the steam tables.  (water vapor pressures at given temperatures)
                    It also seems to be presuming a given ambient air pressure.  The industry standard seems to not factor in local air pressure.
                */

                if (temperature == null || relativeHumidity == null)
                {
                    return null;
                }

                var t = (double)temperature.Value;
                var rh = (double)relativeHumidity.Value;
                var g = t + 459.67;
                var h = -10443.97 / g;
                var i = -11.29465;
                var j = -0.027022355 * g;
                var k = 0.00001289036 * Math.Pow(g, 2);
                var l = -0.000000002478068 * Math.Pow(g, 3);
                var m = 6.5459673 * Math.Log(g);
                var n = Math.Exp(h + i + j + k + l + m); //  ‘Exp(x) function is the natural logarithm e raised to the power of x.
                var o = n * rh * 0.01;
                var p = 0.62198 * (o / (14.696 - o));
                var answer = Math.Round(7000 * p);
                return (decimal)answer;
            }
        }
    }
}
