﻿using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Forms
{
    public class GetForm
    {
        public class Query : IRequest<Dto>
        {
            public Guid FormId { get; set; }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public string LinkedPage { get; set; }
            public Guid? RelatedForm { get; set; }
            public Guid? RelatedForm2 { get; set; }
            public Guid? RelatedForm3 { get; set; }
            public bool? IsRequiredForAllLossTypes { get; set; }
            public bool WaterFormRequired { get; set; }
            public bool MoldFormRequired { get; set; }
            public bool FireFormRequired { get; set; }
            public bool? IsRequiredForResidentialJob { get; set; }
            public bool? IsRequiredForCommercialJob { get; set; }
            public bool WaterForm { get; set; }
            public bool MoldForm { get; set; }
            public bool FireForm { get; set; }
            public string InsuranceClient { get; set; }
            public string CommercialClient { get; set; }
            public bool? IsAvailableInFirstNotice { get; set; }
            public bool Approved { get; set; }
            public string State { get; set; }
            public string Country { get; set; }
            public string FormalLanguage { get; set; }
            public bool IsActive { get; set; }
            public string FormVersion { get; set; }
            public string FileType { get; set; }
            public DateTime? SyncDate { get; set; }
            public string BucketName { get; set; }
            public string FilePath { get; set; }
            public bool IsAuthorizationForm { get; set; }
            public DateTime ModifiedDate { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<Handler> _logger;
            private string BucketName { get; }
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";

            public Handler(
          JobReadOnlyDataContext context,
          IConfiguration config,
          ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
                BucketName = config.GetValue<string>(S3MediaBucketNameKey);
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var form = await _context.FormTemplates.FirstOrDefaultAsync(x => x.Id == request.FormId, cancellationToken);

                if (form is null)                
                    throw new ResourceNotFoundException("Form not found");

                return Map(form);
            }

            private Dto Map(FormTemplate x)
            {
                return new Dto
                {
                    Id = x.Id,
                    Name = x.Name,
                    Description = x.Description,
                    LinkedPage = x.LinkedPage,
                    RelatedForm = x.RelatedForm,
                    RelatedForm2 = x.RelatedForm2,
                    RelatedForm3 = x.RelatedForm3,
                    IsRequiredForAllLossTypes = x.IsRequiredForAllLossTypes,
                    WaterFormRequired = x.WaterFormRequired,
                    MoldFormRequired = x.MoldFormRequired,
                    FireFormRequired = x.FireFormRequired,
                    IsRequiredForResidentialJob = x.IsRequiredForResidentialJob,
                    IsRequiredForCommercialJob = x.IsRequiredForCommercialJob,
                    WaterForm = x.WaterForm,
                    MoldForm = x.MoldForm,
                    FireForm = x.FireForm,
                    InsuranceClient = x.InsuranceClient,
                    CommercialClient = x.CommercialClient,
                    IsAvailableInFirstNotice = x.IsAvailableInFirstNotice,
                    Approved = x.Approved,
                    State = x.State,
                    Country = x.Country,
                    FormalLanguage = x.FormalLanguage,
                    IsActive = x.IsActive,
                    FormVersion = x.FormVersion,
                    FileType = x.FileType,
                    BucketName = BucketName,
                    FilePath = x.MediaPath,
                    IsAuthorizationForm = x.IsAuthorizationForm,
                    ModifiedDate = x.ModifiedDate ?? x.CreatedDate
                };
            }
        }
    }
}
