﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobUploadDueDateExtensionFailed
    {
        public class Event : JobUploadDueDateExtensionFailedEvent, IRequest
        {
            public Event(JobUploadDueDateExtensionFailedEvent.DueDateExtensionFailedDto eventDto, Guid correlationId)
                : base(eventDto, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext db, ILogger<Handler> logger)
            {
                _db = db;
                _logger = logger;
            }

            private Guid MapJobUploadType(int jobUploadType)
            {
                switch (jobUploadType)
                {
                    case (int)JobUploadDueDateExtensionFailed.Handler.UploadType.Initial:
                        return JobUploadTypes.InitialExtension;
                    case (int)JobUploadDueDateExtensionFailed.Handler.UploadType.Daily:
                        return JobUploadTypes.DailyExtension;
                    case (int)JobUploadDueDateExtensionFailed.Handler.UploadType.Final:
                        return JobUploadTypes.FinalExtension;
                    default:
                        _logger.LogWarning("Unknown upload type: {uploadType}", jobUploadType);
                        throw new ValidationException($"Unknown upload type: {jobUploadType}");
                }
            }

            public enum UploadType
            {
                Initial = 1,
                Daily = 3,
                Final = 2
            }

            public async Task<Unit> Handle(JobUploadDueDateExtensionFailed.Event request, CancellationToken cancellationToken)
            {
                var eventDto = request.DueDateExtensionFailed;
                var uploadType = MapJobUploadType(eventDto.JobUploadTypeId);

                var jobUploadLock = await _db.JobUploadLocks
                    .FirstOrDefaultAsync(x => x.JobId == eventDto.JobId && x.JobUploadTypeId == uploadType, cancellationToken: cancellationToken);

                if (jobUploadLock == null)
                {
                    return Unit.Value;
                }

                _db.JobUploadLocks.Remove(jobUploadLock);
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}