﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Mica
{
    public class GetNotesForMica
    {
        public class Query : IRequest<IEnumerable<Dto>>
        {
            public Guid JobId { get; set; }
        }
        public class Dto
        {
            public Guid Id { get; set; }
            public DateTime? CreatedDate { get; set; }
            public string Author { get; set; }
            public DateTime? ActionDate { get; set; }
            public Guid? CategoryId { get; set; }
            public Guid TypeId { get; set; }
            public Guid VisibilityId { get; set; }
            public string Subject { get; set; }
            public string Note { get; set; }
            public bool HasBeenSentToMica { get; set; }
        }

        public class Handler : IRequestHandler<Query, IEnumerable<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly IMicaServiceClient _micaServiceClient;
            private readonly ILogger<Handler> _logger;

            public Handler(
                  JobReadOnlyDataContext context,
                  IMicaServiceClient micaServiceClient,
                  IConfiguration config,
                  ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
                _micaServiceClient = micaServiceClient;
            }

            public async Task<IEnumerable<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Getting Notes to Submit to Mica");

                List<Dto> returnList = new List<Dto>();

                var notes = await _context.JournalNote.Where(x => x.JobId == request.JobId).ToListAsync(cancellationToken);

                var sentEntities = await _micaServiceClient.GetSentEntitiesAsync(new GetSentEntities.Command(request.JobId, 
                    GetSentEntities.EntityType.Correspondence), cancellationToken);

                if (sentEntities.SentEntities?.Any()?? false)
                {
                    var notesSent = notes.Where(k => sentEntities.SentEntities.Select(x => x.Id).ToList().Contains(k.Id)).ToList();
                    returnList = notesSent.Select(x => Map(x, true)).ToList();
                    var notesNotSent = notes.Where(k => !notesSent.Contains(k)).ToList();
                    returnList.AddRange(notesNotSent.Select(x => Map(x, false)).ToList());
                }
                else
                {
                    returnList = notes.Select(x => Map(x, false)).ToList();
                }

                return returnList;
            }

            private Dto Map(Models.JournalNote x, bool sent)
            {
                return new Dto
                {
                    Id = x.Id,
                    CreatedDate = x.CreatedDate,
                    Author = x.Author,
                    ActionDate = x.ActionDate,
                    CategoryId = x.CategoryId,
                    Note = x.Note,
                    Subject = x.Subject,
                    TypeId = x.TypeId,
                    VisibilityId = x.VisibilityId,
                    HasBeenSentToMica = sent
                };
            }
        }
    }
}
