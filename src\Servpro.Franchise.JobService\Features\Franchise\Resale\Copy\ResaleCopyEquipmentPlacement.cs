﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyEquipmentPlacement
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult EquipmentResult { get; set; }
            public ProcessEntityResult AreaResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> EquipmentPlacementIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyEquipmentPlacement>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyEquipmentPlacement> _logger;
            private readonly IMapper _mapper;
            private readonly JobDataContext _context;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;

            public Handler(
                JobDataContext context,
                ILogger<ResaleCopyEquipmentPlacement> logger,
                IMapper mapper,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _context = context;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Processing {entity}", nameof(EquipmentPlacement));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.EquipmentPlacementIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var equipmentPlacementTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedEquipmentPlacementIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(equipmentPlacementTargetIds, 
                    GetEquipmentPlacementIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<EquipmentPlacement, ResaleEquipmentPlacement>(
                    request.ResaleId,
                    equipmentPlacement =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.EquipmentResult.FailedEntities.Contains(equipmentPlacement.EquipmentId))
                            failedDependencies.Add((nameof(Equipment), equipmentPlacement.EquipmentId));
                        if (request.AreaResult.FailedEntities.Contains(equipmentPlacement.JobAreaId))
                            failedDependencies.Add((nameof(JobArea), equipmentPlacement.JobAreaId));

                        return failedDependencies;
                    },
                    alreadyCopiedEquipmentPlacementIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.EquipmentPlacements.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<EquipmentPlacement>> GetSourceEntitiesAsync(List<Guid> equipmentPlacementIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var equipmentPlacements = await _context.EquipmentPlacements
                    .Where(e => equipmentPlacementIds.Contains(e.Id))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", equipmentPlacements.Count);
                return equipmentPlacements;
            }

            private async Task<List<Guid>> GetEquipmentPlacementIdsAsync(List<Guid?> equipmentPlacementTargetIds, 
                CancellationToken cancellationToken)
            {
                return await _context.EquipmentPlacements
                    .Where(x => equipmentPlacementTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
