﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.HistoricalJobs
{
    [Route("api/historical/jobs/")]
    public class HistoricController : Controller
    {
        private readonly IMediator _mediator;
        public readonly ILogger<HistoricController> _logger;

        public HistoricController(IMediator mediator)           
        {
            _mediator = mediator;           
        }

        [HttpPost("commands/historical-search")]
        public async Task<ActionResult<IEnumerable<HistoricalSearch.Dto>>> DetailedSearch([FromBody] HistoricalSearch.Command request)
        {
            var result = await _mediator.Send(request);

            return Ok(result);
        }

        [HttpGet("queries/historical-fnol-report")]
        public async Task<ActionResult<IEnumerable<HistoricalMediaData.Dto>>> HistoricalFnolData([FromQuery] Guid jobId )
        {
            var result = await _mediator.Send(new HistoricalMediaData.Query(jobId, HistoricalMediaData.SearchMedia.Fnol));

            return Ok(result);
        }

        [HttpGet("queries/historical-photos")]
        public async Task<ActionResult<IEnumerable<HistoricalMediaData.Dto>>> HistoricalPhotosData([FromQuery] Guid jobId)
        {
            var result = await _mediator.Send(new HistoricalMediaData.Query(jobId, HistoricalMediaData.SearchMedia.Photos));

            return Ok(result);
        }

        [HttpGet("queries/historical-forms")]
        public async Task<ActionResult<IEnumerable<HistoricalMediaData.Dto>>> HistoricalFormsData([FromQuery] Guid jobId)
        {
            var result = await _mediator.Send(new HistoricalMediaData.Query(jobId, HistoricalMediaData.SearchMedia.Forms));

            return Ok(result);
        }


        [HttpGet("queries/historical-misc-documents")]
        public async Task<ActionResult<IEnumerable<HistoricalMediaData.Dto>>> HistoricalMiscDocumentsData([FromQuery] Guid jobId)
        {
            var result = await _mediator.Send(new HistoricalMediaData.Query(jobId, HistoricalMediaData.SearchMedia.MisDocs));

            return Ok(result);
        }
    }
}
