﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.ProjectNumber;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using System;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.LinkUnlink
{
    public class UnlinkProjects
    {
        public class Command : IRequest<Unit>
        {
            public Guid JobId { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly JobDataContext _dbContext;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ILogger<UnlinkProjects> _logger;
            private readonly IProjectNumberClient _projectNumberClient;
            private GetLookups.Dto _lookups;

            public Handler(
                JobDataContext context,
                ILookupServiceClient lookupServiceClient,
                ILogger<UnlinkProjects> logger,
                IProjectNumberClient projectNumberClient,
                IUserInfoAccessor userInfoAccessor)
            {
                _dbContext = context;
                _lookupServiceClient = lookupServiceClient;
                _logger = logger;
                _userInfoAccessor = userInfoAccessor;
                _projectNumberClient = projectNumberClient;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                _lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var user = _userInfoAccessor.GetUserInfo();

                Job job = await _dbContext.Jobs
                    .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job == null)
                {
                    throw new ResourceNotFoundException($"Job {request.JobId} not found.");
                }

                string jobProjectGroup = Regex.Match(job.ProjectNumber, ProjectNumberIdentifier.ProjectNumberRegExString).Value;
                var isJobLinkedToProjectGroup = await _dbContext.Jobs
                    .Where(j => j.ProjectNumber.Contains(jobProjectGroup) && j.Id != job.Id)
                    .AnyAsync(cancellationToken);

                if (!isJobLinkedToProjectGroup)
                {
                    throw new Exception($"Job {job.Id} is not linked to any other project.");
                }

                job.ProjectNumber = await GetProjectNumberAsync(user.FranchiseSetId.Value, job.LossTypeId);

                _dbContext.Update(job);
                await _dbContext.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task<string> GetProjectNumberAsync(Guid franchiseSetId, Guid lossTypeId)
            {
                var stopwatch = new Stopwatch();
                stopwatch.Start();
                var lossTypeAbbreviation = _lookups.LossTypes.FirstOrDefault(x => x.Id == lossTypeId)?.Abbreviation;
                var projectNumber = await _projectNumberClient.GenerateProjectNumberAsync(franchiseSetId, lossTypeAbbreviation);
                stopwatch.Stop();
                _logger.LogInformation("Project number computed in {elapsedTime}ms", stopwatch.ElapsedMilliseconds);
                return projectNumber;
            }
        }
    }
}
