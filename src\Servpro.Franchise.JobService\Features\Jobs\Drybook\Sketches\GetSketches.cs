﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Sketches
{
    public class GetSketches
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Query(Guid id, Guid franchiseSetId)
            {
                JobId = id;
                FranchiseSetId = franchiseSetId;
            }
            public Guid JobId { get; }
            public Guid FranchiseSetId { get; }
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public Guid JobId { get; set; }
            public string CanvasJson { get; set; }
            public Guid MediaId { get; set; }
            public string Name { get; set; }
            public Guid JobArtifactId { get; set; }
        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobReadOnlyDataContext _db;

            public Handler(JobReadOnlyDataContext db) => _db = db;

            public async Task<ICollection<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var jobSketches = await _db.JobSketch.Include(c => c.MediaMetadata).AsNoTracking().Where(x => x.JobId == request.JobId &&
                                                                                        x.MediaMetadata.FranchiseSetId == request.FranchiseSetId &&
                                                                                        x.MediaMetadata.MediaTypeId == MediaTypes.Sketch &&
                                                                                        !x.MediaMetadata.IsDeleted).ToListAsync(cancellationToken);

                return jobSketches.Select(Map).ToList();
            }

            private Dto Map(JobSketch sketch)
                => new Dto
                {
                    Id = sketch.Id,
                    JobId = sketch.JobId,
                    CanvasJson = sketch.CanvasJson,
                    MediaId = sketch.MediaMetadataId,
                    Name = sketch.Name,
                    JobArtifactId = sketch.MediaMetadata.ArtifactTypeId
                };
        }
    }
}
