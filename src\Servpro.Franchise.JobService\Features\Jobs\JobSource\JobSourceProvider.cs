﻿using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.JobSourceProvider
{
    public class JobSourceProvider : IJobSourcerProvider
    {
        //TODO: Must mock the JobSource until job.SourceOfOpportunity starts being populated, using corporateJobNumber at the moment, update accordingly once SourceOfOpportunity is implemented.
        public string GetJobSource(Job job, string xactId)
        {
            if (!string.IsNullOrEmpty(xactId) && job.CorporateJobNumber.HasValue)
            {
                return JobSource.Xact;
            }

            if (job.StormId.HasValue)
            {
                return JobSource.Iac;
            }

            return job.CorporateJobNumber.HasValue ? JobSource.CallCenter : JobSource.LocalFranchise;
        }
    }
}
