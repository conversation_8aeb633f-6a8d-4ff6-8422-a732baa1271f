﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Task = Servpro.Franchise.JobService.Models.Drybook.Task;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Models
{
    public class ResaleEntity
    {
        public ResaleEntity(
            List<Business> businesses,
            List<Contact> contacts,
            List<Equipment> equipment,
            List<EquipmentModel> equipmentModels,
            List<EquipmentType> equipmentTypes,
            List<EquipmentPlacement> equipmentPlacements,
            List<EquipmentPlacementReading> equipmentPlacementReadings,
            List<Job> jobs,
            List<JobArea> areas,
            List<JobAreaMaterial> areaMaterials,
            List<JobAreaMaterialReading> areaMaterialReadings,
            List<JobBusinessMap> businessMaps,
            List<JobContactMap> contactMaps,
            List<JobInvoice> invoices,
            List<JobMaterial> materials,
            List<JobSketch> sketches,
            List<JobTriStateAnswer> triStateAnswers,
            List<JobVisit> visits,
            List<JobVisitTriStateAnswer> visitTriStateAnswers,
            List<JournalNote> journalNotesWithoutRFT,
            List<JournalNote> journalNotesWithRFT,
            List<MediaMetadata> mediaMetadata,
            List<MobileData> mobileData,
            List<Room> rooms,
            List<RoomFlooringTypeAffected> roomFlooringTypeAffected,
            List<Task> tasks,
            List<Zone> zones,
            List<ZoneReading> zoneReadings,
            List<WipRecord> wipRecords)
        {
            Businesses = businesses;
            Contacts = contacts;
            Equipment = equipment;
            EquipmentModels = equipmentModels;
            EquipmentTypes = equipmentTypes;
            EquipmentPlacements = equipmentPlacements;
            EquipmentPlacementReadings = equipmentPlacementReadings;
            Jobs = jobs;
            Areas = areas;
            AreaMaterials = areaMaterials;
            AreaMaterialReadings = areaMaterialReadings;
            BusinessMaps = businessMaps;
            ContactMaps = contactMaps;
            Invoices = invoices;
            Materials = materials;
            Sketches = sketches;
            TriStateAnswers = triStateAnswers;
            Visits = visits;
            VisitTriStateAnswers = visitTriStateAnswers;
            JournalNotesWithoutRFT = journalNotesWithoutRFT;
            JournalNotesWithRFT = journalNotesWithRFT;
            MediaMetadata = mediaMetadata;
            MobileData = mobileData;
            Rooms = rooms;
            RoomFlooringTypeAffected = roomFlooringTypeAffected;
            Tasks = tasks;
            Zones = zones;
            ZoneReadings = zoneReadings;
            WipRecords = wipRecords;
        }

        public List<Business> Businesses { get; set; }
        public List<Contact> Contacts { get; set; }
        public List<Equipment> Equipment { get; set; }
        public List<EquipmentModel> EquipmentModels { get; set; }
        public List<EquipmentType> EquipmentTypes { get; set; }
        public List<EquipmentPlacement> EquipmentPlacements { get; set; }
        public List<EquipmentPlacementReading> EquipmentPlacementReadings { get; set; }
        public List<Job> Jobs { get; set; }
        public List<JobArea> Areas { get; set; }
        public List<JobAreaMaterial> AreaMaterials { get; set; }
        public List<JobAreaMaterialReading> AreaMaterialReadings { get; set; }
        public List<JobBusinessMap> BusinessMaps { get; set; }
        public List<JobContactMap> ContactMaps { get; set; }
        public List<JobInvoice> Invoices { get; set; }
        public List<JobMaterial> Materials { get; set; }
        public List<JobSketch> Sketches { get; set; }
        public List<JobTriStateAnswer> TriStateAnswers { get; set; }
        public List<JobVisit> Visits { get; set; }
        public List<JobVisitTriStateAnswer> VisitTriStateAnswers { get; set; }
        /// <summary>
        /// Journal Notes without RoomFlooringTypeAffectedIds, these have to be processed
        /// BEFORE Rooms in case of DiaryEntryNotes existing on Rooms
        /// </summary>
        public List<JournalNote> JournalNotesWithoutRFT { get; set; }
        /// <summary>
        /// Journal Notes with RoomFlooringTypeAffectedIds, these have to be processed
        /// AFTER Rooms due to RoomFlooringTypeAffected having a FK to Rooms
        /// </summary>
        public List<JournalNote> JournalNotesWithRFT { get; set; }
        public List<MediaMetadata> MediaMetadata { get; set; }
        public List<MobileData> MobileData { get; set; }
        public List<Room> Rooms { get; set; }
        public List<RoomFlooringTypeAffected> RoomFlooringTypeAffected { get; set; }
        public List<Task> Tasks { get; set; }
        public List<Zone> Zones { get; set; }
        public List<ZoneReading> ZoneReadings { get; set; }
        public List<WipRecord> WipRecords { get; set; }
    }
}
