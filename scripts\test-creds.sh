#!/bin/sh

echo "making /root/.aws directory"
mkdir /root/.aws/

echo "building credentials"
cat > /root/.aws/credentials << EOF1
[default]
aws_access_key_id = XXXXXXXXXXXXXXXXXXXX
aws_secret_access_key = XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
EOF1

echo "building config"
cat > /root/.aws/config << EOF2
[default]
region=us-west-2
EOF2

echo "looking at /root/.aws"
ls -l /root/.aws 

echo "looking at credentials"
cat /root/.aws/credentials

echo "looking at config"
cat /root/.aws/config