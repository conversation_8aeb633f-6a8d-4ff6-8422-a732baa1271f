﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyJobSketch
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult JobResult { get; set; }
            public ProcessEntityResult MediaResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyJobSketch>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyJobSketch> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyJobSketch> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(JobSketch));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var sketchTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedSketchIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(sketchTargetIds, 
                    GetJobSketchIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<JobSketch, ResaleJobSketch>(
                    request.ResaleId,
                    sketch =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.JobResult.FailedEntities.Contains(sketch.JobId))
                            failedDependencies.Add((nameof(Job), sketch.JobId));
                        if (request.MediaResult.FailedEntities.Contains(sketch.MediaMetadataId))
                            failedDependencies.Add((nameof(MediaMetadata), sketch.MediaMetadataId));

                        return failedDependencies;
                    },
                    alreadyCopiedSketchIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    null,
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.JobSketch.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<JobSketch>> GetSourceEntitiesAsync(List<Guid> jobIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var jobSketches = await _context.JobSketch
                    .Where(js => jobIds.Contains(js.JobId))
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", jobSketches.Count);
                return jobSketches;
            }

            private async Task<List<Guid>> GetJobSketchIdsAsync(List<Guid?> sketchTargetIds, CancellationToken cancellationToken)
            {
                return await _context.JobSketch
                    .Where(x => sketchTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
