@model Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models.DrawingsModelDto
@using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers
@using Newtonsoft.Json;

@if (Model?.Drawings != null && Model.Drawings.Any())
{
    <div class="title">{drawings}Drawings{/drawings}</div>

    <div style="position: relative; overflow:hidden;">
        @foreach (var drawing in Model.Drawings)
        {
            <div class="subtitle">@drawing.Name</div>
                <div style="position: relative; transform: scale(0.7); float: left;">
                    @foreach (var canvasItem in @DrawingHelper.GetCanvasItems(drawing.CanvasJson))
                    {
                        @if (canvasItem.Type == "image")
                        {
                            <img alt="drawing"
                                 width="@(canvasItem.Width * canvasItem.ScaleX).ToString()"
                                 height="@(canvasItem.Height * canvasItem.ScaleY).ToString()"
                                 style="@DrawingHelper.GetImageStyle(canvasItem)"
                                 src="@canvasItem.Src" />
                        }
                        @if (canvasItem.Type == "path")
                        {
                            <svg width="1100px"
                                 height="1000px"
                                 style="@DrawingHelper.GetPathStyle(canvasItem)">
                                <path d="@DrawingHelper.GetSvgPath(canvasItem.Path)"
                                      opacity="0.3"
                                      stroke="blue"
                                      stroke-miterlimit="10"
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                      fill="none"
                                      fill-rule= "nonzero"
                                      stroke-width="@canvasItem.StrokeWidth"
                                      transform="translate(@(canvasItem.Left) @(canvasItem.Top)) rotate(@(canvasItem.Angle)) 
                                                 scale(@(canvasItem.ScaleX) @(canvasItem.ScaleY))
                                                 translate(-@(canvasItem.PathOffset.X), -@(canvasItem.PathOffset.Y))" 
                                />
                            </svg>
                        }
                        @if (canvasItem.Type == "textbox")
                        {
                            <div style="@DrawingHelper.GetTextStyle(canvasItem)">
                                <label>@canvasItem.Text</label>
                            </div>
                        }
                    }
                </div>
            <div style='page-break-before: always;'></div>
        }
    </div>
}
