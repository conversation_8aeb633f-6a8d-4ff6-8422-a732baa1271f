﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.Validation;
using Servpro.Franchise.JobService.Infrastructure.ClientRequirementsService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.XactService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.LookupService.Features.LookUps;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using PropertyTypes = Servpro.Franchise.JobService.Common.PropertyTypes;
using Task = System.Threading.Tasks.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements
{
    public interface IClientRequirementsService
    {
        bool IsJobUploadRequired(Job job);
        Task<bool> InitialRequirementsMustBeClientRequired(Job job, GetLookups.Dto lookups, CancellationToken cancellationToken);
        bool GetJobUploadRequiredIndicator(Job job);
        Task<GetJobRequirementsRequestDto> CreateJobRequirementsRequestDto(Job job, CancellationToken cancellationToken);
        Task<CrsValidationResponse> GetJobRequirementsAsync(GetJobRequirementsRequestDto request, CancellationToken cancellationToken);
        Task<CrsValidationResponse> GetJobRequirementsAsync(Job job, CancellationToken cancellationToken);
        CrsValidationResponse FilteredRequirements(CrsValidationResponse response, int ruleId);
        Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken);
        Task<List<GetFormsResultDto>> GetFormsAsync(GetFormsRequestDto request, CancellationToken cancellationToken);
    }

    public class ClientRequirementsService : IClientRequirementsService
    {
        private readonly IFranchiseServiceClient _franchiseServiceClient;
        private readonly ILookupServiceClient _lookupServiceClient;
        private readonly IXactServiceClient _xactServiceClient;
        private readonly ILogger<ClientRequirementsService> _logger;
        private readonly JobReadOnlyDataContext _db;
        private readonly IClientRequirementsServiceClient _crsClient;

        public ClientRequirementsService(ILogger<ClientRequirementsService> logger,
            JobReadOnlyDataContext db,
            IClientRequirementsServiceClient crsClient,
            IFranchiseServiceClient franchiseServiceClient, ILookupServiceClient lookupServiceClient,
            IXactServiceClient xactServiceClient)
        {
            _logger = logger;
            _db = db;
            _crsClient = crsClient;
            _franchiseServiceClient = franchiseServiceClient;
            _lookupServiceClient = lookupServiceClient;
            _xactServiceClient = xactServiceClient;
        }

        public async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting Job {jobId} from database", jobId);
            var stopwatch = Stopwatch.StartNew();

            var job = await _db.Jobs
                .AsNoTracking()
                .Include(j => j.JobTriStateAnswers)
                .Include(j => j.Zones)
                .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);

            if (job is null) { throw new ResourceNotFoundException($"Job not found (Id: {jobId}"); }

            job.JournalNotes = await GetJournalNotes(_db, jobId, cancellationToken);
            job.JobContacts = await GetJobContactMaps(_db, jobId, cancellationToken);
            job.JobAreas = await GetJobAreas(_db, jobId, cancellationToken);
            job.MediaMetadata = await GetMediaMetadata(_db, jobId, cancellationToken);
            job.JobSketches = await GetSketches(_db, jobId, cancellationToken);
            job.JobVisits = await GetJobVisits(_db, jobId, cancellationToken);
            job.LineItems = await GetLineItems(_db, jobId, cancellationToken);

            _logger.LogInformation("Job retrieved in {elapsedMilliseconds} seconds", stopwatch.ElapsedMilliseconds);

            return job;
        }

        public async Task<List<GetFormsResultDto>> GetFormsAsync(GetFormsRequestDto request, CancellationToken cancellationToken)
        {
            return await _crsClient.GetJobForms(request, cancellationToken);
        }

        private static async Task<ICollection<JournalNote>> GetJournalNotes(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            return await jobDataContext.JournalNote
                .AsNoTracking()
                .Where(n => n.JobId == jobId)
                .ToListAsync(cancellationToken);
        }

        private static async Task<ICollection<JobContactMap>> GetJobContactMaps(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            return await jobDataContext.JobContactMap
                .AsNoTracking()
                .Include(contactMap => contactMap.Contact)
                .Where(m => m.JobId == jobId)
                .ToListAsync(cancellationToken);
        }

        private static async Task<ICollection<JobArea>> GetJobAreas(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            var jobAreas = await jobDataContext.JobAreas.Include(x => x.Room)
                                               .ThenInclude(x => x.RoomFlooringTypesAffected).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);

            var jobAreasWithMaterialsAndVisits = await jobDataContext.JobAreas.Include(x => x.JobAreaMaterials)
                                                          .ThenInclude(jam => jam.JobMaterial)
                                                          .Include(x => x.BeginJobVisit)
                                                          .Where(ja => ja.JobId == jobId)
                                                          .Include(x => x.EndJobVisit)
                                                          .Where(ja => ja.JobId == jobId)
                                                            .ToDictionaryAsync(x => x.Id, cancellationToken);

            var jaIds = jobAreas.Select(ja => ja.Id);
            var equipmentPlacements = await jobDataContext.EquipmentPlacements.Include(x => x.Equipment)
                                                .ThenInclude(e => e.EquipmentModel)
                                                .Where(x => jaIds.Contains(x.JobAreaId)).ToListAsync(cancellationToken);
            var epByJobArea = from s in equipmentPlacements
                              group s by s.JobAreaId;

            Dictionary<Guid, EquipmentType> equipmentTypes = await GetEquipmentsTypes(jobDataContext, jobAreas, cancellationToken);

            foreach (var jobArea in jobAreas)
            {
                jobArea.JobAreaMaterials = jobAreasWithMaterialsAndVisits[jobArea.Id].JobAreaMaterials;
                jobArea.BeginJobVisit = jobAreasWithMaterialsAndVisits[jobArea.Id].BeginJobVisit;
                jobArea.EndJobVisit = jobAreasWithMaterialsAndVisits[jobArea.Id].EndJobVisit;
                jobArea.EquipmentPlacements = epByJobArea.FirstOrDefault(x => x.Key == jobArea.Id)?.ToList();
                if (jobArea.EquipmentPlacements == null)
                    continue;

                var eps = jobArea.EquipmentPlacements.Select(ep =>
                {
                    ep.Equipment.EquipmentModel.EquipmentType = equipmentTypes[ep.Equipment.EquipmentModel.EquipmentTypeId];
                    return ep;
                }).ToList();
                jobArea.EquipmentPlacements = eps;
            }
            return jobAreas;

        }

        private static async Task<Dictionary<Guid, EquipmentType>> GetEquipmentsTypes(JobDataContext jobDataContext, List<JobArea> jobAreas, CancellationToken cancellationToken)
        {
            List<Guid> equipmentTypessIds = GetEquipmentTypesIds(jobAreas);
            var equipmentTypes = await jobDataContext.EquipmentTypes.Where(x => equipmentTypessIds.Contains(x.Id)).ToDictionaryAsync(x => x.Id, cancellationToken);
            return equipmentTypes;
        }

        private static List<Guid> GetEquipmentTypesIds(List<JobArea> jobAreas)
        {
            var equipmentTypessIds = new List<Guid>();
            foreach (var jobArea in jobAreas)
            {
                equipmentTypessIds.AddRange(jobArea.EquipmentPlacements.Select(ep => ep.Equipment.EquipmentModel.EquipmentTypeId));
            }

            return equipmentTypessIds;
        }

        private static async Task<ICollection<MediaMetadata>> GetMediaMetadata(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            return await jobDataContext.MediaMetadata
                .AsNoTracking()
                .Include(mediaMetadata => mediaMetadata.JobInvoice)
                .Where(m => m.JobId == jobId)
                .ToListAsync(cancellationToken);
        }

        private static async Task<ICollection<JobSketch>> GetSketches(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            return await jobDataContext.JobSketch
                .Where(x => x.JobId == jobId)
                .Select(x => new JobSketch
                {
                    Id = x.Id,
                    JobId = x.JobId,
                    MediaMetadata = x.MediaMetadata,
                    Name = x.Name,
                    CreatedDate = x.CreatedDate,
                    ModifiedDate = x.ModifiedDate,
                    MediaMetadataId = x.MediaMetadataId
                }).ToListAsync(cancellationToken);
        }

        private static async Task<ICollection<JobVisit>> GetJobVisits(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            return await jobDataContext.JobVisit
                .AsNoTracking()
                .Include(v => v.JobVisitTriStateAnswers)
                .Include(v => v.Rooms)
                .ThenInclude(room => room.RoomFlooringTypesAffected)
                .Where(v => v.JobId == jobId).ToListAsync(cancellationToken);
        }

        private static async Task<ICollection<LineItem>> GetLineItems(JobDataContext jobDataContext, Guid jobId, CancellationToken cancellationToken)
        {
            return await jobDataContext.LineItems
                .AsNoTracking()
                .Where(n => n.JobId == jobId)
                .ToListAsync(cancellationToken);
        }

        public CrsValidationResponse FilteredRequirements(CrsValidationResponse response, int ruleId)
        {
            var result = new CrsValidationResponse
            {
                Result =
                {
                    PhotoRequirements = response.Result.PhotoRequirements.Where(x => x.RuleId == ruleId).ToList(),
                    ClientNotificationRequirements = response.Result.ClientNotificationRequirements.Where(x => x.RuleId == ruleId).ToList(),
                    MiscellaneousRequirements = response.Result.MiscellaneousRequirements.Where(x => x.RuleId == ruleId).ToList(),
                    EstimaticsRequirements = response.Result.EstimaticsRequirements.Where(x => x.RuleId == ruleId).ToList()
                }
            };
            return result;
        }

        public async Task<CrsValidationResponse> GetJobRequirementsAsync(Job job, CancellationToken cancellationToken)
        {
            var crsCommand = await CreateJobRequirementsRequestDto(job, cancellationToken);
            return await _crsClient.GetJobRequirements(crsCommand, cancellationToken);
        }

        public async Task<CrsValidationResponse> GetJobRequirementsAsync(GetJobRequirementsRequestDto request, CancellationToken cancellationToken)
        {
            return await _crsClient.GetJobRequirements(request, cancellationToken);
        }

        public bool IsJobUploadRequired(Job job)
        {
            if (job == null)
            {
                return false;
            }

            // TODO: Determine why this code was commented out
            //if (job.IsLegacyUpload) // SOA
            //{
            //    return true;
            //}

            //if (job.IsSelfPay.HasValue && job.IsSelfPay.Value)
            //{
            //    // no self-pay jobs require an upload now - PBI 27835
            //    return false;
            //}

            //Insurance insuranceInfo = null;
            //// Insurance dropdown selected
            //if (job.JobClientInformation != null && job.JobClientInformation.InsuranceCompanyId.HasValue)
            //{
            //    insuranceInfo = new InsuranceService().GetInsurance(job.JobClientInformation.InsuranceCompanyId.Value);
            //}

            // Local Franchise Job
            //if (job.OfficeJob != null)
            //{
            //    if (job.IsERNETJob.HasValue && job.IsERNETJob.Value)
            //    {
            //        return true;
            //    }

            //    // local, self-pay jobs do not require an upload.
            //    if (job.IsCorpJob == false)
            //    {
            //        if (insuranceInfo == null || insuranceInfo.AuditLocalJobs == false || insuranceInfo.Name.ToUpper().Equals("SELF PAY"))
            //        {
            //            return false;
            //        }
            //    }
            //}

            return true;
        }

        public async Task<GetJobRequirementsRequestDto> CreateJobRequirementsRequestDto(Job job, CancellationToken cancellationToken)
        {
            using var logScope = _logger.BeginScope("{jobId}", job.Id);

            try
            {
                _logger.LogInformation("Starting");
                var stopwatch = Stopwatch.StartNew();
                var lookupsRequest = _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var xactIdRequest = _xactServiceClient.GetJobMfnAsync(job.Id, cancellationToken);
                var franchiseRequest = _franchiseServiceClient.GetFranchiseWithoutUserClaimsAsync(job.FranchiseId, job.FranchiseSetId, cancellationToken: cancellationToken);
                var franchiseSetRequest = _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, cancellationToken);

                var lineItemIds = job.LineItems?
                    .Where(x => x.LineItemId.HasValue)
                    .Select(x => x.LineItemId.Value)
                    .ToList();
                var estimaticsRequest = _xactServiceClient
                    .GetEstimatics(lineItemIds, cancellationToken);

                _logger.LogInformation("Awaiting task list.");
                await Task.WhenAll(lookupsRequest, xactIdRequest, franchiseRequest, estimaticsRequest, franchiseSetRequest);
                _logger.LogInformation("Task list complete.");

                var lookups = await lookupsRequest;
                var xactMfn = await xactIdRequest;
                var franchise = await franchiseRequest;
                var estimatics = await estimaticsRequest;
                var franchiseSet = await franchiseSetRequest;

                var insuranceCarrier = await _db.InsuranceClients.FirstOrDefaultAsync(i => i.Id == job.InsuranceCarrierId, cancellationToken);

                //Adding Logging for debug purpose.
                var journalNotesRules = job.JournalNotes?.Select(a => a.Rules).ToList();
                _logger.LogInformation("Method: {method}. JournalNotesRules: {@journalNotesRules}", nameof(CreateJobRequirementsRequestDto), journalNotesRules);

                var jobSource = lookups.JobSources.FirstOrDefault(x => x.Id == job.JobSourceId)?.Name;

                var requestDto = new GetJobRequirementsRequestDto()
                {
                    Job = new GetJobRequirementsRequestDto.JobValidationRequestJobDto()
                    {
                        Id = job.Id,
                        LossTypeId = job.LossTypeId,
                        ActiveInAudit = false,
                        CollectDeductible = job.CollectDeductible,
                        CorporateJobNumber = job.CorporateJobNumber,
                        Country = lookups.Countries.FirstOrDefault(c => c.Id == job.LossAddress?.State?.CountryId)?.Alpha2Code,
                        DeductibleAmount = job.DeductibleAmount,
                        EstimateType = "Xactimate", // TODO
                        FranchiseSetId = job.FranchiseSetId,
                        InsuranceClient = insuranceCarrier?.Name,
                        InsuranceClientId = insuranceCarrier?.InsuranceNumber,
                        InsuranceClientParentId = insuranceCarrier?.ParentInsuranceNumber,
                        InsuranceClaimNumber = job.InsuranceClaimNumber,
                        IsStormJob = job.StormId.HasValue,
                        IsManagedStormProject = job.IsManagedStormProject.HasValue ? job.IsManagedStormProject.Value: false,
                        IsStreamlined = false, // TODO: Determine how to determine this
                        JobAreas = job.JobAreas?.Where(a => a.RoomId.HasValue).Select(ValidationRequestMapper.Map).ToList(),
                        JobContacts = job.JobContacts?.Select(ValidationRequestMapper.Map).ToList(),
                        JobDates = ValidationRequestMapper.MapJobDates(job.JobDates?.ToList(), job.CorporateJobNumber),
                        JobSketches = job.JobSketches,
                        JobSource = jobSource,
                        JobTriStateAnswers = job.JobTriStateAnswers?.Select(ValidationRequestMapper.Map).ToList(),
                        JobVisits = job.JobVisits?.Select(ValidationRequestMapper.Map).ToList(),
                        JournalNotes = ValidationRequestMapper.MapJournalNotes(job.JournalNotes?.ToList()),
                        LossType = lookups.LossTypes.FirstOrDefault(t => t.Id == job.LossTypeId)?.Name,
                        MasterId = (int?) null, // TODO: Figure out what the hell this is!
                        State = job.LossAddress?.State?.StateAbbreviation,
                        PropertyType =
                            ValidationRequestMapper.Map(lookups.StructureTypes,
                                job.StructureTypeId), // yes, this is really StructureType, but it is incorrect in CRS port TODO: https://gitlab.com/servpro/ftg/client-requirements-service/-/merge_requests/160/diffs#diff-content-96670746220c22923c2271f7f12612ae97856845
                        StructureType = job.PropertyTypeId == PropertyTypes.Residential
                            ? "Residential"
                            : "Commercial", // yes, this is really PropertyType, but it is incorrect in CRS port TODO: https://gitlab.com/servpro/ftg/client-requirements-service/-/merge_requests/160/diffs#diff-content-96670746220c22923c2271f7f12612ae97856845
                        FacilityType = ValidationRequestMapper.Map(lookups.FacilityTypes, job.FacilityTypeId),
                        WorkCenterJobNumber = job.WorkCenterJobNumber,
                        JobProgress = job.JobProgress,
                        Zones = job.Zones?.Select(z => ValidationRequestMapper.Map(z, lookups)).ToList(),
                        MasterFileNumber = xactMfn?.MasterFileNumber,
                        FranchiseType = franchise != null ? franchise.FranchiseType : "Unknown",
                        FranchiseSet = new GetJobRequirementsRequestDto.FranchiseSet()
                        {
                            TimeZone = new GetJobRequirementsRequestDto.TimeZone()
                            {
                                WindowsTimeZoneIdentifier = franchiseSet?.PrimaryTimeZoneId != null ? TimeZoneHelper.GetWindowsTimeZoneIdentifier(franchiseSet.PrimaryTimeZoneId) : string.Empty
                            }
                        }
                    },
                    FranchiseSetId = job.FranchiseSetId
                };

                if (!requestDto.Job.InsuranceClientParentId.HasValue || requestDto.Job.InsuranceClientParentId == 0)
                    requestDto.Job.InsuranceClientParentId = insuranceCarrier?.InsuranceNumber;

                // Set JobDate Exceptions if any exist
                var customerCalledDate = job.JobDates?.FirstOrDefault(d =>
                    d.JobDateTypeId == JobDateTypes.CustomerCalled && d.ExceptionReasonId.HasValue);
                if (customerCalledDate?.ExceptionReasonId != null)
                {
                    // get the legacy int id
                    var legacyId = lookups.CustomerCalledExceptionReasons
                        .FirstOrDefault(r => r.Id == customerCalledDate.ExceptionReasonId)?.LegacyId;
                    requestDto.Job.JobMilestones.Add(new GetJobRequirementsRequestDto.JobValidationRequestMilestone()
                    {
                        CustomerCalledExceptionId = legacyId
                    });
                }

                var siteInspectedDateTime = job.JobDates?.FirstOrDefault(d =>
                    d.JobDateTypeId == JobDateTypes.InitialOnSiteArrival && d.ExceptionReasonId.HasValue);
                if (siteInspectedDateTime?.ExceptionReasonId != null)
                {
                    requestDto.Job.JobMilestones.Add(new GetJobRequirementsRequestDto.JobValidationRequestMilestone()
                    {
                        ArrivalExceptionCommentId = siteInspectedDateTime.ExceptionReasonId
                    });
                }

                foreach (var mm in job.MediaMetadata.Where(mm => !mm.IsDeleted))
                {
                    if (mm.FormTemplateId.HasValue)
                    {
                        requestDto.Job.JobForms.Add(new GetJobRequirementsRequestDto.JobValidationRequestJobForm()
                        {
                            FormTemplateId = mm.FormTemplateId.Value,
                            Id = mm.Id
                        });
                    }
                    else
                    {
                        requestDto.Job.MediaMetadata.Add(
                            new GetJobRequirementsRequestDto.JobValidationRequestMediaMetadata()
                            {
                                Id = mm.Id,
                                ArtifactTypeId = mm.ArtifactTypeId,
                                Description = mm.Description,
                                FormTemplateId = mm.FormTemplateId,
                                FranchiseSetId = mm.FranchiseSetId,
                                IsDeleted = mm.IsDeleted,
                                IsForUpload = mm.IsForUpload,
                                JobId = job.Id,
                                JobAreaId = mm.JobAreaId,
                                JobVisitId = mm.JobVisitId,
                                ZoneId = mm.ZoneId,
                                JobAreaMaterialId = mm.JobAreaMaterialId,
                                JobSketch = mm.JobSketch,
                                JobSketchId = mm.JobSketchId,
                                MediaPath = mm.MediaPath,
                                Name = mm.Name,
                                MediaTypeId = mm.MediaTypeId,
                                UploadedSuccessfully = mm.UploadedSuccessfully
                            });

                        if (mm.JobInvoice != null)
                        {
                            requestDto.Job.JobSummaries.Add(
                                new GetJobRequirementsRequestDto.JobValidationRequestJobSummary()
                                {
                                    JobRevenue = mm.JobInvoice.Amount
                                });
                        }
                    }
                }

                foreach (var li in job.LineItems.Where(li => !li.IsDeleted))
                {
                    var estimatic = estimatics?.FirstOrDefault(e => e.LineItemId == li.LineItemId);

                    requestDto.Job.ScopeLineItems.Add(new GetJobRequirementsRequestDto.ScopeLineItem()
                    {
                        RoomId = li.RoomId,
                        LineItemId = li.Id,
                        JobVisitId = li.JobVisitId ?? Guid.Empty,
                        DeletedDate = li.DeletedDate,
                        Quantity = li.Quantity,
                        LineItem = new GetJobRequirementsRequestDto.JobValidationLineItem()
                        {
                            ActivityCode = li.ActivityCode,
                            Category = li.Category,
                            Code = li.Code,
                            Description = li.Description,
                            UnitOfMeasure = li.UnitOfMeasure,
                            EstimateItemType = estimatic == null
                                ? new GetJobRequirementsRequestDto.XactEstimateItemType()
                                : new GetJobRequirementsRequestDto.XactEstimateItemType()
                                {
                                    AfterHours = estimatic.IsAfterHours,
                                    CleanContents = estimatic.IsCleanContents ? 1 : 0,
                                    PackContents = estimatic.IsPackContents ? 1 : 0,
                                    Supervisory = estimatic.IsSupervisory
                                },
                            GroupCodes = estimatic?.GroupCodes?.Select(ValidationRequestMapper.Map).ToList()
                        }
                    });
                }

                _logger.LogInformation("Complete in {elapsedMilliseconds} seconds", stopwatch.ElapsedMilliseconds);
                _logger.LogTrace("Complete {@dto}", requestDto);
                return requestDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create CRS Dto.");
                throw;
            }
        }

        public bool GetJobUploadRequiredIndicator(Job job)
        {
            var jobUploadRequired = IsJobUploadRequired(job);

            // TODO: Job Program does not exist
            //if (jobUploadRequired)
            //{
            //    var programTypeId = job.JobProgramTypeId;
            //    if (programTypeId.HasValue && programTypeId.Value == (int)JobProgramTypeEnum.XactAnalysis)
            //    {
            //        jobUploadRequired = false; // should be able to use this existing variable
            //    }
            //}
            return jobUploadRequired;
        }

        public async Task<bool> InitialRequirementsMustBeClientRequired(Job job, GetLookups.Dto lookups, CancellationToken cancellationToken)
        {
            var stateFarmId = (await _db.InsuranceClients.FirstOrDefaultAsync(x => x.Name == "State Farm", cancellationToken))?.Id;
            return stateFarmId.HasValue && job?.InsuranceCarrierId == stateFarmId;
        }
    }
}