﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Data
{
    /// <summary>
    /// This context exists to take advantage of Auroras read replicas
    /// We have to have a seperate data context for this approach to work.
    /// It should point to the Aurora read replica endpoint so that read only operations (GETs)
    /// can pull data from replicas of the database (up to 15 per zone).
    /// </summary>
    public class JobReadOnlyDataContext : JobDataContext
    {
        public bool IsUnitTesting = false;

        public JobReadOnlyDataContext(DbContextOptions<JobReadOnlyDataContext> options) : base(options) { }

        public override int SaveChanges()
        {
            if (!IsUnitTesting)
            {
                throw new InvalidOperationException("This context is read-only.");
            }
            else
            {
                return base.SaveChanges();
            }
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            if (!IsUnitTesting)
            {
                throw new InvalidOperationException("This context is read-only.");
            }
            else
            {
                return await base.SaveChangesAsync(cancellationToken);
            }
        }
    }
}
