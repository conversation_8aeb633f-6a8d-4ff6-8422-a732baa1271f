﻿namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public abstract class CrsValidationBase
    {
        public string Name { get; set; }
        public int RuleId { get; set; }
        public string Description { get; set; }
        public string Notification { get; set; }
        public int ValidationStatus { get; set; }
        public string DoNothingMessage { get; set; }
        public string FailMessage { get; set; }
        public string PassMessage { get; set; }
        public bool? RequiredForInitialUpload { get; set; }
    }
}