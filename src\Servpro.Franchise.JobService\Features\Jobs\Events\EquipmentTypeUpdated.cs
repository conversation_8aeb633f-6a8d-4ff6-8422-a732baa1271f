﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Equipments;

using System;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class EquipmentTypeUpdated
    {
        public class Event : EquipmentTypeUpdatedEvent, IRequest
        {
            public Event(EquipmentTypeUpdatedDto dto, Guid correlationId) : base(dto, correlationId)
            {

            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<EquipmentTypeUpdated> _logger;
            private readonly JobDataContext _context;

            public Handler(JobDataContext context, 
                ILogger<EquipmentTypeUpdated> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{equipmentId}", request.EquipmentTypeUpdatedDto.Id);
                _logger.LogInformation("Handler began with {@incomingEvent}", request);

                var equipmentType = await _context.EquipmentTypes.FirstOrDefaultAsync(e => 
                    e.Id == request.EquipmentTypeUpdatedDto.Id, cancellationToken);

                if (equipmentType is null)
                {
                    _logger.LogInformation($"EquipmentType not found: {request.EquipmentTypeUpdatedDto.Id}");
                    return Unit.Value;
                }

                equipmentType.Name = request.EquipmentTypeUpdatedDto.Name;
                equipmentType.BaseEquipmentTypeId = request.EquipmentTypeUpdatedDto.BaseEquipmentTypeId;
                equipmentType.ModifiedBy = request.EquipmentTypeUpdatedDto.ModifiedBy;
                equipmentType.ModifiedDate = request.EquipmentTypeUpdatedDto.ModifiedDate;
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Handler completed successfully");

                return Unit.Value;
            }
        }
    }
}
