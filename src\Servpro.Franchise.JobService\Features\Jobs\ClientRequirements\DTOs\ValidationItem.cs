﻿using System;
using System.Collections.Generic;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs
{
    public class ValidationItem
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Notification { get; set; }
        public bool IsMet { get; set; }
        public bool AllowBypass { get; set; }
        public bool AllowAcknowledgement { get; set; }
        public int? ExceptionNoteId { get; set; }


        /// <summary>
        /// This is a detailed status which breaks down by Exception also
        /// </summary>
        public ValidationItemStatus Status { get; set; }

        public int OriginalValidationStatus { get; set; }

        /// <summary>
        /// Active or Complete; period.
        /// </summary>
        public ValidationItemValidationStatus ValidationStatus { get; set; }

        public int Id { get; set; }
        public long? ArtifactTypeId { get; set; }
        public Guid? FormTemplateId { get; set; }
        public Guid? JobAreaId { get; set; }
        public Guid? ZoneId { get; set; }
        public Guid? JobVisitId { get; set; }
        public Guid? JobArtifactId { get; set; }
        public long? JobArtifactWcId { get; set; }
        public Guid? JobFormId { get; set; }
        public Guid? RelatedFormId { get; set; }
        public Guid? RoomId { get; set; }
        public int? ScopeLineItemId { get; set; }
        public string ScopeLineItemInfo { get; set; }
        public int? XactLineItemId { get; set; }
        public int RuleId { get; set; }
        public string FormType { get; set; }
        public string ValidationEffect { get; set; }
        public bool AskedPerVisit { get; set; }
        public string GroupName { get; set; }
        public List<KeyValuePair<string, string>> RuleParameters { get; set; }
        public string MaterialType { get; set; }
        public Guid? JobTriStateQuestionId { get; set; }
        public bool IsActive { get; set; }
        public bool? IsAvailableInFirstNotice { get; set; }

        public ValidationItem()
        {
            RuleParameters = new List<KeyValuePair<string, string>>();
        }

        public ValidationItem(ValidationItem item)
        {
            Name = item.Name;
            Description = item.Description;
            Notification = item.Notification;
            IsMet = item.IsMet;
            AllowBypass = item.AllowBypass;
            AllowAcknowledgement = item.AllowAcknowledgement;
            ExceptionNoteId = item.ExceptionNoteId;
            Status = item.Status;
            ValidationStatus = item.ValidationStatus;
            Id = item.Id;
            ArtifactTypeId = item.ArtifactTypeId;
            FormTemplateId = item.FormTemplateId;
            JobAreaId = item.JobAreaId;
            ZoneId = item.ZoneId;
            JobVisitId = item.JobVisitId;
            JobArtifactId = item.JobArtifactId;
            RuleParameters = item.RuleParameters;
            JobArtifactWcId = item.JobArtifactWcId;
            JobFormId = item.JobFormId;
            RelatedFormId = item.RelatedFormId;
            RoomId = item.RoomId;
            ScopeLineItemId = item.ScopeLineItemId;
            ScopeLineItemInfo = item.ScopeLineItemInfo;
            XactLineItemId = item.XactLineItemId;
            RuleId = item.RuleId;
            FormType = item.FormType;
            ValidationEffect = item.ValidationEffect;
            AskedPerVisit = item.AskedPerVisit;
            GroupName = item.GroupName;
            MaterialType = item.MaterialType;
            JobTriStateQuestionId = item.JobTriStateQuestionId;
            IsAvailableInFirstNotice = item.IsAvailableInFirstNotice;
        }
    }
}