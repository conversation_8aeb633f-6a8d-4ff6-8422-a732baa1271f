﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Media;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class MediaMovedToMediaStore
    {
        public class Event : MediaMovedToMediaStoreEvent, IRequest
        {
            public Event(MediaMovedToMediaStoreDto dto, Guid correlationId) : base(dto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;

            public Handler(JobDataContext dataContext, ILogger<Handler> logger)
            {
                _db = dataContext;
                _logger = logger;
            }

            public async Task<Unit> Handle(MediaMovedToMediaStore.Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Precessing event: {@payload}", request);
                var media = request.MediaLocation;
                if (media == null)
                {
                    _logger.LogDebug("The Media property was null");
                    return Unit.Value;
                }

                foreach (var medium in media)
                {
                    _logger.LogDebug("Processing moved media to store: {@mediaInfo}", medium);
                    var metadata = await _db.MediaMetadata.FirstOrDefaultAsync(x => x.Id == medium.MediaId, cancellationToken);
                    if (metadata == null)
                    {
                        _logger.LogDebug("The media Id {MediaId} was not found in the database", medium.MediaId);
                        continue;
                    }

                    await LogMediaStringValidations(metadata);

                    metadata.MediaPath = medium.MediaPath;
                    metadata.BucketName = medium.BucketName;
                }
                await _db.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task LogMediaStringValidations(MediaMetadata mediaMetadata)
            {
                if(string.IsNullOrEmpty(mediaMetadata.MediaPath))
                    _logger.LogInformation("The media path is null or empty");

                if (string.IsNullOrEmpty(mediaMetadata.BucketName))
                    _logger.LogInformation("The BucketName is null or empty");
            }
        }
    }
}