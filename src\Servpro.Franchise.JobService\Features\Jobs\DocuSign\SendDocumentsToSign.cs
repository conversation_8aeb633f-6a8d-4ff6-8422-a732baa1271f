﻿using Amazon.DynamoDBv2.Model;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Amazon.SQS;
using Amazon.SQS.Model;

using Aspose.Pdf.Facades;

using FluentValidation;

using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.ClientRequirements.DTOs;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.DocuSign;
using Servpro.Franchise.LookupService.Constants;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.JournalNotes.JournalNoteCreatedEvent;

namespace Servpro.Franchise.JobService.Features.Jobs.DocuSign
{
    public class SendDocumentsToSign
    {
        public class Command : IRequest<Unit>
        {
            public List<Guid> TemplateIds { get; set; }
            public List<DocuSignEmail> Emails { get; set; }
            public Guid JobId { get; set; }
            public List<ImportDocument> ImportedDocuments { get; set; }
        }

        public class Handler : IRequestHandler<Command>
        {
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string SendAndSubscribeSQSUrl = "AWS:SqsSignUrl";
            private readonly JobDataContext _db;
            private readonly ILogger<SendDocumentsToSign> _logger;
            private readonly IAmazonS3 _s3Client;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly IAmazonSQS _sqsClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IFormsService _formService;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IDocuSignUtility _docuSignUtility;
            private static string _jobNote = "Documents have been sent for signatures";
            private static string _subject = "DocuSign Request";
            private static string _s3FolderName = "DocuSign";
            private readonly string _sqsUrl;
            private readonly string _s3Bucket;


            public Handler(ILogger<SendDocumentsToSign> logger,
                           IAmazonS3 s3Client,
                           IConfiguration config,
                           IUserInfoAccessor userInfoAccessor,
                           IAmazonSQS sqsClient,
                           IFranchiseServiceClient franchiseServiceClient,
                           IFormsService formService,
                           ISessionIdAccessor sessionIdAccessor,
                           IDocuSignUtility docuSignUtility,
                           JobDataContext db)
            {
                _logger = logger;
                _s3Client = s3Client;
                _userInfoAccessor = userInfoAccessor;
                _sqsClient = sqsClient;
                _franchiseServiceClient = franchiseServiceClient;
                _formService = formService;
                _sessionIdAccessor = sessionIdAccessor;
                _db = db;
                _docuSignUtility = docuSignUtility;
                _sqsUrl = config[SendAndSubscribeSQSUrl];
                _s3Bucket = config[S3MediaBucketNameKey];
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                if (request.TemplateIds.Count == 0)
                    return Unit.Value;

                using var scope = _logger.BeginScope("{jobId}", request.JobId);
                _logger.LogInformation("Creating SendDocumentsToBeSignedEvent");
                var user = _userInfoAccessor.GetUserInfo();

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();

                var job = await GetJobAsync(request.JobId, cancellationToken);
                var franchiseSet = await _franchiseServiceClient.GetFranchiseSetAsync(job.FranchiseSetId, cancellationToken);
                var franchise = franchiseSet.Franchises.FirstOrDefault(x => x.Id == job.FranchiseId);

                var sendAndSubscribe = CreateInitialSendAndSubscribeRequest(request, job, user);

                var templateIds = await RemoveDuplicateFormTemplates(request.TemplateIds, request.JobId, cancellationToken);

                foreach (var templateId in templateIds)
                {
                    var emailsToSend = MapEmails(request);

                    using var templateIdScope = _logger.BeginScope("{templateId}", templateId);
                    var importedDocument = request.ImportedDocuments?.FirstOrDefault(x => x.FormTemplateId == templateId);
                    var createDocumentRequest = new CreateDocumentRequest(templateId, job, franchise, franchiseSet, importedDocument, emailsToSend);
                    var doc = await CreateDocumentAndDetermineSigningData(createDocumentRequest, cancellationToken);
                    sendAndSubscribe.Documents.Add(doc);
                }

                _logger.LogDebug("DocuSign send and subscribe request: {@request}", sendAndSubscribe);

                var response = await SendToSendAndSubscribeQueue(sendAndSubscribe, correlationId, cancellationToken);
                if (response.HttpStatusCode != System.Net.HttpStatusCode.OK)
                    throw new Exception($"An error occurred trying to generate DocuSign send and subscribe request: {request.JobId}.  Sending the request to send and subscribe resulted in the following reponse: {response}");

                await AddJournalNote(request.JobId, user, correlationId, cancellationToken);

                return Unit.Value;
            }

            private async Task<HashSet<Guid>> RemoveDuplicateFormTemplates(List<Guid> templateIds, Guid jobId, CancellationToken cancellationToken)
            {
                var formTemplatesOutForSignatureMap = await _docuSignUtility.GetFormTemplatesOutForSignatureAsync(jobId, cancellationToken);

                var newList = new HashSet<Guid>();
                foreach (var template in templateIds.Distinct())
                {
                    if (!formTemplatesOutForSignatureMap.GetValueOrDefault(template))
                        newList.Add(template);
                }
                return newList;
            }

            /// <summary>
            /// Creates the document to be signed and determines the locations to sign as well as who needs to sign in the location
            /// </summary>
            private async Task<Document> CreateDocumentAndDetermineSigningData(CreateDocumentRequest request, CancellationToken cancellationToken)
            {
                var (_, job, franchise, _, _, emailsToSend) = request;
                var (doc, stream) = await CreateDocumentToBeSignedAsync(request, cancellationToken);
                AddSignatureLocations(doc, stream, emailsToSend);
                AddSignersToDocument(doc, job, franchise, emailsToSend);
                return doc;
            }

            private void AddSignersToDocument(
                Document doc,
                Job job,
                FranchiseDto franchise,
                Emails emailsToSent)
            {
                //Customer
                AddSigner(doc, emailsToSent.CustomerEmail, job.Customer.FullName);
                //Employee
                AddSigner(doc, emailsToSent.EmployeeEmail, franchise.Name);
                //Additionals Emails
                foreach (var additionEmail in emailsToSent.AdditionalEmails)
                {
                    AddAdditionalSigner(doc, additionEmail);
                }
            }

            private void AddSigner(Document doc, DocuSignEmail docuSignEmail, string name)
            {
                _logger.LogDebug("Add Signer - Doc: {name}, docuSignEmail: {@dto}", doc.Name, docuSignEmail);

                doc.Signers.Add(new Signer() { Address = docuSignEmail.Address, Name = name, Type = docuSignEmail.IsSignatureRequiered ? docuSignEmail.SignerType : SignerType.CarbonCopyRecipient, EmailType = docuSignEmail.Type });
            }

            private void AddAdditionalSigner(Document doc, DocuSignEmail docuSignEmail)
            {
                if (docuSignEmail.SignerType != SignerType.CarbonCopyRecipient)
                {
                    if (docuSignEmail.IsSignatureRequiered)
                    {
                        doc.Signers.Add(new Signer() { Address = docuSignEmail.Address, Name = docuSignEmail.Address, Type = docuSignEmail.SignerType, EmailType = docuSignEmail.Type });
                    }
                    else
                    {
                        _logger.LogWarning("Signer was not marked as required or a CC, skipping adding them as a Signer");
                    }
                }
                else
                {
                    doc.Signers.Add(new Signer() { Address = docuSignEmail.Address, Name = "CarbonCopyRecipient", Type = SignerType.CarbonCopyRecipient, EmailType = EmailType.Additional });
                }

            }

            private SendAndSubscribeRequest CreateInitialSendAndSubscribeRequest(Command request, Job job, UserInfo user)
                => new SendAndSubscribeRequest()
                {
                    JobId = request.JobId,
                    FranchiseSetId = job.FranchiseSetId,
                    Username = user.Username,
                    ProjectNumber = job.ProjectNumber
                };

            private async Task<(Document, MemoryStream)> CreateDocumentToBeSignedAsync(CreateDocumentRequest request, 
                CancellationToken cancellationToken)
            {
                var (formTemplateId, job, franchise, franchiseSet, importedDocument, _) = request;
                _logger.LogInformation($"Creating Document data for {formTemplateId}");

                var doc = new Document();
                var formTemplate = await _db.FormTemplates.FirstOrDefaultAsync(x => x.Id == formTemplateId, cancellationToken);

                var stream = importedDocument != null
                    ? await GetImportedDocumentStreamAsync(importedDocument, cancellationToken)
                    : await CreatePrefilledDocument(job, franchise, franchiseSet, formTemplate, cancellationToken);

                var streamCopy = new MemoryStream();
                await stream.CopyToAsync(streamCopy, cancellationToken);

                var key = $"{_s3FolderName}/{job.FranchiseSetId}/{job.Id}/{formTemplateId}";
                await SaveDocumentToS3(key, formTemplate.Name, stream, cancellationToken);

                doc.Name = formTemplate.Name;
                doc.FormTemplateId = formTemplateId;
                doc.S3Path = key;

                return (doc, streamCopy);
            }

            private async Task<MemoryStream> GetImportedDocumentStreamAsync(ImportDocument importedDocument, CancellationToken cancellationToken)
            {
                var stream = new MemoryStream();
                var getObjectRequest = new GetObjectRequest()
                {
                    BucketName = _s3Bucket,
                    Key = importedDocument.MediaPath
                };

                using (var response = await _s3Client.GetObjectAsync(getObjectRequest, cancellationToken))
                using (var responseStream = response.ResponseStream)
                {
                    await responseStream.CopyToAsync(stream, cancellationToken);
                    stream.Seek(0, SeekOrigin.Begin);
                }

                return stream;
            }

            private async Task SaveDocumentToS3(string key, string fileName, MemoryStream ms, CancellationToken cancellationToken)
            {
                var fileTransferUtility = new TransferUtility(_s3Client);
                ms.Position = 0;

                var transferRequest = new TransferUtilityUploadRequest
                {
                    BucketName = _s3Bucket,
                    Key = key,
                    InputStream = ms,
                    ContentType = "application/pdf",
                    Headers = { ContentDisposition = $"attachment; filename={fileName}" }
                };

                await fileTransferUtility.UploadAsync(transferRequest, cancellationToken);
            }

            private void AddSignatureLocations(Document doc, MemoryStream stream, Emails emailsToSends)
            {
                var templatesCodeWithComments = new HashSet<string> { "28529", "28530", "28531", "28749" };
                stream.Position = 0;
                var document = new Aspose.Pdf.Document(stream);
                var form = new Form(document);
                var isTemplateInCode = templatesCodeWithComments.Any(code => doc.Name.StartsWith(code));

                _logger.LogDebug("Setting fields for form: {@form}", form);

                ProcessFields(form, document, doc, emailsToSends);

                if (isTemplateInCode)
                    ProcessTemplateFields(document, doc);
            }

            private void ProcessFields(Form form, Aspose.Pdf.Document document, Document doc, Emails emailsToSends)
            {
                var fields = document.Form.Fields.ToList();

                var signatureFields = fields
                    .Where(y => form.GetFieldType(y.FullName) == FieldType.Signature);

                foreach (var field in signatureFields)
                {
                    var coordinates = Map(field, document, doc);
                    coordinates.FieldName = field.FullName;

                    var lowerFieldName = field.FullName.ToLower();
                    if (lowerFieldName.Contains(DocuSignKeywords.InitialHereField))
                    {
                        coordinates.Type = SignatureType.Initial;
                        doc.CustomerSignaturePositions.Add(coordinates);
                        emailsToSends.CustomerEmail.IsSignatureRequiered = true;
                    }
                    else
                    {
                        coordinates.Type = SignatureType.Sign;
                        AssignCoordinatesAndDetermineRequiredSignatures(coordinates, lowerFieldName, doc, emailsToSends);
                    }
                }
                
                var radioFields = fields
                    .Where(y => form.GetFieldType(y.FullName) == FieldType.Radio);

                //This fragment of code validate if two Radiobuttons with the same NAME are checked or not
                //This is for imports files that includes filled out radio buttons is sent to be signed, 
                //these should be maintained and should not be required in DocuSign.
                for (var i = 0; i < radioFields.Count(); i = i + 2)
                {
                    if (radioFields.ElementAtOrDefault(i) is null || radioFields.ElementAtOrDefault(i + 1) is null)
                        continue;

                    if (radioFields.ElementAtOrDefault(i).FullName == radioFields.ElementAtOrDefault(i + 1).FullName && radioFields.ElementAtOrDefault(i).ActiveState == radioFields.ElementAtOrDefault(i + 1).ActiveState)
                    {
                        for(var y = 0; y < 2 ; y++)
                        {
                            var position = y == 0 ? i : i + 1;
                            var field = radioFields.ElementAtOrDefault(position);
                            var coordinates = Map(field, document, doc);
                            coordinates.FieldName = field.FullName;
                            coordinates.Type = SignatureType.Radio;
                            var radioButtonField = (Aspose.Pdf.Forms.RadioButtonOptionField)field;
                            coordinates.ChoiceLabel = radioButtonField.OptionName;
                            doc.CustomerSignaturePositions.Add(coordinates);
                        }
                    }
                }
            }

            private void AssignCoordinatesAndDetermineRequiredSignatures(Coordinate coordinates, 
                string lowerFieldName, 
                Document doc, 
                Emails emailsToSend)
            {
                if (DocuSignKeywords.CustomerFieldIdentifiers.Any(lowerFieldName.Contains))
                {
                    doc.CustomerSignaturePositions.Add(coordinates);
                    _logger.LogDebug($"Set Customer Field to Sign and is requiered");
                    emailsToSend.CustomerEmail.IsSignatureRequiered = true;
                    SetAdditionalEmailsAsRequired(emailsToSend, SignerType.Customer);
                }
                else if (DocuSignKeywords.FranchiseFieldIdentifiers.Any(lowerFieldName.Contains))
                {
                    doc.FranchiseSignaturePositions.Add(coordinates);
                    _logger.LogDebug($"Set Franchise Field to Sign and is requiered");
                    emailsToSend.EmployeeEmail.IsSignatureRequiered = true;
                    SetAdditionalEmailsAsRequired(emailsToSend, SignerType.Franchise);
                }
                else if (DocuSignKeywords.ClaimsProfessionalFieldIdentifiers.Any(lowerFieldName.Contains))
                {
                    doc.ClaimsProfessionalSignaturePositions.Add(coordinates);
                    _logger.LogDebug($"Set Claims Professional Field to Sign and is required");
                    SetAdditionalEmailsAsRequired(emailsToSend, SignerType.ClaimsProfessional);
                }
            }

            private void SetAdditionalEmailsAsRequired(Emails emailsToSend, SignerType signerType)
            {
                foreach (var additionalEmail in emailsToSend.AdditionalEmails.Where(x => x.SignerType == signerType))
                {
                    additionalEmail.IsSignatureRequiered = true;
                }
            }

            private void ProcessTemplateFields(Aspose.Pdf.Document document, Document doc)
            {
                var recommendationField = (Aspose.Pdf.Forms.TextBoxField)document.Form.Fields.Where(y => y.AlternateName != null && y.AlternateName.Contains("Recommendation")).FirstOrDefault();

                if (recommendationField != null)
                {
                    recommendationField.Multiline = true;
                    recommendationField.DefaultAppearance = new Aspose.Pdf.Annotations.DefaultAppearance("Arial", 10, System.Drawing.Color.Black);
                    var coord = Map(recommendationField, document, doc);
                    coord.FieldName = recommendationField.FullName;
                    coord.Type = SignatureType.Text;
                    doc.CustomerSignaturePositions.Add(coord);
                }
                var additionalCommentsField = (Aspose.Pdf.Forms.TextBoxField)document.Form.Fields.Where(y => y.AlternateName != null && y.AlternateName.Contains("Comments")).FirstOrDefault();
                if (additionalCommentsField != null)
                {
                    additionalCommentsField.Multiline = true;
                    additionalCommentsField.DefaultAppearance = new Aspose.Pdf.Annotations.DefaultAppearance("Arial", 10, System.Drawing.Color.Black);
                    var coord = Map(additionalCommentsField, document, doc);
                    coord.FieldName = additionalCommentsField.FullName;
                    coord.Type = SignatureType.Text;
                    doc.CustomerSignaturePositions.Add(coord);
                }
            }

            private Coordinate Map(Aspose.Pdf.Forms.Field field, Aspose.Pdf.Document document, Document doc)
            {
                var coordinates = new Coordinate();
                coordinates.PageNumber = field.PageIndex;
                coordinates.X = (int)Math.Floor(field.Rect.LLX);
                //aspose calculates Y position from the bottom but docusign calculates it from the top
                coordinates.Y = (int)Math.Floor(document.Pages[field.PageIndex].Rect.Height - field.Rect.URY);

                if (doc.Name.Equals("28501 - Customer Information Form - Water Damage"))
                {
                    coordinates.X -= 2;
                    coordinates.Y -= 2;
                    if (field is Aspose.Pdf.Forms.SignatureField signatureField) coordinates.Y -= 4;
                }
                return coordinates;
            }


            private async Task<MemoryStream> CreatePrefilledDocument(Job job, FranchiseDto franchise, GetFranchiseSetDto franchiseSet, FormTemplate formTemplate, CancellationToken cancellationToken)
            {
                try
                {
                    return await GetFormDataAsync(job, franchise, franchiseSet, formTemplate, cancellationToken);
                }
                catch (Exception e)
                {
                    _logger.LogError("Error retrieving forms. {errorMessage}. {@error}", e.Message, e);
                    throw;
                }
            }

            private async Task<Job> GetJobAsync(Guid jobId, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
                job.MediaMetadata = await _db.MediaMetadata.Include(mm => mm.JobInvoice).Where(mm => mm.JobId == jobId).ToListAsync(cancellationToken);
                job.JobContacts = await _db.JobContactMap.Include(jc => jc.Contact).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);
                job.JobAreas = await _db.JobAreas.Include(x => x.JobAreaMaterials).Where(jc => jc.JobId == jobId).ToListAsync(cancellationToken);
                return job;
            }

            private async Task AddJournalNote(Guid jobId, UserInfo user, Guid correlationId, CancellationToken cancellationToken)
            {
                var journalNote = CreateJournalNote(jobId, user);
                var journalNoteCreatedEvent = GenerateJournalNoteCreatedEvent(journalNote, correlationId, user);

                await _db.JournalNote.AddAsync(journalNote, cancellationToken);
                await _db.OutboxMessages.AddAsync(journalNoteCreatedEvent, cancellationToken);
            }

            private OutboxMessage GenerateJournalNoteCreatedEvent(JournalNote journalNote, Guid correlationId, UserInfo userInfo)
            {
                var journalNoteCreatedDto = MapJournalNoteDto(journalNote, userInfo);
                var journalNoteCreatedEvent = new JournalNoteCreatedEvent(journalNoteCreatedDto, correlationId);
                return new OutboxMessage(journalNoteCreatedEvent.ToJson(), nameof(JournalNoteCreatedEvent), correlationId, userInfo.Username);
            }

            private async Task<MemoryStream> GetFormDataAsync(Job job, FranchiseDto franchise, GetFranchiseSetDto franchiseSet, FormTemplate formTemplate, CancellationToken cancellationToken)
            {
                var mergedFormRequest = new MergeFormsRequest(job, franchise, franchiseSet);
                mergedFormRequest.FormTemplates.Add(formTemplate);
                var formData = await _formService.GetMergedFormsAsync(mergedFormRequest, cancellationToken);

                return formData;
            }

            public async Task<SendMessageResponse> SendToSendAndSubscribeQueue(SendAndSubscribeRequest documents, Guid correlationId, CancellationToken cancellationToken)
            {
                if (documents != null)
                {
                    var messageRequest = new SendMessageRequest()
                    {
                        QueueUrl = _sqsUrl,
                        MessageBody = documents.ToJson(),
                        MessageAttributes = new Dictionary<string, MessageAttributeValue>
                        {
                            {
                            "CorrelationId",
                            new MessageAttributeValue
                                {StringValue = correlationId.ToString(), DataType = nameof(String)}
                            },
                            {
                            "MessageType",
                            new MessageAttributeValue
                                {StringValue = nameof(documents), DataType = nameof(String)}
                            }
                        }
                    };

                    var response = await _sqsClient.SendMessageAsync(messageRequest, cancellationToken);
                    return response;
                }

                return null;
            }

            private JournalNote CreateJournalNote(Guid jobId, UserInfo user)
                => new JournalNote
                {
                    JobId = jobId,
                    CreatedDate = DateTime.UtcNow,
                    Author = user.Name,
                    Note = _jobNote,
                    ActionDate = DateTime.UtcNow,
                    CategoryId = JournalNoteCategories.Notes,
                    TypeId = JournalNoteTypes.OtherJobDetails,
                    VisibilityId = JournalNoteVisibilityTypes.FranchiseClientandCustomer,
                    Subject = _subject,
                    IncludeInSummaryCoverPage = true
                };

            private JournalNoteDto MapJournalNoteDto(JournalNote journalNote, UserInfo userInfo) =>
                new JournalNoteDto
                {
                    Id = journalNote.Id,
                    JobId = journalNote.JobId.Value,
                    CreatedDate = journalNote.CreatedDate,
                    Author = journalNote.Author,
                    Message = journalNote.Note,
                    ActionDate = journalNote.ActionDate,
                    CategoryId = journalNote.CategoryId,
                    TypeId = journalNote.TypeId,
                    VisibilityId = journalNote.VisibilityId,
                    Subject = journalNote.Subject,
                    CreatedBy = userInfo.Username,
                    CreatedById = userInfo.Id,
                    RuleIds = journalNote.RuleIds?.ToList()
                };

            public Emails MapEmails(Command request)
            {
                return new Emails()
                {
                    CustomerEmail = MapDocuSignEmail(request.Emails.FirstOrDefault(x => x.Type == EmailType.Customer)),
                    EmployeeEmail = MapDocuSignEmail(request.Emails.FirstOrDefault(x => x.Type == EmailType.Employee)),
                    AdditionalEmails = request.Emails.Where(x => x.Type == EmailType.Additional).Select(MapDocuSignEmail).ToList()
                };
            }

            private DocuSignEmail MapDocuSignEmail(DocuSignEmail email)
                => new DocuSignEmail
                {
                    Address = email.Address,
                    Type = email.Type,
                    SignerType = email.SignerType,
                    IsSignatureRequiered = false
                };
        }
    }
}