﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Documents.MediaEvents;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.Franchise.JobService.Features.Jobs.Mapping;

using Servpro.FranchiseSystems.Framework.Setup.FeatureFlags;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class SaveDocument
    {
        public class Command : IRequest<List<Guid>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public ICollection<MediaDto> Media { get; set; }
            public string Username { get; set; }
        }

        public class MediaDto
        {
            public Guid ArtifactTypeId { get; set; }
            public bool IsForUpload { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public Guid Id { get; set; }
            public Guid? FormTemplateId { get; set; }
            public JobInvoiceDto JobInvoice { get; set; }
        }

        public class JobInvoiceDto
        {
            public string InvoiceNumber { get; set; }
            public string Description { get; set; }
            public string Source { get; set; }
            public DateTime Date { get; set; }
            public double Amount { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, List<Guid>>
        {
            private readonly JobDataContext _db;
            private readonly IMediator _mediator;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly IMediaEventGenerator _mediaEventGenerator;
            private readonly IConfiguration _config;
            private readonly ILogger<SaveDocument> _logger;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private readonly IFeatureFlagUtility _featureFlags;

            public Handler(JobDataContext db, IUserInfoAccessor userInfoAccessor, ISessionIdAccessor sessionIdAccessor,
                IMediator mediator, IMediaEventGenerator mediaEventGenerator, IConfiguration config, ILogger<SaveDocument> logger, IFeatureFlagUtility featureFlag)
            {
                _mediator = mediator;
                _mediaEventGenerator = mediaEventGenerator;
                _config = config;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _db = db;
                _logger = logger;
                _featureFlags = featureFlag;
            }

            public async Task<List<Guid>> Handle(Command request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Saving Document to Job: {jobId}", request.JobId);

                var userInfo = _userInfoAccessor.GetUserInfo() ?? 
                                new UserInfo(Guid.NewGuid(), request.FranchiseSetId, request.Username, request.Username);

                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                await SatisfyAtpRequirementValidation(request, userInfo, cancellationToken);

                var mediaMetadata = request.Media.Select(media => {
                    if ((media.FormTemplateId == null || media.FormTemplateId == Guid.Empty) &&
                        (media.ArtifactTypeId == Guid.Empty))
                    {
                        _logger.LogWarning("Invalid artifact for MediaId {MediaId}: Both FormTemplateId and ArtifactTypeId are missing or empty.", media.Id);
                    }

                    return Map(request, media);
                }).ToList();

                await _db.MediaMetadata.AddRangeAsync(mediaMetadata, cancellationToken);

                await HandleInvoiceData(request.JobId, mediaMetadata, correlationId, userInfo, cancellationToken);
                await GenerateSaveDocumentEvent(request, correlationId, userInfo);
                var outboxMessage = _mediaEventGenerator.GenerateMediaAddedEvent(mediaMetadata, correlationId, userInfo);
                await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);

                return mediaMetadata.Select(x => x.Id).ToList();
            }

            private async Task HandleInvoiceData(Guid jobId, List<MediaMetadata> mediaMetadata, Guid correlationId, UserInfo userInfo, CancellationToken cancellationToken)
            {
                var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);
                var invoices = mediaMetadata.Where(x => x.JobInvoice != null).Select(x => x.JobInvoice).ToList();

                if (invoices.Count == 0)
                    return;

                foreach (var invoice in invoices)
                {
                    await GenerateInvoiceCreatedEvent(jobId, invoice, correlationId, userInfo);
                    job.TotalRevenue ??= 0;
                    job.TotalRevenue += Convert.ToDecimal(invoice.Amount);
                }

                var call = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => x.AssociatedJobId == job.Id, cancellationToken);
                if (call != null && call.RevenueInvoiced != job.TotalRevenue)
                {
                    if (call.RevenueInvoiced != null)
                        call.InvoiceAmountChanged = true;
                    call.RevenueInvoiced = job.TotalRevenue;
                    call.InvoicedAmountLastUpdated = DateTime.UtcNow;
                }

                await GenerateJobDetailsUpdatedEvent(job, userInfo);
            }

            private async Task GenerateInvoiceCreatedEvent(Guid jobId, JobInvoice invoice, Guid correlationId, UserInfo userInfo)
            {
                var invoiceEventDto = new InvoiceCreatedEvent.InvoiceCreatedDto()
                {

                    JobId = jobId,
                    InvoiceId = invoice.Id,
                    MediaMetadataId = invoice.MediaMetadataId,
                    InvoiceNumber = invoice.InvoiceNumber,
                    Description = invoice.Description,
                    Source = invoice.Source,
                    Amount = invoice.Amount,
                    Date = invoice.Date,
                    CreatedUtc = DateTime.UtcNow,
                    CreatedBy = userInfo.Username

                };
                var invoiceEvent = new InvoiceCreatedEvent(invoiceEventDto, correlationId);
                var outboxMessage = new OutboxMessage(invoiceEvent.ToJson(), nameof(InvoiceCreatedEvent),
                    correlationId, userInfo.Username);
                await _db.OutboxMessages.AddAsync(outboxMessage);
            }

            private async Task GenerateJobDetailsUpdatedEvent(Job job, UserInfo userInfo)
            {
                var correlationId = _sessionIdAccessor.GetCorrelationGuid();
                var eventDto = JobDetailsUpdatedMapper.MapJobDetailsUpdatedDto(job, false, userInfo);
                var newEvent = new JobDetailsUpdatedEvent(eventDto, correlationId);
                var outboxMessage = new OutboxMessage(newEvent.ToJson(), nameof(JobDetailsUpdatedEvent),
                         correlationId, userInfo.Username);
                await _db.OutboxMessages.AddAsync(outboxMessage);
            }

            private async Task GenerateSaveDocumentEvent(Command request, Guid correlationId, UserInfo userInfo)
            {
                foreach (var media in request.Media)
                {
                    var eventDto = new DocumentSavedDto()
                    {
                        Id = media.Id,
                        JobId = request.JobId,
                        FranchiseSetId = request.FranchiseSetId,
                        IsDeleted = false,
                        IsForUpload = media.IsForUpload,
                        Name = media.Name,
                        MediaTypeId = MediaTypes.Document,
                        ArtifactTypeId = media.ArtifactTypeId,
                        MediaPath = media.MediaPath,
                        FormTemplateId = media.FormTemplateId,
                        SignedDate = media.FormTemplateId.HasValue ? DateTime.UtcNow : (DateTime?)null,
                        SyncDate = media.FormTemplateId.HasValue ? DateTime.UtcNow : (DateTime?)null
                    };

                    var outboxEvent = new DocumentSavedEvent(eventDto, correlationId);
                    var outboxMessage = new OutboxMessage(outboxEvent.ToJson(), nameof(DocumentSavedEvent),
                        correlationId, userInfo?.Username);

                    await _db.OutboxMessages.AddAsync(outboxMessage);
                }
            }

            private async Task SatisfyAtpRequirementValidation(Command request, UserInfo userInfo, CancellationToken cancellationToken)
            {
                _logger.LogDebug("SatisfyAtpRequirementValidation: User {@user}", userInfo);
                if (request.Media.All(x => x.FormTemplateId.HasValue))
                {
                    var atpRequirement = new SatisfyAtpRequirement.Command()
                    {
                        JobId = request.JobId,
                        Date = DateTime.UtcNow,
                        FormTemplateIds = request.Media.Select(x => x.FormTemplateId).ToList(),
                        ModifiedBy = userInfo.Username
                    };
                    _logger.LogInformation("SatisfyAtpRequirementValidation: Sending atpRequirement request {@request}", atpRequirement);
                    await _mediator.Send(atpRequirement, cancellationToken);
                }
            }

            private MediaMetadata Map(Command command, MediaDto media)
            {
                var jobInvoice = media.JobInvoice != null ? MapInvoice(media.JobInvoice) : null;
                var mediaMetadata = new MediaMetadata
                {
                    Id = media.Id,
                    JobId = command.JobId,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    FranchiseSetId = command.FranchiseSetId,
                    IsDeleted = false,
                    IsForUpload = media.IsForUpload,
                    Name = media.Name,
                    MediaTypeId = MediaTypes.Document,
                    ArtifactTypeId = media.ArtifactTypeId,
                    MediaPath = media.MediaPath,
                    FormTemplateId = media.FormTemplateId,
                    SignedDate = media.FormTemplateId.HasValue ? DateTime.UtcNow : (DateTime?)null,
                    SyncDate = media.FormTemplateId.HasValue ? DateTime.UtcNow : (DateTime?)null,
                    JobInvoice = jobInvoice,
                    JobInvoiceId = jobInvoice?.Id,
                    BucketName = _config.GetValue<string>(S3MediaBucketNameKey),
                    UploadedSuccessfully = true
                };
                return mediaMetadata;
            }

            private JobInvoice MapInvoice(JobInvoiceDto jobInvoice)
            {
                if (jobInvoice == null || ( jobInvoice!= null && string.IsNullOrEmpty(jobInvoice.InvoiceNumber)))
                    return null;

                return new JobInvoice()
                {
                    Id = Guid.NewGuid(),
                    InvoiceNumber = jobInvoice.InvoiceNumber,
                    Description = jobInvoice.Description,
                    Source = jobInvoice.Source,
                    Date = jobInvoice.Date,
                    Amount = jobInvoice.Amount
                };
            }
        }
    }
}

