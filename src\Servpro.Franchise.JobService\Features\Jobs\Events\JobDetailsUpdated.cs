﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Common.Utils;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.JobUpload;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.GoogleService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Infrastructure.Utilities;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService;
using Servpro.Franchise.LookupService.Constants;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Leads;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;
using Servpro.FranchiseSystems.Framework.Setup.FeatureFlags;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.JobDetailsUpdatedEvent;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class JobDetailsUpdated
    {
        public class Event : JobDetailsUpdatedEvent, IRequest
        {
            public Event(JobDetailsUpdatedDto jobDetailsUpdated, Guid correlationId) : base(jobDetailsUpdated, correlationId) { }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<JobDetailsUpdated> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILookupServiceClient _lookupService;
            private readonly IGoogleServiceClient _googleServiceClient;
            private readonly IInsuranceCarrierUtility _insuranceCarrierUtility;
            private readonly IFeatureFlagUtility _featureFlags;

            public Handler(
                JobDataContext db,
                ILogger<JobDetailsUpdated> logger,
                IFranchiseServiceClient franchiseServiceClient,
                IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor,
                IGoogleServiceClient googleServiceClient,
                ILookupServiceClient lookupService,
                IFeatureFlagUtility featureFlags,
                IInsuranceCarrierUtility insuranceCarrierUtility)
            {
                _db = db;
                _logger = logger;
                _franchiseServiceClient = franchiseServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _lookupService = lookupService;
                _googleServiceClient = googleServiceClient;
                _insuranceCarrierUtility = insuranceCarrierUtility;
                _featureFlags = featureFlags;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}", request.JobDetailsUpdated.Id);
                _logger.LogInformation("Processing request: {@payload}", request);
                var userInfo = _userInfoAccessor.GetUserInfo();
                var correlationId = GetCorrelationId();
                var lookupTask = _lookupService.GetLookupsAsync(cancellationToken);
                var lookups = await lookupTask;

                var jobDetailsUpdated = request.JobDetailsUpdated;
                _logger.LogDebug("{event} {rmIntegration} Params received {@json}", nameof(JobDetailsUpdated), true, jobDetailsUpdated);

                var job = await _db.Jobs
                    .Include(x => x.JobTriStateAnswers)
                    .Include(j => j.Caller)
                    .Include(j => j.Customer)
                    .ThenInclude(c => c.Business)
                    .FirstOrDefaultAsync(x => x.Id == jobDetailsUpdated.Id, cancellationToken);

                if (job is null)
                {
                    _logger.LogWarning("{event} The Job with Id({ID}) Does Not Exist", nameof(JobDetailsUpdated), jobDetailsUpdated.Id);
                    return Unit.Value;
                }

                // InsuranceClientId is defaulting to empty guid in event when there was no change - there should always be a valid InsuranceClientId
                // it should never be an Empty guid (even if its a self pay job)
                if (job.InsuranceCarrierId != null &&
                    jobDetailsUpdated.InsuranceClientId != Guid.Empty &&  
                    job.InsuranceCarrierId != jobDetailsUpdated.InsuranceClientId)
                {
                    _logger.LogInformation("Getting Franchise info for Job {jobId}", job.Id);
                    var franchiseInfo = await _franchiseServiceClient.GetFranchiseWithoutUserClaimsAsync(job.FranchiseId, job.FranchiseSetId, cancellationToken: cancellationToken);

                    if (franchiseInfo == null)
                    {
                        throw new Exception($"Franchise {job.FranchiseId} doesn't exists.");
                    }

                    // If the InsuranceCarrier has changed, to self pay or Unknown, we need to remove due dates as these should not require uploads
                    var selfPayInsuranceId = (await _db.InsuranceClients
                        .FirstOrDefaultAsync(ic => ic.Name.ToLower() == "self pay" || ic.Name.ToLower() == "selfpay", cancellationToken))?.Id;
                    var unknownInsuranceId = (await _db.InsuranceClients
                        .FirstOrDefaultAsync(ic => ic.Name.ToLower() == "unknown", cancellationToken))?.Id;

                    if ((jobDetailsUpdated.InsuranceClientId == selfPayInsuranceId ||
                        jobDetailsUpdated.InsuranceClientId == unknownInsuranceId) && job.CorporateJobNumber.HasValue)
                    {
                        _logger.LogInformation("Insurance Client was updated to Self Pay or Unknown, Triggering Upload to update CCMS/Due Dates.");
                        job.InsuranceCarrierId = jobDetailsUpdated.InsuranceClientId;
                        try
                        {
                            await UploadJob(job, userInfo, cancellationToken, correlationId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error occurred while trying to perform an upload.");
                        }
                    }

                    await RaiseInsuranceCarrierChangedEvent(userInfo, job.Id, jobDetailsUpdated.InsuranceClientId,
                        correlationId, job.CorporateJobNumber, (int) franchiseInfo.FranchiseNumber);

                    // Add first notice activity when insurance carrier is updated
                    _logger.LogInformation("Add first notice activity AddInsuranceCarrier");
                    await _db.FirstNoticeActivity.AddAsync(new FirstNoticeActivity
                    {
                        ActivityTypeId = ActivityType.AddInsuranceCarrier,
                        RevisedValue = request.JobDetailsUpdated.InsuranceClientId.ToString(),
                        ModifiedBy = request.JobDetailsUpdated.UpdatedBy,
                        CreatedBy = request.JobDetailsUpdated.UpdatedBy,
                        ModifiedDate = DateTime.UtcNow,
                        CreatedDate = DateTime.UtcNow,
                        RecordSourceId = RecordSourceTypes.RestorationManager,
                        JobId = request.JobDetailsUpdated.Id,
                        Id = Guid.NewGuid()
                    }, cancellationToken);

                    var projectKeyFieldsUpdated = new ProjectKeyFieldsUpdatedEvent.ProjectKeyFieldsUpdatedDto
                    {
                        FranchiseId = job.FranchiseId,
                        FranchiseSetId = job.FranchiseSetId,
                        JobId = job.Id,
                        JobProgress = (FranchiseSystems.Framework.Messaging.Events.Common.Enums.JobProgress)job.JobProgress,
                        MentorId = job.MentorId,
                        ProjectNumber = job.ProjectNumber,
                        PropertyTypeId = job.PropertyTypeId,
                        Username = request.JobDetailsUpdated.UpdatedBy,
                        BusinessName = job.BusinessName,
                        LossTypeId = job.LossTypeId,
                        Customer = new ProjectKeyFieldsUpdatedEvent.ContactDto
                        {
                            FirstName = job.Customer?.FirstName,
                            LastName = job.Customer?.LastName
                        }
                    };

                    _logger.LogInformation("{projectKeyFieldsUpdated}: ", projectKeyFieldsUpdated);

                    var @event = new ProjectKeyFieldsUpdatedEvent(projectKeyFieldsUpdated, correlationId);
                    var outboxMessage = new OutboxMessage(@event.ToJson(), nameof(ProjectKeyFieldsUpdatedEvent), correlationId, request.JobDetailsUpdated.UpdatedBy ?? job.CreatedBy);
                    await _db.OutboxMessages.AddAsync(outboxMessage, cancellationToken);
                    _logger.LogInformation("Generated event {eventName}: {@payload}", nameof(ProjectKeyFieldsUpdatedEvent), @event);
                }

                if (jobDetailsUpdated.FranchiseId.HasValue && jobDetailsUpdated.FranchiseId != job.FranchiseId)
                {
                    _logger.LogInformation("Setting new FranchiseId to Job: {jobId}", job.Id);
                    await RaiseJobFranchiseChangedEvent(userInfo, job.Id, jobDetailsUpdated.FranchiseId.Value, correlationId);
                }

                if (_insuranceCarrierUtility.IsClaimNumberUpdated(jobDetailsUpdated.ClaimNumber, job.InsuranceClaimNumber))
                {
                    _logger.LogInformation("Updating claim number to: {claimNumber}", jobDetailsUpdated.ClaimNumber);
                    await RaiseInsuranceClaimNumberChangedEvent(job.Id, jobDetailsUpdated.ClaimNumber, userInfo?.Username, DateTime.UtcNow, correlationId);
                }

				try
                {
                    var lossAddressGeoLocation = await GetLossAddressGeoLocation(request,  cancellationToken);
                    _logger.LogInformation("Job loss geolocation: {@lossAddressGeoLocation}", lossAddressGeoLocation);
                    if (jobDetailsUpdated.IsFinancialUpdated)
                    {
                        _logger.LogDebug("{event} Mapping job QBO details", nameof(JobDetailsUpdated));
                        await MapJobQBOJobDetails(job, lookups, jobDetailsUpdated, lossAddressGeoLocation);                    
                    }
                    else
                    {
                        _logger.LogDebug("{event} Mapping job details", nameof(JobDetailsUpdated));
                        await MapJobDetails(job, lookups, jobDetailsUpdated, lossAddressGeoLocation, request.SourceServiceName);
                    }

                    await _db.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("{event} Successful job updated", nameof(JobDetailsUpdated));
                }
                catch(Exception e)
                {
                    _logger.LogWarning(e, "{event} Error", nameof(JobDetailsUpdated));
                }

                return Unit.Value;
            }

            private async Task UploadJob(Job job, UserInfo userInfo, CancellationToken cancellationToken, Guid correlationId)
            {
                job.JobContacts = await _db.JobContactMap
                            .Include(x => x.Contact)
                            .Where(x => x.JobId == job.Id)
                            .ToListAsync(cancellationToken);

                var uploadsToXact = JobUploadsToXact(job);

                // check for any upload locks
                var uploadTypeId = JobUploadTypes.JobInitial;
                var uploadLock = await _db.JobUploadLocks
                    .AsNoTracking()
                    .Where(l => l.JobId == job.Id && l.JobUploadTypeId == uploadTypeId)
                    .FirstOrDefaultAsync(cancellationToken: cancellationToken);

                if (uploadLock != null)
                    throw new ValidationException($"Requested upload is already in-progress.  Requested by: {uploadLock.CreatedBy} at {uploadLock.CreatedDate}");

                bool isExtension = false;

                if (uploadsToXact && !isExtension)
                    await RaiseXactUploadEvent(job, userInfo, correlationId, cancellationToken);
                else
                    await RaiseCorpSyncEvent(job, correlationId, cancellationToken);
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername, CancellationToken cancellationToken)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _db.OutboxMessages.AddAsync(newEvent, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);
            }

            private async Task RaiseXactUploadEvent(Job job, UserInfo userInfo, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising {eventName}", nameof(XactJobUploadStartedEvent));
                var lookups = await _lookupService.GetLookupsAsync(cancellationToken);
                var insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(ic => ic.Id == job.InsuranceCarrierId, cancellationToken);

                var xactDto = UploadEventGenerator.GetXactJobUploadStarted(
                    lookups,
                    insuranceClient?.InsuranceNumber ?? 0,
                    job,
                    XactJobUploadStartedEvent.UploadType.Initial,
                    userInfo.Username,
                    userInfo.Id
                    );

                var xactUploadEvent = new XactJobUploadStartedEvent(xactDto, correlationId);

                await GenerateOutboxMessage(xactUploadEvent.ToJson(), nameof(XactJobUploadStartedEvent),
                    xactUploadEvent.CorrelationId, userInfo.Username, cancellationToken);
            }

            private async Task RaiseCorpSyncEvent(Job job, Guid correlationId, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Raising {eventName}", nameof(CorporateJobSyncStartedEvent));
                InsuranceClient insuranceClient = null;
                if (job.InsuranceCarrierId.HasValue)
                {
                    insuranceClient = await _db.InsuranceClients.FirstOrDefaultAsync(c => c.Id == job.InsuranceCarrierId.Value, cancellationToken);
                }

                var franchise = await _franchiseServiceClient.GetFranchiseAsync(job.FranchiseId, job.FranchiseSetId, false, cancellationToken);

                if (franchise == null)
                {
                    const string error = "Franchise Service was unavailable. Unable to determine FranchiseNumber.";
                    throw new RetryEventException(error);
                }

                var franchiseNumber = (int)franchise.FranchiseNumber; // not sure why someone made this a long instead of an int

                job.JobContacts = await _db.JobContactMap
                   .Include(x => x.Contact)
                       .ThenInclude(y => y.Business)
                   .Where(x => x.JobId == job.Id)
                   .ToListAsync(cancellationToken);

                job.JournalNotes = await _db.JournalNote
                    .Where(x => x.JobId == job.Id)
                    .ToListAsync(cancellationToken);

                var jobUploadDto = UploadEventGenerator.GetJobUploadForCorporateJobSync(
                    job,
                    insuranceClient,
                    UploadType.Initial,
                    nameof(JobDetailsUpdated),
                    Guid.Empty,
                    franchiseNumber,
                    JobUploadsToXact(job),
                    true,
                    false,
                    null
                    );

                // this should NOT happen, but putting it in here anyway
                if (jobUploadDto.CorporateServiceDto.DoesJobUploadToXact && !jobUploadDto.CorporateServiceDto.IsExtension)
                {
                    var eventDto = new XactJobUploadFailedEvent.XactJobUploadFailedDto()
                    {
                        ProjectNumber = job.ProjectNumber,
                        CreatedUtc = DateTime.UtcNow,
                        Error = "XactAnalysis jobs cannot use Corporate Sync",
                        JobId = job.Id,
                        Username = nameof(JobDetailsUpdated)
                    };
                    var outgoingEvent = new XactJobUploadFailedEvent(eventDto, correlationId);
                    await GenerateOutboxMessage(outgoingEvent.ToJson(), nameof(XactJobUploadFailedEvent),
                        outgoingEvent.CorrelationId, nameof(JobDetailsUpdated), cancellationToken);
                    return;
                }

                var corpSyncEvent = new CorporateJobSyncStartedEvent(jobUploadDto, correlationId);

                await GenerateOutboxMessage(corpSyncEvent.ToJson(), nameof(CorporateJobSyncStartedEvent),
                    corpSyncEvent.CorrelationId, nameof(JobDetailsUpdated), cancellationToken);
            }

            private async Task RaiseCorporateJobSyncFailedEvent(Guid jobId, string error, Guid correlationId, CancellationToken cancellationToken)
            {
                var dto = new CorporateJobSyncFailedEvent.JobSyncFailedDto()
                {
                    JobId = jobId,
                    CreatedBy = nameof(JobDetailsUpdated),
                    CreatedUtc = DateTime.UtcNow,
                    Error = error,
                    UploadType = CorporateJobSyncFailedEvent.UploadType.Initial,
                    WasAutoUpload = true
                };

                var newEvent = new OutboxMessage(new CorporateJobSyncFailedEvent(dto, correlationId).ToJson(),
                    nameof(CorporateJobSyncFailedEvent),
                    correlationId,
                    nameof(JobDetailsUpdated));
                await _db.OutboxMessages.AddAsync(newEvent, cancellationToken);
                await _db.SaveChangesAsync(cancellationToken);
            }

            private bool JobUploadsToXact(Job job)
            {
                return job.CorporateJobNumber.HasValue && job.JobDispatchTypeId.HasValue &&
                       job.JobDispatchTypeId.Value == DispatchTypes.NonScanEr;
            }

            private async Task MapJobDetails(Job job,
                                             GetLookups.Dto lookups,
                                             JobDetailsUpdatedDto jobDetailsUpdated,
                                             Location lossAddressGeoLocation,
                                             string sourceServiceName)
            {
                job.FranchiseId = jobDetailsUpdated.FranchiseId ?? job.FranchiseId;
                job.LossAddress = MapLossAddress(jobDetailsUpdated.LossAddress, lookups, lossAddressGeoLocation);
                job.InsuranceCarrierId = jobDetailsUpdated.InsuranceClientId;
                job.InsuranceClaimNumber = jobDetailsUpdated.ClaimNumber;
                job.InsurancePolicyNumber = jobDetailsUpdated.PolicyNumber;
                job.IsWaterAvailable = jobDetailsUpdated.IsWaterAvailable;
                job.JobFileCoordinatorId = jobDetailsUpdated.JobFileCoordId;
                job.LossNote = jobDetailsUpdated.LossNotes;
                job.PropertyTypeId = jobDetailsUpdated.PropertyTypeId;
                job.StructureTypeId = jobDetailsUpdated.StructureTypeId;
                job.RecpDispatcherId = jobDetailsUpdated.DispatcherId;
                job.YearStructureBuilt = jobDetailsUpdated.YearBuilt;
                job.DeductibleAmount = jobDetailsUpdated.DeductibleAmount ?? job.DeductibleAmount;
                job.ProjectManagerId = jobDetailsUpdated.ProjectManagerId;
                job.ProductionManagerId = jobDetailsUpdated.ProductionManagerId;
                job.CrewChiefId = jobDetailsUpdated.CrewChiefId;
                job.MarketingRepId = sourceServiceName == "RMService" ? job.MarketingRepId : jobDetailsUpdated.MarketingRepId;
                job.FacilityTypeId = jobDetailsUpdated.FacilityTypeId;
                job.IsChase = jobDetailsUpdated.IsChase;
                job.IsElectricAvailable = jobDetailsUpdated.IsElectricAvailable;
                job.LevelsAffected = jobDetailsUpdated.LevelsAffected;
                job.IsMultiUnitStructure = jobDetailsUpdated.IsMultiUnitStructure;
                job.SquareFeet = jobDetailsUpdated.SquareFeet;
                job.WorkOrderNumber = jobDetailsUpdated.WorkOrderNumber;
                job.PurchaseOrderNumber = jobDetailsUpdated.PurchaseOrderNumber;
                job.SiteReferenceNumber = jobDetailsUpdated.SiteReferenceNumber;
                job.NteAmount = jobDetailsUpdated.NteAmount;
                job.IsRedFlagged = jobDetailsUpdated.IsRedFlagged;
                job.RedFlagNotes = jobDetailsUpdated.RedFlagNotes;
                job.GeneralManagerId = jobDetailsUpdated.GeneralManagerId;
                job.OfficeManagerId = jobDetailsUpdated.OfficeManagerId;
                job.ReconSupportId = jobDetailsUpdated.ReconSupportId;
                job.IsThereStandingWater = jobDetailsUpdated.IsStandingWater;
                job.DropDownQuestionResponses = new Dictionary<Guid, Guid>
                {
                    [DropdownQuestions.Covid19] = jobDetailsUpdated.CoronaResponseId
                };
                if (job.PriorityResponderId != jobDetailsUpdated.PriorityResponderId)
                {
                    var mentorInfo = await _franchiseServiceClient.GetMentorDashboardInfoDtoAsync(job.FranchiseSetId);
                    job.PriorityResponderFullName = mentorInfo.Users
                        .FirstOrDefault(x => x.UserId == jobDetailsUpdated.PriorityResponderId)?.FullName;
                }
                job.PriorityResponderId = jobDetailsUpdated.PriorityResponderId;
                job.ReferredById = jobDetailsUpdated.ReferredById;
                job.ReportedById = jobDetailsUpdated.ReportedById;
                job.ModifiedDate = DateTime.UtcNow;

                // Update triState answers
                UpdateTriStateAnswer(job, JobTriStateQuestions.IsWaterAvailable, jobDetailsUpdated.IsWaterAvailable);
                UpdateTriStateAnswer(job, JobTriStateQuestions.IsElectricityAvailable, jobDetailsUpdated.IsElectricAvailable);
                UpdateTriStateAnswer(job, JobTriStateQuestions.IsStandingWater, jobDetailsUpdated.IsStandingWater);
            }

            private void UpdateTriStateAnswer(Job job, Guid QuestionId, bool? answer)
            {
                var triStateAnswer = job.JobTriStateAnswers
                    .FirstOrDefault(x => x.JobTriStateQuestionId == QuestionId);

                if (!answer.HasValue && triStateAnswer != null)
                {
                    job.JobTriStateAnswers.Remove(triStateAnswer);
                }
                else
                {
                    if (triStateAnswer is null)
                    {
                        job.JobTriStateAnswers.Add(new Models.Drybook.JobTriStateAnswer
                        {
                            Answer = answer,
                            CreatedBy = "RM",
                            CreatedDate = DateTime.UtcNow,
                            JobId = job.Id,
                            JobTriStateQuestionId = QuestionId
                        });
                    }
                    else 
                    {
                        triStateAnswer.Answer = answer.Value;
                    }
                }
            }

            private async Task MapJobQBOJobDetails(Job job,
                                                   GetLookups.Dto lookups,
                                                   JobDetailsUpdatedDto jobDetailsUpdated,
                                                   Location lossAddressGeoLocation)
            {          
                job.TotalAmountDue = jobDetailsUpdated.TotalAmountDue;
                job.TotalRevenue = jobDetailsUpdated.TotalRevenue;

                var call = await _db.ExternalMarketingCalls.FirstOrDefaultAsync(x => x.AssociatedJobId == job.Id);
                if (call != null && call.RevenueInvoiced != job.TotalRevenue)
                {
                    if (call.RevenueInvoiced != null)
                        call.InvoiceAmountChanged = true;
                    call.RevenueInvoiced = job.TotalRevenue;
                    call.InvoicedAmountLastUpdated = DateTime.UtcNow;
                }
            }

            private Address MapAddress(JobDetailsUpdatedEvent.AddressDto addr) =>
                new Address(addr.Address1,
                    addr.City,
                    addr.PostalCode,
                    MapState(addr.State),
                    (AddressType)addr.AddressType)
                {
                    Address2 = addr.Address2
                };

            private State MapState(StateDto state) =>
                new State(state.StateId,
                    state.StateName,
                    state.StateAbbreviation,
                    state.CountryId,
                    state.CountryName,
                    state.CountyName);

            private async Task RaiseInsuranceCarrierChangedEvent(UserInfo userInfo, Guid jobId, Guid insuranceClientId, Guid correlationId, int? corporateJobNumber, int franchiseNumber)
            {
                var jobDto = new InsuranceCarrierChangedEvent.JobDto
                {
                    JobId = jobId,
                    CorporateJobNumber = corporateJobNumber,
                    InsuranceCarrierId = insuranceClientId,
                    FranchiseNumber = franchiseNumber,
                    CreatedBy = userInfo?.Username ?? "JobService",
                    CreatedById = userInfo?.Id ?? Guid.Empty
                };
                var insuranceCarrierChangedEvent = new InsuranceCarrierChangedEvent(jobDto, correlationId);
                _logger.LogDebug("{event} InsuranceCarrier changed, publish InsuranceCarrierChangedEvent", nameof(JobDetailsUpdated));

                await GenerateOutboxMessage(insuranceCarrierChangedEvent.ToJson(),
                    nameof(InsuranceCarrierChangedEvent), correlationId, jobDto.CreatedBy);
            }

            private async Task RaiseJobFranchiseChangedEvent(UserInfo userInfo, Guid jobId, Guid franchiseId,
                Guid correlationId)
            {
                var jobFranchiseChangedEvent = new JobFranchiseChangedEvent(correlationId, jobId, franchiseId,
                    userInfo?.Username ?? "JobService", DateTime.UtcNow);
                await GenerateOutboxMessage(jobFranchiseChangedEvent.ToJson(), nameof(JobFranchiseChangedEvent),
                    correlationId, userInfo?.Username ?? "JobService");
            }

            private async Task RaiseInsuranceClaimNumberChangedEvent(Guid jobId, string claimNumber, string updatedBy, DateTime updatedUtc, Guid correlationId)
            {
                var eventDto = new ClaimNumberUpdatedEvent.ClaimNumberUpdatedDto(jobId, claimNumber, updatedBy ?? "JobService", updatedUtc);
                var claimNumberEvent = new ClaimNumberUpdatedEvent(eventDto, correlationId);
                await GenerateOutboxMessage(claimNumberEvent.ToJson(),
                    nameof(ClaimNumberUpdatedEvent), correlationId, updatedBy ?? "JobService");
            }

            private Guid GetCorrelationId()
            {
                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out var correlationId))
                    correlationId = Guid.NewGuid();
                return correlationId;
            }

            private async Task GenerateOutboxMessage(string message, string messageType, Guid correlationId, string createdByUsername)
            {
                var newEvent = new OutboxMessage(message, messageType,
                    correlationId, createdByUsername);
                await _db.OutboxMessages.AddAsync(newEvent);
            }

            private async Task<Location> GetLossAddressGeoLocation(Event request, CancellationToken cancellationToken)
            {
                var address = request.JobDetailsUpdated.LossAddress;

                if (string.IsNullOrEmpty(address?.Address1)) return null;

                var state = MapState(address.State);

                var formattedAddress = string.Format("{0},{1}{2}, {3} {4}",
                    address?.Address1 ?? string.Empty,
                    string.IsNullOrEmpty(address?.Address2) ? string.Empty : string.Format(" {0},", address?.Address2),
                    address?.City ?? string.Empty,
                    state?.StateAbbreviation ?? string.Empty,
                    address?.PostalCode ?? string.Empty).Trim();

                var geocodeResponse = await _googleServiceClient.GetGeocode(formattedAddress, cancellationToken);

                return geocodeResponse?.Results?.FirstOrDefault()?.Geometry?.Location;
            }

            private Address MapLossAddress(Event.AddressDto address, GetLookups.Dto lookups, Location lossAddressGeoLocation)
            {
                if (string.IsNullOrEmpty(address?.Address1)) return null;

                return new Address(
                   address.Address1,
                   address.City,
                   IntakeUtils.ClearZipCodeMask(address.PostalCode),
                   MapState(address.State),
                   AddressType.Physical)
                {
                    Address2 = address.Address2,
                    Latitude = (decimal)(lossAddressGeoLocation?.Latitude ?? 0.0),
                    Logitude = (decimal)(lossAddressGeoLocation?.Longitude ?? 0.0)
                };
            }
        }
    }
}
