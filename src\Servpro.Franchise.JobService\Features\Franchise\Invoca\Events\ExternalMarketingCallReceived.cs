﻿using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.EquipmentService;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Franchises.Invoca;
using Servpro.FranchiseSystems.Framework.Setup.Caching;
using System;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Invoca.Events
{
    public class ExternalMarketingCallReceived
    {
        public class Event : ExternalMarketingCallReceivedEvent, IRequest
        {
            public Event(ExternalMarketingCallDto externalCallDto, Guid correlationId) : base(externalCallDto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<Handler> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IDistributedCache _cache;
            private readonly JobDataContext _context;
            private readonly string _newCallReceivedCacheSecondsKey = "Invoca:NewCallReceivedCachedSeconds";
            private readonly int _newCallReceivedCacheSeconds = 3;
            

            public Handler(JobDataContext context,
                ILogger<Handler> logger,
                IDistributedCache cache,
                IConfiguration config,
                IFranchiseServiceClient franchiseServiceClient)
            {
                _context = context;
                _logger = logger;
                _franchiseServiceClient = franchiseServiceClient;
                _cache = cache;
                _newCallReceivedCacheSeconds = config.GetValue(_newCallReceivedCacheSecondsKey, 3);

            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Handler began with {@incomingEvent}", request);

                var dto = request.ExternalCallDto;
                var invocaCall = await _context.ExternalMarketingCalls
                       .FirstOrDefaultAsync(eq => eq.CallRecordingId == dto.CallRecordingId, cancellationToken);
                if (invocaCall != null)
                {
                    // prevent updates that have an older CallReceivedDateTime
                    //  this is because events in a bulk could be processed in any order via sqs queues
                    if (dto.CallReceivedDateTime > invocaCall.CallReceivedDateTime)
                    {
                        _logger.LogDebug($"Invoca Call found: {dto.CallRecordingId}, updating it");
                        MapUpdateCall(dto, invocaCall);
                        _context.ExternalMarketingCalls.Update(invocaCall);
                        await _context.SaveChangesAsync(cancellationToken);
                    }
                }
                else {
                    // This should prevent call event duplicates as much as possible.
                    // This is because several duplicate events can come in near the same time and this aggregates them by
                    // acting as a lock based on CallRecordingId
                    await PerformJitterWait(cancellationToken);
                    var cachedInvocaCall = await _cache.GetOrCreateAsync($"invoca-job-call-{dto.CallRecordingId}", async x =>
                    {
                        //add some random jitter
                        x.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(_newCallReceivedCacheSeconds);
                        _logger.LogDebug($"Invoca Call not found: {dto.CallRecordingId}, adding it");
                        var call = Map(dto);
                        await _context.ExternalMarketingCalls.AddAsync(call, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);
                        return call;
                    }, cancellationToken: cancellationToken);
                    if(dto.CallReceivedDateTime > cachedInvocaCall?.CallReceivedDateTime)
                    {
                        //We know this is an update scenario that is just too quick to have made it into the database
                        // we should try again at a later time, using the events retry policy
                        throw new RetryEventException("Event has been determined to be an update that was too quick for processsing immediately. It will be reprocessed.");
                    }
                }

                _logger.LogDebug("Handler completed successfully");

                return Unit.Value;
            }

            private async System.Threading.Tasks.Task PerformJitterWait(CancellationToken cancellationToken)
            {
                var randomNumGen = new Random();
                var jitter = randomNumGen.Next(100, 200);
                await System.Threading.Tasks.Task.Delay(jitter, cancellationToken);
            }

            public ExternalMarketingCall Map(ExternalMarketingCallReceivedEvent.ExternalMarketingCallDto dto)
            {
                return new ExternalMarketingCall
                {
                    Id = Guid.NewGuid(),
                    CreatedBy = nameof(ExternalMarketingCallReceivedEvent),
                    CreatedDate = DateTime.UtcNow,
                    CallRecordingId = dto.CallRecordingId,
                    CallSourceId = dto.CallSourceId,
                    CallStartTime = dto.CallStartTime,
                    FranchiseSetId = dto.FranchiseSetId,
                    FranchiseId = dto.FranchiseId,
                    CallDuration = dto.CallDuration,
                    CallerPhoneNumber = dto.CallerPhoneNumber,
                    CallReceivedDateTime = dto.CallReceivedDateTime,
                    RecordingLink = dto.RecordingLink
                };
            }

            private void MapUpdateCall(ExternalMarketingCallReceivedEvent.ExternalMarketingCallDto updatedCall, ExternalMarketingCall call)
            {
                call.CallSourceId = AssignGuid(call.CallSourceId, updatedCall.CallSourceId);
                call.CallStartTime = updatedCall.CallStartTime;
                call.FranchiseId = AssignGuid(call.FranchiseId, updatedCall.FranchiseId);
                call.FranchiseSetId = AssignGuid(call.FranchiseSetId, updatedCall.FranchiseSetId);
                call.CallDuration = updatedCall.CallDuration;
                call.CallReceivedDateTime = updatedCall.CallReceivedDateTime;
                call.CallerPhoneNumber = updatedCall.CallerPhoneNumber ?? call.CallerPhoneNumber;
                call.RecordingLink = updatedCall.RecordingLink ?? call.RecordingLink;
                call.ModifiedDate = DateTime.UtcNow;
                call.ModifiedBy = nameof(ExternalMarketingCallReceivedEvent);
            }

            private Guid? AssignGuid(Guid? currentValue, Guid? requestValue)
            {
                if (requestValue == null)
                    return currentValue;
                return requestValue;
            }
            private Guid AssignGuid(Guid currentValue, Guid requestValue)
            {
                if (requestValue == Guid.Empty)
                    return currentValue;
                return requestValue;
            }

        }
    }
}
