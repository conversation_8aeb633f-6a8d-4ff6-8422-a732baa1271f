﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Sketches
{
    [Route("api/sketches")]
    public class SketchesController : ControllerBase
    {
        private readonly IMediator _mediator;

        public SketchesController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet("/api/jobs/{jobId}/drybook/queries/get-sketches", Name = "GetSketches")]
        public async Task<ActionResult<ICollection<GetSketches.Dto>>> GetDocumentsAsync(Guid jobId)
        {

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetSketches.Query(jobId, User.FranchiseSetId().Value));
            return Ok(response);
        }

        [HttpPost("/api/{jobId}/commands/save-drybook-sketch", Name = "AddSketchAsync")]
        public async Task<ActionResult<PostSketch.ResponseDto>> AddSketchAsync(Guid jobId, [FromBody] PostSketch.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.MediaMetadata.JobId = jobId;
            command.MediaMetadata.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/{jobId}/commands/edit-drybook-sketch", Name = "EditSketchAsync")]
        public async Task<ActionResult<EditSketch.ResponseDto>> EditSketchAsync(Guid jobId, [FromBody] EditSketch.Command command)
        {
            if (jobId != command.MediaMetadata.JobId)
                return BadRequest();

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            try
            {
                command.MediaMetadata.FranchiseSetId = User.FranchiseSetId().Value;
                var response = await _mediator.Send(command);
                return Ok(response);
            }
            catch (ResourceNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpPost("/api/jobs/{jobId}/drybook/download-sketch", Name = "DownloadSketch")]
        public async Task<ActionResult<DownloadSketch.Dto>> DownloadDocumentsAsync(Guid jobId, [FromBody] DownloadSketch.Query query)
        {
            if (jobId == Guid.Empty)
                return BadRequest();

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            query.JobId = jobId;
            query.FranchiseSetId = User.FranchiseSetId().Value;
            var response = await _mediator.Send(query);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/drybook/delete-sketch", Name = "DeleteSketch")]
        public async Task<ActionResult<bool>> DeleteSketchAsync(Guid jobId, [FromBody] DeleteSketch.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }
    }
}
