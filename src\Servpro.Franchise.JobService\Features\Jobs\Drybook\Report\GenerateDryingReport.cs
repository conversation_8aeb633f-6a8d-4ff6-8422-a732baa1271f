﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.Constants;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Jobs.Drybook.Report.Helpers;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.DryingReport.Models;

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ResourceNotFoundException =
    Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions.ResourceNotFoundException;

namespace Servpro.Franchise.JobService.Features.Jobs.Drybook.Report
{
    public class GenerateDryingReport
    {
        public class Dto : DryingReportGeneratedDto
        {
            public DateTime FranchiseLocalTime { get; set; }

            public PdfBookMarks BookMarks { get; set; } = new PdfBookMarks(
              new List<PdfBookMark>
                {
                    new PdfBookMark("jobSummary", new List<PdfBookMark>(){
                        new PdfBookMark("customerAndJobInfo"),
                        new PdfBookMark("timestamps"),
                        new PdfBookMark("lossInfo"),
                        new PdfBookMark("franchiseInfo"),
                        new PdfBookMark("additionalLossInfo"),
                    }),
                    new PdfBookMark("equipment", new List<PdfBookMark>(){
                        new PdfBookMark("placementCountByDateType"),
                        new PdfBookMark("usageSummary"),
                        new PdfBookMark("usageChart"),
                    }),
                    new PdfBookMark("dailyNarrative"),
                    new PdfBookMark("diaryNotes"),
                    new PdfBookMark("zoneComposition", new List<PdfBookMark>()
                    {
                        new PdfBookMark("zoneCompName")
                    }),
                    new PdfBookMark("drawings"),
                    new PdfBookMark("zoneEquipmentUsage", new List<PdfBookMark>(){

                        new PdfBookMark("zoneEquipUsageName")
                    }),
                    new PdfBookMark("monitoring", new List<PdfBookMark>(){
                        new PdfBookMark("atmosphericDehumidifierReadings", new List<PdfBookMark>(){
                        new PdfBookMark("atmosphericDehumZoneName")
                        }),
                        new PdfBookMark("moistureContentReadings", new List<PdfBookMark>(){
                         new PdfBookMark("moistureContentZoneName")
                        })
                    })
                }
            );
        }

        public class Command : IRequest<Dto>
        {
            // empty ctor for deserialization purposes
            // otherwise this will fail to deserialize
            public Command() { }
            public Command(Guid jobId, Guid franchiseSetId)
            {
                JobId = jobId;
                FranchiseSetId = franchiseSetId;
            }

            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }
        }

        public class QueryValidator : AbstractValidator<Command>
        {
            public QueryValidator()
            {
                RuleFor(x => x.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly IMediator _mediator;
            private readonly ILogger<GenerateDryingReport> _logger;
            private readonly JobReadOnlyDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IFranchiseServiceClient _franchiseServiceClient;

            public Handler(IMediator mediator, ILogger<GenerateDryingReport> logger,
                ILookupServiceClient lookupServiceClient,
                IFranchiseServiceClient franchiseServiceClient,
                JobReadOnlyDataContext context)
            {
                _mediator = mediator;
                _logger = logger;
                _context = context;
                _lookupServiceClient = lookupServiceClient;
                _franchiseServiceClient = franchiseServiceClient;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}{franchiseSetId}", request.JobId, request.FranchiseSetId);
                var jobId = request.JobId;
                _logger.LogInformation("DryingReportLog - Generating drying report for jobId: {jobId}", jobId);
                var job = await _context.Jobs
                    .Include(job => job.JobContacts)
                        .ThenInclude(contacts => contacts.Contact)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == jobId && x.FranchiseSetId == request.FranchiseSetId, cancellationToken);

                if (job is null)
                    throw new ResourceNotFoundException($"Job not found (Id: {jobId}");

                var franchiseTimeZone = DryingReportHelper.GetFranchiseSetPrimaryTimeZone(job.FranchiseSetId, _franchiseServiceClient, _lookupServiceClient, _logger).Result;
                var currentFranchiseDateTime = DateTime.UtcNow.GetLocalFranchiseDateTime(franchiseTimeZone);

                var dryingModel = new Dto
                {
                    SummaryModel = await _mediator.Send(new GetSummary.Query(job, franchiseTimeZone), cancellationToken),
                    JournalNotesModel = await _mediator.Send(new GetJournalNotes.Query(job, franchiseTimeZone), cancellationToken),
                    DailyEquipmentCountModel = await _mediator.Send(new GetDailyEquipmentCount.Query(job, franchiseTimeZone), cancellationToken),
                    ZoneCompositionModel = await _mediator.Send(new GetZoneComposition.Query(job, franchiseTimeZone), cancellationToken),
                    DrawingsModel = null,
                    MonitoringModel = new MonitoringModelDto
                    {
                        AtmosphericReadingsModel = await _mediator.Send(new GetAtmosphericReadings.Query(job, franchiseTimeZone), cancellationToken),
                        MoistureContentReadingsModel = await _mediator.Send(new GetMoistureContentReadings.Query(job, franchiseTimeZone), cancellationToken),
                    },
                    EquipmentUsageModel = await _mediator.Send(new GetEquipmentUsage.Query(job, franchiseTimeZone), cancellationToken),
                    IsWaterJob = WaterLossTypes.List.Contains(job.LossTypeId),
                    FranchiseLocalTime = currentFranchiseDateTime
                };

                _logger.LogInformation("DryingReportLog - job and dryingModel for report {jobId}, DryingReportModel: {@dryingModel}", jobId, dryingModel);
                _logger.LogDebug("DryingReportModel", dryingModel);

                if (dryingModel.IsWaterJob)
                {
                    _logger.LogInformation("DryingReportLog - job is water job - getting daily Narrative for jobId: {jobId}", jobId);
                    dryingModel.DailyNarrativeModel = await _mediator.Send(new GetDailyNarrative.Query(jobId, franchiseTimeZone), cancellationToken);
                }

                _logger.LogInformation("DryingReportLog - GenerateDryingReport - completed for jobId: {Id}, drying report: {@dryingModel}", job.Id, dryingModel);
                dryingModel.DrawingsModel = await _mediator.Send(new GetSketches.Query(jobId), cancellationToken);
                _logger.LogInformation("DryingReportLog - completed getting model for jobId: {Id}", job.Id);
                return dryingModel;
            }
        }
    }
}