﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure.GoogleService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Features.LookUps;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Client;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.GeoLocationUpdatedEvent;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class GeoLocationUpdated
    {
        #region Event
        public class Event : GeoLocationUpdatedEvent, IRequest
        {
            public Event(GeoLocationUpdatedDto geoLocation, Guid correlationId) : base(geoLocation, correlationId)
            {
            }
        }
        #endregion Event

        #region Handler
        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _db;
            private readonly ILogger<Handler> _logger;
            private readonly IGoogleServiceClient _googleServiceClient;

            public Handler(JobDataContext db,
                ILogger<Handler> logger,
                IGoogleServiceClient googleServiceClient)
            {
                _db = db;
                _logger = logger;
                _googleServiceClient = googleServiceClient;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} receieved", nameof(GeoLocationUpdatedEvent));

                var jobId = request.GeoLocationUpdated?.JobId;

                var job = await _db.Jobs.FirstOrDefaultAsync(x => x.Id == jobId, cancellationToken);

                if (job != null) 
                {
                    var lossAddress = job.LossAddress;

                    if (lossAddress != null)
                    {
                        var formattedAddress = _googleServiceClient.GetLossAddressGeoLocation(lossAddress);
                        _logger.LogInformation("Job loss geolocation: {@lossAddress}", lossAddress);
                        var geocodeResponse = await _googleServiceClient.GetGeocode(formattedAddress, cancellationToken);

                        var latitude = (decimal) (geocodeResponse?.Results?.FirstOrDefault()?.Geometry?.Location?.Latitude ?? 0.0);
                        var logitude = (decimal) (geocodeResponse?.Results?.FirstOrDefault()?.Geometry?.Location?.Longitude ?? 0.0);
                       
                        job.LossAddress.Latitude = latitude;
                        job.LossAddress.Logitude = logitude;
                        _logger.LogInformation("{event}: Updating JobId {@JobId}, with Latitude: {@Latitude} and logitude: {@Logitude}", nameof(GeoLocationUpdatedEvent), jobId, latitude, logitude);
                    }
                }

                await _db.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{event} saved: JobId {id}", nameof(GeoLocationUpdatedEvent), jobId);

                return Unit.Value;
            }


        }
        #endregion Handler
    }
}
