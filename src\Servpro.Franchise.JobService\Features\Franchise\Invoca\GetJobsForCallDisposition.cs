﻿using FluentValidation;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.FranchiseService;
using Servpro.Franchise.JobService.Infrastructure.MicaService;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.LookupService.Constants;

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Servpro.Franchise.JobService.Features.Franchise.Invoca
{
    public class GetJobsForCallDisposition
    {
        public class Query : IRequest<List<Dto>>
        {
            public Guid FranchiseId { get; set; }
            public string CustomerName { get; set; }
            public string PhoneNumber { get; set; }
            public int Take { get; set; }
            public int Skip { get; set; }
        }

        public class Dto
        {
            public Guid JobId { get; set; }
            public string ProjectNumber { get; set; }
            public string CustomerName { get; set; }
            public DateTime CreatedDate { get; set; }
            public DateTime LossDate { get; set; }
            public string  CorporateNumber { get; set; }
            public string JobProgress { get; set; }
            public Guid FranchiseId { get; set; }
            public Guid FranchiseSetId { get; set; }
            public Guid? CallDisposition { get; set; }
            public long FranchiseNumber { get; set; }

        }


        public class Handler : IRequestHandler<Query, List<Dto>>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<GetJobsForCallDisposition> _logger;
            private readonly IFranchiseServiceClient _franchiseServiceClient;
            private readonly IConfiguration _config;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private const string InvocaSearchDays = "Invoca:SearchDays";
           

            public Handler(IConfiguration config,                
                ILogger<GetJobsForCallDisposition> logger,
                IFranchiseServiceClient franchiseServiceClient,
                JobReadOnlyDataContext context,
                IUserInfoAccessor userInfoAccessor)
            {
                _config = config;
                _logger = logger;
                _context = context;
                _franchiseServiceClient = franchiseServiceClient;
                _userInfoAccessor = userInfoAccessor;
            }

            public async Task<List<Dto>> Handle(Query request, CancellationToken cancellationToken)
            {
                _logger.LogDebug("Getting Jobs for Invoca Call Disposition");
                var userInfo = _userInfoAccessor.GetUserInfo();
                var jobs = await GetJobs(request, userInfo.FranchiseSetId, cancellationToken);
                
                _logger.LogDebug("Found Jobs for Invoca Call Disposition: {count}",jobs.Count);

                jobs = jobs
                      .Skip(request.Skip)
                      .Take(request.Take)
                      .ToList();
                var foundFranchises = await GetFranchises(jobs, cancellationToken);
                return jobs.Select(x=>Map(x, foundFranchises)).ToList();

            }
            private Dto Map(WipRecord job, List<FranchiseDto> foundFranchises)
            {
                var franchise = foundFranchises.FirstOrDefault(k => k.Id == job.FranchiseId) ?? new FranchiseDto { FranchiseNumber = 0 };
                return new Dto
                {
                    JobId = job.Id,
                    ProjectNumber = job.ProjectNumber,
                    CustomerName = job.CustomerFullName,
                    CreatedDate = job.CreatedDate,
                    LossDate = job.DateOfLoss,
                    CorporateNumber = job.CorporateJobNumber.ToString(),
                    JobProgress =job.Progress,
                    FranchiseId = job.FranchiseId,
                    FranchiseSetId = job.FranchiseSetId,
                    CallDisposition = job.CallDisposition,
                    FranchiseNumber = franchise.FranchiseNumber
                };
            }

            private async Task<List<WipRecord>> GetJobs(Query request,Guid? FranchiseSetId, CancellationToken cancellationToken )
            {
                List<WipRecord> jobs = new List<WipRecord>();
                bool noName = false;
                bool noPhone = false;
                if (String.IsNullOrEmpty(request.CustomerName))
                    noName = true;

                if (String.IsNullOrEmpty(request.PhoneNumber))
                    noPhone = true;

                var jobsQuery = _context.WipRecords
                             .Where(x => x.FranchiseSetId == FranchiseSetId);

                if (!noName)
                    jobsQuery = jobsQuery.Where(x => x.CustomerFullName.Contains(request.CustomerName));
                if (!noPhone)
                    jobsQuery = jobsQuery.Where(k => k.CallerPhoneNumbers.Contains(request.PhoneNumber));

                jobs = await jobsQuery
                               .OrderByDescending(x => x.CreatedDate)
                               .ToListAsync(cancellationToken);

                return jobs;

            }
            private async Task<List<FranchiseDto>> GetFranchises(List<WipRecord> calls, CancellationToken cancellationToken)
            {
                List<Guid> foundFranchiseIds = calls.Select(k => k.FranchiseId).Distinct().ToList();
                var franchiseTasks = foundFranchiseIds.Select(id => _franchiseServiceClient.GetFranchiseAsync(id, cancellationToken));

                try
                {
                    var franchises = await Task.WhenAll(franchiseTasks);
                    return franchises.Where(franchise => franchise != null).ToList();
                }
                catch (Exception ex)
                {
                    foreach (var item in foundFranchiseIds)
                    {
                        _logger.LogWarning("Error getting the franchise: {item}, {ex}", item, ex);
                    }
                    return new List<FranchiseDto>();
                }
            }

        }
    }
}
