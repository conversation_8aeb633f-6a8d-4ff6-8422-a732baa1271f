﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Features.Franchise.Resale.Models;
using Servpro.Franchise.JobService.Infrastructure.Utilities.ResaleSplitCombo;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Common.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Franchise.Resale.Copy
{
    public class ResaleCopyWipRecord
    {
        public class Command : IRequest<ProcessEntityResult>
        {
            public ProcessEntityResult ContactResult { get; set; }
            public Dictionary<string, object> MappingOptions { get; set; }
            public HashSet<Guid> JobIds { get; set; }
            public Guid ResaleId { get; set; }
        }

        public class Handler :
            ResaleCopyBase<ResaleCopyWipRecord>,
            IRequestHandler<Command, ProcessEntityResult>
        {
            private readonly ILogger<ResaleCopyWipRecord> _logger;
            private readonly IMapper _mapper;
            private readonly IServiceProvider _services;
            private readonly IResaleSplitComboUtility _resaleSplitComboUtility;
            private static readonly string _prependProjectNumberDefault = "R";
            private JobDataContext _context;

            public Handler(
                ILogger<ResaleCopyWipRecord> logger,
                IMapper mapper,
                IServiceProvider services,
                IResaleSplitComboUtility resaleSplitComboUtility) : base(logger, mapper)
            {
                _logger = logger;
                _mapper = mapper;
                _services = services;
                _resaleSplitComboUtility = resaleSplitComboUtility;
            }

            public async Task<ProcessEntityResult> Handle(Command request, CancellationToken cancellationToken)
            {
                using var serviceScope = _services.CreateScope();
                _context = serviceScope.ServiceProvider.GetRequiredService<JobDataContext>();

                _logger.LogInformation("Processing {entity}", nameof(WipRecord));
                var sourceEntities = await _resaleSplitComboUtility.GetSouceEntitiesAsync(request.JobIds.ToList(),
                    GetSourceEntitiesAsync, 
                    cancellationToken);
                var wipTargetIds = sourceEntities
                    .Select(x => GuidTransformHelpers.TransformToManipulatedGuid(x.Id, request.ResaleId))
                    .ToList();
                var alreadyCopiedWipIds = await _resaleSplitComboUtility.GetAlreadyCopiedEntityIdsAsync(wipTargetIds, 
                    GetWipRecordIdsAsync, 
                    cancellationToken);
                return await ProcessEntitiesAsync<WipRecord, ResaleWipRecord>(
                    request.ResaleId,
                    wipRecord =>
                    {
                        var failedDependencies = new List<(string Name, Guid Id)>();

                        if (request.ContactResult.FailedEntities.Contains(wipRecord.CallerId))
                            failedDependencies.Add((nameof(Contact), wipRecord.CallerId));
                        if (request.ContactResult.FailedEntities.Contains(wipRecord.CustomerId))
                            failedDependencies.Add((nameof(Contact), wipRecord.CustomerId));
                        if (wipRecord.ReferredByName.HasValue && request.ContactResult.FailedEntities.Contains(wipRecord.ReferredByName.Value))
                            failedDependencies.Add((nameof(Contact), wipRecord.ReferredByName.Value));

                        return failedDependencies;
                    },
                    alreadyCopiedWipIds.ToHashSet(),
                    sourceEntities,
                    request.MappingOptions,
                    sourceEntity =>
                    {
                        // Prepend "R" to the begining of the selling franchise's project number
                        sourceEntity.ProjectNumber = _prependProjectNumberDefault + sourceEntity.ProjectNumber;
                    },
                    null,
                    _context,
                    async (targetEntity, context, cancellationToken) =>
                    {
                        await context.WipRecords.AddAsync(targetEntity, cancellationToken);
                    },
                    cancellationToken);
            }

            private async Task<List<WipRecord>> GetSourceEntitiesAsync(List<Guid> jobIds, CancellationToken cancellationToken)
            {
                _logger.LogTrace("Getting source franchise records from the database.");

                var wipRecords = await _context.WipRecords
                    .Where(wr => jobIds.Contains(wr.Id))
                    .ToListAsync(cancellationToken);

                _logger.LogTrace("Found {entityCount} records from the database.", wipRecords.Count);
                return wipRecords;
            }

            private async Task<List<Guid>> GetWipRecordIdsAsync(List<Guid?> wipTargetIds, CancellationToken cancellationToken)
            {
                return await _context.WipRecords
                    .Where(x => wipTargetIds.Contains(x.Id))
                    .Select(x => x.Id)
                    .ToListAsync(cancellationToken);
            }
        }
    }
}
