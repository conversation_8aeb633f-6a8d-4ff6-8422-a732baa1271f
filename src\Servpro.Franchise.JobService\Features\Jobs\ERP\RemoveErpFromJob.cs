﻿using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Features.Jobs.Erp.ErpService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Erp
{
    public class RemoveErpFromJob
    {
        public class Command : IRequest
        {
            public Command(Guid jobId)
            {
                JobId = jobId;
            }

            public Guid JobId { get; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command>
        {
            private readonly ILogger<Handler> _logger;
            private readonly IErpService _erpService;

            public Handler(ILogger<Handler> logger,
                           IErpService erpService)
            {
                _logger = logger;
                _erpService = erpService;
            }

            public async Task<Unit> Handle(Command request, CancellationToken cancellationToken)
            {
                await _erpService.RemoveErpFromJobAsync(request.JobId, cancellationToken);
                return Unit.Value;
            }
        }

    }
}
