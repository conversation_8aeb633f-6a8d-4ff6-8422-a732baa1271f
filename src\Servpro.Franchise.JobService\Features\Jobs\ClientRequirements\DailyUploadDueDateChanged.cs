﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Upload;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.ClientRequirements
{
    public class DailyUploadDueDateChanged
    {
        public class Event : DailyUploadDueDateChangedEvent, IRequest
        {
            public Event(DailyDueDateDto dailyDueDate, Guid correlationId)
                : base(dailyDueDate, correlationId) { }

            public class Handler : IRequestHandler<Event>
            {
                private readonly JobDataContext _db;
                private readonly ILogger<DailyUploadDueDateChanged> _logger;

                public Handler(JobDataContext jobDataContext, ILogger<DailyUploadDueDateChanged> logger)
                {
                    _db = jobDataContext;
                    _logger = logger;
                }

                public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
                {
                    using var scope = _logger.BeginScope(
                        "{jobId}{corporateJobNumber}",
                        request.DailyDueDate?.JobId,
                        request.DailyDueDate?.CorporateJobNumber);

                    _logger.LogInformation("Begin handler with: {@request}", request);

                    if (request.DailyDueDate == null)
                    {
                        _logger.LogWarning("DailyDueDateDto was null for {request}", request);
                        return Unit.Value;
                    }

                    var job = await _db.Jobs
                        .FirstOrDefaultAsync(j => j.Id == request.DailyDueDate.JobId, cancellationToken);

                    if (job is null)
                    {
                        _logger.LogWarning("Job not found Id: {id}", request.DailyDueDate.JobId);
                        throw new ResourceNotFoundException($"Job not found: {request.DailyDueDate.JobId}");
                    }

                    if (job.DailyUploadDueDateModifiedDate > request.DailyDueDate.CreatedUtc)
                    {
                        _logger.LogInformation("Current data is more recent, disregarding update");
                        return Unit.Value;
                    }

                    _logger.LogInformation("Updating daily upload due date on job and wip.");

                    job.DailyUploadDueDate = request.DailyDueDate.DailyUploadDueUtc;
                    job.DailyUploadDueDateModifiedDate = request.DailyDueDate.CreatedUtc;

                    await _db.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Processing complete. Exiting.");
                    return Unit.Value;
                }
            }
        }
    }
}