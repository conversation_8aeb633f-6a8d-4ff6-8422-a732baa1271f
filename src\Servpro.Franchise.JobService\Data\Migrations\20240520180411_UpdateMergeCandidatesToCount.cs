﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class UpdateMergeCandidatesToCount : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("6ff1ee26-88e8-4895-97c2-ddadc8a9b6ce"));

            migrationBuilder.AddColumn<int>(
                name: "MergeCandidatesCount",
                table: "WipRecord",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AddColumn<Guid>(
                name: "InsuranceCarrierId",
                table: "MergeCandidates",
                type: "char(36)",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                collation: "latin1_swedish_ci");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("6ddd74c1-4974-4d25-b91e-4ed7b885c90a"), null, new DateTime(2024, 5, 20, 18, 4, 10, 269, DateTimeKind.Utc).AddTicks(42), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });

            var populateCount = @"
            UPDATE WipRecord
            SET MergeCandidatesCount = LENGTH(MergeCandidates)/40
            WHERE LENGTH(MergeCandidates) > 2;";

            migrationBuilder.Sql(populateCount);

            if (MigrationHelper.IsPipeline())
                migrationBuilder.Sql(MigrationHelper.ChangeDelimiterSql);

            //FindMergeCandidates and FindAllMergeCandidates sprocs
            var findMergeCandidates = @"
            DROP PROCEDURE IF EXISTS findMergeCandidates;
            CREATE PROCEDURE findMergeCandidates (IN jobId char(36))
            BEGIN
                DECLARE residentialPropertyType CHAR(36) DEFAULT '00000001-0011-5365-7276-70726f496e63';
                DECLARE onHold INT(11) DEFAULT 17;
                DECLARE turnedDown INT(11) DEFAULT 19;
                DECLARE notSoldCancelled INT(11) DEFAULT 16;
    
                SET @address1 = (SELECT JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) FROM Job WHERE Id = CONVERT(jobId USING latin1));
                SET @franchiseSetId = (SELECT FranchiseSetId FROM Job WHERE Id = CONVERT(jobId USING latin1));
                SET @franchiseId = (SELECT FranchiseId FROM Job WHERE Id = CONVERT(jobId USING latin1));
    
                SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, FranchiseId, MergeTarget, InsuranceCarrierId, Address1 
                FROM
                (
                    SELECT Id, RecordSourceId, LossTypeId, JobProgress, FranchiseSetId, FranchiseId, MergeTarget, MergeSource, InsuranceCarrierId, JSON_UNQUOTE(JSON_EXTRACT(LossAddress, '$.Address1')) AS Address1
                    FROM Job
                    WHERE JSON_EXTRACT(LossAddress, '$.Address1') = CONVERT(@address1 USING latin1)
                        AND Id <> CONVERT(jobId USING latin1)
                        AND FranchiseSetId = CONVERT(@franchiseSetId USING latin1)
                        AND FranchiseId = CONVERT(@franchiseId USING latin1)
                        AND PropertyTypeId = residentialPropertyType
                        AND JobProgress NOT IN (onHold, turnedDown, notSoldCancelled)
                ) as MergeTable
                WHERE (MergeTarget IS NULL OR MergeTarget = '') 
					AND (MergeSource IS NULL OR MergeSource = '');
            END;";
            migrationBuilder.Sql(findMergeCandidates);

            if (MigrationHelper.IsPipeline())
            {
                migrationBuilder.Sql(MigrationHelper.DelimiterSql);
                migrationBuilder.Sql(MigrationHelper.ResetDelimiterSql);
            }
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("6ddd74c1-4974-4d25-b91e-4ed7b885c90a"));

            migrationBuilder.DropColumn(
                name: "MergeCandidatesCount",
                table: "WipRecord");

            migrationBuilder.DropColumn(
                name: "InsuranceCarrierId",
                table: "MergeCandidates");

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("6ff1ee26-88e8-4895-97c2-ddadc8a9b6ce"), null, new DateTime(2024, 2, 29, 16, 50, 38, 983, DateTimeKind.Utc).AddTicks(5054), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "*******" });
        }
    }
}
