﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Contacts;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class NonStandardIdentifierChanged
    {
        public class Event : NonStandardIdentifierChangedEvent, IRequest
        {
            public Event(JobDto jobDto, Guid correlationId) : base(jobDto, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly ILogger<NonStandardIdentifierChanged> _logger;
            private readonly JobDataContext _context;

            public Handler(
                ILogger<NonStandardIdentifierChanged> logger,
                JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("Event: {eventName}, JobId: {jobId}",
                    nameof(NonStandardIdentifierChangedEvent),
                    request.Job.JobId);
                _logger.LogInformation("Starting with request {@request}", request);

                var jobDto = request.Job;
                var job = await _context.Jobs.FirstOrDefaultAsync(x => x.Id == request.Job.JobId, cancellationToken);
                var wipRecord = await _context.WipRecords.FirstOrDefaultAsync(w => w.Id == request.Job.JobId, cancellationToken);

                if (job == null || wipRecord == null)
                {
                    _logger.LogWarning("{event} The Job or WipRecord with Id: {id} Does Not Exist", nameof(NonStandardIdentifierChangedEvent), jobDto.JobId);
                    return Unit.Value;
                }

                job.IsNonStandardJob = jobDto.IsNonStandardJob;
                job.ModifiedBy = nameof(NonStandardIdentifierChangedEvent);
                wipRecord.IsNonStandardJob = jobDto.IsNonStandardJob;
                wipRecord.ModifiedBy = nameof(NonStandardIdentifierChangedEvent);

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }
        }
    }
}
