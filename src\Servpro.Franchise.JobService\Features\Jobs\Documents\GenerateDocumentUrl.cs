﻿using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    public class GenerateDocumentUrl
    {
        public class Command : IRequest<ICollection<PreSignedDto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }

            public string OptionalContentType { get; set; }
            public ICollection<MediaDto> Media { get; set; }
        }

        public class MediaDto
        {
            public Guid ArtifactTypeId { get; set; }
            public bool IsForUpload { get; set; }
            public string Name { get; set; }
            public string ContentType { get; set; }
        }

        public class Validator : AbstractValidator<Command>
        {
            public Validator()
            {
                RuleFor(m => m.JobId).NotEmpty();
            }
        }

        public class PreSignedDto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public string Key { get; set; }
            public string BucketName { get; set; }
        }

        public class Handler : IRequestHandler<Command, ICollection<PreSignedDto>>
        {
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private readonly ILogger<Handler> _logger;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(IConfiguration config, IAmazonS3 clientS3, ILogger<Handler> logger)
            {
                _config = config;
                _clientS3 = clientS3;
                _logger = logger;
            }

            private static readonly HashSet<string> AllowedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".esx", ".ppt", ".xls", ".xlsx", ".doc", ".docx", ".json", ".xml"
            };

            public async Task<ICollection<PreSignedDto>> Handle(Command request,
                CancellationToken cancellationToken)
            {
                _logger.LogInformation("Generating pre-signed URLs for JobId: {JobId}, FranchiseSetId: {FranchiseSetId}, MediaCount: {MediaCount}",
                                        request.JobId, request.FranchiseSetId, request.Media?.Count ?? 0);

                foreach (var media in request.Media)
                {
                    var ext = System.IO.Path.GetExtension(media.Name);
                    if (!AllowedExtensions.Contains(ext))
                    {
                        _logger.LogWarning("File rejected due to invalid extension: {FileName} ({Extension})", media.Name, ext);
                        throw new ValidationException($"Files with extension '{ext}' are not allowed.");
                    }
                }

                var s3urls = request.Media.AsEnumerable().Select(media => GetPreSignedUrl(media, request)).ToList();

                _logger.LogInformation("Successfully generated {Count} pre-signed URLs for JobId: {JobId}", s3urls.Count, request.JobId);

                return s3urls;
            }


            private PreSignedDto GetPreSignedUrl(MediaDto media, Command request)
            {
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                var Id = Guid.NewGuid();

                var fileExt = media.Name.Split('.')[1];
                var key = $"{request.FranchiseSetId}/{request.JobId}/Documents/{Id}.{fileExt}";

                ResponseHeaderOverrides responseHeader = new ResponseHeaderOverrides
                {
                    ContentDisposition = $"attachment; filename={media.Name}"
                };

                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = _config[S3MediaBucketNameKey],
                    Key = key,
                    Verb = HttpVerb.PUT,
                    ContentType = media.ContentType ?? "multipart/form-data",
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan),
                    ResponseHeaderOverrides = responseHeader,
                };

                var preSignedUrlDto = new PreSignedDto
                {
                    Id = Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = media.Name,
                    Key = key,
                    BucketName = preSignedUrlRequest.BucketName
                };

                return preSignedUrlDto;
            }
        }
    }
}