﻿using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Artifacts
{
    public class GetArtifact
    {
        public class Query : IRequest<Dto>
        {
            public Guid Id { get; set; }
        }

        public class Dto
        {
            public Guid FranchiseSetId { get; set; }
            public Guid JobId { get; set; }
            public Job Job { get; set; }
            public Guid MediaTypeId { get; set; }
            public Guid ArtifactTypeId { get; set; }
            public Guid? FormTemplateId { get; set; } /* If this has a value, it IS a Form; otherwise an Artifact */
            public bool IsForm { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public string MediaPath { get; set; }
            public bool IsDeleted { get; set; }
            public bool IsForUpload { get; set; }
            public bool UploadedSuccessfully { get; set; }
            public Guid? JobSketchId { get; set; }
            public JobSketch JobSketch { get; set; }
            public Guid? JobInvoiceId { get; set; }
            public JobInvoice JobInvoice { get; set; }
            public DateTime? SignedDate { get; set; }
            public string FormVersion { get; set; }
            public DateTime? SyncDate { get; set; }
            public DateTime? ArtifactDate { get; set; }
            public string Comment { get; set; }
            public Guid? JobAreaId { get; set; }
            public JobArea JobArea { get; set; }
            public Guid? JobVisitId { get; set; }
            public JobVisit JobVisit { get; set; }
            public Guid? ZoneId { get; set; }
            public Guid? JobAreaMaterialId { get; set; }
            public string BucketName { get; set; }
        }

        public class Handler : IRequestHandler<Query, Dto>
        {
            private readonly JobReadOnlyDataContext _context;
            private readonly ILogger<Handler> _logger;

            public Handler(
              JobReadOnlyDataContext context,
              ILogger<Handler> logger)
            {
                _context = context;
                _logger = logger;
            }

            public async Task<Dto> Handle(Query request, CancellationToken cancellationToken)
            {
                var jobArtifact = await _context.MediaMetadata
                    .FirstOrDefaultAsync(x => x.Id == request.Id && x.IsDeleted == false, cancellationToken);

                if (jobArtifact is null)
                    throw new ResourceNotFoundException("Job artifact not found");

                return Map(jobArtifact);
            }

            private Dto Map(MediaMetadata x)
            {
                return new Dto
                {
                    FranchiseSetId = x.FranchiseSetId,
                    JobId = x.JobId,
                    MediaTypeId = x.MediaTypeId,
                    ArtifactTypeId = x.ArtifactTypeId,
                    FormTemplateId = x.FormTemplateId, /* If this has a value, it IS a Form; otherwise an Artifact */
                    IsForm = !x.FormTemplateId.IsNullOrEmpty(),
                    Name = x.Name,
                    Description = x.Description,
                    MediaPath = x.MediaPath,
                    BucketName = x.BucketName,
                    IsDeleted = x.IsDeleted,
                    IsForUpload = x.IsForUpload,
                    UploadedSuccessfully = x.UploadedSuccessfully,
                    JobSketchId = x.JobSketchId,
                    JobSketch = x.JobSketch,
                    JobInvoiceId = x.JobInvoiceId,
                    JobInvoice = x.JobInvoice,
                    SignedDate = x.SignedDate,
                    FormVersion = x.FormVersion,
                    SyncDate = x.SyncDate,
                    ArtifactDate = x.ArtifactDate,
                    Comment = x.Comment,
                    JobAreaId = x.JobAreaId,
                    JobArea = x.JobArea,
                    JobVisitId = x.JobVisitId,
                    JobVisit = x.JobVisit,
                    ZoneId = x.ZoneId,
                    JobAreaMaterialId = x.JobAreaMaterialId,
                };
            }
        }
    }
}
