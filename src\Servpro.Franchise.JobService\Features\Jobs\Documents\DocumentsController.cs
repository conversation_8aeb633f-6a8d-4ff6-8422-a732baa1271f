﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Servpro.Franchise.JobService.Features.Jobs.Documents.FormTemplates;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Documents;
using Microsoft.Extensions.Hosting.Internal;
using Newtonsoft.Json;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.Franchise.JobService.Features.Jobs.Documents.Forms;
using FluentValidation;

// For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Servpro.Franchise.JobService.Features.Jobs.Documents
{
    [Route("api/documents")]
    public class DocumentsController : ControllerBase
    {
        private readonly IMediator _mediator;
        
        public DocumentsController(IMediator mediator)
        {
            _mediator = mediator;
        }
        
        [HttpGet("/api/jobs/{jobId}/documents", Name = "GetDocuments")]
        public async Task<ActionResult<ICollection<GetDocument.Dto>>> GetDocumentsAsync(Guid jobId)
        {

            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetDocument.Query(jobId, User.FranchiseSetId().Value));
            return Ok(response);
        }

        [HttpGet("/api/jobs/{jobId}/queries/get-forms", Name = "GetForms")]
        public async Task<ActionResult<ICollection<GetForms.Dto>>> GetFormsAsync(Guid jobId)
        {
            var franchiseSetId = User?.FranchiseSetId();
            if (!franchiseSetId.HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetForms.Query(jobId, franchiseSetId.Value));
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/generate-merged-form", Name = "GenerateMergedFormsAsync")]
        public async Task<IActionResult> GenerateMergedFormsAsync(Guid jobId, [FromBody] DisplayForm.Command command)
        {
            var franchiseSetId = User?.FranchiseSetId();
            if (!franchiseSetId.HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);
            if (jobId == Guid.Empty)
                return BadRequest();

            command.JobId = jobId;
            command.FranchiseSetId = franchiseSetId.Value;

            var dto = await _mediator.Send(command);

            if (dto == null)
                return BadRequest();

            var stream = dto.Stream;
            var bytes = stream.ToArray();
            var ms = new MemoryStream(bytes);

            return File(ms, dto.FileType, dto.FileName);
        }

        [HttpGet("/api/jobs/{jobId}/queries/misc-documents", Name = "GetMiscDocuments")]
        public async Task<ActionResult<ICollection<GetMiscellaneousDocuments.Dto>>> GetMiscellaneousDocumentsAsync(Guid jobId)
        {
            
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetMiscellaneousDocuments.Query(jobId, User.FranchiseSetId().Value));
            return Ok(response);
            
        }


        [HttpPost("/api/jobs/{jobId}/commands/delete-documents", Name = "DeleteDocuments")]
        public async Task<ActionResult<bool>> DeleteAsync(Guid jobId, [FromBody] DeleteDocument.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/save-document-metadata", Name = "SaveDocumentMetadata")]
        public async Task<ActionResult<ICollection<SaveDocumentMetadata.Dto>>> PostAsync(Guid jobId, [FromBody] SaveDocumentMetadata.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }
        
        [HttpPost("/api/jobs/{jobId}/commands/download-documents", Name = "DownloadDocuments")]
        public async Task<ActionResult<ICollection<DownloadDocument.Dto>>> DownloadDocumentsAsync(Guid jobId, [FromBody] DownloadDocument.Query query)
        {
            if (jobId == Guid.Empty)
                return BadRequest();

            if (!User.FranchiseSetId().HasValue )
                return StatusCode(StatusCodes.Status403Forbidden);

            query.FranchiseSetId = User.FranchiseSetId().Value;

            query.JobId = jobId;
            var response = await _mediator.Send(query);
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobId}/commands/update-misc-documents", Name = nameof(UpdateMiscellaneousDocumentAsync))]
        public async Task<ActionResult<UpdateMiscellaneousDocuments.Dto>> UpdateMiscellaneousDocumentAsync(Guid jobId, [FromBody] UpdateMiscellaneousDocuments.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("/api/jobs/commands/generate-documents-url", Name = "GenerateDocumentUrl")]
        public async Task<ActionResult<ICollection<SaveDocumentMetadata.Dto>>> PostAsync([FromBody] GenerateDocumentUrl.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.FranchiseSetId = User.FranchiseSetId().Value;

            try
            {
                var response = await _mediator.Send(command);
                return Ok(response);
            }
            catch (ValidationException ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("/api/jobs/{jobId}/commands/save-documents", Name = "SaveDocument")]
        public async Task<ActionResult<ICollection<SaveDocumentMetadata.Dto>>> PostAsync(Guid jobId, [FromBody] SaveDocument.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.JobId = jobId;
            command.FranchiseSetId = User.FranchiseSetId().Value;

            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPut("/api/jobs/{jobId}/commands/update-invoice", Name = nameof(UpdateInvoiceAsync))]
        public async Task<ActionResult<UpdateInvoice.Dto>> UpdateInvoiceAsync(Guid jobId, [FromBody] UpdateInvoice.Command command)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            command.FranchiseSetId = User.FranchiseSetId().Value;
            command.JobId = jobId;
            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpGet("/api/jobs/{jobId}/queries/get-invoice", Name = "GetInvoice")]
        public async Task<ActionResult<GetInvoice.Dto>> GetInvoiceAsync(Guid jobId)
        {
            if (!User.FranchiseSetId().HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            var response = await _mediator.Send(new GetInvoice.Query(jobId));
            return Ok(response);
        }

        [HttpGet("/api/jobs/{JobFormId}/queries/get-form", Name = "GetForm")]
        public async Task<ActionResult<GetForms.Dto>> GetFormAsync(Guid jobFormId)
        {
            if (jobFormId == Guid.Empty)
                return BadRequest();

            var response = await _mediator.Send(new GetForm.Query(jobFormId));
            return Ok(response);
        }

        [HttpPost("/api/jobs/{jobFormId}/commands/delete-form", Name = "DeleteForm")]
        public async Task<ActionResult<bool>> DeleteAsync(Guid jobFormId)
        {
            if (jobFormId == Guid.Empty)
                return BadRequest();

            var response = await _mediator.Send(new DeleteForm.Command(jobFormId));
            return Ok(response);
        }

        [HttpGet("/api/jobs/{jobId}/forms/template/{templateId}", Name = "GetFormTemplate")]
        public async Task<ActionResult<GetForms.Dto>> GetFormAsync(Guid jobId, Guid templateId)
        {
            var franchiseSetId = User?.FranchiseSetId();
            if (!franchiseSetId.HasValue)
                return StatusCode(StatusCodes.Status403Forbidden);

            if (jobId == Guid.Empty || templateId == Guid.Empty)
                return BadRequest();

            var response = await _mediator.Send(new GetFormTemplate.Query(jobId, templateId, franchiseSetId.Value));
            return Ok(response);
        }
    }
}
