﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;
using Servpro.Franchise.JobService.Models.Drybook;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs.Drybook;
using Task = System.Threading.Tasks.Task;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Events
{
    public class EquipmentPlacedUpdated
    {
        public class Event : EquipmentPlacementUpdatedEvent, IRequest
        {
            public Event(EquipmentPlacedUpdatedDto equipmentPlaced, Guid correlationId) : base(equipmentPlaced, correlationId)
            {
            }
        }

        public class Handler : IRequestHandler<Event>
        {
            private readonly JobDataContext _context;
            private readonly ILogger<Handler> _logger;

            public Handler(ILogger<Handler> logger, JobDataContext context)
            {
                _logger = logger;
                _context = context;
            }

            public async Task<Unit> Handle(Event request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("{event} Handler Activated", nameof(EquipmentPlacementUpdatedEvent));
                var dto = request.EquipmentPlacementUpdated;
                var endDate = DateTime.UtcNow;
                try
                {
                    var job = await _context.Jobs
                        .Include(j => j.JobAreas)
                        .FirstOrDefaultAsync(q => q.Id == dto.JobId, cancellationToken: cancellationToken);

                    if (job is null)
                        throw new ResourceNotFoundException($"Job not found: {dto.JobId}");

                    var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == dto.RoomId);

                    if (jobArea is null)
                    {
                        jobArea = job.JobAreas.FirstOrDefault(x => x.JobAreaTypeId == JobAreaTypes.Job);
                        if (jobArea is null)
                            throw new ResourceNotFoundException($"Job Room not found: {dto.RoomId}");
                    }
                    if (job.JobVisits.Count > 0)
                        endDate = job.JobVisits.Max(x => x.Date);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    throw;
                }
                

              

                var equipmentsIds = dto.Placements.Select(x => x.EquipmentId).ToList();

                var equipmentPlacementToUpdate = await _context.EquipmentPlacements
                    .Where(x => equipmentsIds.Contains(x.EquipmentId))
                    .ToListAsync(cancellationToken);

                foreach (var item in equipmentPlacementToUpdate)
                {
                    var itemSource = dto.Placements.FirstOrDefault(q => q.EquipmentId == item.EquipmentId);

                    if (itemSource == null) continue;
                    item.BeginDate = itemSource.BeginDate;

                    if (itemSource.EndDate.HasValue && itemSource.EndDate <= endDate)
                       item.EndDate = itemSource.EndDate;
                    
                }

                await _context.SaveChangesAsync(cancellationToken);

                return Unit.Value;
            }

            private async Task JobPlaceEquipments(Job job, EquipmentPlacedDto dto, CancellationToken cancellationToken)
            {
                var equipments = dto.Placements.ToList();

                var equipmentsToAdd = equipments.Select(x => x.EquipmentId).ToList();
                var existingEquipment = (await _context.Equipments
                    .Include(x => x.EquipmentModel)
                    .ThenInclude(x => x.EquipmentType)
                    .Where(x => x.FranchiseSetId == dto.FranchiseSetId
                                && equipmentsToAdd.Contains(x.Id))
                    .ToListAsync(cancellationToken));

                var jobArea = job.JobAreas.FirstOrDefault(x => x.RoomId == dto.RoomId);

                if (jobArea is null)
                {
                    //verify default jobArea
                    jobArea = job.JobAreas.FirstOrDefault(q => q.JobAreaTypeId == JobAreaTypes.Job);

                    if (jobArea == null)
                    {
                        jobArea = new Models.Drybook.JobArea
                        {
                            Id = Guid.NewGuid(),
                            CreatedBy = dto.Username,
                            CreatedDate = DateTime.UtcNow,
                            JobId = dto.JobId,
                            Name = "Default Job Room",
                            JobAreaTypeId = JobAreaTypes.Job
                        };

                        job.JobAreas.Add(jobArea);
                    }
                }

                foreach (var item in existingEquipment)
                {
                    jobArea.EquipmentPlacements ??= new List<EquipmentPlacement>();

                    var itemInfo = equipments.FirstOrDefault(q=> q.EquipmentId == item.Id);

                    jobArea.EquipmentPlacements.Add(new EquipmentPlacement
                    {
                        Id = itemInfo?.Id ?? Guid.NewGuid(),
                        CreatedBy = dto.Username,
                        CreatedDate = DateTime.UtcNow,
                        BeginDate = DateTime.UtcNow,
                        EquipmentId = item.Id,
                        IsUsedInValidation = false
                    });
                }

                await _context.SaveChangesAsync(cancellationToken);
            }
        }
    }
}
