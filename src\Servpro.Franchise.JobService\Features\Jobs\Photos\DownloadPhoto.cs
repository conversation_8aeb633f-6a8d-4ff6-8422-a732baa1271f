﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Servpro.Franchise.JobService.Common;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Models;

namespace Servpro.Franchise.JobService.Features.Jobs.Photos
{
    public class DownloadPhoto
    {
        public class Query : IRequest<ICollection<Dto>>
        {
            public Guid JobId { get; set; }
            public Guid FranchiseSetId { get; set; }

            public ICollection<Guid> Ids { get; set; } = new List<Guid>();
        }

        public class Validator : AbstractValidator<Query>
        {
            public Validator()
            {
                RuleFor(m => m.Ids).NotEmpty();
                RuleForEach(m => m.Ids).NotEmpty();
            }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public string SignedUrl { get; set; }
            public string Name { get; set; }
            public string MediaPath { get; set; }
            public string BucketName { get; set; }
        }

        public class Handler : IRequestHandler<Query, ICollection<Dto>>
        {
            private readonly JobDataContext _db;
            private readonly IConfiguration _config;
            private readonly IAmazonS3 _clientS3;
            private const string S3MediaBucketNameKey = "Aws:S3MediaBucketName";
            private const string S3PreSignedUrlExpirationKey = "Aws:S3PreSignedUrlExpiration";

            public Handler(JobDataContext db, IConfiguration config, IAmazonS3 clientS3)
            {
                _db = db;
                _config = config;
                _clientS3 = clientS3;
            }

            public async Task<ICollection<Dto>> Handle(Query request,
                CancellationToken cancellationToken)
            {
                var mediaMetadata = await _db.MediaMetadata.Where(x => x.JobId == request.JobId &&
                                                                 x.FranchiseSetId == request.FranchiseSetId &&
                                                                 (x.MediaTypeId == MediaTypes.Photo || x.MediaTypeId == MediaTypes.Sketch) &&
                                                                 request.Ids.Contains(x.Id) &&
                                                                 !x.IsDeleted).ToListAsync(cancellationToken);

                var result = mediaMetadata.Select(GetPreSignedUrl).ToList();
                return result;
            }

            private Dto GetPreSignedUrl(MediaMetadata metadata)
            {
                var key = metadata.MediaPath;
                var s3ExpirationTimeSpan = _config.GetValue(S3PreSignedUrlExpirationKey, new TimeSpan(24, 0, 0));
                // Create a GetPreSignedUrlRequest request
                GetPreSignedUrlRequest preSignedUrlRequest = new GetPreSignedUrlRequest
                {
                    BucketName = metadata.BucketName.IsNullOrWhiteSpace() ? _config[S3MediaBucketNameKey] : metadata.BucketName,
                    Key = key,
                    Expires = DateTime.UtcNow.Add(s3ExpirationTimeSpan)
                };

                var preSignedUrlDto = new Dto
                {
                    Id = metadata.Id,
                    SignedUrl = _clientS3.GetPreSignedURL(preSignedUrlRequest),
                    Name = metadata.Name,
                    MediaPath = metadata.MediaPath,
                    BucketName = metadata.BucketName
                };

                return preSignedUrlDto;
            }
        }
    }
}