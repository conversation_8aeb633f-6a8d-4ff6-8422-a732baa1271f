﻿using FluentValidation;
using FluentValidation.Results;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Servpro.Franchise.JobService.Common.ExtensionMethods;
using Servpro.Franchise.JobService.Data;
using Servpro.Franchise.JobService.Infrastructure;
using Servpro.Franchise.JobService.Infrastructure.LookupService;
using Servpro.Franchise.JobService.Models;
using Servpro.FranchiseSystems.Framework.Messaging;
using Servpro.FranchiseSystems.Framework.Messaging.Events.Jobs;
using Servpro.FranchiseSystems.Framework.Setup.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Servpro.FranchiseSystems.Framework.Messaging.Common.Exceptions;

namespace Servpro.Franchise.JobService.Features.Jobs.Locks

{
    public class LockJob
    {
        public class Command : IRequest<Dto>
        {
            public Guid JobId { get; set; }
            public string DeviceId { get; set; }
            public Guid ApplicationId { get; set; }
        }

        public class Dto
        {
            public Guid Id { get; set; }
            public bool IsLocked { get; set; }
            public Guid LockedByUserId { get; set; }
            public string LockedByUserFullName { get; set; }
            public DateTime LockedTimestamp { get; set; }
            public string LockedByDevice { get; set; }
            public Guid LockedByApplicationId { get; set; }
            public string LockedByApplicationName { get; set; }
            public Guid? UnlockedByUserId { get; set; }
            public DateTime? UnlockedTimestamp { get; set; }
            public string UnlockedByDevice { get; set; }
            public bool IsAlreadyLocked { get; set; }
        }

        public class CommandValidator : AbstractValidator<Command>
        {
            public CommandValidator()
            {
                RuleFor(x => x.ApplicationId).NotNull().NotEmpty();
            }
        }

        public class Handler : IRequestHandler<Command, Dto>
        {
            private readonly JobDataContext _context;
            private readonly ILookupServiceClient _lookupServiceClient;
            private readonly IUserInfoAccessor _userInfoAccessor;
            private readonly ISessionIdAccessor _sessionIdAccessor;
            private readonly ILogger<LockJob> _logger;

            public Handler(
                JobDataContext context,
                ILookupServiceClient lookupServiceClient,
                IUserInfoAccessor userInfoAccessor,
                ISessionIdAccessor sessionIdAccessor,
                ILogger<LockJob> logger)
            {
                _context = context;
                _lookupServiceClient = lookupServiceClient;
                _userInfoAccessor = userInfoAccessor;
                _sessionIdAccessor = sessionIdAccessor;
                _logger = logger;
            }

            public async Task<Dto> Handle(Command request, CancellationToken cancellationToken)
            {
                using var scope = _logger.BeginScope("{jobId}{eventName}", request.JobId, nameof(LockJob));
                _logger.LogInformation("Begin command with request: {@request}", request);

                ValidateRequest(request);

                Job job = await GetJob(request, cancellationToken);

                // get the most recent job-lock entry
                var jobLocked = job.JobLocks
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefault();

                _logger.LogInformation("Most recent job lock entry: {@lock}", jobLocked);

                if (jobLocked != null && jobLocked.IsLocked)
                    return Map(jobLocked, true);

                UserInfo user = _userInfoAccessor.GetUserInfo();

                ValidateUserFranchiseSet(job, user);

                var applicationName = await GetApplicationName(request, cancellationToken);

                JobLock jobLock = CreateJobLock(request, user, applicationName);

                ClearOldJobLocks(job);

                _logger.LogInformation("Created new job lock: {@newJobLock}.", jobLock);

                job.JobLocks.Add(jobLock);

                OutboxMessage newEvent = GenerateEvent(jobLock, user);
                await _context.OutboxMessages.AddAsync(newEvent, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);
                return Map(jobLock, false);
            }

            private void ValidateRequest(Command request)
            {
                if (string.IsNullOrEmpty(request.DeviceId))
                {
                    throw new ValidationException(new List<ValidationFailure> {
                        new ValidationFailure(nameof(request.DeviceId), "DeviceId is required")
                    });
                }
            }

            private async Task<Job> GetJob(Command request, CancellationToken cancellationToken)
            {
                var job = await _context.Jobs
                                        .Include(j => j.JobLocks)
                                        .FirstOrDefaultAsync(j => j.Id == request.JobId, cancellationToken);

                if (job == null)
                    throw new ResourceNotFoundException($"Job not found: {request.JobId}");

                return job;
            }

            private void ValidateUserFranchiseSet(Job job, UserInfo user)
            {
                if (job.FranchiseSetId != user.FranchiseSetId)
                {
                    _logger.LogWarning($"User's franchiseSetId ({user.FranchiseSetId}) does not match the job's franchiseSetId ({job.FranchiseSetId})");
                    throw new ValidationException($"User's franchiseSetId ({user.FranchiseSetId}) does not match the job's franchiseSetId ({job.FranchiseSetId})");
                }
            }

            private async Task<string> GetApplicationName(Command request, CancellationToken cancellationToken)
            {
                var lookups = await _lookupServiceClient.GetLookupsAsync(cancellationToken);
                var clientApplication = lookups.ClientApplications.FirstOrDefault(c => c.Id == request.ApplicationId);
                if (clientApplication == null)
                    throw new ValidationException($"Client Application Id not found");

                return clientApplication.Name;
            }

            private JobLock CreateJobLock(Command request, UserInfo user, string applicationName)
            {
                return new JobLock
                {
                    IsLocked = true,
                    JobId = request.JobId,
                    LockedByUserId = user.Id,
                    LockedByUserFullName = user.Name,
                    LockedByDeviceId = request.DeviceId,
                    LockedTime = DateTime.UtcNow,
                    LockedByApplicationId = request.ApplicationId,
                    LockedByApplicationName = applicationName,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = user.Username,
                    ModifiedBy = user.Username
                };
            }

            private void ClearOldJobLocks(Job job)
            {
                foreach (var @lock in job.JobLocks.Where(x => x.IsLocked))
                {
                    @lock.IsLocked = false;
                }
            }

            private OutboxMessage GenerateEvent(JobLock jobLock, UserInfo user)
            {
                var jobLockDto = new JobLockDto
                {
                    Id = jobLock.Id,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.UnlockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId,
                    JobId = jobLock.JobId
                };

                if (!Guid.TryParse(_sessionIdAccessor.GetCorrelationId(), out Guid correlationId))
                    correlationId = Guid.NewGuid();

                return new OutboxMessage(new JobLockEvent(jobLockDto, correlationId).ToJson(),
                    nameof(JobLockEvent),
                    correlationId,
                    user.Name);
            }

            private Dto Map(JobLock jobLock, bool islock)
            {
                return new Dto
                {
                    Id = jobLock.Id,
                    IsLocked = jobLock.IsLocked,
                    LockedByUserId = jobLock.LockedByUserId,
                    LockedByUserFullName = jobLock.LockedByUserFullName,
                    LockedTimestamp = jobLock.LockedTime,
                    LockedByDevice = jobLock.LockedByDeviceId,
                    LockedByApplicationId = jobLock.LockedByApplicationId,
                    LockedByApplicationName = jobLock.LockedByApplicationName,
                    UnlockedByUserId = jobLock.UnlockedByUserId,
                    UnlockedTimestamp = jobLock.UnlockedTime,
                    UnlockedByDevice = jobLock.UnlockedByDeviceId,
                    IsAlreadyLocked = islock
                };
            }
        }
    }
}
