﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Servpro.Franchise.JobService.Data.Migrations
{
    public partial class OptimizeWithIndexesPart2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("c41bf10e-99d4-45de-b1bc-771d9dafbb43"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("f9697bc1-4b60-44ca-8db5-88f2df90ae0c"), null, new DateTime(2022, 8, 2, 19, 30, 14, 630, DateTimeKind.Utc).AddTicks(9362), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });

            migrationBuilder.CreateIndex(
                name: "IX_Task_JobId_JobTriStateQuestionId",
                table: "Task",
                columns: new[] { "JobId", "JobTriStateQuestionId" });

            migrationBuilder.CreateIndex(
                name: "IX_Task_JobId_TaskStatusId",
                table: "Task",
                columns: new[] { "JobId", "TaskStatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_ArtifactTypeId_JobId_IsDeleted",
                table: "MediaMetadata",
                columns: new[] { "ArtifactTypeId", "JobId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_JobId_FranchiseSetId_MediaTypeId_IsDeleted_For~",
                table: "MediaMetadata",
                columns: new[] { "JobId", "FranchiseSetId", "MediaTypeId", "IsDeleted", "FormTemplateId" });

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_JobId_JobInvoiceId",
                table: "MediaMetadata",
                columns: new[] { "JobId", "JobInvoiceId" });

            migrationBuilder.CreateIndex(
                name: "IX_MediaMetadata_JobId_JobSketchId",
                table: "MediaMetadata",
                columns: new[] { "JobId", "JobSketchId" });

            migrationBuilder.CreateIndex(
                name: "IX_JournalNote_JobId_VisibilityId",
                table: "JournalNote",
                columns: new[] { "JobId", "VisibilityId" });

            migrationBuilder.CreateIndex(
                name: "IX_JobContactMap_ContactId_JobId_JobContactTypeId",
                table: "JobContactMap",
                columns: new[] { "ContactId", "JobId", "JobContactTypeId" });

            migrationBuilder.CreateIndex(
                name: "IX_JobBusinessMaps_BusinessId_JobBusinessTypeId",
                table: "JobBusinessMaps",
                columns: new[] { "BusinessId", "JobBusinessTypeId" });

            migrationBuilder.CreateIndex(
                name: "IX_JobBusinessMaps_JobId_JobBusinessTypeId",
                table: "JobBusinessMaps",
                columns: new[] { "JobId", "JobBusinessTypeId" });

            migrationBuilder.CreateIndex(
                name: "IX_Job_FranchiseSetId_JobFileCoordinatorId_ProjectManagerId_Cre~",
                table: "Job",
                columns: new[] { "FranchiseSetId", "JobFileCoordinatorId", "ProjectManagerId", "CrewChiefId", "RecpDispatcherId", "GeneralManagerId", "OfficeManagerId", "ReconSupportId", "ProductionManagerId", "PriorityResponderId" });

            migrationBuilder.CreateIndex(
                name: "IX_Job_Id_FranchiseSetId_MentorId",
                table: "Job",
                columns: new[] { "Id", "FranchiseSetId", "MentorId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Task_JobId_JobTriStateQuestionId",
                table: "Task");

            migrationBuilder.DropIndex(
                name: "IX_Task_JobId_TaskStatusId",
                table: "Task");

            migrationBuilder.DropIndex(
                name: "IX_MediaMetadata_ArtifactTypeId_JobId_IsDeleted",
                table: "MediaMetadata");

            migrationBuilder.DropIndex(
                name: "IX_MediaMetadata_JobId_FranchiseSetId_MediaTypeId_IsDeleted_For~",
                table: "MediaMetadata");

            migrationBuilder.DropIndex(
                name: "IX_MediaMetadata_JobId_JobInvoiceId",
                table: "MediaMetadata");

            migrationBuilder.DropIndex(
                name: "IX_MediaMetadata_JobId_JobSketchId",
                table: "MediaMetadata");

            migrationBuilder.DropIndex(
                name: "IX_JournalNote_JobId_VisibilityId",
                table: "JournalNote");

            migrationBuilder.DropIndex(
                name: "IX_JobContactMap_ContactId_JobId_JobContactTypeId",
                table: "JobContactMap");

            migrationBuilder.DropIndex(
                name: "IX_JobBusinessMaps_BusinessId_JobBusinessTypeId",
                table: "JobBusinessMaps");

            migrationBuilder.DropIndex(
                name: "IX_JobBusinessMaps_JobId_JobBusinessTypeId",
                table: "JobBusinessMaps");

            migrationBuilder.DropIndex(
                name: "IX_Job_FranchiseSetId_JobFileCoordinatorId_ProjectManagerId_Cre~",
                table: "Job");

            migrationBuilder.DropIndex(
                name: "IX_Job_Id_FranchiseSetId_MentorId",
                table: "Job");

            migrationBuilder.DeleteData(
                table: "VersionData",
                keyColumn: "Id",
                keyValue: new Guid("f9697bc1-4b60-44ca-8db5-88f2df90ae0c"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "OutboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.AlterColumn<DateTime>(
                name: "Version",
                table: "InboxMessages",
                type: "timestamp(6)",
                rowVersion: true,
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp(6)",
                oldRowVersion: true,
                oldNullable: true)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn);

            migrationBuilder.InsertData(
                table: "VersionData",
                columns: new[] { "Id", "CreatedBy", "CreatedDate", "ModifiedBy", "ModifiedDate", "ReleaseNotesUrl", "VersionAcknowledge", "VersionNumber" },
                values: new object[] { new Guid("c41bf10e-99d4-45de-b1bc-771d9dafbb43"), null, new DateTime(2022, 8, 2, 15, 28, 35, 154, DateTimeKind.Utc).AddTicks(7782), null, null, "https://servpro.interactgo.com/Interact/Pages/Section/SubFullOne.aspx?subsection=3906", true, "2.0.0.0" });

        }
    }
}
